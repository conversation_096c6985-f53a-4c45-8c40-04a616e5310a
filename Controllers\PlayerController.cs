using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.Interface;
using WebApplication_HM.DTOs.ResultDTO;
using WebApplication_HM.DTOs.RequestDTO;

namespace WebApplication_HM.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PlayerController : ControllerBase
    {
        private readonly IPlayerService _playerService;
        public PlayerController(IPlayerService playerService)
        {
            _playerService = playerService;
        }

        /// <summary>
        /// 用户登录接口，校验账号密码，返回登录结果
        /// </summary>
        /// <param name="request">登录请求参数</param>
        /// <returns>登录结果</returns>
        [HttpPost("Login")]
        public ActionResult<LoginResultDTO> Login([FromBody] LoginRequestDTO request)
        {
            var result = _playerService.Login(request);

            // 如果登录成功，设置Session
            if (result.Success && result.UserId > 0)
            {
                // 设置用户ID到Session
                HttpContext.Session.SetInt32("UserId", result.UserId);

                // 设置用户登录信息到Session
                var userLoginInfo = new
                {
                    userId = result.UserId,
                    nickname = result.NickName,
                    username = request.Number,
                    loginTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                HttpContext.Session.SetString("UserLogin", System.Text.Json.JsonSerializer.Serialize(userLoginInfo));

                // 设置Session过期时间
                HttpContext.Session.SetString("LoginTime", DateTime.Now.ToString());
            }

            return Ok(result);
        }

        /// <summary>
        /// 玩家注册
        /// </summary>
        /// <param name="request">注册请求参数</param>
        /// <returns>注册结果</returns>
        [HttpPost("Register")]
        public ActionResult<RegisterResultDTO> Register([FromBody] RegisterRequestDTO request)
        {
            var result = _playerService.Register(request);
            return Ok(result);
        }

        /// <summary>
        /// 用户登出接口
        /// </summary>
        /// <returns>登出结果</returns>
        [HttpPost("Logout")]
        public ActionResult<object> Logout()
        {
            try
            {
                // 清除Session
                HttpContext.Session.Clear();

                return Ok(new
                {
                    Success = true,
                    Message = "登出成功"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Message = "登出失败：" + ex.Message
                });
            }
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <returns>用户信息</returns>
        [HttpGet("GetCurrentUser")]
        public ActionResult<object> GetCurrentUser()
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null || userId <= 0)
                {
                    return Unauthorized(new
                    {
                        Success = false,
                        Message = "用户未登录"
                    });
                }

                var userLoginJson = HttpContext.Session.GetString("UserLogin");
                if (string.IsNullOrEmpty(userLoginJson))
                {
                    return Unauthorized(new
                    {
                        Success = false,
                        Message = "用户会话已过期"
                    });
                }

                var userLogin = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(userLoginJson);

                return Ok(new
                {
                    Success = true,
                    Data = userLogin
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Message = "获取用户信息失败：" + ex.Message
                });
            }
        }

        /// <summary>
        /// 获取玩家基础信息
        /// </summary>
        /// <param name="request">玩家信息查询请求</param>
        /// <returns>玩家信息结果</returns>
        [HttpPost("GetPlayerInfo")]
        public ActionResult<PlayerInfoResultDTO> GetPlayerInfo([FromBody] PlayerInfoRequestDTO request)
        {
            var result = _playerService.GetPlayerInfo(request);
            return Ok(result);
        }

        /// <summary>
        /// 领取战斗奖励
        /// </summary>
        /// <param name="request">领取奖励请求</param>
        /// <returns>奖励结果</returns>
        [HttpPost("ClaimBattleReward")]
        public async Task<ActionResult<ClaimRewardResultDTO>> ClaimBattleReward([FromBody] ClaimRewardRequestDTO request)
        {
            var result = await _playerService.ClaimBattleReward(request);
            return Ok(result);
        }

        /// <summary>
        /// 玩家发起战斗，自动分配怪物并结算，返回战斗结果
        /// </summary>
        /// <param name="request">战斗请求参数</param>
        /// <returns>战斗结果</returns>
        [HttpPost("CalculateBattle")]
        public async Task<ActionResult<BattleResultDTO>> CalculateBattle([FromBody] BattleRequestDTO request)
        {
            var result = await _playerService.BattleCalculate(request);
            return Ok(result);
        }

        #region 宠物核心系统接口

        /// <summary>
        /// 获取用户宠物列表 Player/GetUserPets
        /// </summary>
        /// <param name="request">宠物列表查询请求</param>
        /// <returns>宠物列表结果</returns>
        [HttpPost("GetUserPets")]
        public ActionResult<PetListResultDTO> GetUserPets([FromBody] PetListRequestDTO request)
        {
            var result = _playerService.GetUserPets(request);
            return Ok(result);
        }

        /// <summary>
        /// 根据状态获取宠物列表 Player/GetUserPetsByStatus
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="status">宠物状态（可选：牧场、携带）</param>
        /// <param name="onlyMain">是否只查询主战宠物（可选）</param>
        /// <returns>宠物列表结果</returns>
        [HttpGet("GetUserPetsByStatus")]
        public ActionResult<PetListResultDTO> GetUserPetsByStatus(int userId, [FromQuery] string? status = null, [FromQuery] bool? onlyMain = null)
        {
            var request = new PetListRequestDTO
            {
                UserId = userId,
                Status = status,
                OnlyMainPet = onlyMain
            };
            var result = _playerService.GetUserPets(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取牧场宠物列表 Player/GetRanchPets
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>牧场宠物列表</returns>
        [HttpGet("GetRanchPets")]
        public ActionResult<PetListResultDTO> GetRanchPets(int userId)
        {
            var request = new PetListRequestDTO
            {
                UserId = userId,
                Status = "牧场"
            };
            var result = _playerService.GetUserPets(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取携带宠物列表 Player/GetCarryPets
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>携带宠物列表</returns>
        [HttpGet("GetCarryPets")]
        public ActionResult<PetListResultDTO> GetCarryPets(int userId)
        {
            var request = new PetListRequestDTO
            {
                UserId = userId,
                Status = "携带"
            };
            var result = _playerService.GetUserPets(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取主战宠物 Player/GetMainPet - 专为Battle.html设计
        /// 返回Battle.html期望的JSON格式数据
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>主战宠物信息</returns>
        [HttpGet("GetMainPet")]
        public async Task<ActionResult<BattlePetInfoResultDTO>> GetMainPet(int userId)
        {
            var result = await _playerService.GetMainPet(userId);
            return Ok(result);
        }

        /// <summary>
        /// 获取主战宠物列表格式 Player/GetMainPetList - 兼容原有接口
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>主战宠物列表信息</returns>
        [HttpGet("GetMainPetList")]
        public ActionResult<PetListResultDTO> GetMainPetList(int userId)
        {
            var request = new PetListRequestDTO
            {
                UserId = userId,
                OnlyMainPet = true
            };
            var result = _playerService.GetUserPets(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取宠物详细信息
        /// </summary>
        /// <param name="request">宠物详情查询请求</param>
        /// <returns>宠物详情结果</returns>
        [HttpPost("GetPetDetail")]
        public async Task<ActionResult<PetDetailResultDTO>> GetPetDetail([FromBody] PetDetailRequestDTO request)
        {
            var result = await _playerService.GetPetDetail(request);
            return Ok(result);
        }

        /// <summary>
        /// 设置主战宠物
        /// </summary>
        /// <param name="request">设置主战宠物请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("SetMainPet")]
        public ActionResult<LoginResultDTO> SetMainPet([FromBody] SetMainPetRequestDTO request)
        {
            var result = _playerService.SetMainPet(request);
            return Ok(result);
        }

        /// <summary>
        /// 计算宠物最终属性 (POST方式)
        /// </summary>
        /// <param name="request">属性计算请求</param>
        /// <returns>属性计算结果</returns>
        [HttpPost("GetPetAttributes")]
        public ActionResult<AttributeResultDTO> GetPetAttributes([FromBody] AttributeRequestDTO request)
        {
            var result = _playerService.GetPetAttributes(request);
            return Ok(result);
        }

        /// <summary>
        /// 计算宠物最终属性 (GET方式，兼容前端调用)
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="petId">宠物ID</param>
        /// <returns>属性计算结果</returns>
        [HttpGet("GetPetAttributes")]
        public ActionResult<AttributeResultDTO> GetPetAttributesGet([FromQuery] int userId, [FromQuery] int petId)
        {
            var request = new AttributeRequestDTO
            {
                UserId = userId,
                PetId = petId
            };
            var result = _playerService.GetPetAttributes(request);
            return Ok(result);
        }

        #endregion

        #region 地图战斗系统接口

        /// <summary>
        /// 获取地图列表
        /// </summary>
        /// <param name="request">地图列表查询请求</param>
        /// <returns>地图列表结果</returns>
        [HttpPost("GetMapList")]
        public ActionResult<MapListResultDTO> GetMapList([FromBody] MapListRequestDTO request)
        {
            var result = _playerService.GetMapList(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取地图详细信息
        /// </summary>
        /// <param name="request">地图详情查询请求</param>
        /// <returns>地图详情结果</returns>
        [HttpPost("GetMapDetail")]
        public ActionResult<MapDetailResultDTO> GetMapDetail([FromBody] MapDetailRequestDTO request)
        {
            var result = _playerService.GetMapDetail(request);
            return Ok(result);
        }

        #endregion

        #region 配置数据系统接口

        /// <summary>
        /// 获取游戏配置数据
        /// </summary>
        /// <param name="request">配置查询请求</param>
        /// <returns>游戏配置结果</returns>
        [HttpPost]
        public ActionResult<GameConfigResultDTO> GetGameConfig([FromBody] ConfigRequestDTO request)
        {
            var result = _playerService.GetGameConfig(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取宠物配置列表
        /// </summary>
        /// <returns>宠物配置结果</returns>
        [HttpGet("GetPetConfigs")]
        public ActionResult<GameConfigResultDTO> GetPetConfigs()
        {
            var result = _playerService.GetPetConfigs();
            return Ok(result);
        }

        /// <summary>
        /// 获取技能配置列表
        /// </summary>
        /// <returns>技能配置结果</returns>
        [HttpGet("GetSkillConfigs")]
        public ActionResult<GameConfigResultDTO> GetSkillConfigs()
        {
            var result = _playerService.GetSkillConfigs();
            return Ok(result);
        }

        /// <summary>
        /// 获取道具配置列表
        /// </summary>
        /// <returns>道具配置结果</returns>
        [HttpGet("GetItemConfigs")]
        public ActionResult<GameConfigResultDTO> GetItemConfigs()
        {
            var result = _playerService.GetItemConfigs();
            return Ok(result);
        }

        /// <summary>
        /// 获取怪物配置列表
        /// </summary>
        /// <returns>怪物配置结果</returns>
        [HttpGet("GetMonsterConfigs")]
        public ActionResult<GameConfigResultDTO> GetMonsterConfigs()
        {
            var result = _playerService.GetMonsterConfigs();
            return Ok(result);
        }

        /// <summary>
        /// 获取境界配置列表
        /// </summary>
        /// <returns>境界配置结果</returns>
        [HttpGet("GetRealmConfigs")]
        public ActionResult<GameConfigResultDTO> GetRealmConfigs()
        {
            var result = _playerService.GetRealmConfigs();
            return Ok(result);
        }

        /// <summary>
        /// 获取装备配置列表
        /// </summary>
        /// <returns>装备配置结果</returns>
        [HttpGet("GetEquipmentConfigs")]
        public ActionResult<GameConfigResultDTO> GetEquipmentConfigs()
        {
            var result = _playerService.GetEquipmentConfigs();
            return Ok(result);
        }

        /// <summary>
        /// 获取可合成宠物列表（只包含五系宠物：金、木、水、火、土）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>可合成宠物列表，包含状态字段（0=主宠，1=非主宠）和是否主宠字段</returns>
        [HttpGet("synthesis-available")]
        public ActionResult GetSynthesisAvailablePets([FromQuery] int userId)
        {
            try
            {
                if (userId <= 0)
                {
                    return BadRequest(new { success = false, message = "用户ID无效" });
                }

                // 调用服务层获取可合成宠物列表
                var petListResult = _playerService.GetSynthesisAvailablePets(userId);

                if (!petListResult.Success || petListResult.Pets == null)
                {
                    return Ok(new {
                        success = false,
                        message = petListResult.Message,
                        data = new List<object>(),
                        mainPet = (object?)null
                    });
                }

                // 查找主宠
                var mainPet = petListResult.Pets.FirstOrDefault(pet => pet.IsMainPet);
                object? mainPetInfo = null;

                if (mainPet != null)
                {
                    mainPetInfo = new
                    {
                        宠物序号 = mainPet.Id,
                        宠物名字 = mainPet.Name,
                        形象 = mainPet.PetNo,
                        等级 = mainPet.Level,
                        成长 = mainPet.Growth,
                        五行 = mainPet.Element,
                        生命 = mainPet.Hp,
                        最大生命 = mainPet.Hp,
                        攻击 = 0, // 使用默认值，后续可以通过属性计算接口获取
                        防御 = 0,
                        速度 = 0,
                        当前经验 = mainPet.Exp,
                        状态 = "0" // 主宠状态为0
                    };
                }

                // 转换为返回格式
                var availablePets = petListResult.Pets.Select(pet => new
                {
                    宠物序号 = pet.Id,
                    宠物名字 = pet.Name,
                    形象 = pet.PetNo,
                    等级 = pet.Level,
                    成长 = pet.Growth,
                    五行 = pet.Element,
                    生命 = pet.Hp,
                    最大生命 = pet.Hp,
                    攻击 = 0, // 使用默认值
                    防御 = 0,
                    速度 = 0,
                    当前经验 = pet.Exp,
                    状态 = pet.IsMain ? "0" : "1", // 主宠状态为0，非主宠为1
                    是否主宠 = pet.IsMain
                }).ToList();

                return Ok(new {
                    success = true,
                    message = petListResult.Message,
                    data = availablePets,
                    mainPet = mainPetInfo
                });
            }
            catch (Exception ex)
            {
                // 记录错误日志
                Console.WriteLine($"获取可合成宠物列表失败，用户ID: {userId}, 错误: {ex.Message}");
                return StatusCode(500, new {
                    success = false,
                    message = "获取可合成宠物列表失败",
                    data = new List<object>(),
                    mainPet = (object?)null
                });
            }
        }

        /// <summary>
        /// 获取可涅槃（转生）宠物列表（只包含神系宠物：神、神圣、聖、佛、魔、人、鬼、巫、萌、仙、灵、次元等）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>可涅槃宠物列表，包含状态字段（0=主宠，1=非主宠）和是否主宠字段</returns>
        [HttpGet("nirvana-available")]
        public ActionResult GetNirvanaAvailablePets([FromQuery] int userId)
        {
            try
            {
                if (userId <= 0)
                {
                    return BadRequest(new { success = false, message = "用户ID无效" });
                }

                // 调用服务层获取可涅槃宠物列表
                var petListResult = _playerService.GetNirvanaAvailablePets(userId);

                if (!petListResult.Success || petListResult.Pets == null)
                {
                    return Ok(new {
                        success = false,
                        message = petListResult.Message,
                        data = new List<object>(),
                        mainPet = (object?)null
                    });
                }

                // 查找主宠
                var mainPet = petListResult.Pets.FirstOrDefault(pet => pet.IsMainPet);
                object? mainPetInfo = null;

                if (mainPet != null)
                {
                    mainPetInfo = new
                    {
                        宠物序号 = mainPet.Id,
                        宠物名字 = mainPet.Name,
                        形象 = mainPet.PetNo,
                        等级 = mainPet.Level,
                        成长 = mainPet.Growth,
                        五行 = mainPet.Element,
                        生命 = mainPet.Hp,
                        最大生命 = mainPet.Hp,
                        攻击 = 0,
                        防御 = 0,
                        速度 = 0,
                        当前经验 = mainPet.Exp,
                        状态 = "0"
                    };
                }

                // 转换为返回格式
                var availablePets = petListResult.Pets.Select(pet => new
                {
                    宠物序号 = pet.Id,
                    宠物名字 = pet.Name,
                    形象 = pet.PetNo,
                    等级 = pet.Level,
                    成长 = pet.Growth,
                    五行 = pet.Element,
                    生命 = pet.Hp,
                    最大生命 = pet.Hp,
                    攻击 = 0,
                    防御 = 0,
                    速度 = 0,
                    当前经验 = pet.Exp,
                    状态 = pet.IsMain ? "0" : "1",
                    是否主宠 = pet.IsMain
                }).ToList();

                return Ok(new {
                    success = true,
                    message = petListResult.Message,
                    data = availablePets,
                    mainPet = mainPetInfo
                });
            }
            catch (Exception ex)
            {
                // 记录错误日志
                Console.WriteLine($"获取可涅槃宠物列表失败，用户ID: {userId}, 错误: {ex.Message}");
                return StatusCode(500, new {
                    success = false,
                    message = "获取可涅槃宠物列表失败",
                    data = new List<object>(),
                    mainPet = (object?)null
                });
            }
        }

        /// <summary>
        /// 获取可进化宠物列表（只包含携带的宠物）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>可进化宠物列表，包含状态字段（0=主宠，1=非主宠）和是否主宠字段</returns>
        [HttpGet("evolution-available")]
        public ActionResult GetEvolutionAvailablePets([FromQuery] int userId)
        {
            try
            {
                if (userId <= 0)
                {
                    return BadRequest(new { success = false, message = "用户ID无效" });
                }

                // 调用服务层获取可进化宠物列表
                var petListResult = _playerService.GetEvolutionAvailablePets(userId);

                if (!petListResult.Success || petListResult.Pets == null)
                {
                    return Ok(new {
                        success = false,
                        message = petListResult.Message,
                        data = new List<object>(),
                        mainPet = (object?)null
                    });
                }

                // 查找主宠
                var mainPet = petListResult.Pets.FirstOrDefault(pet => pet.IsMainPet);
                object? mainPetInfo = null;

                if (mainPet != null)
                {
                    mainPetInfo = new
                    {
                        宠物序号 = mainPet.Id,
                        宠物名字 = mainPet.Name,
                        形象 = mainPet.PetNo,
                        等级 = mainPet.Level,
                        成长 = mainPet.Growth,
                        五行 = mainPet.Element,
                        生命 = mainPet.Hp,
                        最大生命 = mainPet.Hp,
                        攻击 = 0,
                        防御 = 0,
                        速度 = 0,
                        当前经验 = mainPet.Exp,
                        状态 = "0"
                    };
                }

                // 转换为返回格式
                var availablePets = petListResult.Pets.Select(pet => new
                {
                    宠物序号 = pet.Id,
                    宠物名字 = pet.Name,
                    形象 = pet.PetNo,
                    等级 = pet.Level,
                    成长 = pet.Growth,
                    五行 = pet.Element,
                    生命 = pet.Hp,
                    最大生命 = pet.Hp,
                    攻击 = 0,
                    防御 = 0,
                    速度 = 0,
                    当前经验 = pet.Exp,
                    状态 = pet.IsMain ? "0" : "1",
                    是否主宠 = pet.IsMain
                }).ToList();

                return Ok(new {
                    success = true,
                    message = petListResult.Message,
                    data = availablePets,
                    mainPet = mainPetInfo
                });
            }
            catch (Exception ex)
            {
           
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 根据等级计算所需经验值
        /// </summary>
        /// <param name="level">目标等级</param>
        /// <returns>所需经验值</returns>
        private int GetExpForLevel(int level)
        {
            if (level <= 1) return 0;

            // 简化的经验计算公式：level * level * 100
            // 实际项目中应该使用准确的经验表
            return level * level * 100;
        }

        /// <summary>
        /// 根据经验值计算宠物等级
        /// </summary>
        /// <param name="exp">经验值</param>
        /// <returns>宠物等级</returns>
        private int CalculatePetLevel(int exp)
        {
            if (exp <= 0) return 1;

            // 简化的等级计算公式：sqrt(exp / 100)
            // 实际项目中应该使用准确的等级表
            var level = (int)Math.Sqrt(exp / 100.0);
            return Math.Max(1, level);
        }

        #endregion

        #region 装备系统接口

        /// <summary>
        /// 获取用户装备背包
        /// </summary>
        /// <param name="request">装备列表查询请求</param>
        /// <returns>装备列表结果</returns>
        [HttpPost("GetUserEquipments")]
        public ActionResult<EquipmentListResultDTO> GetUserEquipments([FromBody] EquipmentListRequestDTO request)
        {
            var result = _playerService.GetUserEquipments(request);
            return Ok(result);
        }

        /// <summary>
        /// 装备道具
        /// </summary>
        /// <param name="request">装备请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("EquipItem")]
        public ActionResult<LoginResultDTO> EquipItem([FromBody] EquipRequestDTO request)
        {
            var result = _playerService.EquipItem(request);
            return Ok(result);
        }

        /// <summary>
        /// 卸下装备
        /// </summary>
        /// <param name="request">卸装请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("UnequipItem")]
        public ActionResult<LoginResultDTO> UnequipItem([FromBody] UnequipRequestDTO request)
        {
            var result = _playerService.UnequipItem(request);
            return Ok(result);
        }

        #endregion

        #region 境界系统接口



        #endregion

        #region 背包道具系统接口

        /// <summary>
        /// 获取背包物品列表
        /// </summary>
        /// <param name="request">背包查询请求</param>
        /// <returns>背包结果</returns>
        [HttpPost("GetBag")]
        public ActionResult<BagResultDTO> GetBag([FromBody] BagRequestDTO request)
        {
            var result = _playerService.GetBag(request);
            return Ok(result);
        }

        /// <summary>
        /// 使用道具
        /// </summary>
        /// <param name="request">使用道具请求</param>
        /// <returns>使用结果</returns>
        [HttpPost("UseItem")]
        public ActionResult<UseItemResultDTO> UseItem([FromBody] UseItemRequestDTO request)
        {
            var result = _playerService.UseItem(request);
            return Ok(result);
        }

        /// <summary>
        /// 出售道具
        /// </summary>
        /// <param name="request">出售道具请求</param>
        /// <returns>出售结果</returns>
        [HttpPost("SellItem")]
        public ActionResult<SellItemResultDTO> SellItem([FromBody] SellItemRequestDTO request)
        {
            var result = _playerService.SellItem(request);
            return Ok(result);
        }

        /// <summary>
        /// 整理背包
        /// </summary>
        /// <param name="request">整理背包请求</param>
        /// <returns>整理结果</returns>
        [HttpPost("SortBag")]
        public ActionResult<SortBagResultDTO> SortBag([FromBody] SortBagRequestDTO request)
        {
            var result = _playerService.SortBag(request);
            return Ok(result);
        }

        #endregion

        #region 战斗系统API

        /// <summary>
        /// 战斗计算接口
        /// </summary>
        /// <param name="request">战斗请求参数</param>
        /// <returns>战斗结果</returns>
        [HttpPost("BattleCalculate")]
        public async Task<ActionResult<BattleResultDTO>> BattleCalculate([FromBody] BattleRequestDTO request)
        {
            try
            {
                // 添加详细的请求日志
                Console.WriteLine($"[BattleAPI] 收到战斗请求: UserId={request?.UserId}, MapId={request?.MapId}, PetId={request?.PetId}, SkillId={request?.SkillId}");

                // 验证请求参数
                if (request == null)
                {
                    Console.WriteLine("[BattleAPI] 请求为空");
                    return BadRequest(new { message = "请求参数不能为空" });
                }

                if (request.UserId <= 0)
                {
                    Console.WriteLine($"[BattleAPI] 无效的用户ID: {request.UserId}");
                    return BadRequest(new { message = "无效的用户ID" });
                }

                if (request.MapId <= 0)
                {
                    Console.WriteLine($"[BattleAPI] 无效的地图ID: {request.MapId}");
                    return BadRequest(new { message = "无效的地图ID" });
                }

                if (request.PetId <= 0)
                {
                    Console.WriteLine($"[BattleAPI] 无效的宠物ID: {request.PetId}");
                    return BadRequest(new { message = "无效的宠物ID" });
                }

                Console.WriteLine("[BattleAPI] 参数验证通过，调用战斗服务");
                var result = await _playerService.BattleCalculate(request);
                Console.WriteLine($"[BattleAPI] 战斗计算完成: IsBattleEnd={result.IsBattleEnd}, IsWin={result.IsWin}");

                return Ok(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[BattleAPI] 战斗计算异常: {ex.Message}");
                Console.WriteLine($"[BattleAPI] 异常堆栈: {ex.StackTrace}");

                return BadRequest(new BattleResultDTO
                {
                    IsBattleEnd = true,
                    IsWin = false,
                    Message = ex.Message,
                    AutoBattleStatus = 10 // 错误标记
                });
            }
        }

        #endregion
    }
}