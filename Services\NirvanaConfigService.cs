using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 转生配置服务
    /// </summary>
    public class NirvanaConfigService : INirvanaConfigService
    {
        private readonly ISqlSugarClient _db;
        private readonly IMemoryCache _cache;
        private readonly ILogger<NirvanaConfigService> _logger;

        private const string CACHE_KEY_ALL_CONFIGS = "pet_nirvana_configs_all";
        private const string CACHE_KEY_MAIN_PET_PREFIX = "pet_nirvana_configs_main_";
        private const int CACHE_EXPIRY_MINUTES = 30;

        public NirvanaConfigService(ISqlSugarClient db, IMemoryCache cache, ILogger<NirvanaConfigService> logger)
        {
            _db = db;
            _cache = cache;
            _logger = logger;
        }

        /// <summary>
        /// 获取活跃配置
        /// </summary>
        public async Task<List<PetNirvanaConfig>> GetActiveConfigsAsync()
        {
            try
            {
                return await _cache.GetOrCreateAsync(CACHE_KEY_ALL_CONFIGS, async entry =>
                {
                    entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(CACHE_EXPIRY_MINUTES);
                    
                    var configs = await _db.Queryable<PetNirvanaConfig>()
                        .Where(c => c.IsActive == 1)
                        .OrderBy(c => new { c.MainPetNo, c.SubPetNo })
                        .ToListAsync();

                    _logger.LogInformation("从数据库加载转生配置 {Count} 条", configs.Count);
                    return configs;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取活跃转生配置失败");
                return new List<PetNirvanaConfig>();
            }
        }

        /// <summary>
        /// 查找匹配的配置
        /// </summary>
        public async Task<PetNirvanaConfig> FindMatchingConfigAsync(int mainPetNo, int subPetNo, int nirvanaPetNo)
        {
            try
            {
                var configs = await GetActiveConfigsAsync();
                
                var matchingConfig = configs.FirstOrDefault(c => 
                    c.MainPetNo == mainPetNo && 
                    c.SubPetNo == subPetNo && 
                    c.NirvanaPetNo == nirvanaPetNo);

                if (matchingConfig != null)
                {
                    _logger.LogInformation("找到匹配的转生配置 - 主宠:{MainPet}, 副宠:{SubPet}, 涅槃兽:{NirvanaPet}", 
                        mainPetNo, subPetNo, nirvanaPetNo);
                }
                else
                {
                    _logger.LogWarning("未找到匹配的转生配置 - 主宠:{MainPet}, 副宠:{SubPet}, 涅槃兽:{NirvanaPet}", 
                        mainPetNo, subPetNo, nirvanaPetNo);
                }

                return matchingConfig;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查找匹配转生配置失败");
                return null;
            }
        }

        /// <summary>
        /// 根据主宠获取配置
        /// </summary>
        public async Task<List<PetNirvanaConfig>> GetConfigsByMainPetAsync(int mainPetNo)
        {
            try
            {
                var cacheKey = CACHE_KEY_MAIN_PET_PREFIX + mainPetNo;
                
                return await _cache.GetOrCreateAsync(cacheKey, async entry =>
                {
                    entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(15);
                    
                    var configs = await _db.Queryable<PetNirvanaConfig>()
                        .Where(c => c.MainPetNo == mainPetNo && c.IsActive == 1)
                        .OrderBy(c => new { c.SubPetNo, c.NirvanaPetNo })
                        .ToListAsync();

                    _logger.LogInformation("获取主宠 {MainPetNo} 的转生配置 {Count} 条", mainPetNo, configs.Count);
                    return configs;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据主宠获取转生配置失败");
                return new List<PetNirvanaConfig>();
            }
        }

        /// <summary>
        /// 更新配置
        /// </summary>
        public async Task<bool> UpdateConfigAsync(PetNirvanaConfig config)
        {
            try
            {
                var result = await _db.Updateable(config).ExecuteCommandAsync();
                
                if (result > 0)
                {
                    // 清除相关缓存
                    ClearCache();
                    _logger.LogInformation("更新转生配置成功 - ID:{Id}", config.Id);
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新转生配置失败 - ID:{Id}", config.Id);
                return false;
            }
        }

        /// <summary>
        /// 创建配置
        /// </summary>
        public async Task<PetNirvanaConfig> CreateConfigAsync(PetNirvanaConfig config)
        {
            try
            {
                // 检查是否已存在相同配置
                var existingConfig = await _db.Queryable<PetNirvanaConfig>()
                    .FirstAsync(c => c.MainPetNo == config.MainPetNo && 
                                   c.SubPetNo == config.SubPetNo && 
                                   c.NirvanaPetNo == config.NirvanaPetNo);

                if (existingConfig != null)
                {
                    _logger.LogWarning("转生配置已存在 - 主宠:{MainPet}, 副宠:{SubPet}, 涅槃兽:{NirvanaPet}", 
                        config.MainPetNo, config.SubPetNo, config.NirvanaPetNo);
                    return null;
                }

                config.CreateTime = DateTime.Now;
                var result = await _db.Insertable(config).ExecuteReturnEntityAsync();
                
                if (result != null)
                {
                    // 清除相关缓存
                    ClearCache();
                    _logger.LogInformation("创建转生配置成功 - ID:{Id}", result.Id);
                }
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建转生配置失败");
                return null;
            }
        }

        /// <summary>
        /// 删除配置
        /// </summary>
        public async Task<bool> DeleteConfigAsync(int id)
        {
            try
            {
                // 软删除 - 设置为不活跃
                var result = await _db.Updateable<PetNirvanaConfig>()
                    .SetColumns(c => c.IsActive == 0)
                    .Where(c => c.Id == id)
                    .ExecuteCommandAsync();
                
                if (result > 0)
                {
                    // 清除相关缓存
                    ClearCache();
                    _logger.LogInformation("删除转生配置成功 - ID:{Id}", id);
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除转生配置失败 - ID:{Id}", id);
                return false;
            }
        }

        /// <summary>
        /// 根据配置ID获取配置
        /// </summary>
        public async Task<PetNirvanaConfig> GetConfigByIdAsync(int id)
        {
            try
            {
                return await _db.Queryable<PetNirvanaConfig>()
                    .FirstAsync(c => c.Id == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID获取转生配置失败 - ID:{Id}", id);
                return null;
            }
        }

        /// <summary>
        /// 批量创建配置
        /// </summary>
        public async Task<bool> BatchCreateConfigsAsync(List<PetNirvanaConfig> configs)
        {
            try
            {
                if (configs == null || !configs.Any())
                    return false;

                // 设置创建时间
                foreach (var config in configs)
                {
                    config.CreateTime = DateTime.Now;
                }

                var result = await _db.Insertable(configs).ExecuteCommandAsync();
                
                if (result > 0)
                {
                    // 清除相关缓存
                    ClearCache();
                    _logger.LogInformation("批量创建转生配置成功 - 数量:{Count}", configs.Count);
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量创建转生配置失败");
                return false;
            }
        }

        /// <summary>
        /// 获取配置统计信息
        /// </summary>
        public async Task<(int total, int active, int inactive)> GetConfigStatisticsAsync()
        {
            try
            {
                var total = await _db.Queryable<PetNirvanaConfig>().CountAsync();
                var active = await _db.Queryable<PetNirvanaConfig>().Where(c => c.IsActive == 1).CountAsync();
                var inactive = total - active;

                return (total, active, inactive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取转生配置统计信息失败");
                return (0, 0, 0);
            }
        }

        /// <summary>
        /// 清除缓存
        /// </summary>
        private void ClearCache()
        {
            try
            {
                _cache.Remove(CACHE_KEY_ALL_CONFIGS);
                
                // 清除主宠相关的缓存（这里简化处理，实际可以更精确）
                // 在实际应用中，可以维护一个缓存键列表来精确清除
                _logger.LogInformation("清除转生配置缓存");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清除转生配置缓存失败");
            }
        }
    }
}
