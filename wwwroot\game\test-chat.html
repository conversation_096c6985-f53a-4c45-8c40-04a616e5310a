<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>聊天功能测试</title>
    <script src="pages/Content/Javascript/jquery-1.8.3.min.js"></script>
    <script src="js/chat-websocket.js"></script>
    <script src="js/chat-handler.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .chat-box {
            border: 1px solid #ddd;
            height: 300px;
            overflow-y: auto;
            padding: 10px;
            margin-bottom: 20px;
            background-color: #fafafa;
        }
        .chat-input {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        .chat-input input[type="text"] {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .chat-input select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .chat-input button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .chat-input button:hover {
            background-color: #0056b3;
        }
        .login-section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 4px;
        }
        .login-section input {
            margin: 5px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .send_ms0 {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #007bff;
            background-color: rgba(0, 123, 255, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>聊天功能测试页面</h1>
        
        <div id="connectionStatus" class="status disconnected">
            连接状态：未连接
        </div>
        
        <div class="login-section">
            <h3>用户登录</h3>
            <input type="number" id="playerId" placeholder="玩家ID" value="1001">
            <input type="text" id="playerName" placeholder="玩家名称" value="测试用户">
            <input type="password" id="password" placeholder="密码" value="">
            <button onclick="loginChat()">登录聊天</button>
        </div>
        
        <div class="chat-box" id="chatDiv">
            <div class="send_ms0">
                <font color="#0041F9">[系统]：欢迎使用聊天测试页面</font>
            </div>
        </div>
        
        <div class="chat-input">
            <select id="chatType">
                <option value="chat">公聊</option>
                <option value="private">私聊</option>
            </select>
            <input type="text" id="targetUser" placeholder="私聊目标用户ID（私聊时填写）">
            <select id="messageColor">
                <option value="黑">黑色</option>
                <option value="绿">绿色</option>
                <option value="粉">粉色</option>
                <option value="蓝">蓝色</option>
            </select>
        </div>
        
        <div class="chat-input">
            <input type="text" id="messageInput" placeholder="输入消息内容..." onkeydown="if(event.keyCode==13) sendTestMessage()">
            <button onclick="sendTestMessage()">发送消息</button>
            <button onclick="reconnectChat()">重新连接</button>
        </div>
        
        <div style="margin-top: 20px;">
            <h3>操作说明：</h3>
            <ul>
                <li>1. 首先点击"登录聊天"建立连接</li>
                <li>2. 等待连接状态变为"已连接"</li>
                <li>3. 选择聊天类型（公聊/私聊）</li>
                <li>4. 如果是私聊，填写目标用户ID</li>
                <li>5. 输入消息内容并发送</li>
                <li>6. 可以打开多个页面测试多用户聊天</li>
            </ul>
        </div>
    </div>

    <script>
        // 全局变量
        let testChatClient = null;
        
        // 页面加载完成后初始化
        $(document).ready(function() {
            console.log('测试页面加载完成');
            initializeTestChat();
        });
        
        // 初始化测试聊天
        function initializeTestChat() {
            testChatClient = new ChatWebSocketClient();
            
            // 重写消息处理方法以适配测试页面
            testChatClient.appendToChatDiv = function(messageHtml) {
                $('#chatDiv').append(messageHtml);
                $('#chatDiv').scrollTop($('#chatDiv')[0].scrollHeight);
            };
            
            // 重写连接状态更新
            const originalOnOpen = testChatClient.onOpen;
            testChatClient.onOpen = function(event) {
                originalOnOpen.call(this, event);
                updateConnectionStatus(true);
            };
            
            const originalOnClose = testChatClient.onClose;
            testChatClient.onClose = function(event) {
                originalOnClose.call(this, event);
                updateConnectionStatus(false);
            };
            
            // 自动连接
            testChatClient.connect();
        }
        
        // 更新连接状态显示
        function updateConnectionStatus(isConnected) {
            const statusDiv = $('#connectionStatus');
            if (isConnected) {
                statusDiv.removeClass('disconnected').addClass('connected');
                statusDiv.text('连接状态：已连接');
            } else {
                statusDiv.removeClass('connected').addClass('disconnected');
                statusDiv.text('连接状态：未连接');
            }
        }
        
        // 登录聊天
        function loginChat() {
            const playerId = parseInt($('#playerId').val());
            const playerName = $('#playerName').val().trim();
            const password = $('#password').val();
            
            if (!playerId || !playerName) {
                alert('请填写玩家ID和名称');
                return;
            }
            
            if (testChatClient && testChatClient.isConnected) {
                testChatClient.login(playerId, playerName, password);
            } else {
                alert('WebSocket未连接，请等待连接建立');
            }
        }
        
        // 发送测试消息
        function sendTestMessage() {
            const messageInput = $('#messageInput');
            const content = messageInput.val().trim();
            
            if (!content) {
                return;
            }
            
            if (!testChatClient || !testChatClient.isConnected) {
                alert('聊天服务未连接');
                return;
            }
            
            const chatType = $('#chatType').val();
            const color = $('#messageColor').val();
            const messageContent = content + '&&' + color;
            
            let success = false;
            if (chatType === 'private') {
                const targetUser = $('#targetUser').val().trim();
                if (!targetUser) {
                    alert('私聊请填写目标用户ID');
                    return;
                }
                success = testChatClient.sendChatMessage(messageContent, 'private', targetUser);
            } else {
                success = testChatClient.sendChatMessage(messageContent, 'chat');
            }
            
            if (success) {
                messageInput.val('');
            }
        }
        
        // 重新连接
        function reconnectChat() {
            if (testChatClient) {
                testChatClient.disconnect();
                setTimeout(() => {
                    testChatClient.connect();
                }, 1000);
            }
        }
    </script>
</body>
</html>
