-- =====================================================
-- 等级系统补充表结构 SQL 脚本
-- 创建时间: 2025-01-31
-- 说明: 补充创建等级系统所需的剩余数据库表
-- =====================================================

-- 等级变更日志表
CREATE TABLE `level_change_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `pet_id` int(11) NOT NULL COMMENT '宠物ID',
  `old_level` int(11) NOT NULL COMMENT '原等级',
  `new_level` int(11) NOT NULL COMMENT '新等级',
  `old_exp` bigint(20) NOT NULL COMMENT '原经验',
  `new_exp` bigint(20) NOT NULL COMMENT '新经验',
  `change_reason` varchar(100) NOT NULL COMMENT '变更原因',
  `change_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_pet_id` (`pet_id`),
  KEY `idx_change_time` (`change_time`),
  KEY `idx_user_pet` (`user_id`, `pet_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='等级变更日志表';

-- =====================================================
-- 初始化数据脚本
-- =====================================================

-- 插入宠物经验系统配置
INSERT INTO `exp_system_config` (`system_name`, `max_exp`, `max_level`, `exp_formula`, `is_active`) VALUES
('pet', 94539554417, 200, 'cumulative', 1),
('player', 10000000000, 150, 'cumulative', 1),
('guild', 1000000000, 100, 'cumulative', 1);

-- =====================================================
-- 等级配置数据导入脚本（示例）
-- 注意：实际的等级配置数据需要从原项目的 shikong.mask 文件导入
-- 这里提供一些示例数据用于测试
-- =====================================================

-- 插入示例等级配置数据（前10级）
INSERT INTO `level_config` (`level`, `required_exp`, `upgrade_exp`) VALUES
(1, 0, 1000),
(2, 1000, 2000),
(3, 3000, 3000),
(4, 6000, 4000),
(5, 10000, 5000),
(6, 15000, 6000),
(7, 21000, 7000),
(8, 28000, 8000),
(9, 36000, 9000),
(10, 45000, 10000);

-- =====================================================
-- 索引优化脚本
-- =====================================================

-- 为 level_config 表添加性能优化索引
ALTER TABLE `level_config` ADD INDEX `idx_level_active` (`level`, `is_active`);
ALTER TABLE `level_config` ADD INDEX `idx_exp_range` (`required_exp`, `upgrade_exp`);

-- 为 exp_system_config 表添加性能优化索引
ALTER TABLE `exp_system_config` ADD INDEX `idx_system_active` (`system_name`, `is_active`);

-- 为 level_change_log 表添加复合索引优化查询
ALTER TABLE `level_change_log` ADD INDEX `idx_user_time` (`user_id`, `change_time` DESC);
ALTER TABLE `level_change_log` ADD INDEX `idx_pet_time` (`pet_id`, `change_time` DESC);
ALTER TABLE `level_change_log` ADD INDEX `idx_reason_time` (`change_reason`, `change_time` DESC);

-- =====================================================
-- 数据验证脚本
-- =====================================================

-- 验证表结构是否创建成功
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS,
    CREATE_TIME
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('level_config', 'exp_system_config', 'level_change_log');

-- 验证索引是否创建成功
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('level_config', 'exp_system_config', 'level_change_log')
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 验证初始数据是否插入成功
SELECT 'exp_system_config' as table_name, COUNT(*) as record_count FROM exp_system_config
UNION ALL
SELECT 'level_config' as table_name, COUNT(*) as record_count FROM level_config;

-- =====================================================
-- 数据迁移辅助脚本
-- =====================================================

-- 创建临时表用于数据导入验证
CREATE TABLE `level_config_temp` (
  `level` int(11) NOT NULL,
  `required_exp` bigint(20) NOT NULL,
  `upgrade_exp` bigint(20) NOT NULL,
  PRIMARY KEY (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='等级配置临时表';

-- 数据一致性检查存储过程
DELIMITER $$
CREATE PROCEDURE CheckLevelConfigConsistency()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE current_level INT;
    DECLARE current_required_exp BIGINT;
    DECLARE current_upgrade_exp BIGINT;
    DECLARE next_required_exp BIGINT;
    DECLARE error_count INT DEFAULT 0;
    
    DECLARE level_cursor CURSOR FOR 
        SELECT level, required_exp, upgrade_exp 
        FROM level_config 
        WHERE is_active = 1 
        ORDER BY level;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 创建临时结果表
    DROP TEMPORARY TABLE IF EXISTS consistency_check_result;
    CREATE TEMPORARY TABLE consistency_check_result (
        level INT,
        error_type VARCHAR(100),
        error_message VARCHAR(500)
    );
    
    OPEN level_cursor;
    
    read_loop: LOOP
        FETCH level_cursor INTO current_level, current_required_exp, current_upgrade_exp;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 检查升级经验是否与下一级的累积经验一致
        SELECT required_exp INTO next_required_exp 
        FROM level_config 
        WHERE level = current_level + 1 AND is_active = 1;
        
        IF next_required_exp IS NOT NULL THEN
            IF (current_required_exp + current_upgrade_exp) != next_required_exp THEN
                INSERT INTO consistency_check_result VALUES (
                    current_level, 
                    'EXP_INCONSISTENCY', 
                    CONCAT('等级 ', current_level, ' 的经验计算不一致: ', 
                           current_required_exp, ' + ', current_upgrade_exp, 
                           ' != ', next_required_exp)
                );
                SET error_count = error_count + 1;
            END IF;
        END IF;
        
        -- 检查经验值是否为负数
        IF current_required_exp < 0 OR current_upgrade_exp < 0 THEN
            INSERT INTO consistency_check_result VALUES (
                current_level, 
                'NEGATIVE_EXP', 
                CONCAT('等级 ', current_level, ' 存在负数经验值')
            );
            SET error_count = error_count + 1;
        END IF;
        
    END LOOP;
    
    CLOSE level_cursor;
    
    -- 输出检查结果
    IF error_count = 0 THEN
        SELECT '数据一致性检查通过' as result, 0 as error_count;
    ELSE
        SELECT '数据一致性检查失败' as result, error_count;
        SELECT * FROM consistency_check_result;
    END IF;
    
END$$
DELIMITER ;

-- =====================================================
-- 性能监控脚本
-- =====================================================

-- 创建等级系统性能监控视图
CREATE VIEW level_system_stats AS
SELECT 
    'level_config' as table_name,
    COUNT(*) as total_records,
    MIN(level) as min_level,
    MAX(level) as max_level,
    MIN(required_exp) as min_exp,
    MAX(required_exp) as max_exp
FROM level_config WHERE is_active = 1
UNION ALL
SELECT 
    'level_change_log' as table_name,
    COUNT(*) as total_records,
    MIN(old_level) as min_level,
    MAX(new_level) as max_level,
    MIN(old_exp) as min_exp,
    MAX(new_exp) as max_exp
FROM level_change_log
WHERE change_time >= DATE_SUB(NOW(), INTERVAL 30 DAY);

-- =====================================================
-- 清理脚本（谨慎使用）
-- =====================================================

-- 清理临时表
-- DROP TABLE IF EXISTS `level_config_temp`;

-- 清理存储过程
-- DROP PROCEDURE IF EXISTS CheckLevelConfigConsistency;

-- 清理视图
-- DROP VIEW IF EXISTS level_system_stats;

-- =====================================================
-- 脚本执行完成
-- =====================================================
SELECT 'Level System Database Setup Completed!' as status;
