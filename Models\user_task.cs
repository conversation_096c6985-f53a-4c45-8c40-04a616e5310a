﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///用户任务表
    ///</summary>
    [SugarTable("user_task")]
    public partial class user_task
    {
           public user_task(){


           }
           /// <summary>
           /// Desc:用户任务ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int user_task_id {get;set;}

           /// <summary>
           /// Desc:用户ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:任务ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string task_id {get;set;}

           /// <summary>
           /// Desc:任务状态(0=已完成,1=进行中,2=已放弃)
           /// Default:1
           /// Nullable:True
           /// </summary>           
           public byte? task_status {get;set;}

           /// <summary>
           /// Desc:接取时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? accepted_at {get;set;}

           /// <summary>
           /// Desc:完成时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? completed_at {get;set;}

           /// <summary>
           /// Desc:放弃时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? abandoned_at {get;set;}

           /// <summary>
           /// Desc:完成次数(用于可重复任务)
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? completion_count {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? created_at {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? updated_at {get;set;}

    }
}
