<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 任务进度显示测试</title>
    <link rel="stylesheet" href="../pages/css/index.css">
    <style>
        body {
            font-family: '宋体', SimSun, serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .demo-container {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .api-data {
            background: #e8f4f8;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 任务进度显示测试</h1>
        
        <div class="test-section">
            <h3>📋 测试说明</h3>
            <div class="info result">
                <strong>测试目标</strong>: 验证 progressDisplay 字段的显示效果<br>
                <strong>API接口</strong>: /api/task/detail/1/TASK_002<br>
                <strong>新增字段</strong>: progressDisplay - 格式化的进度显示文本<br>
                <strong>CSS样式</strong>: 参考 objectiveDescription 的展示方式
            </div>
        </div>

        <!-- API数据展示 -->
        <div class="test-section">
            <h3>📊 API返回数据示例</h3>
            <div class="api-data" id="apiData">
{
    "objectives": [
        {
            "objectiveId": 27,
            "objectiveType": "KILL_MONSTER",
            "objectiveTypeName": "击杀怪物",
            "targetId": "111",
            "targetAmount": 10,
            "objectiveDescription": "<span>击杀小狸猫10个</span></br>",
            "currentAmount": 0,
            "isCompleted": false,
            "completionPercentage": 0,
            "progressDisplay": "<span>击杀小狸猫(0/10)个 未完成</span></br>"
        },
        {
            "objectiveId": 28,
            "objectiveType": "KILL_MONSTER", 
            "objectiveTypeName": "击杀怪物",
            "targetId": "1",
            "targetAmount": 10,
            "objectiveDescription": "<span>击杀金波姆10个</span></br>",
            "currentAmount": 0,
            "isCompleted": false,
            "completionPercentage": 0,
            "progressDisplay": "<span>击杀金波姆(0/10)个 未完成</span></br>"
        }
    ]
}
            </div>
        </div>

        <!-- 任务目标显示测试 -->
        <div class="test-section">
            <h3>🎯 任务目标显示 (objectiveDescription)</h3>
            <div class="demo-container">
                <h4>任务目标：</h4>
                <div class="task_objective_description">
                    <span>击杀小狸猫10个</span></br>
                </div>
                <div class="task_objective_description">
                    <span>击杀金波姆10个</span></br>
                </div>
            </div>
        </div>

        <!-- 完成进度显示测试 -->
        <div class="test-section">
            <h3>📈 完成进度显示 (progressDisplay)</h3>
            <div class="demo-container">
                <h4>完成进度：</h4>
                <div class="task_progress_container">
                    <div class="task_progress_display">
                        <span class="progress_incomplete">击杀小狸猫(0/10)个 未完成</span></br>
                    </div>
                </div>
                <div class="task_progress_container">
                    <div class="task_progress_display">
                        <span class="progress_incomplete">击杀金波姆(0/10)个 未完成</span></br>
                    </div>
                </div>
            </div>
        </div>

        <!-- 不同状态的进度显示 -->
        <div class="test-section">
            <h3>🔄 不同状态的进度显示</h3>
            <div class="demo-container">
                <h4>进行中状态：</h4>
                <div class="task_progress_container">
                    <div class="task_progress_display">
                        <span class="progress_incomplete">击杀小狸猫(5/10)个 未完成</span></br>
                    </div>
                </div>
                
                <h4>已完成状态：</h4>
                <div class="task_progress_container">
                    <div class="task_progress_display">
                        <span class="progress_completed">击杀小狸猫(10/10)个 已完成</span></br>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实际API测试 -->
        <div class="test-section">
            <h3>🔗 实际API测试</h3>
            <button class="test-button" onclick="testTaskDetailAPI()">测试 /api/task/detail/1/TASK_002</button>
            <button class="test-button" onclick="testProgressDisplay()">测试进度显示渲染</button>
            
            <div id="apiTestResults"></div>
        </div>

        <!-- 样式对比 -->
        <div class="test-section">
            <h3>🎨 样式对比</h3>
            <div class="demo-container">
                <table style="width:100%; border-collapse: collapse;">
                    <tr style="background: #f2f2f2;">
                        <th style="border: 1px solid #ddd; padding: 8px;">字段</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">显示效果</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">CSS类</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">objectiveDescription</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">
                            <div class="task_objective_description">
                                <span>击杀小狸猫10个</span>
                            </div>
                        </td>
                        <td style="border: 1px solid #ddd; padding: 8px;">.task_objective_description</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">progressDisplay</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">
                            <div class="task_progress_display">
                                <span class="progress_incomplete">击杀小狸猫(5/10)个 未完成</span>
                            </div>
                        </td>
                        <td style="border: 1px solid #ddd; padding: 8px;">.task_progress_display</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <script>
        async function testTaskDetailAPI() {
            const resultsDiv = document.getElementById('apiTestResults');
            resultsDiv.innerHTML = '<div class="info result">正在测试API...</div>';
            
            try {
                const response = await fetch('/api/task/detail/1/TASK_002');
                const data = await response.json();
                
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div class="success result">
                            <strong>✅ API调用成功</strong><br>
                            任务名称: ${data.data.userTask.taskInfo.taskName}<br>
                            目标数量: ${data.data.userTask.taskInfo.objectives.length}<br>
                            是否已接受: ${data.data.isAccepted ? '是' : '否'}
                        </div>
                        <div class="api-data">${JSON.stringify(data, null, 2)}</div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div class="error result">❌ API返回错误: ${data.message}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error result">❌ API调用失败: ${error.message}</div>`;
            }
        }

        async function testProgressDisplay() {
            const resultsDiv = document.getElementById('apiTestResults');
            resultsDiv.innerHTML = '<div class="info result">正在测试进度显示渲染...</div>';
            
            try {
                const response = await fetch('/api/task/detail/1/TASK_002');
                const data = await response.json();
                
                if (data.success && data.data.userTask.taskInfo.objectives) {
                    let progressHtml = '<div class="success result"><strong>✅ 进度显示渲染测试</strong></div>';
                    progressHtml += '<div class="demo-container"><h4>渲染结果：</h4>';
                    
                    data.data.userTask.taskInfo.objectives.forEach(objective => {
                        if (objective.progressDisplay) {
                            const progressClass = objective.isCompleted ? 'progress_completed' : 'progress_incomplete';
                            progressHtml += `
                                <div class="task_progress_container">
                                    <div class="task_progress_display">
                                        <span class="${progressClass}">${objective.progressDisplay}</span>
                                    </div>
                                </div>
                            `;
                        }
                    });
                    
                    progressHtml += '</div>';
                    resultsDiv.innerHTML = progressHtml;
                } else {
                    resultsDiv.innerHTML = `<div class="error result">❌ 无法获取进度数据</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error result">❌ 测试失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
