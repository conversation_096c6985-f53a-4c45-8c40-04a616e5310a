using SqlSugar;
using System;

namespace WebApplication_HM.Models
{
    /// <summary>
    /// 可合成宠物ID验证表
    /// </summary>
    [SugarTable("synthesis_valid_pets")]
    public class synthesis_valid_pets
    {
        /// <summary>
        /// 宠物编号
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public int pet_no { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool is_active { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime create_time { get; set; } = DateTime.Now;
    }
}
