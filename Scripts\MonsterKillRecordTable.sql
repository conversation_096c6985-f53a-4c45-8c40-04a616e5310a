-- =====================================================
-- 怪物击杀记录表创建脚本（简化版）
-- 用途: 记录每场战斗击杀的怪物，支持击杀任务系统
-- 创建时间: 2025-01-09
-- =====================================================

cc. 创建怪物击杀记录表（简化版）
CREATE TABLE IF NOT EXISTS `monster_kill_record` (
    `record_id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    `user_id` INT NOT NULL COMMENT '用户ID',
    `monster_id` INT NOT NULL COMMENT '怪物ID',
    `battle_id` VARCHAR(100) COMMENT '战斗ID（关联战斗记录）',
    `kill_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '击杀时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    -- 外键约束
    FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`monster_id`) REFERENCES `monster_config`(`monster_id`) ON DELETE CASCADE,

    -- 索引优化
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_monster_id` (`monster_id`),
    INDEX `idx_user_monster` (`user_id`, `monster_id`),
    INDEX `idx_kill_time` (`kill_time` DESC),
    INDEX `idx_battle_id` (`battle_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='怪物击杀记录表（简化版）';

-- 2. 创建击杀统计视图（便于任务系统查询）
CREATE OR REPLACE VIEW `v_monster_kill_stats` AS
SELECT
    user_id,
    monster_id,
    COUNT(*) as kill_count,
    MIN(kill_time) as first_kill_time,
    MAX(kill_time) as last_kill_time,
    DATE(kill_time) as kill_date
FROM monster_kill_record
GROUP BY user_id, monster_id, DATE(kill_time);

-- 3. 创建用户今日击杀统计视图
CREATE OR REPLACE VIEW `v_user_daily_kill_stats` AS
SELECT
    user_id,
    DATE(kill_time) as kill_date,
    COUNT(*) as total_kills,
    COUNT(DISTINCT monster_id) as unique_monsters
FROM monster_kill_record
WHERE DATE(kill_time) = CURDATE()
GROUP BY user_id, DATE(kill_time);

-- 4. 创建存储过程：获取用户指定怪物的击杀数量
DELIMITER //
CREATE PROCEDURE `sp_get_user_monster_kill_count`(
    IN p_user_id INT,
    IN p_monster_id INT,
    IN p_start_date DATE,
    IN p_end_date DATE,
    OUT p_kill_count INT
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        SET p_kill_count = 0;
    END;

    SELECT COUNT(*) INTO p_kill_count
    FROM monster_kill_record
    WHERE user_id = p_user_id
    AND monster_id = p_monster_id
    AND DATE(kill_time) BETWEEN IFNULL(p_start_date, '1900-01-01') AND IFNULL(p_end_date, CURDATE());
END //
DELIMITER ;

-- 5. 创建函数：快速插入击杀记录
DELIMITER //
CREATE PROCEDURE `sp_insert_kill_record`(
    IN p_user_id INT,
    IN p_monster_id INT,
    IN p_battle_id VARCHAR(100)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
    END;

    START TRANSACTION;

    INSERT INTO monster_kill_record (user_id, monster_id, battle_id, kill_time)
    VALUES (p_user_id, p_monster_id, p_battle_id, NOW());

    COMMIT;
END //
DELIMITER ;

-- 6. 创建触发器：自动更新任务进度（击杀怪物类型任务）
DELIMITER //
CREATE TRIGGER `tr_monster_kill_update_task_progress`
AFTER INSERT ON `monster_kill_record`
FOR EACH ROW
BEGIN
    -- 更新击杀怪物类型的任务进度
    UPDATE user_task_progress utp
    JOIN task_objective to ON utp.objective_id = to.objective_id
    JOIN user_task ut ON utp.user_task_id = ut.user_task_id
    SET 
        utp.current_amount = utp.current_amount + 1,
        utp.is_completed = CASE 
            WHEN utp.current_amount + 1 >= to.target_amount THEN 1 
            ELSE 0 
        END,
        utp.updated_at = NOW()
    WHERE ut.user_id = NEW.user_id
    AND ut.task_status = 1  -- 进行中的任务
    AND to.objective_type = 'KILL_MONSTER'
    AND to.target_id = CAST(NEW.monster_id AS CHAR)
    AND utp.is_completed = 0;  -- 只更新未完成的目标
END //
DELIMITER ;

-- 7. 创建数据清理存储过程（清理过期记录）
DELIMITER //
CREATE PROCEDURE `sp_cleanup_old_kill_records`(
    IN p_days_to_keep INT DEFAULT 90
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
    END;
    
    START TRANSACTION;
    
    -- 删除指定天数之前的记录
    DELETE FROM monster_kill_record 
    WHERE kill_time < DATE_SUB(NOW(), INTERVAL p_days_to_keep DAY);
    
    -- 记录清理信息
    SELECT ROW_COUNT() as deleted_records, NOW() as cleanup_time;
    
    COMMIT;
END //
DELIMITER ;

-- 8. 创建复合索引优化查询性能
CREATE INDEX `idx_user_monster_date` ON `monster_kill_record` (`user_id`, `monster_id`, `kill_time` DESC);
CREATE INDEX `idx_battle_user` ON `monster_kill_record` (`battle_id`, `user_id`);

-- 9. 添加表注释
ALTER TABLE `monster_kill_record` COMMENT = '怪物击杀记录表（简化版） - 记录每场战斗击杀的怪物，支持击杀任务系统';

-- 10. 验证表结构创建
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS,
    CREATE_TIME
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'monster_kill_record';

-- 验证视图创建
SELECT 
    TABLE_NAME,
    TABLE_TYPE,
    TABLE_COMMENT
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('v_monster_kill_stats', 'v_user_daily_kill_stats');

-- 验证存储过程创建
SELECT 
    ROUTINE_NAME,
    ROUTINE_TYPE,
    ROUTINE_COMMENT
FROM information_schema.ROUTINES 
WHERE ROUTINE_SCHEMA = DATABASE() 
AND ROUTINE_NAME IN ('sp_get_user_monster_kill_count', 'sp_batch_insert_kill_records', 'sp_cleanup_old_kill_records');

-- 验证触发器创建
SELECT 
    TRIGGER_NAME,
    EVENT_MANIPULATION,
    EVENT_OBJECT_TABLE,
    ACTION_TIMING
FROM information_schema.TRIGGERS 
WHERE TRIGGER_SCHEMA = DATABASE() 
AND TRIGGER_NAME = 'tr_monster_kill_update_task_progress';

-- =====================================================
-- 创建完成提示
-- =====================================================
SELECT 
    '怪物击杀记录表创建完成' as message,
    NOW() as creation_time,
    '支持击杀任务自动更新进度' as feature;
