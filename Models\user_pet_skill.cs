﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///用户宠物技能表
    ///</summary>
    [SugarTable("user_pet_skill")]
    public partial class user_pet_skill
    {
           public user_pet_skill(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:用户宠物表主键ID（外键）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_pet_id {get;set;}

           /// <summary>
           /// Desc:技能编号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int skill_id {get;set;}

           /// <summary>
           /// Desc:技能等级或附加数值
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? skill_level {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

    }
}
