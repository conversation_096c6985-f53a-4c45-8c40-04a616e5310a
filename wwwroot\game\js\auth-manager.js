/**
 * 用户认证和会话管理模块
 * 负责登录状态管理、用户信息存储、权限验证等
 */

class AuthManager {
    constructor() {
        this.currentUser = null;
        this.loginCallbacks = [];
        this.logoutCallbacks = [];
        this.stateChangeListeners = []; // 状态变化监听器
        this.init();
    }

    /**
     * 初始化认证管理器
     */
    init() {
        // 从存储中恢复登录状态
        this.restoreLoginState();
        
        // 监听存储变化（多标签页同步）
        window.addEventListener('storage', (e) => {
            if (e.key === 'userLogin') {
                this.restoreLoginState();
                this.notifyStateChange();
            }
        });
    }

    /**
     * 添加状态变化监听器
     * @param {Function} callback 回调函数，参数为 (isLoggedIn, user)
     */
    onStateChange(callback) {
        if (typeof callback === 'function') {
            this.stateChangeListeners.push(callback);
        }
    }

    /**
     * 移除状态变化监听器
     * @param {Function} callback 要移除的回调函数
     */
    removeStateChangeListener(callback) {
        const index = this.stateChangeListeners.indexOf(callback);
        if (index > -1) {
            this.stateChangeListeners.splice(index, 1);
        }
    }

    /**
     * 通知所有监听器状态已变化
     */
    notifyStateChange() {
        const isLoggedIn = this.isLoggedIn();
        const user = this.getCurrentUser();

        this.stateChangeListeners.forEach(callback => {
            try {
                callback(isLoggedIn, user);
            } catch (error) {
                console.error('状态变化监听器执行错误:', error);
            }
        });
    }

    /**
     * 从本地存储恢复登录状态
     */
    restoreLoginState() {
        try {
            const loginData = localStorage.getItem('userLogin') || sessionStorage.getItem('userLogin');
            if (loginData) {
                const userData = JSON.parse(loginData);
                
                // 检查登录是否过期（24小时）
                const loginTime = new Date(userData.loginTime);
                const now = new Date();
                const hoursDiff = (now - loginTime) / (1000 * 60 * 60);
                
                if (hoursDiff < 24) {
                    this.currentUser = userData;
                    this.triggerLoginCallbacks(userData);
                    // 通知状态变化
                    this.notifyStateChange();
                    console.log('登录状态已恢复:', userData.nickname);
                } else {
                    // 登录过期，清除状态
                    this.logout();
                    console.log('登录状态已过期');
                }
            }
        } catch (error) {
            console.error('恢复登录状态失败:', error);
            this.clearLoginData();
        }
    }

    /**
     * 用户登录
     * @param {string} username 用户名
     * @param {string} password 密码
     * @param {boolean} rememberMe 是否记住登录状态
     * @returns {Promise<Object>} 登录结果
     */
    async login(username, password, rememberMe = false) {
        try {
            const response = await fetch('/api/Player/Login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    Number: username,
                    Password: password
                })
            });

            const result = await response.json();

            if (result.success) {
                const loginData = {
                    userId: result.userId,
                    nickname: result.nickName,
                    username: username,
                    loginTime: new Date().toISOString(),
                    rememberMe: rememberMe
                };

                // 保存登录状态
                this.saveLoginState(loginData, rememberMe);
                this.currentUser = loginData;

                // 触发登录回调
                this.triggerLoginCallbacks(loginData);

                // 通知状态变化
                this.notifyStateChange();

                console.log('登录成功:', loginData);
                return { success: true, user: loginData };
            } else {
                return { success: false, message: result.message };
            }
        } catch (error) {
            console.error('登录请求失败:', error);
            return { success: false, message: '网络错误，请稍后重试' };
        }
    }

    /**
     * 用户注册
     * @param {string} username 用户名
     * @param {string} password 密码
     * @param {string} nickname 昵称
     * @returns {Promise<Object>} 注册结果
     */
    async register(username, password, nickname) {
        try {
            const response = await fetch('/api/Player/Register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    Account: username,
                    Password: password,
                    Nickname: nickname
                })
            });

            const result = await response.json();
            return {
                success: result.success,
                message: result.message
            };
        } catch (error) {
            console.error('注册请求失败:', error);
            return { success: false, message: '网络错误，请稍后重试' };
        }
    }

    /**
     * 用户登出
     */
    logout() {
        const oldUser = this.currentUser;
        this.currentUser = null;
        this.clearLoginData();

        // 触发登出回调
        this.triggerLogoutCallbacks(oldUser);

        // 通知状态变化
        this.notifyStateChange();

        console.log('用户已登出');
    }

    /**
     * 保存登录状态
     * @param {Object} loginData 登录数据
     * @param {boolean} rememberMe 是否记住登录状态
     */
    saveLoginState(loginData, rememberMe) {
        const storage = rememberMe ? localStorage : sessionStorage;
        storage.setItem('userLogin', JSON.stringify(loginData));
        
        // 清除另一个存储中的数据
        const otherStorage = rememberMe ? sessionStorage : localStorage;
        otherStorage.removeItem('userLogin');
    }

    /**
     * 清除登录数据
     */
    clearLoginData() {
        localStorage.removeItem('userLogin');
        sessionStorage.removeItem('userLogin');
    }

    /**
     * 检查是否已登录
     * @returns {boolean} 是否已登录
     */
    isLoggedIn() {
        return this.currentUser !== null;
    }

    /**
     * 获取当前用户信息
     * @returns {Object|null} 当前用户信息
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * 获取当前用户ID
     * @returns {number|null} 当前用户ID
     */
    getCurrentUserId() {
        return this.currentUser ? this.currentUser.userId : null;
    }

    /**
     * 获取当前用户昵称
     * @returns {string|null} 当前用户昵称
     */
    getCurrentNickname() {
        return this.currentUser ? this.currentUser.nickname : null;
    }

    /**
     * 检查登录状态并重定向
     * @param {string} redirectUrl 未登录时的重定向URL
     * @returns {boolean} 是否已登录
     */
    requireLogin(redirectUrl = '/login.html') {
        if (!this.isLoggedIn()) {
            window.location.href = redirectUrl;
            return false;
        }
        return true;
    }

    /**
     * 添加登录回调
     * @param {Function} callback 登录回调函数
     */
    onLogin(callback) {
        this.loginCallbacks.push(callback);
    }

    /**
     * 添加登出回调
     * @param {Function} callback 登出回调函数
     */
    onLogout(callback) {
        this.logoutCallbacks.push(callback);
    }

    /**
     * 触发登录回调
     * @param {Object} userData 用户数据
     */
    triggerLoginCallbacks(userData) {
        this.loginCallbacks.forEach(callback => {
            try {
                callback(userData);
            } catch (error) {
                console.error('登录回调执行失败:', error);
            }
        });
    }

    /**
     * 触发登出回调
     * @param {Object} userData 用户数据
     */
    triggerLogoutCallbacks(userData) {
        this.logoutCallbacks.forEach(callback => {
            try {
                callback(userData);
            } catch (error) {
                console.error('登出回调执行失败:', error);
            }
        });
    }

    /**
     * 刷新用户信息
     * @returns {Promise<Object>} 用户信息
     */
    async refreshUserInfo() {
        if (!this.isLoggedIn()) {
            throw new Error('用户未登录');
        }

        try {
            const response = await fetch('/api/Player/GetPlayerInfo', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    UserId: this.getCurrentUserId()
                })
            });

            const result = await response.json();
            
            if (result.success) {
                // 更新用户信息
                this.currentUser.playerInfo = result.playerInfo;
                this.saveLoginState(this.currentUser, this.currentUser.rememberMe);
                return result.playerInfo;
            } else {
                throw new Error(result.message || '获取用户信息失败');
            }
        } catch (error) {
            console.error('刷新用户信息失败:', error);
            throw error;
        }
    }

    /**
     * 验证登录状态（向服务器验证）
     * @returns {Promise<boolean>} 验证结果
     */
    async validateLogin() {
        if (!this.isLoggedIn()) {
            return false;
        }

        try {
            // 通过获取用户信息来验证登录状态
            await this.refreshUserInfo();
            return true;
        } catch (error) {
            // 验证失败，清除登录状态
            this.logout();
            return false;
        }
    }
}

// 创建全局认证管理器实例
const authManager = new AuthManager();

// 导出到全局作用域
window.authManager = authManager;

// 兼容性：为游戏API适配器提供用户ID
window.getCurrentUserId = () => {
    const userId = authManager.getCurrentUserId();
    if (userId && userId > 0) {
        return userId;
    }

    // 检查是否为开发环境
    const isDev = window.location.hostname === 'localhost' ||
                 window.location.hostname === '127.0.0.1' ||
                 window.location.hostname.includes('dev');

    if (isDev) {
        console.warn('⚠️ 开发环境：用户未登录，返回null');
        return null;
    } else {
        console.error('❌ 生产环境：用户未登录');
        return null;
    }
};

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 如果在游戏页面且未登录，重定向到登录页
    if (window.location.pathname.includes('/game') && !authManager.isLoggedIn()) {
        //未登录直接跳转到登录页面
        window.location.href = '/login.html';
    }
});

console.log('认证管理器已加载');
