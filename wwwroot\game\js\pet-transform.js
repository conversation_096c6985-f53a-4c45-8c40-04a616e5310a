/**
 * 百变宠物系统前端脚本
 * 提供百变宠物的所有前端功能
 */

class PetTransformManager {
    constructor() {
        this.transformablePets = [];
        this.godPets = [];
        this.holyPets = [];
        this.userStats = null;
        this.config = null;
        this.selectedSourcePet = null;
        this.selectedTargetPet = null;
    }

    /**
     * 初始化百变宠物系统
     */
    async initialize(userId) {
        try {
            console.log('初始化百变宠物系统...');
            
            // 初始化默认配置
            await this.initializeDefaultConfig();
            
            // 加载选择界面数据
            await this.loadSelectionData(userId);
            
            // 绑定事件
            this.bindEvents();
            
            console.log('百变宠物系统初始化完成');
        } catch (error) {
            console.error('初始化百变宠物系统失败:', error);
            this.showMessage('初始化失败，请刷新页面重试', 'error');
        }
    }

    /**
     * 初始化默认配置
     */
    async initializeDefaultConfig() {
        try {
            const response = await fetch('/api/PetTransform/initialize', {
                method: 'POST'
            });
            const result = await response.json();
            
            if (result.success) {
                console.log('百变配置初始化成功');
            }
        } catch (error) {
            console.error('初始化配置失败:', error);
        }
    }

    /**
     * 加载选择界面数据
     */
    async loadSelectionData(userId) {
        try {
            const response = await fetch(`/api/PetTransform/selection-data/${userId}`);
            const result = await response.json();
            
            if (result.success) {
                this.transformablePets = result.data.transformablePets || [];
                this.godPets = result.data.godPets || [];
                this.holyPets = result.data.holyPets || [];
                this.userStats = result.data.userStats || {};
                this.config = result.data.config || {};
                
                // 更新界面
                this.updateTransformablePetsList();
                this.updateUserStats();
                this.updateConfigInfo();
                
                console.log(`加载了${this.transformablePets.length}个可变换宠物`);
            } else {
                this.showMessage('加载数据失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('加载选择数据失败:', error);
            this.showMessage('加载数据失败，请重试', 'error');
        }
    }

    /**
     * 更新可变换宠物列表
     */
    updateTransformablePetsList() {
        const container = document.getElementById('transformablePetsList');
        if (!container) return;

        container.innerHTML = '';

        this.transformablePets.forEach(pet => {
            const petElement = this.createPetElement(pet);
            container.appendChild(petElement);
        });
    }

    /**
     * 创建宠物元素
     */
    createPetElement(pet) {
        const div = document.createElement('div');
        div.className = `pet-item ${pet.isGodPet ? 'god-pet' : ''} ${pet.isHolyPet ? 'holy-pet' : ''}`;
        div.dataset.petNo = pet.petNo;
        
        div.innerHTML = `
            <img src="${pet.imagePath}" alt="${pet.name}" onerror="this.src='Content/PetPhoto/default.jpg'">
            <div class="pet-info">
                <div class="pet-name">${pet.name}</div>
                <div class="pet-element">${pet.element}</div>
                <div class="pet-attribute">${pet.attribute}</div>
                <div class="rarity-level">稀有度: ${pet.rarityLevel}</div>
            </div>
        `;

        div.addEventListener('click', () => this.selectTargetPet(pet));
        return div;
    }

    /**
     * 选择目标宠物
     */
    selectTargetPet(pet) {
        // 移除之前的选中状态
        document.querySelectorAll('.pet-item.selected').forEach(item => {
            item.classList.remove('selected');
        });

        // 添加选中状态
        const petElement = document.querySelector(`[data-pet-no="${pet.petNo}"]`);
        if (petElement) {
            petElement.classList.add('selected');
        }

        this.selectedTargetPet = pet;
        this.updateTransformButton();
        
        console.log('选择目标宠物:', pet.name);
    }

    /**
     * 随机生成神宠
     */
    async randomGenerateGodPet(userId, sourcePetId = null, usedItemId = null) {
        try {
            this.showLoading('正在随机生成神宠...');

            const request = {
                userId: userId,
                sourcePetId: sourcePetId,
                usedItemId: usedItemId
            };

            const response = await fetch('/api/PetTransform/random-god-pet', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(request)
            });

            const result = await response.json();
            this.hideLoading();

            if (result.success && result.data.success) {
                const data = result.data;
                let message = `百变成功！获得了 ${data.resultPetName}`;
                
                if (data.isHolyPet) {
                    message += ' (神圣宠物)';
                } else if (data.isGodPet) {
                    message += ' (神宠)';
                }
                
                message += `\n消耗金币: ${data.costGold}`;
                
                this.showMessage(message, 'success');
                
                // 刷新用户数据
                await this.loadSelectionData(userId);
                
                // 触发宠物列表更新
                if (typeof window.external.updatePetMain_page === 'function') {
                    window.external.updatePetMain_page();
                }
            } else {
                this.showMessage(result.data?.message || '随机生成神宠失败', 'error');
            }
        } catch (error) {
            this.hideLoading();
            console.error('随机生成神宠失败:', error);
            this.showMessage('随机生成神宠失败，请重试', 'error');
        }
    }

    /**
     * 执行宠物变换
     */
    async transformPet(userId, sourcePetId, targetPetNo, transformType = 'Specified', usedItemId = null) {
        try {
            this.showLoading('正在执行宠物变换...');

            const request = {
                userId: userId,
                sourcePetId: sourcePetId,
                targetPetNo: targetPetNo,
                transformType: transformType,
                usedItemId: usedItemId
            };

            const response = await fetch('/api/PetTransform/transform', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(request)
            });

            const result = await response.json();
            this.hideLoading();

            if (result.success && result.data.success) {
                const data = result.data;
                let message = `变换成功！${data.resultPetName}`;
                message += `\n消耗金币: ${data.costGold}`;
                
                this.showMessage(message, 'success');
                
                // 刷新用户数据
                await this.loadSelectionData(userId);
                
                // 触发宠物列表更新
                if (typeof window.external.updatePetMain_page === 'function') {
                    window.external.updatePetMain_page();
                }
            } else {
                this.showMessage(result.data?.message || '宠物变换失败', 'error');
            }
        } catch (error) {
            this.hideLoading();
            console.error('宠物变换失败:', error);
            this.showMessage('宠物变换失败，请重试', 'error');
        }
    }

    /**
     * 验证宠物是否可变换
     */
    async validateTransformablePet(petNo) {
        try {
            const response = await fetch(`/api/PetTransform/validate/${petNo}`);
            const result = await response.json();
            return result.success && result.data;
        } catch (error) {
            console.error('验证宠物失败:', error);
            return false;
        }
    }

    /**
     * 检查冷却时间
     */
    async checkCooldown(userId, transformType) {
        try {
            const response = await fetch(`/api/PetTransform/cooldown/${userId}/${transformType}`);
            const result = await response.json();
            
            if (result.success) {
                return result.data;
            }
            return { InCooldown: false, RemainingTime: 0 };
        } catch (error) {
            console.error('检查冷却时间失败:', error);
            return { InCooldown: false, RemainingTime: 0 };
        }
    }

    /**
     * 更新用户统计信息
     */
    updateUserStats() {
        if (!this.userStats) return;

        const elements = {
            'totalTransforms': this.userStats.totalTransforms || 0,
            'successCount': this.userStats.successCount || 0,
            'godPetCount': this.userStats.godPetCount || 0,
            'holyPetCount': this.userStats.holyPetCount || 0,
            'successRate': this.userStats.successRate || 0,
            'totalCostGold': this.userStats.totalCostGold || 0
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    /**
     * 更新配置信息
     */
    updateConfigInfo() {
        if (!this.config) return;

        const elements = {
            'costGold': this.config.costGold || 0,
            'requiredLevel': this.config.requiredLevel || 0,
            'godPetRate': this.config.godPetRate || 0,
            'holyPetRate': this.config.holyPetRate || 0,
            'cooldownTime': Math.floor((this.config.cooldownTime || 0) / 1000)
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    /**
     * 更新变换按钮状态
     */
    updateTransformButton() {
        const button = document.getElementById('transformButton');
        if (!button) return;

        if (this.selectedTargetPet) {
            button.disabled = false;
            button.textContent = `变换为 ${this.selectedTargetPet.name}`;
        } else {
            button.disabled = true;
            button.textContent = '请选择目标宠物';
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 随机神宠按钮
        const randomGodButton = document.getElementById('randomGodButton');
        if (randomGodButton) {
            randomGodButton.addEventListener('click', () => {
                const userId = this.getCurrentUserId();
                this.randomGenerateGodPet(userId);
            });
        }

        // 变换按钮
        const transformButton = document.getElementById('transformButton');
        if (transformButton) {
            transformButton.addEventListener('click', () => {
                if (this.selectedTargetPet) {
                    const userId = this.getCurrentUserId();
                    const sourcePetId = this.getSelectedSourcePetId();
                    this.transformPet(userId, sourcePetId, this.selectedTargetPet.petNo);
                }
            });
        }
    }

    /**
     * 获取当前用户ID
     */
    getCurrentUserId() {
        // 这里应该从全局变量或其他地方获取当前用户ID
        return 1; // 临时返回1，实际应用中需要动态获取
    }

    /**
     * 获取选中的源宠物ID
     */
    getSelectedSourcePetId() {
        // 这里应该从宠物选择界面获取选中的源宠物ID
        return 1; // 临时返回1，实际应用中需要动态获取
    }

    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        if (typeof window.parent.showBox === 'function') {
            window.parent.showBox(message);
        } else if (typeof showMessage === 'function') {
            showMessage(message, type);
        } else {
            alert(message);
        }
    }

    /**
     * 显示加载状态
     */
    showLoading(message) {
        console.log('Loading:', message);
        // 这里可以显示加载动画
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        console.log('Loading finished');
        // 这里可以隐藏加载动画
    }
}

// 全局实例
const petTransformManager = new PetTransformManager();

// 导出给其他模块使用
if (typeof window !== 'undefined') {
    window.PetTransformManager = PetTransformManager;
    window.petTransformManager = petTransformManager;
}

// 自动初始化（如果在宠物主页面中）
if (typeof document !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        // 检查是否在百变宠物页面
        if (document.getElementById('petTransformContainer')) {
            petTransformManager.initialize(1); // 这里的用户ID应该动态获取
        }
    });
}
