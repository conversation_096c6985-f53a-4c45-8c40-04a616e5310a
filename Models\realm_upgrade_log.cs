using System;
using SqlSugar;

namespace WebApplication_HM.Models
{
    /// <summary>
    /// 境界升级记录表
    /// </summary>
    [SugarTable("realm_upgrade_log")]
    public partial class realm_upgrade_log
    {
        public realm_upgrade_log()
        {

        }

        /// <summary>
        /// Desc:自增主键ID
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int id { get; set; }

        /// <summary>
        /// Desc:用户ID
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int user_id { get; set; }

        /// <summary>
        /// Desc:宠物ID
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int pet_id { get; set; }

        /// <summary>
        /// Desc:升级前境界等级
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int from_realm_level { get; set; }

        /// <summary>
        /// Desc:升级后境界等级
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int to_realm_level { get; set; }

        /// <summary>
        /// Desc:升级类型 (修炼丹/玄元丹)
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string upgrade_type { get; set; }

        /// <summary>
        /// Desc:是否成功 (1成功 0失败)
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int success { get; set; }

        /// <summary>
        /// Desc:消耗金币
        /// Default:0
        /// Nullable:False
        /// </summary>           
        public long cost_gold { get; set; }

        /// <summary>
        /// Desc:消耗道具JSON
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string? cost_items { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:CURRENT_TIMESTAMP
        /// Nullable:True
        /// </summary>           
        public DateTime? created_at { get; set; }
    }
}
