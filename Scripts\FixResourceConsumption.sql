-- 修复资源消耗相关的数据库问题
-- 执行前请备份数据库

-- 1. 检查用户表gold字段的null值情况
SELECT 
    id,
    username,
    nickname,
    gold,
    CASE 
        WHEN gold IS NULL THEN 'NULL'
        WHEN gold < 0 THEN 'NEGATIVE'
        WHEN gold = 0 THEN 'ZERO'
        ELSE 'POSITIVE'
    END as gold_status
FROM user 
WHERE gold IS NULL OR gold < 0
ORDER BY id;

-- 2. 修复用户表中gold字段为null的记录
UPDATE user 
SET gold = 0 
WHERE gold IS NULL;

-- 3. 检查用户道具表中数量异常的记录
SELECT 
    ui.id,
    ui.user_id,
    ui.item_id,
    ui.item_count,
    ic.name as item_name,
    CASE 
        WHEN ui.item_count IS NULL THEN 'NULL'
        WHEN ui.item_count < 0 THEN 'NEGATIVE'
        WHEN ui.item_count = 0 THEN 'ZERO'
        ELSE 'POSITIVE'
    END as count_status
FROM user_item ui
LEFT JOIN item_config ic ON ui.item_id = ic.item_no
WHERE ui.item_count IS NULL OR ui.item_count < 0
ORDER BY ui.user_id, ui.item_id;

-- 4. 修复用户道具表中数量为null或负数的记录
UPDATE user_item 
SET item_count = 0 
WHERE item_count IS NULL OR item_count < 0;

-- 5. 删除数量为0的道具记录（可选，根据业务需求决定）
-- DELETE FROM user_item WHERE item_count = 0;

-- 6. 检查合成相关配置
SELECT * FROM game_config 
WHERE config_key LIKE '%synthesis%' 
ORDER BY config_key;

-- 7. 如果没有合成配置，插入默认配置
INSERT IGNORE INTO game_config (config_key, config_value, description) VALUES
('synthesis.cost_gold', '50000', '合成消耗金币'),
('synthesis.cd_time', '10000', '合成冷却时间(毫秒)'),
('synthesis.min_level', '40', '合成最低等级要求'),
('synthesis.base_success_rate', '70', '基础成功率(%)');

-- 8. 检查用户宠物表中的异常数据
SELECT 
    up.id,
    up.user_id,
    up.pet_no,
    up.exp,
    up.growth,
    up.state,
    pc.name as pet_name
FROM user_pet up
LEFT JOIN pet_config pc ON up.pet_no = pc.pet_no
WHERE up.state IS NULL OR up.exp IS NULL OR up.growth IS NULL
ORDER BY up.user_id, up.id;

-- 9. 修复用户宠物表中的null值
UPDATE user_pet 
SET 
    exp = COALESCE(exp, 0),
    growth = COALESCE(growth, 0),
    state = COALESCE(state, 1)
WHERE exp IS NULL OR growth IS NULL OR state IS NULL;

-- 10. 创建资源消耗日志表（如果不存在）
CREATE TABLE IF NOT EXISTS resource_consumption_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型：SYNTHESIS, EVOLUTION, etc.',
    gold_before BIGINT DEFAULT 0 COMMENT '操作前金币',
    gold_after BIGINT DEFAULT 0 COMMENT '操作后金币',
    gold_consumed BIGINT DEFAULT 0 COMMENT '消耗金币',
    items_consumed TEXT COMMENT '消耗道具JSON',
    operation_result VARCHAR(20) DEFAULT 'SUCCESS' COMMENT '操作结果：SUCCESS, FAILED',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_operation (user_id, operation_type),
    INDEX idx_created_at (created_at)
) COMMENT='资源消耗日志表';

-- 11. 创建触发器监控金币变化（可选）
DELIMITER $$

DROP TRIGGER IF EXISTS user_gold_change_log$$

CREATE TRIGGER user_gold_change_log
AFTER UPDATE ON user
FOR EACH ROW
BEGIN
    IF OLD.gold != NEW.gold THEN
        INSERT INTO resource_consumption_log (
            user_id, 
            operation_type, 
            gold_before, 
            gold_after, 
            gold_consumed,
            operation_result
        ) VALUES (
            NEW.id,
            'GOLD_CHANGE',
            OLD.gold,
            NEW.gold,
            OLD.gold - NEW.gold,
            'SUCCESS'
        );
    END IF;
END$$

DELIMITER ;

-- 12. 验证修复结果
SELECT 
    '用户金币null值数量' as check_item,
    COUNT(*) as count
FROM user 
WHERE gold IS NULL

UNION ALL

SELECT 
    '用户金币负值数量' as check_item,
    COUNT(*) as count
FROM user 
WHERE gold < 0

UNION ALL

SELECT 
    '道具数量null值数量' as check_item,
    COUNT(*) as count
FROM user_item 
WHERE item_count IS NULL

UNION ALL

SELECT 
    '道具数量负值数量' as check_item,
    COUNT(*) as count
FROM user_item 
WHERE item_count < 0

UNION ALL

SELECT 
    '合成配置数量' as check_item,
    COUNT(*) as count
FROM game_config 
WHERE config_key LIKE '%synthesis%';

-- 13. 创建测试用户和数据（用于测试）
INSERT IGNORE INTO user (id, username, password, nickname, gold) VALUES
(1001, 'test_user_1001', 'password123', '测试用户1001', 100000),
(1002, 'test_user_1002', 'password123', '测试用户1002', 10000),
(1003, 'test_user_1003', 'password123', '测试用户1003', 0);

-- 14. 创建测试宠物数据
INSERT IGNORE INTO user_pet (user_id, pet_no, exp, growth, state, custom_name) VALUES
(1001, 1001, 1000000, 45.5, 1, '测试主宠'),
(1001, 1002, 800000, 35.2, 1, '测试副宠'),
(1002, 1001, 500000, 25.8, 1, '测试宠物1'),
(1002, 1002, 600000, 30.1, 1, '测试宠物2');

-- 15. 创建测试道具数据
INSERT IGNORE INTO user_item (user_id, item_id, item_count, item_pos, item_seq) VALUES
(1001, '2016100402', 10, 1, 1),
(1001, '2016100403', 5, 1, 2),
(1002, '2016100402', 2, 1, 1),
(1003, '2016100402', 0, 1, 1);

-- 执行完成提示
SELECT 'Resource consumption fix completed!' as status;
