<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>宠物五行筛选测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-section h3 { margin-top: 0; color: #333; }
        .test-button { background: #4CAF50; color: white; border: none; padding: 8px 16px; margin: 5px; border-radius: 4px; cursor: pointer; }
        .test-button:hover { background: #45a049; }
        .test-button.synthesis { background: #2196F3; }
        .test-button.nirvana { background: #9C27B0; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .pet-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px; margin: 10px 0; }
        .pet-card { border: 1px solid #ddd; padding: 10px; border-radius: 5px; background: #f9f9f9; }
        .pet-card.five-element { border-color: #4CAF50; background: #e8f5e8; }
        .pet-card.god-element { border-color: #9C27B0; background: #f3e5f5; }
        .element-tag { display: inline-block; padding: 2px 6px; border-radius: 3px; font-size: 11px; font-weight: bold; margin-left: 5px; }
        .element-five { background: #4CAF50; color: white; }
        .element-god { background: #9C27B0; color: white; }
        .comparison-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .comparison-table th, .comparison-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .comparison-table th { background: #f2f2f2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐾 宠物五行筛选测试</h1>
        
        <div class="test-section">
            <h3>📋 测试说明</h3>
            <div class="info result">
                <strong>合成接口 (synthesis-available)</strong>: 只返回五系宠物（金、木、水、火、土），排除神系宠物<br>
                <strong>涅槃接口 (nirvana-available)</strong>: 只返回神系宠物（神、神圣、聖、佛、魔、人、鬼、巫、萌、仙、灵、次元），排除五系宠物
            </div>
        </div>

        <!-- API接口测试 -->
        <div class="test-section">
            <h3>🔗 API接口测试</h3>
            <button class="test-button synthesis" onclick="testSynthesisAPI()">测试合成宠物接口</button>
            <button class="test-button nirvana" onclick="testNirvanaAPI()">测试涅槃宠物接口</button>
            <button class="test-button" onclick="compareAPIs()">对比两个接口</button>
            <div id="apiResults"></div>
        </div>

        <!-- 宠物展示 -->
        <div class="test-section">
            <h3>🎮 宠物列表展示</h3>
            <div id="petDisplay"></div>
        </div>

        <!-- 五行统计 -->
        <div class="test-section">
            <h3>📊 五行统计对比</h3>
            <div id="elementStats"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5000/api';
        const TEST_USER_ID = 1;

        function addResult(sectionId, message, type = 'info', data = null) {
            const section = document.getElementById(sectionId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            
            const time = new Date().toLocaleTimeString();
            let content = `[${time}] ${message}`;
            
            if (data) {
                content += '\n' + JSON.stringify(data, null, 2);
            }
            
            div.textContent = content;
            section.appendChild(div);
            section.scrollTop = section.scrollHeight;
        }

        // 测试合成宠物接口
        async function testSynthesisAPI() {
            try {
                addResult('apiResults', '🔄 测试合成宠物接口 (synthesis-available)...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/Player/synthesis-available?userId=${TEST_USER_ID}`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    const pets = result.data || [];
                    addResult('apiResults', `✅ 合成宠物接口成功，返回 ${pets.length} 只五系宠物`, 'success');
                    
                    // 统计五行分布
                    const elementStats = {};
                    pets.forEach(pet => {
                        const element = pet.五行 || '未知';
                        elementStats[element] = (elementStats[element] || 0) + 1;
                    });
                    
                    addResult('apiResults', `📊 五行分布: ${JSON.stringify(elementStats)}`, 'info');
                    displayPets('synthesis', pets);
                } else {
                    addResult('apiResults', '❌ 合成宠物接口失败', 'error', result);
                }
            } catch (error) {
                addResult('apiResults', `💥 合成宠物接口异常: ${error.message}`, 'error');
            }
        }

        // 测试涅槃宠物接口
        async function testNirvanaAPI() {
            try {
                addResult('apiResults', '🔄 测试涅槃宠物接口 (nirvana-available)...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/Player/nirvana-available?userId=${TEST_USER_ID}`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    const pets = result.data || [];
                    addResult('apiResults', `✅ 涅槃宠物接口成功，返回 ${pets.length} 只神系宠物`, 'success');
                    
                    // 统计五行分布
                    const elementStats = {};
                    pets.forEach(pet => {
                        const element = pet.五行 || '未知';
                        elementStats[element] = (elementStats[element] || 0) + 1;
                    });
                    
                    addResult('apiResults', `📊 五行分布: ${JSON.stringify(elementStats)}`, 'info');
                    displayPets('nirvana', pets);
                } else {
                    addResult('apiResults', '❌ 涅槃宠物接口失败', 'error', result);
                }
            } catch (error) {
                addResult('apiResults', `💥 涅槃宠物接口异常: ${error.message}`, 'error');
            }
        }

        // 对比两个接口
        async function compareAPIs() {
            try {
                addResult('apiResults', '🔄 对比合成和涅槃接口...', 'info');
                
                // 同时调用两个接口
                const [synthesisResponse, nirvanaResponse] = await Promise.all([
                    fetch(`${API_BASE_URL}/Player/synthesis-available?userId=${TEST_USER_ID}`),
                    fetch(`${API_BASE_URL}/Player/nirvana-available?userId=${TEST_USER_ID}`)
                ]);
                
                const synthesisResult = await synthesisResponse.json();
                const nirvanaResult = await nirvanaResponse.json();
                
                const synthesisPets = synthesisResult.success ? (synthesisResult.data || []) : [];
                const nirvanaPets = nirvanaResult.success ? (nirvanaResult.data || []) : [];
                
                // 统计对比
                const comparison = {
                    合成宠物数量: synthesisPets.length,
                    涅槃宠物数量: nirvanaPets.length,
                    总宠物数量: synthesisPets.length + nirvanaPets.length,
                    合成五行分布: getElementDistribution(synthesisPets),
                    涅槃五行分布: getElementDistribution(nirvanaPets),
                    重叠宠物: findOverlapPets(synthesisPets, nirvanaPets)
                };
                
                addResult('apiResults', '✅ 接口对比完成', 'success', comparison);
                displayComparison(synthesisPets, nirvanaPets);
                
            } catch (error) {
                addResult('apiResults', `💥 接口对比异常: ${error.message}`, 'error');
            }
        }

        // 获取五行分布
        function getElementDistribution(pets) {
            const stats = {};
            pets.forEach(pet => {
                const element = pet.五行 || '未知';
                stats[element] = (stats[element] || 0) + 1;
            });
            return stats;
        }

        // 查找重叠宠物
        function findOverlapPets(synthesisPets, nirvanaPets) {
            const synthesisIds = new Set(synthesisPets.map(p => p.宠物序号));
            const overlaps = nirvanaPets.filter(p => synthesisIds.has(p.宠物序号));
            return overlaps.map(p => ({ id: p.宠物序号, name: p.宠物名字, element: p.五行 }));
        }

        // 显示宠物列表
        function displayPets(type, pets) {
            const container = document.getElementById('petDisplay');
            const title = type === 'synthesis' ? '🔥 合成宠物（五系）' : '✨ 涅槃宠物（神系）';
            const cardClass = type === 'synthesis' ? 'five-element' : 'god-element';
            const elementClass = type === 'synthesis' ? 'element-five' : 'element-god';
            
            let html = `<h4>${title}</h4><div class="pet-grid">`;
            
            pets.forEach(pet => {
                html += `
                    <div class="pet-card ${cardClass}">
                        <strong>${pet.宠物名字}</strong>
                        <span class="element-tag ${elementClass}">${pet.五行}</span><br>
                        等级: ${pet.等级} | 成长: ${pet.成长}<br>
                        状态: ${pet.状态 === '0' ? '主宠' : '普通'}
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }

        // 显示对比结果
        function displayComparison(synthesisPets, nirvanaPets) {
            const container = document.getElementById('elementStats');
            
            const fiveElements = ['金', '木', '水', '火', '土', '1', '2', '3', '4', '5'];
            const godElements = ['神', '神圣', '聖', '佛', '魔', '人', '鬼', '巫', '萌', '仙', '灵', '次元'];
            
            const synthesisStats = getElementDistribution(synthesisPets);
            const nirvanaStats = getElementDistribution(nirvanaPets);
            
            let html = `
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>五行类型</th>
                            <th>合成接口数量</th>
                            <th>涅槃接口数量</th>
                            <th>筛选结果</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            // 五系宠物统计
            fiveElements.forEach(element => {
                const synthesisCount = synthesisStats[element] || 0;
                const nirvanaCount = nirvanaStats[element] || 0;
                const result = synthesisCount > 0 && nirvanaCount === 0 ? '✅ 正确' : 
                              synthesisCount === 0 && nirvanaCount === 0 ? '⚪ 无数据' : '❌ 错误';
                
                html += `
                    <tr>
                        <td>${element} (五系)</td>
                        <td>${synthesisCount}</td>
                        <td>${nirvanaCount}</td>
                        <td>${result}</td>
                    </tr>
                `;
            });
            
            // 神系宠物统计
            godElements.forEach(element => {
                const synthesisCount = synthesisStats[element] || 0;
                const nirvanaCount = nirvanaStats[element] || 0;
                const result = synthesisCount === 0 && nirvanaCount > 0 ? '✅ 正确' : 
                              synthesisCount === 0 && nirvanaCount === 0 ? '⚪ 无数据' : '❌ 错误';
                
                html += `
                    <tr>
                        <td>${element} (神系)</td>
                        <td>${synthesisCount}</td>
                        <td>${nirvanaCount}</td>
                        <td>${result}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                testSynthesisAPI();
            }, 1000);
        });
    </script>
</body>
</html>
