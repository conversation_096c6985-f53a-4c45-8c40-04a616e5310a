using System.ComponentModel.DataAnnotations;
using WebApplication_HM.Models;

namespace WebApplication_HM.DTOs
{
    #region 请求DTO

    /// <summary>
    /// 获取任务列表请求DTO
    /// </summary>
    public class TaskListRequestDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        public int UserId { get; set; }

        /// <summary>
        /// 任务类型过滤(可选)
        /// </summary>
        public int? TaskType { get; set; }

        /// <summary>
        /// 任务状态过滤(可选)
        /// </summary>
        public int? TaskStatus { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; } = 20;
    }

    /// <summary>
    /// 接取任务请求DTO
    /// </summary>
    public class AcceptTaskRequestDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        public int UserId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        [Required]
        public string TaskId { get; set; }
    }

    /// <summary>
    /// 完成任务请求DTO
    /// </summary>
    public class CompleteTaskRequestDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        public int UserId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        [Required]
        public string TaskId { get; set; }

        /// <summary>
        /// 是否一键完成
        /// </summary>
        public bool IsOneClick { get; set; } = false;
    }

    /// <summary>
    /// 放弃任务请求DTO
    /// </summary>
    public class AbandonTaskRequestDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        public int UserId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        [Required]
        public string TaskId { get; set; }
    }

    /// <summary>
    /// 更新任务进度请求DTO
    /// </summary>
    public class UpdateTaskProgressRequestDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        public int UserId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        [Required]
        public string TaskId { get; set; }

        /// <summary>
        /// 目标类型
        /// </summary>
        [Required]
        public string ObjectiveType { get; set; }

        /// <summary>
        /// 目标ID
        /// </summary>
        public string TargetId { get; set; }

        /// <summary>
        /// 增加的进度数量
        /// </summary>
        public int Amount { get; set; } = 1;
    }

    #endregion

    #region 响应DTO

    /// <summary>
    /// 任务信息DTO
    /// </summary>
    public class TaskInfoDto
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskId { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; set; }

        /// <summary>
        /// 任务描述
        /// </summary>
        public string TaskDescription { get; set; }

        /// <summary>
        /// 任务类型
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务类型名称
        /// </summary>
        public string TaskTypeName { get; set; }

        /// <summary>
        /// 是否可重复
        /// </summary>
        public bool IsRepeatable { get; set; }

        /// <summary>
        /// 前置任务ID
        /// </summary>
        public string PrerequisiteTask { get; set; }

        /// <summary>
        /// 前置任务名称
        /// </summary>
        public string PrerequisiteTaskName { get; set; }

        /// <summary>
        /// 指定宠物ID
        /// </summary>
        public string RequiredPet { get; set; }

        /// <summary>
        /// 指定宠物名称
        /// </summary>
        public string RequiredPetName { get; set; }

        /// <summary>
        /// 是否网络任务
        /// </summary>
        public bool IsNetworkTask { get; set; }

        /// <summary>
        /// 任务目标列表
        /// </summary>
        public List<TaskObjectiveDto> Objectives { get; set; } = new List<TaskObjectiveDto>();

        /// <summary>
        /// 任务奖励
        /// </summary>
        public string Rewards { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; }
    }

    /// <summary>
    /// 任务目标DTO
    /// </summary>
    public class TaskObjectiveDto
    {
        /// <summary>
        /// 目标ID
        /// </summary>
        public int ObjectiveId { get; set; }

        /// <summary>
        /// 目标类型
        /// </summary>
        public string ObjectiveType { get; set; }

        /// <summary>
        /// 目标类型名称
        /// </summary>
        public string ObjectiveTypeName { get; set; }

        /// <summary>
        /// 目标ID
        /// </summary>
        public string TargetId { get; set; }

        /// <summary>
        /// 目标名称
        /// </summary>
        public string TargetName { get; set; }

        /// <summary>
        /// 目标数量
        /// </summary>
        public int TargetAmount { get; set; }

        /// <summary>
        /// 目标描述
        /// </summary>
        public string ObjectiveDescription { get; set; }

        /// <summary>
        /// 当前进度
        /// </summary>
        public int CurrentAmount { get; set; }

        /// <summary>
        /// 是否完成
        /// </summary>
        public bool IsCompleted { get; set; }

        /// <summary>
        /// 完成百分比
        /// </summary>
        public decimal CompletionPercentage { get; set; }

        /// <summary>
        /// 格式化的进度显示文本
        /// </summary>
        public string ProgressDisplay { get; set; }
    }

    /// <summary>
    /// 任务奖励DTO
    /// </summary>
    public class TaskRewardDto
    {
        /// <summary>
        /// 奖励类型
        /// </summary>
        public string RewardType { get; set; }

        /// <summary>
        /// 奖励类型名称
        /// </summary>
        public string RewardTypeName { get; set; }

        /// <summary>
        /// 奖励ID
        /// </summary>
        public string RewardId { get; set; }

        /// <summary>
        /// 奖励名称
        /// </summary>
        public string RewardName { get; set; }

        /// <summary>
        /// 奖励数量
        /// </summary>
        public int RewardAmount { get; set; }

        /// <summary>
        /// 奖励描述
        /// </summary>
        public string RewardDescription { get; set; }

        /// <summary>
        /// 奖励图标
        /// </summary>
        public string RewardIcon { get; set; }
    }

    /// <summary>
    /// 用户任务DTO
    /// </summary>
    public class UserTaskDto
    {
        /// <summary>
        /// 用户任务ID
        /// </summary>
        public int UserTaskId { get; set; }

        /// <summary>
        /// 任务信息
        /// </summary>
        public TaskInfoDto TaskInfo { get; set; }

        /// <summary>
        /// 任务状态
        /// </summary>
        public int TaskStatus { get; set; }

        /// <summary>
        /// 任务状态名称
        /// </summary>
        public string TaskStatusName { get; set; }

        /// <summary>
        /// 接取时间
        /// </summary>
        public DateTime AcceptedAt { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        /// 完成次数
        /// </summary>
        public int CompletionCount { get; set; }

        /// <summary>
        /// 任务进度
        /// </summary>
        public List<TaskObjectiveDto> Progress { get; set; } = new List<TaskObjectiveDto>();

        /// <summary>
        /// 总体完成百分比
        /// </summary>
        public decimal OverallCompletionPercentage { get; set; }

        /// <summary>
        /// 是否可以完成
        /// </summary>
        public bool CanComplete { get; set; }

        /// <summary>
        /// 是否可以一键完成
        /// </summary>
        public bool CanOneClick { get; set; }
    }

    /// <summary>
    /// 任务列表响应DTO
    /// </summary>
    public class TaskListResponseDto
    {
        /// <summary>
        /// 可接取任务列表
        /// </summary>
        public List<TaskInfoDto> AvailableTasks { get; set; } = new List<TaskInfoDto>();

        /// <summary>
        /// 进行中任务列表
        /// </summary>
        public List<UserTaskDto> InProgressTasks { get; set; } = new List<UserTaskDto>();

        /// <summary>
        /// 已完成任务列表
        /// </summary>
        public List<UserTaskDto> CompletedTasks { get; set; } = new List<UserTaskDto>();

        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int PageIndex { get; set; }

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; }
    }

    /// <summary>
    /// 任务详情响应DTO
    /// </summary>
    public class TaskDetailResponseDto
    {
        /// <summary>
        /// 用户任务信息
        /// </summary>
        public UserTaskDto UserTask { get; set; }

        /// <summary>
        /// 是否已接取
        /// </summary>
        public bool IsAccepted { get; set; }

        /// <summary>
        /// 可接取状态信息
        /// </summary>
        public string AcceptabilityMessage { get; set; }
    }

    /// <summary>
    /// 任务操作结果DTO
    /// </summary>
    public class TaskOperationResultDto
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 获得的奖励(完成任务时)
        /// </summary>
        public List<TaskRewardDto> Rewards { get; set; } = new List<TaskRewardDto>();

        /// <summary>
        /// 更新后的任务状态
        /// </summary>
        public UserTaskDto UpdatedTask { get; set; }
    }

    #endregion

    #region 内部DTO

    /// <summary>
    /// 任务奖励配置DTO(用于JSON序列化)
    /// </summary>
    public class TaskRewardConfigDto
    {
        /// <summary>
        /// 奖励类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 奖励ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 奖励数量
        /// </summary>
        public int Amount { get; set; }
    }

    /// <summary>
    /// 新格式任务奖励配置DTO（支持复合奖励格式）
    /// </summary>
    public class TaskRewardObjectDto
    {
        /// <summary>
        /// 经验奖励
        /// </summary>
        public int? Exp { get; set; }

        /// <summary>
        /// 金币奖励
        /// </summary>
        public int? Gold { get; set; }

        /// <summary>
        /// 元宝奖励
        /// </summary>
        public int? Yuanbao { get; set; }

        /// <summary>
        /// 水晶奖励
        /// </summary>
        public int? Crystal { get; set; }

        /// <summary>
        /// 道具奖励列表
        /// </summary>
        public List<TaskRewardItemDto>? Items { get; set; }
    }

    /// <summary>
    /// 任务奖励道具DTO
    /// </summary>
    public class TaskRewardItemDto
    {
        /// <summary>
        /// 道具ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 道具数量
        /// </summary>
        public int Amount { get; set; }

        /// <summary>
        /// 道具名称（可选）
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 道具品质（可选）
        /// </summary>
        public int? Quality { get; set; }
    }

    /// <summary>
    /// 任务进度更新事件DTO
    /// </summary>
    public class TaskProgressUpdateEventDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 目标类型
        /// </summary>
        public string ObjectiveType { get; set; }

        /// <summary>
        /// 目标ID
        /// </summary>
        public string TargetId { get; set; }

        /// <summary>
        /// 增加的数量
        /// </summary>
        public int Amount { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 额外数据
        /// </summary>
        public Dictionary<string, object> ExtraData { get; set; } = new Dictionary<string, object>();
    }

    #endregion
}
