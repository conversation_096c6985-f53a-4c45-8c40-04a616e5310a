using System.Net.WebSockets;
using WebApplication_HM.DTOs.ResultDTO;

namespace WebApplication_HM.Interface
{
    /// <summary>
    /// 实时服务接口
    /// </summary>
    public interface IRealTimeService
    {
        #region 连接管理

        /// <summary>
        /// 添加玩家连接
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="socket">WebSocket连接</param>
        /// <param name="playerName">玩家昵称</param>
        void AddPlayerConnection(int playerId, WebSocket socket, string playerName);

        /// <summary>
        /// 移除玩家连接
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        void RemovePlayerConnection(int playerId);

        /// <summary>
        /// 通过连接ID获取玩家ID
        /// </summary>
        /// <param name="connectionId">连接ID</param>
        /// <returns>玩家ID</returns>
        int? GetPlayerIdByConnection(string connectionId);

        /// <summary>
        /// 获取在线玩家数量
        /// </summary>
        /// <returns>在线玩家数量</returns>
        int GetOnlinePlayerCount();

        /// <summary>
        /// 获取在线玩家列表
        /// </summary>
        /// <returns>在线玩家状态列表</returns>
        List<PlayerStatusDTO> GetOnlinePlayers();

        #endregion

        #region 实时战斗推送

        /// <summary>
        /// 发送实时战斗推送
        /// </summary>
        /// <param name="battleNotification">战斗通知信息</param>
        Task SendBattleNotification(RealTimeBattleDTO battleNotification);

        /// <summary>
        /// 发送战斗开始通知
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="battleId">战斗ID</param>
        /// <param name="mapId">地图ID</param>
        /// <param name="mapName">地图名称</param>
        Task SendBattleStartNotification(int playerId, string battleId, int mapId, string mapName);

        #endregion

        #region 玩家状态同步

        /// <summary>
        /// 更新玩家状态
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="status">新状态</param>
        void UpdatePlayerStatus(int playerId, string status);

        /// <summary>
        /// 更新玩家状态（带地图信息）
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="status">新状态</param>
        /// <param name="mapId">当前地图ID</param>
        /// <param name="mapName">当前地图名称</param>
        Task UpdatePlayerStatusWithMap(int playerId, string status, int? mapId = null, string mapName = null);

        #endregion

        #region 系统公告

        /// <summary>
        /// 发送系统公告给指定玩家
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="notice">公告信息</param>
        void SendSystemNotice(int playerId, SystemNoticeDTO notice);

        /// <summary>
        /// 广播系统公告
        /// </summary>
        /// <param name="notice">公告信息</param>
        void BroadcastSystemNotice(SystemNoticeDTO notice);

        /// <summary>
        /// 发送游戏事件通知给指定玩家
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="eventNotice">事件通知</param>
        void SendGameEvent(int playerId, GameEventNoticeDTO eventNotice);

        /// <summary>
        /// 广播游戏事件通知
        /// </summary>
        /// <param name="eventNotice">事件通知</param>
        void BroadcastGameEvent(GameEventNoticeDTO eventNotice);

        #endregion

        #region 境界突破通知

        /// <summary>
        /// 发送境界突破通知
        /// </summary>
        /// <param name="notification">境界突破通知</param>
        Task SendRealmBreakthroughNotification(RealmNotificationDTO notification);

        /// <summary>
        /// 发送境界修炼进度通知
        /// </summary>
        /// <param name="progress">修炼进度</param>
        Task SendRealmProgressNotification(RealmProgressDTO progress);

        #endregion

        #region 装备通知

        /// <summary>
        /// 发送装备获得通知
        /// </summary>
        /// <param name="notification">装备通知</param>
        Task SendEquipmentNotification(EquipmentNotificationDTO notification);

        /// <summary>
        /// 发送装备强化通知
        /// </summary>
        /// <param name="notification">强化通知</param>
        Task SendEquipmentEnhanceNotification(EquipmentEnhanceNotificationDTO notification);

        #endregion

        #region 通用发送方法

        /// <summary>
        /// 发送消息给指定玩家
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="message">消息内容</param>
        void SendMessage(int playerId, string message);

        /// <summary>
        /// 广播消息给所有在线玩家
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="messageType">消息类型</param>
        void BroadcastMessage(string message, string messageType);

        /// <summary>
        /// 发送心跳检测给指定玩家
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        void SendHeartbeat(int playerId);

        /// <summary>
        /// 发送消息给指定玩家（Task版本）
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="message">消息内容</param>
        Task SendToPlayer(int playerId, string message);

        /// <summary>
        /// 广播消息给所有在线玩家（Task版本）
        /// </summary>
        /// <param name="message">消息内容</param>
        Task BroadcastToAll(string message);

        #endregion
    }
} 