<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <script language="javascript" src="../javascript/prototype.js"></script>
    <link href="../css/iframe.css" rel="stylesheet" type="text/css" />
    <style type="text/css">
        <!--
        body {
            margin-left: 15px;
            margin-top: 15px;
            margin-right: 0px;
            margin-bottom: 0px;
            font-size: 12px;
        }

        #Layer1 {
            position: absolute;
            left: 413px;
            top: 75px;
            width: 27px;
            height: 26px;
            z-index: 1;
        }
        -->
    </style>
    <!--[if IE 6]>
    <script type="text/javascript">try { document.execCommand('BackgroundImageCache', false, true); } catch(e) {}
    </script>
    <![endif]-->
</head>
<!--background-image:url(img/map1.gif)-->

<body style="margin:0px;" oncontextmenu="event.returnValue=false" onkeydown="KeyDown()">

    <div style="width:786px;height:321px;margin-left:5px;margin-top:5px; background-image:url(img/map7.jpg)"
        oncontextmenu="event.returnValue=false">
    </div>






    <div style="position:absolute;left:206px;top:53px;border:0px;cursor:pointer;" title="魔石荒野"
        onclick="openMode('206')">
        <div style="width:75px; height:24px;background:url(img/140.png); _background:none; _filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled='true', sizingMethod='image', src='img/03.png');
"></div>
    </div>

    <div style="position:absolute;left:265px;top:155px;border:0px;cursor:pointer;" title="希望之谷"
        onclick="openMode('205')">
        <div style="width:75px; height:24px;background:url(img/149_1.png); _background:none; _filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled='true', sizingMethod='image', src='img/149_0.png');
"></div>
    </div>


    <div style="position:absolute; left:552px; top:26px; border:0px; cursor:pointer; width: 72px;" title="魔法森林"
        onclick="openMode('203')">
        <div style="width:75px; height:24px;background:url(img/137.png); _background:none; _filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled='true', sizingMethod='image', src='img/03.png');
"></div>
    </div>

    <div style="position:absolute;left:573px;top:277px;border:0px;cursor:pointer;" title="魔龙岛" onclick="openMode('202')"
        ;goMap(0,134);"=;goMap(0,134);">
        <div style="width:75px; height:24px;background:url(img/134.png); _background:none; _filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled='true', sizingMethod='image', src='img/03.png');
"></div>
    </div>

    <div style="position:absolute;left:666px;top:109px;border:0px;cursor:pointer;" title="生命之泉"
    onclick="openMode('207')">
        <div style="width:75px; height:24px;background:url(img/145.png); _background:none; _filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled='true', sizingMethod='image', src='img/03.png');
"></div>
    </div>

    <div style="position:absolute;left:68px;top:270px;border:0px;cursor:pointer;" title="黑暗之谷"
        onclick="copyWord('黑暗之谷');goMap(0,148);">
        <div style="width:75px; height:24px;background:url(img/148.png); _background:none; _filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled='true', sizingMethod='image', src='img/03.png');
"></div>
    </div>

    <div style="position:absolute; left:27px; top:180px; border:0px; cursor:pointer; width: 73px;" title="光之谷"
        onclick="openMode('201')">
        <div style="width:75px; height:24px;background:url(img/131.png); _background:none; _filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled='true', sizingMethod='image', src='img/03.png');
"></div>
    </div>

    <div style="position:absolute; left:301px; top:269px; border:0px; cursor:pointer; width: 76px;" title="恶魔之谷"
        onclick="openMode('204');fbMap(144);">
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
    </div>

    <div style="position:absolute; left:54px; top:26px; border:0px; cursor:pointer; width: 89px;" title="魔界地宫"
        onclick="copyWord('魔界地宫');fbMap(143);">
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
    </div>

    <div style="position:absolute;left:20px;top:150px; border:0px; cursor:pointer; width: 36px; height:19px"
        align="center" title="切换地图" onclick="window.parent.openUrl('BattleMap.html')">
        <img src="img/left.gif" onclick="window.parent.openUrl('BattleMap.html?' + Math.random())" alt='切换地图' />
    </div>




</body>

</html>
<script language="javascript" type="text/javascript">

    // Add by DuHao 2009-5-13
    function copyWord(words) {
        window.parent.$('baike_input').value = words;
    }

    function goMap(n, t) {
        //if(n==0)
        //{
        //if(confirm('你还没有打开该地图，要使用钥匙打开吗？')==true)
        //{
        //openMap(t);
        //}
        //else return;
        //}
        //else
        //{
        var opt = {
            method: 'get',
            onSuccess: function (e) {
                //window.parent.Alert(t.responseText);
                var v = e.responseText;
                if (isNaN(v)) {
                    if (!confirm(v)) {
                        return false;
                    }
                    var opt = {
                        method: 'get',
                        onSuccess: function (f) {
                            //window.parent.Alert(t.responseText);
                            var v = parseInt(f.responseText);
                            if (v == 11) {
                                window.parent.Alert("进入地图成功！");
                                czl(t);
                                //window.parent.$('gw').src='./function/Team_Mod.php?n='+t;
                            }
                        },
                        on404: function (f) {
                        },
                        onFailure: function (f) {
                        },
                        asynchronous: true
                    }
                    //window.status = '../function/mapGate.php?type=1&n='+n;return false;
                    var ajax = new Ajax.Request('../function/mapGate.php?type=2&n=' + t, opt);
                }
                else if (v == 10) {
                    czl(t);
                    //window.parent.$('gw').src='./function/Team_Mod.php?n='+t;
                }
                else if (v == 1) {
                    window.parent.Alert("地图数据错误");
                }
                else if (v == 2) {
                    window.parent.Alert("你的当前等级不够");
                }
                else if (v == 3) {
                    window.parent.Alert("你没有相关道具");
                }
                else if (v == 12) {
                    if (n == 0) {
                        if (confirm('你还没有打开该地图，要使用钥匙打开吗？') == true) {
                            openMap(t);
                        }
                        else return;
                    }
                    else {
                        window.parent.$('gw').src = './function/Team_Mod.php?n=' + t;
                    }
                }
            },
            on404: function (e) {
            },
            onFailure: function (e) {
            },
            asynchronous: true
        }
        //window.status = '../function/mapGate.php?type=1&n='+t;return false;
        var ajax = new Ajax.Request('../function/mapGate.php?type=1&n=' + t, opt);
        /*if(!confirm(""))
        {
            return false;
        }
        window.parent.$('gw').src='./function/Team_Mod.php?n='+n;*/
        //}
    }

    //副本地图判断
    function fbMap(id) {
        if (id != "") {
            window.parent.$('gw').src = './function/fb_Mod.php?mapid=' + id;
        }
    }


    function openMode(i) {
        /* alert("跳转" + "MapInfo/t" + i + ".html?");*/
        window.location.href = "MapInfo/t" + i + ".html?" + Math.random();
    }

    // Add by DuHao 2009-5-13
    function copyWord(words) {
        window.parent.$('baike_input').value = words;
    }
</script>