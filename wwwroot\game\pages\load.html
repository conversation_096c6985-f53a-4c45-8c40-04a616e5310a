
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<!--[if IE 6]><script type="text/javascript">try{ document.execCommand("BackgroundImageCache", false, true); } catch(e) {}
</script>
<![endif]-->
<body style="background-color: #FFFCEB;margin-top:0px;">
    <center>
        <div style="margin-top:140px;">
            <img style=" position: absolute;left: 260px;" src="http://**************:8089/img/loading.gif">
			<div id="timev" style="position:absolute;text-align:center;color:#F98F2C;font-weight:bold;font-size:2em;left: 289px;top: 150px;height: 40px;">5</div>
        </div>
    </center>


    <script language="javascript">
        var readH;
        var pt = 0;
        var id;
        var zd;
        function loadtime(m) {
          
          
            if (m < 1 && pt == 0) {
                window.clearTimeout(readH);
				
                window.parent.jinru();
                return;
            }
            else {
                document.getElementById("timev").innerHTML = m--;
                readH = window.setTimeout("loadtime(" + m + ");", 1000);
            }
        }
        function pause(m) {
            if (pt == 1) return;
            if (m == 0) {
                window.parent.document.getElementById("gw").src = "./function/Fight_Mod.php?p=213918&s=t";
            }
            pt = 1;
        }
        loadtime(1)
    </script>
   
</body>
</html>