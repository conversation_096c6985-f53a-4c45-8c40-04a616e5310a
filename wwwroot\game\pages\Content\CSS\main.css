﻿body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,form,fieldset,input,textarea,p,blockquote,th,td,em {padding:0; margin:0; outline:none} 
fieldset,img {border:0} 
address,caption,cite,code,dfn,em,strong,th,var {font-weight:normal; font-style:normal} 
strong {font-weight:bold}
ol,ul {list-style:none} 
h1,h2,h3,h4,h5,h6 {font-weight:normal; font-size:100%} 
input {font-size:12px; color:#333}
.l {float:left}
.r {float:right}
.clearfix:after {content: "."; display: block; height: 0; clear: both; visibility: hidden; }  
.clearfix{zoom:1;}  
.clear {clear:both; height:0; overflow:hidden}
body {
	font: 12px/normal "瀹嬩綋";
	/* [disabled]color:#007ea0; */
	overflow-x: hidden;
}
a,a:visited {color:#513a11; text-decoration:none}
a {blr:expression(this.onFocus=this.blur());outline:none;}
area{blr:expression(this.onFocus=this.blur());outline:none;}
a:hover {text-decoration:underline}
