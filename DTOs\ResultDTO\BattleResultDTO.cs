namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 战斗结果DTO
    /// </summary>
    public class BattleResultDTO
    {
        /// <summary>
        /// 是否胜利
        /// </summary>
        public bool IsWin { get; set; }
        
        /// <summary>
        /// 战斗是否结束
        /// </summary>
        public bool IsBattleEnd { get; set; }
        
        /// <summary>
        /// 当前回合数
        /// </summary>
        public int CurrentRound { get; set; }
        
        /// <summary>
        /// 所有回合的战斗记录
        /// </summary>
        public List<BattleRoundDTO> BattleRounds { get; set; } = new List<BattleRoundDTO>();
        
        /// <summary>
        /// 玩家当前血量
        /// </summary>
        public int PlayerCurrentHp { get; set; }
        
        /// <summary>
        /// 怪物当前血量
        /// </summary>
        public int MonsterCurrentHp { get; set; }
        
        /// <summary>
        /// 玩家最大血量
        /// </summary>
        public int PlayerMaxHp { get; set; }
        
        /// <summary>
        /// 怪物最大血量
        /// </summary>
        public int MonsterMaxHp { get; set; }
        
        /// <summary>
        /// 获得经验（仅胜利时）
        /// </summary>
        public int Exp { get; set; }
        
        /// <summary>
        /// 掉落物品（仅胜利时）
        /// </summary>
        public List<string> DropItems { get; set; } = new List<string>();
        
        /// <summary>
        /// 战斗消息
        /// </summary>
        public string Message { get; set; } = "";

        /// <summary>
        /// 战斗唯一标识（用于安全领取奖励）
        /// </summary>
        public string BattleId { get; set; } = "";

        // === 原项目兼容字段 (WindowsFormsApplication7 FightResult) ===

        /// <summary>
        /// 战斗是否结束 (1为结束 0为没结束) - 兼容原项目
        /// </summary>
        public int BattleEndFlag { get; set; }

        /// <summary>
        /// 是否死亡 (1为死亡 0为存活) - 兼容原项目
        /// </summary>
        public int IsDead { get; set; }

        /// <summary>
        /// 总输出伤害 - 兼容原项目
        /// </summary>
        public long TotalDamageDealt { get; set; }

        /// <summary>
        /// 总受到伤害 - 兼容原项目
        /// </summary>
        public long TotalDamageReceived { get; set; }

        /// <summary>
        /// 对方剩余HP - 兼容原项目
        /// </summary>
        public long MonsterRemainingHp { get; set; }

        /// <summary>
        /// 己方剩余HP - 兼容原项目
        /// </summary>
        public long PlayerRemainingHp { get; set; }

        /// <summary>
        /// 获得道具字符串 - 兼容原项目
        /// </summary>
        public string ItemsGainedString { get; set; } = "";

        /// <summary>
        /// 获得元宝 - 兼容原项目
        /// </summary>
        public int YuanbaoGained { get; set; }

        /// <summary>
        /// 获得金币 - 兼容原项目
        /// </summary>
        public int GoldGained { get; set; }

        /// <summary>
        /// 获得经验 - 兼容原项目 (与Exp字段保持同步)
        /// </summary>
        public int ExperienceGained { get; set; }

        /// <summary>
        /// 抵消伤害 - 兼容原项目
        /// </summary>
        public long DamageReduced { get; set; }

        /// <summary>
        /// 加深伤害 - 兼容原项目
        /// </summary>
        public long DamageAmplified { get; set; }

        /// <summary>
        /// 吸血回复 - 兼容原项目
        /// </summary>
        public long LifeSteal { get; set; }

        /// <summary>
        /// 吸魔回复 - 兼容原项目
        /// </summary>
        public long ManaSteal { get; set; }

        /// <summary>
        /// 剩余魔法值 - 兼容原项目
        /// </summary>
        public long RemainingMp { get; set; }

        /// <summary>
        /// 是否先手 (1为先手 0为后手) - 兼容原项目
        /// </summary>
        public int IsFirstStrike { get; set; }

        /// <summary>
        /// 自动战斗状态 (0正常 1打不过 2没钥匙) - 兼容原项目
        /// </summary>
        public int AutoBattleStatus { get; set; }
    }
    
    /// <summary>
    /// 战斗回合DTO
    /// </summary>
    public class BattleRoundDTO
    {
        /// <summary>
        /// 回合数
        /// </summary>
        public int Round { get; set; }
        
        /// <summary>
        /// 攻击者类型（Player/Monster）
        /// </summary>
        public string AttackerType { get; set; } = "";
        
        /// <summary>
        /// 伤害值
        /// </summary>
        public int Damage { get; set; }
        
        /// <summary>
        /// 是否暴击
        /// </summary>
        public bool IsCritical { get; set; }
        
        /// <summary>
        /// 是否命中
        /// </summary>
        public bool IsHit { get; set; }
        
        /// <summary>
        /// 使用的技能ID
        /// </summary>
        public string SkillId { get; set; } = "";
        
        /// <summary>
        /// 回合描述
        /// </summary>
        public string Description { get; set; } = "";
        
        /// <summary>
        /// 攻击后玩家血量
        /// </summary>
        public int PlayerHpAfter { get; set; }
        
        /// <summary>
        /// 攻击后怪物血量
        /// </summary>
        public int MonsterHpAfter { get; set; }
    }
} 