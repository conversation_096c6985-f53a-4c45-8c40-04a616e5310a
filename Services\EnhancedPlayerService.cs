using WebApplication_HM.Interface;
using WebApplication_HM.DTOs.ResultDTO;
using WebApplication_HM.DTOs.RequestDTO;
using WebApplication_HM.Sugar;
using WebApplication_HM.Models;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 增强的玩家服务（兼容现有系统并添加装备属性计算）
    /// </summary>
    public class EnhancedPlayerService : IPlayerService
    {
        private readonly PlayerService _basePlayerService;
        private readonly IEquipmentAttributeService _equipmentAttributeService;
        private readonly DbContext _dbContext;
        private readonly ILogger<EnhancedPlayerService> _logger;

        public EnhancedPlayerService(
            PlayerService basePlayerService,
            IEquipmentAttributeService equipmentAttributeService,
            DbContext dbContext,
            ILogger<EnhancedPlayerService> logger)
        {
            _basePlayerService = basePlayerService;
            _equipmentAttributeService = equipmentAttributeService;
            _dbContext = dbContext;
            _logger = logger;
        }

        #region 委托给基础服务的方法

        public async Task<BattleResultDTO> BattleCalculate(BattleRequestDTO request)
        {
            return await _basePlayerService.BattleCalculate(request);
        }

        public LoginResultDTO Login(LoginRequestDTO request)
        {
            return _basePlayerService.Login(request);
        }

        public RegisterResultDTO Register(RegisterRequestDTO request)
        {
            return _basePlayerService.Register(request);
        }

        public PlayerInfoResultDTO GetPlayerInfo(PlayerInfoRequestDTO request)
        {
            return _basePlayerService.GetPlayerInfo(request);
        }

        public async Task<ClaimRewardResultDTO> ClaimBattleReward(ClaimRewardRequestDTO request)
        {
            return await _basePlayerService.ClaimBattleReward(request);
        }



        public async Task<PetDetailResultDTO> GetPetDetail(PetDetailRequestDTO request)
        {
            return await _basePlayerService.GetPetDetail(request);
        }

        public LoginResultDTO SetMainPet(SetMainPetRequestDTO request)
        {
            return _basePlayerService.SetMainPet(request);
        }

        public MapListResultDTO GetMapList(MapListRequestDTO request)
        {
            return _basePlayerService.GetMapList(request);
        }

        public GameConfigResultDTO GetPetConfigs()
        {
            return _basePlayerService.GetPetConfigs();
        }

        public LoginResultDTO EquipItem(EquipRequestDTO request)
        {
            return _basePlayerService.EquipItem(request);
        }

        public BagResultDTO GetBag(BagRequestDTO request)
        {
            return _basePlayerService.GetBag(request);
        }

        public GameConfigResultDTO GetEquipmentConfigs()
        {
            return _basePlayerService.GetEquipmentConfigs();
        }

        public GameConfigResultDTO GetGameConfig(ConfigRequestDTO request)
        {
            return _basePlayerService.GetGameConfig(request);
        }

        public GameConfigResultDTO GetItemConfigs()
        {
            return _basePlayerService.GetItemConfigs();
        }

        public MapDetailResultDTO GetMapDetail(MapDetailRequestDTO request)
        {
            return _basePlayerService.GetMapDetail(request);
        }

        public GameConfigResultDTO GetMonsterConfigs()
        {
            return _basePlayerService.GetMonsterConfigs();
        }

        public GameConfigResultDTO GetRealmConfigs()
        {
            return _basePlayerService.GetRealmConfigs();
        }

        public GameConfigResultDTO GetSkillConfigs()
        {
            return _basePlayerService.GetSkillConfigs();
        }

        public EquipmentListResultDTO GetUserEquipments(EquipmentListRequestDTO request)
        {
            return _basePlayerService.GetUserEquipments(request);
        }

        public async Task<BattlePetInfoResultDTO> GetMainPet(int userId)
        {
            return await _basePlayerService.GetMainPet(userId);
        }

        public PetListResultDTO GetUserPets(PetListRequestDTO request)
        {
            return _basePlayerService.GetUserPets(request);
        }

        public SellItemResultDTO SellItem(SellItemRequestDTO request)
        {
            return _basePlayerService.SellItem(request);
        }

        public SortBagResultDTO SortBag(SortBagRequestDTO request)
        {
            return _basePlayerService.SortBag(request);
        }

        public LoginResultDTO UnequipItem(UnequipRequestDTO request)
        {
            return _basePlayerService.UnequipItem(request);
        }



        public UseItemResultDTO UseItem(UseItemRequestDTO request)
        {
            return _basePlayerService.UseItem(request);
        }

        public PetListResultDTO GetSynthesisAvailablePets(int userId)
        {
            return _basePlayerService.GetSynthesisAvailablePets(userId);
        }

        public PetListResultDTO GetNirvanaAvailablePets(int userId)
        {
            return _basePlayerService.GetNirvanaAvailablePets(userId);
        }

        public PetListResultDTO GetEvolutionAvailablePets(int userId)
        {
            return _basePlayerService.GetEvolutionAvailablePets(userId);
        }

        #endregion

        /// <summary>
        /// 增强的属性计算方法（包含装备属性）
        /// </summary>
        /// <param name="request">属性计算请求</param>
        /// <returns>包含装备加成的属性结果</returns>
        public async Task<AttributeResultDTO> GetPetAttributesEnhancedAsync(AttributeRequestDTO request)
        {
            try
            {
                // 1. 获取基础属性（使用现有系统）
                var baseAttributes = _basePlayerService.GetPetAttributes(request);

                // 2. 获取宠物ID（通过pet_no查询）
                var pet = await _dbContext.Db.Queryable<user_pet>()
                    .Where(x => x.user_id == request.UserId && x.pet_no == request.PetId)
                    .FirstAsync();

                if (pet == null)
                    return baseAttributes;

                // 3. 计算装备属性加成
                var equipmentAttributes = await _equipmentAttributeService.CalculateEquipmentAttributesAsync(pet.pet_no, request.UserId);

                // 4. 合并属性
                var enhancedAttributes = new AttributeResultDTO
                {
                    Atk = baseAttributes.Atk + (int)equipmentAttributes.GetValueOrDefault("攻击", 0),
                    Def = baseAttributes.Def + (int)equipmentAttributes.GetValueOrDefault("防御", 0),
                    Hit = baseAttributes.Hit + (int)equipmentAttributes.GetValueOrDefault("命中", 0),
                    Dodge = baseAttributes.Dodge + (int)equipmentAttributes.GetValueOrDefault("闪避", 0),
                    Spd = baseAttributes.Spd + (int)equipmentAttributes.GetValueOrDefault("速度", 0),
                    Hp = baseAttributes.Hp + (int)equipmentAttributes.GetValueOrDefault("生命", 0),
                    Mp = baseAttributes.Mp + (int)equipmentAttributes.GetValueOrDefault("魔法", 0),
                    Deepen = equipmentAttributes.GetValueOrDefault("加深", 0),
                    Offset = equipmentAttributes.GetValueOrDefault("抵消", 0),
                    Vamp = equipmentAttributes.GetValueOrDefault("吸血", 0),
                    VampMp = equipmentAttributes.GetValueOrDefault("吸魔", 0)
                };

                // 5. 添加详细信息（可选）
                if (request.UserId > 0) // 可以通过某种标志来决定是否包含详细信息
                {
                    var detailInfo = await _equipmentAttributeService.GetDetailedAttributeCalculationAsync(pet.pet_no, request.UserId);
                    enhancedAttributes.DetailInfo = new AttributeDetailInfo
                    {
                        BaseAttributes = new Dictionary<string, double>
                        {
                            {"攻击", baseAttributes.Atk}, {"命中", baseAttributes.Hit},
                            {"防御", baseAttributes.Def}, {"速度", baseAttributes.Spd},
                            {"闪避", baseAttributes.Dodge}, {"生命", baseAttributes.Hp},
                            {"魔法", baseAttributes.Mp}
                        },
                        EquipmentAttributes = detailInfo.EquipmentAttributes,
                        SuitAttributes = detailInfo.SuitAttributes,
                        GemstoneAttributes = detailInfo.GemstoneAttributes,
                        TotalAttributes = detailInfo.TotalAttributes
                    };
                }

                return enhancedAttributes;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "增强属性计算失败，用户ID: {UserId}, 宠物ID: {PetId}", 
                    request.UserId, request.PetId);
                
                // 出错时返回基础属性
                return _basePlayerService.GetPetAttributes(request);
            }
        }

        /// <summary>
        /// 兼容现有接口的属性计算方法
        /// </summary>
        /// <param name="request">属性计算请求</param>
        /// <returns>属性计算结果</returns>
        public AttributeResultDTO GetPetAttributes(AttributeRequestDTO request)
        {
            // 对于同步调用，使用基础属性计算
            // 异步增强版本通过单独的API提供
            return _basePlayerService.GetPetAttributes(request);
        }

        /// <summary>
        /// 增强的战斗计算（包含装备属性）
        /// </summary>
        /// <param name="request">战斗请求</param>
        /// <returns>战斗结果</returns>
        public async Task<BattleResultDTO> BattleCalculateEnhancedAsync(BattleRequestDTO request)
        {
            try
            {
                // 1. 获取增强的玩家属性
                var enhancedPlayerAttr = await GetPetAttributesEnhancedAsync(
                    new AttributeRequestDTO { UserId = request.UserId, PetId = request.PetId });

                // 2. 创建临时的战斗请求，使用增强属性
                // 这里需要修改战斗计算逻辑以支持传入自定义属性
                // 由于现有战斗系统的限制，我们可以：
                // a) 临时修改数据库中的宠物属性
                // b) 或者重写战斗计算逻辑

                // 方案A：临时修改宠物属性（推荐）
                var pet = await _dbContext.Db.Queryable<user_pet>()
                    .Where(x => x.user_id == request.UserId && x.pet_no == request.PetId)
                    .FirstAsync();

                if (pet != null)
                {
                    // 备份原始属性
                    var originalAtk = pet.atk;
                    var originalDef = pet.def;
                    var originalHit = pet.hit;
                    var originalDodge = pet.dodge;
                    var originalSpd = pet.spd;
                    var originalHp = pet.hp;
                    var originalMp = pet.mp;

                    try
                    {
                        // 临时设置增强属性
                        await _dbContext.Db.Updateable<user_pet>()
                            .SetColumns(x => new user_pet
                            {
                                atk = enhancedPlayerAttr.Atk,
                                def = enhancedPlayerAttr.Def,
                                hit = enhancedPlayerAttr.Hit,
                                dodge = enhancedPlayerAttr.Dodge,
                                spd = enhancedPlayerAttr.Spd,
                                hp = enhancedPlayerAttr.Hp,
                                mp = enhancedPlayerAttr.Mp
                            })
                            .Where(x => x.id == pet.id)
                            .ExecuteCommandAsync();

                        // 执行战斗计算
                        var battleResult = await _basePlayerService.BattleCalculate(request);

                        // 恢复原始属性
                        await _dbContext.Db.Updateable<user_pet>()
                            .SetColumns(x => new user_pet
                            {
                                atk = originalAtk,
                                def = originalDef,
                                hit = originalHit,
                                dodge = originalDodge,
                                spd = originalSpd,
                                hp = originalHp,
                                mp = originalMp
                            })
                            .Where(x => x.id == pet.id)
                            .ExecuteCommandAsync();

                        return battleResult;
                    }
                    catch
                    {
                        // 确保在异常情况下也能恢复属性
                        await _dbContext.Db.Updateable<user_pet>()
                            .SetColumns(x => new user_pet
                            {
                                atk = originalAtk,
                                def = originalDef,
                                hit = originalHit,
                                dodge = originalDodge,
                                spd = originalSpd,
                                hp = originalHp,
                                mp = originalMp
                            })
                            .Where(x => x.id == pet.id)
                            .ExecuteCommandAsync();
                        throw;
                    }
                }

                // 如果找不到宠物，使用基础战斗计算
                return await _basePlayerService.BattleCalculate(request);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "增强战斗计算失败，回退到基础战斗计算");
                return await _basePlayerService.BattleCalculate(request);
            }
        }
    }
}
