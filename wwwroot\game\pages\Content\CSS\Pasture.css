﻿        body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, textarea, p, blockquote, th, td, em {
            padding: 0;
            margin: 0;
            outline: none;
            font-size: 12px;
        }

        .task {
            width: 788px;
            height: 319px;
            background: #f2ebc5;
            color: #B06A01;
            font-size: 12px;
        }

        .task_left {
            width: 138px;
            height: 319px;
            float: left;
            background-image: url(../Img/Pasture/muchang_03.jpg);
        }

        .task_right {
            width: 650px;
            height: 319px;
            float: left;
            background-image: url(../Img/Pasture/cangku02.jpg);
        }

        #Layer1 {
            position: absolute;
            width: 39px;
            height: 17px;
            z-index: 1;
            left: 88px;
            top: 280px;
            background-image: url(../Img/Pasture/cangku04.jpg);
        }

        .task_dh {
            width: 650px;
            height: 29px;
            float: left;
        }

        .task_dh01 {
            width: 90px;
            height: 29px;
            float: left;
            margin-left: 10px;
        }

        .task_dh02 {
            width: 300px;
            height: 20px;
            float: left;
            margin: 8px 0 0 100px;
        }

        .cion {
            height: 18px;
            float: left;
            padding-left: 10px;
        }

        .cion01 {
            height: 16px;
            float: left;
            padding: 5px 0 0 5px;
        }

        ol, ul {
            list-style: none;
        }

        .box01 {
            width: 292px;
            float: left;
            padding-left: 14px;
        }

        .box02 {
            width: 292px;
            float: left;
            height: 28px;
        }

        .box03 {
            width: 290px;
            float: left;
            border: #D9BD7A 1px solid;
            height: 235px;
            background-color: #FFF;
        }

        .box04 {
            width: 292px;
            float: left;
            height: 20px;
            color: #B06A01;
            padding-top: 4px;
        }

        .box06 {
            width: 280px;
            float: left;
            border: #D9BD7A 1px solid;
            height: 225px;
            background-image: url(../Img/Pasture/muchang_16.jpg);
            padding: 5px;
            line-height: 20px;
            overflow-x: hidden;
            overflow-y: auto;
            ovscrollbar-face-color: #E1D395;
            scrollbar-highlight-color: #ffffff;
            scrollbar-3dlight-color: #E1D395;
            scrollbar-shadow-color: #ffffff;
            scrollbar-darkshadow-color: #F3EDC9;
            scrollbar-track-color: #F3EDC9;
            scrollbar-arrow-color: #ffffff;
        }

        .bb01 {
          
		  
            padding-top: 3px;
        }

        .bb02 {
           
          
            padding-top: 8px;
        }
		.bb01 img{
			width:70px;}

        .leaft {
            margin: 3px 0 0 5px;
            background: url(../Img/Pasture/i_bnt.gif) repeat scroll 0 -68px;
            width: 39px;
            height: 17px;
            border: 0 none;
            cursor: pointer;
        }

        .task_nav {
            width: 640px;
            height: 29px;
        }

            .task_nav li {
                float: left;
                height: 29px;
            }

                .task_nav li a {
                    width: 89px;
                    display: block;
                }

        .a01 {
            background: url(../Img/Pasture/muchang_06.jpg) no-repeat;
            height: 29px;
        }

        .task_nav li.on .a01 {
            background: url(../Img/Pasture/muchang_06.jpg) no-repeat 0 -29px;
            height: 29px;
        }

        .a02 {
            background: url(../Img/Pasture/muchang_06.jpg) no-repeat -90px 0;
            height: 29px;
        }

        .task_nav li.on .a02 {
            background: url(../Img/Pasture/muchang_06.jpg) no-repeat -90px -29px;
            height: 29px;
        }


        .dt_item h1 {
            background: url(../Img/Pasture/mctitle01.gif) no-repeat;
            height: 19px;
            font-size: 0;
            margin: 0 0 5px 0;
        }

        .dt_item h2 {
            background: url(../Img/Pasture/shbaby.gif) no-repeat;
            height: 19px;
            font-size: 0;
            margin: 0 0 5px 0;
        }

        .dt_item h3 {
            color: #bf7d1a;
            font-size: 12px;
            font-weight: normal;
            line-height: 24px;
            background: #fff;
            padding-left: 5px;
            border-bottom: #bf7d1a 1px solid;
        }

            .dt_item h3 span {
                float: right;
                padding-right: 10px;
            }

        .dt_task {
            width: 640px;
            height: 290px;
            float: left;
        }

        .dt_list {
            overflow-x: hidden;
            overflow-y: scroll;
            background: #fff;
            height: 210px;
            width: 290px;
            scrollbar-face-color: #E1D395;
            scrollbar-highlight-color: #ffffff;
            scrollbar-3dlight-color: #E1D395;
            scrollbar-shadow-color: #ffffff;
            scrollbar-darkshadow-color: #F3EDC9;
            scrollbar-track-color: #F3EDC9;
            scrollbar-arrow-color: #ffffff;
            color: #BF7D1A;
            border-top-width: 1px;
            border-right-width: 0px;
            border-bottom-width: 0px;
            border-left-width: 0px;
            border-top-style: solid;
            border-right-style: none;
            border-bottom-style: none;
            border-left-style: none;
            border-top-color: #D9BD7A;
        }

            .dt_list li {
                line-height: 24px;
                height: 24px;
                overflow: hidden;
            }

                .dt_list li a {
                    overflow: hidden;
                    padding-left: 5px;
                }

                .dt_list li span {
                    padding-right: 5px;
                    float: right;
                    color: #060;
                }

        .petab {
            width: 280px;
            margin-left: auto;
            margin-right: auto;
            margin-top: 5px;
            border-collapse: collapse;
        }

        .ch01 {
            background: url(../Img/Pasture/zz.gif) no-repeat center center;
            height: 50px;
        }

        .ch02 {
            height: 70px;
        }

        .pet {
            width: 70px;
            margin-left: auto;
            margin-right: auto;
        }

        .part_con {
            text-indent: 2em;
            color: #bf7d1a;
            font-size: 12px;
            line-height: 20px;
        }

        .plus {
            clear: both;
            height: 24px;
            line-height: 24px;
            text-align: right;
            color: #BF7D1A;
        }

            .plus a {
                color: #f00;
            }

        .con {
            display: none;
        }

        .answer {
            width: 615px;
            height: 250px;
            border: #b4a449 1px solid;
            background: #fff;
            margin-left: auto;
            margin-right: auto;
            float: left;
            margin: 10px 0 0 10px;
        }

            .answer p {
                line-height: 20px;
                color: #BF7D1A;
                font-size: 12px;
                padding: 15px 0 0 10px;
            }

            .answer strong {
                color: #bf7d1a;
            }

            .answer b {
                color: #333;
            }

        .tit01 {
            border-collapse: collapse;
            width: 100%;
            font-size: 12px;
        }

            .tit01 td {
                text-align: center;
       			
                color: #bf7d1a;
            }

        .conbtn {
            background: url(../Img/Pasture/bnt.gif) no-repeat;
            width: 39px;
            height: 17px;
            cursor: pointer;
            border: 0 none;
            margin: 0px 5px 0 5px;
        }

        .close {
            width: 35px;
            height: 35px;
            position: absolute;
            right: 0;
            top: 5px;
            cursor: pointer;
        }

        .tgtab {
            width: 580px;
            margin-left: auto;
            margin-right: auto;
            border-collapse: collapse;
            color: #BF7D1A;
        }

            .tgtab td {
                text-align: center;
            }

            .tgtab tr {
                line-height: 30px;
            }