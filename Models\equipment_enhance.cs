﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///装备强化表
    ///</summary>
    [SugarTable("equipment_enhance")]
    public partial class equipment_enhance
    {
           public equipment_enhance(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:用户ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:装备ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string equipment_id {get;set;}

           /// <summary>
           /// Desc:强化等级
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? enhance_level {get;set;}

           /// <summary>
           /// Desc:强化经验
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? enhance_exp {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? update_time {get;set;}

    }
}
