﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///地图配置表
    ///</summary>
    [SugarTable("map_config")]
    public partial class map_config
    {
           public map_config(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:地图ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int map_id {get;set;}

           /// <summary>
           /// Desc:地图名称
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string map_name {get;set;}

           /// <summary>
           /// Desc:地图描述
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? map_desc {get;set;}

           /// <summary>
           /// Desc:地图大小
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? map_size {get;set;}

           /// <summary>
           /// Desc:地图类型
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? map_type {get;set;}

           /// <summary>
           /// Desc:图集名称
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? atlast_name {get;set;}

           /// <summary>
           /// Desc:地图背景图片
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? background {get;set;}

           /// <summary>
           /// Desc:地图背景音乐
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? bgm {get;set;}

           /// <summary>
           /// Desc:背景音乐是否循环
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? bgm_loop {get;set;}

           /// <summary>
           /// Desc:背景音乐音量
           /// Default:1.00
           /// Nullable:True
           /// </summary>           
           public decimal? bgm_volume {get;set;}

           /// <summary>
           /// Desc:是否播放背景音乐
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? bgm_play {get;set;}

           /// <summary>
           /// Desc:是否静音
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? bgm_mute {get;set;}

           /// <summary>
           /// Desc:是否暂停
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? bgm_pause {get;set;}

           /// <summary>
           /// Desc:地图图标
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ico {get;set;}

           /// <summary>
           /// Desc:地图类型
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? type {get;set;}

    }
}
