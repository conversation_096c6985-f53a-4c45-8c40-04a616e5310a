/**
 * 数据缓存管理器
 * 提供智能缓存机制，减少重复API调用，提升页面响应速度
 */

class CacheManager {
    constructor() {
        this.cache = new Map();
        this.cacheExpiry = new Map();
        this.defaultTTL = 5 * 60 * 1000; // 默认5分钟过期
        this.maxCacheSize = 100; // 最大缓存条目数
        
        // 不同类型数据的缓存时间配置
        this.ttlConfig = {
            'pet-info': 3 * 60 * 1000,      // 宠物信息：3分钟
            'pet-skills': 5 * 60 * 1000,    // 宠物技能：5分钟
            'pet-equipment': 2 * 60 * 1000, // 宠物装备：2分钟
            'player-info': 10 * 60 * 1000,  // 玩家信息：10分钟
            'skill-configs': 30 * 60 * 1000 // 技能配置：30分钟
        };
        
        this.init();
    }

    init() {
        // 定期清理过期缓存
        setInterval(() => this.cleanExpiredCache(), 60 * 1000); // 每分钟清理一次
        
        // 监听页面卸载，清理缓存
        window.addEventListener('beforeunload', () => this.clear());
        
        console.log('缓存管理器已初始化');
    }

    /**
     * 生成缓存键
     */
    generateKey(type, params = {}) {
        const paramStr = Object.keys(params)
            .sort()
            .map(key => `${key}:${params[key]}`)
            .join('|');
        return `${type}${paramStr ? `_${paramStr}` : ''}`;
    }

    /**
     * 设置缓存
     */
    set(key, data, ttl = null) {
        try {
            // 如果缓存已满，删除最旧的条目
            if (this.cache.size >= this.maxCacheSize) {
                const oldestKey = this.cache.keys().next().value;
                this.delete(oldestKey);
            }

            const expiryTime = Date.now() + (ttl || this.defaultTTL);
            
            this.cache.set(key, {
                data: JSON.parse(JSON.stringify(data)), // 深拷贝
                timestamp: Date.now(),
                size: JSON.stringify(data).length
            });
            
            this.cacheExpiry.set(key, expiryTime);
            
            console.log(`缓存已设置: ${key}, 过期时间: ${new Date(expiryTime).toLocaleTimeString()}`);
        } catch (error) {
            console.error('设置缓存失败:', error);
        }
    }

    /**
     * 获取缓存
     */
    get(key) {
        try {
            if (!this.cache.has(key)) {
                return null;
            }

            const expiryTime = this.cacheExpiry.get(key);
            if (Date.now() > expiryTime) {
                this.delete(key);
                console.log(`缓存已过期: ${key}`);
                return null;
            }

            const cached = this.cache.get(key);
            console.log(`缓存命中: ${key}`);
            return JSON.parse(JSON.stringify(cached.data)); // 返回深拷贝
        } catch (error) {
            console.error('获取缓存失败:', error);
            return null;
        }
    }

    /**
     * 删除缓存
     */
    delete(key) {
        this.cache.delete(key);
        this.cacheExpiry.delete(key);
        console.log(`缓存已删除: ${key}`);
    }

    /**
     * 清理过期缓存
     */
    cleanExpiredCache() {
        const now = Date.now();
        let cleanedCount = 0;

        for (const [key, expiryTime] of this.cacheExpiry.entries()) {
            if (now > expiryTime) {
                this.delete(key);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            console.log(`清理了 ${cleanedCount} 个过期缓存条目`);
        }
    }

    /**
     * 清空所有缓存
     */
    clear() {
        this.cache.clear();
        this.cacheExpiry.clear();
        console.log('所有缓存已清空');
    }

    /**
     * 使指定类型的缓存失效
     */
    invalidateByType(type) {
        let invalidatedCount = 0;
        
        for (const key of this.cache.keys()) {
            if (key.startsWith(type)) {
                this.delete(key);
                invalidatedCount++;
            }
        }
        
        if (invalidatedCount > 0) {
            console.log(`使 ${invalidatedCount} 个 ${type} 类型的缓存失效`);
        }
    }

    /**
     * 包装API调用，自动处理缓存
     */
    async wrapApiCall(cacheKey, apiCall, ttl = null) {
        // 尝试从缓存获取
        const cached = this.get(cacheKey);
        if (cached !== null) {
            return cached;
        }

        try {
            // 调用API
            const result = await apiCall();
            
            // 只缓存成功的结果
            if (result && (result.success !== false)) {
                this.set(cacheKey, result, ttl);
            }
            
            return result;
        } catch (error) {
            console.error('API调用失败:', error);
            throw error;
        }
    }

    /**
     * 获取缓存统计信息
     */
    getStats() {
        const totalSize = Array.from(this.cache.values())
            .reduce((sum, item) => sum + item.size, 0);
            
        return {
            count: this.cache.size,
            totalSize: totalSize,
            averageSize: this.cache.size > 0 ? Math.round(totalSize / this.cache.size) : 0,
            keys: Array.from(this.cache.keys())
        };
    }

    /**
     * 便捷方法：缓存宠物信息
     */
    cachePetInfo(userId, data) {
        const key = this.generateKey('pet-info', { userId });
        this.set(key, data, this.ttlConfig['pet-info']);
    }

    /**
     * 便捷方法：获取缓存的宠物信息
     */
    getCachedPetInfo(userId) {
        const key = this.generateKey('pet-info', { userId });
        return this.get(key);
    }

    /**
     * 便捷方法：缓存宠物技能
     */
    cachePetSkills(petId, data) {
        const key = this.generateKey('pet-skills', { petId });
        this.set(key, data, this.ttlConfig['pet-skills']);
    }

    /**
     * 便捷方法：获取缓存的宠物技能
     */
    getCachedPetSkills(petId) {
        const key = this.generateKey('pet-skills', { petId });
        return this.get(key);
    }

    /**
     * 便捷方法：缓存宠物装备
     */
    cachePetEquipment(userId, petId, data) {
        const key = this.generateKey('pet-equipment', { userId, petId });
        this.set(key, data, this.ttlConfig['pet-equipment']);
    }

    /**
     * 便捷方法：获取缓存的宠物装备
     */
    getCachedPetEquipment(userId, petId) {
        const key = this.generateKey('pet-equipment', { userId, petId });
        return this.get(key);
    }

    /**
     * 便捷方法：使宠物相关缓存失效
     */
    invalidatePetCache(petId = null) {
        if (petId) {
            this.invalidateByType(`pet-skills_petId:${petId}`);
            this.invalidateByType(`pet-equipment_`);
        } else {
            this.invalidateByType('pet-');
        }
    }
}

// 创建全局实例
window.cacheManager = new CacheManager();

console.log('数据缓存管理器已初始化');
