// 主战斗应用组件
const { onMounted, watch } = Vue

// 动态导入依赖
let useBattleStore, BattleArena, DamageDisplay

// 异步加载依赖
const loadDependencies = async () => {
    const battleStoreModule = await import('../stores/battle.js')
    useBattleStore = battleStoreModule.useBattleStore

    const battleArenaModule = await import('./BattleArena.js')
    BattleArena = battleArenaModule.default

    const damageDisplayModule = await import('./DamageDisplay.js')
    DamageDisplay = damageDisplayModule.default
}

export default {
    name: 'BattleApp',
    components: {
        BattleArena,
        DamageDisplay
    },
    setup() {
        // 状态管理
        const battleStore = useBattleStore()
        
        // 生命周期
        onMounted(async () => {
            console.log('BattleApp组件已挂载')
            await battleStore.initializeBattle()
            
            // 模拟开始第一回合
            setTimeout(() => {
                battleStore.playNextRound()
            }, 1000)
        })
        
        // 方法
        const handleAnimationComplete = () => {
            console.log('动画完成')
            // 可以在这里处理下一回合
        }
        
        const handlePlayerPetClick = () => {
            console.log('点击玩家宠物')
            // 可以显示宠物信息或技能面板
        }
        
        const handleMonsterClick = () => {
            console.log('点击怪物')
            // 可以进行攻击
            battleStore.playNextRound()
        }
        
        return {
            battleStore,
            handleAnimationComplete,
            handlePlayerPetClick,
            handleMonsterClick
        }
    },
    template: `
        <div class="battle-app">
            <!-- 战斗场景 -->
            <BattleArena 
                :battle-state="battleStore"
                @animation-complete="handleAnimationComplete"
                @player-pet-click="handlePlayerPetClick"
                @monster-click="handleMonsterClick"
            />
            
            <!-- 伤害显示 -->
            <DamageDisplay 
                :damage-info="battleStore.currentDamage"
                :visible="battleStore.showDamage"
            />
            
            <!-- 战斗状态显示 -->
            <div v-if="battleStore.battleStatus === 'ended'" class="battle-result">
                <h2>战斗结束</h2>
                <p>{{ battleStore.cwHp <= 0 ? '失败' : '胜利' }}</p>
            </div>
            
            <!-- 调试信息 -->
            <div class="debug-info" v-if="true">
                <p>战斗状态: {{ battleStore.battleStatus }}</p>
                <p>当前回合: {{ battleStore.currentRound }}</p>
                <p>玩家HP: {{ battleStore.cwHp }}/{{ battleStore.cwMaxHp }}</p>
                <p>怪物HP: {{ battleStore.gwHp }}/{{ battleStore.gwMaxHp }}</p>
                <p>显示伤害: {{ battleStore.showDamage }}</p>
            </div>
        </div>
    `
}
