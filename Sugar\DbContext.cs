using SqlSugar;
using System.Linq.Expressions;

namespace WebApplication_HM.Sugar
{
    public class DbContext
    {
        public SqlSugarScope Db;
        public DbContext(IConfiguration configuration)
        {
            Db = new SqlSugarScope(new ConnectionConfig()
            {
                ConnectionString = configuration.GetConnectionString("DefaultConnection"),
                DbType = DbType.MySql,
                IsAutoCloseConnection = true
            },
            db =>
            {
                //执行SQL前事件
                db.Aop.OnLogExecuting = (sql, pars) =>
                {
                    try
                    {
                        Console.WriteLine($"SQL: {sql}");
                    }
                    catch
                    {
                        // 忽略日志错误，防止影响主要功能
                    }
                };
            });
        }

        /// <summary>
        /// 获取数据库处理对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public SimpleClient<T> GetEntityDB<T>() where T : class, new()
        {
            return new SimpleClient<T>(Db);
        }

        /// <summary>
        /// 获取一个自定义的数据库处理对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="db"></param>
        /// <returns></returns>
        public SimpleClient<T> GetEntityDB<T>(SqlSugarScope db) where T : class, new()
        {
            return new SimpleClient<T>(db);
        }
    }
}