using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using SqlSugar;
using WebApplication_HM.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 测试数据控制器
    /// 用于创建和管理测试数据
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class TestDataController : ControllerBase
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<TestDataController> _logger;

        public TestDataController(ISqlSugarClient db, ILogger<TestDataController> logger)
        {
            _db = db;
            _logger = logger;
        }

        /// <summary>
        /// 初始化牧场测试数据
        /// </summary>
        /// <returns>初始化结果</returns>
        [HttpPost("init-pasture-data")]
        public async Task<IActionResult> InitPastureData()
        {
            try
        {
                _logger.LogInformation("开始初始化牧场测试数据");

                // 1. 确保测试用户存在
                var existingUser = await _db.Queryable<user>().FirstAsync(u => u.id == 1);
                if (existingUser == null)
                {
                    var testUser = new user
                    {
                        id = 1,
                        username = "testuser",
                        password = "password123",
                        nickname = "测试用户",
                        money = "100000",
                        diamond = 5000,
                        crystal = 3000,
                        pasture_capacity = 80,
                        create_time = DateTime.Now
                    };
                    await _db.Insertable(testUser).ExecuteCommandAsync();
                    _logger.LogInformation("创建测试用户成功");
                }
                else
                {
                    // 更新用户的牧场容量
                    await _db.Updateable<user>()
                        .SetColumns(u => new user { pasture_capacity = 80 })
                        .Where(u => u.id == 1)
                        .ExecuteCommandAsync();
                    _logger.LogInformation("更新测试用户牧场容量");
                }

                // 2. 创建测试宠物配置
                var petConfigs = new List<pet_config>
                {
                    new pet_config { pet_no = 264, name = "小火龙", attribute = "火", image = "264", description = "火系宠物" },
                    new pet_config { pet_no = 265, name = "小水龟", attribute = "水", image = "265", description = "水系宠物" },
                    new pet_config { pet_no = 266, name = "小草精", attribute = "草", image = "266", description = "草系宠物" },
                    new pet_config { pet_no = 267, name = "小雷鸟", attribute = "雷", image = "267", description = "雷系宠物" },
                    new pet_config { pet_no = 268, name = "小土熊", attribute = "土", image = "268", description = "土系宠物" },
                    new pet_config { pet_no = 269, name = "风精灵", attribute = "风", image = "269", description = "风系宠物" },
                    new pet_config { pet_no = 270, name = "光天使", attribute = "光", image = "270", description = "光系宠物" },
                    new pet_config { pet_no = 271, name = "暗影兽", attribute = "暗", image = "271", description = "暗系宠物" }
                };

                foreach (var config in petConfigs)
                {
                    var existing = await _db.Queryable<pet_config>().FirstAsync(p => p.pet_no == config.pet_no);
                    if (existing == null)
                    {
                        await _db.Insertable(config).ExecuteCommandAsync();
                    }
                }
                _logger.LogInformation("创建宠物配置完成");

                // 3. 清理用户1的现有宠物数据
                await _db.Deleteable<user_pet>().Where(p => p.user_id == 1).ExecuteCommandAsync();
                _logger.LogInformation("清理现有宠物数据");

                // 4. 创建测试宠物数据（包含五系和神系宠物）
                var testPets = new List<user_pet>
                {
                    // 五系宠物（用于合成）
                    new user_pet { user_id = 1, pet_no = 264, custom_name = "金龙", level = 45, exp = 12000, growth = 25.5m, element = "金", hp = 150, mp = 80, status = "牧场", is_main = false, create_time = DateTime.Now },
                    new user_pet { user_id = 1, pet_no = 265, custom_name = "木精", level = 42, exp = 15000, growth = 28.2m, element = "木", hp = 180, mp = 100, status = "牧场", is_main = false, create_time = DateTime.Now },
                    new user_pet { user_id = 1, pet_no = 266, custom_name = "水龟", level = 48, exp = 8000, growth = 22.8m, element = "水", hp = 120, mp = 120, status = "牧场", is_main = false, create_time = DateTime.Now },
                    new user_pet { user_id = 1, pet_no = 267, custom_name = "火鸟", level = 50, exp = 18000, growth = 30.1m, element = "火", hp = 160, mp = 140, status = "牧场", is_main = false, create_time = DateTime.Now },
                    new user_pet { user_id = 1, pet_no = 268, custom_name = "土熊", level = 46, exp = 13000, growth = 26.7m, element = "土", hp = 200, mp = 60, status = "牧场", is_main = false, create_time = DateTime.Now },

                    // 神系宠物（用于涅槃）
                    new user_pet { user_id = 1, pet_no = 269, custom_name = "神龙", level = 65, exp = 25000, growth = 35.5m, element = "神", hp = 180, mp = 160, status = "携带", is_main = true, create_time = DateTime.Now },
                    new user_pet { user_id = 1, pet_no = 270, custom_name = "神圣天使", level = 62, exp = 20000, growth = 32.3m, element = "神圣", hp = 170, mp = 180, status = "携带", is_main = false, create_time = DateTime.Now },
                    new user_pet { user_id = 1, pet_no = 271, custom_name = "聖獸", level = 68, exp = 16000, growth = 29.8m, element = "聖", hp = 190, mp = 110, status = "携带", is_main = false, create_time = DateTime.Now },
                    new user_pet { user_id = 1, pet_no = 272, custom_name = "佛陀", level = 70, exp = 30000, growth = 40.0m, element = "佛", hp = 220, mp = 200, status = "牧场", is_main = false, create_time = DateTime.Now },
                    new user_pet { user_id = 1, pet_no = 273, custom_name = "魔王", level = 66, exp = 28000, growth = 38.5m, element = "魔", hp = 210, mp = 150, status = "牧场", is_main = false, create_time = DateTime.Now },
                    new user_pet { user_id = 1, pet_no = 274, custom_name = "人族战士", level = 64, exp = 26000, growth = 36.2m, element = "人", hp = 195, mp = 120, status = "牧场", is_main = false, create_time = DateTime.Now },
                    new user_pet { user_id = 1, pet_no = 275, custom_name = "鬼王", level = 63, exp = 24000, growth = 34.8m, element = "鬼", hp = 185, mp = 140, status = "牧场", is_main = false, create_time = DateTime.Now },
                    new user_pet { user_id = 1, pet_no = 276, custom_name = "巫师", level = 61, exp = 22000, growth = 33.5m, element = "巫", hp = 175, mp = 180, status = "牧场", is_main = false, create_time = DateTime.Now },
                    new user_pet { user_id = 1, pet_no = 277, custom_name = "萌宠", level = 60, exp = 20000, growth = 32.0m, element = "萌", hp = 165, mp = 160, status = "牧场", is_main = false, create_time = DateTime.Now },
                    new user_pet { user_id = 1, pet_no = 278, custom_name = "仙人", level = 72, exp = 35000, growth = 42.5m, element = "仙", hp = 230, mp = 220, status = "牧场", is_main = false, create_time = DateTime.Now },
                    new user_pet { user_id = 1, pet_no = 279, custom_name = "灵兽", level = 67, exp = 27000, growth = 37.8m, element = "灵", hp = 200, mp = 190, status = "牧场", is_main = false, create_time = DateTime.Now },
                    new user_pet { user_id = 1, pet_no = 280, custom_name = "次元龙", level = 69, exp = 32000, growth = 41.2m, element = "次元", hp = 215, mp = 210, status = "牧场", is_main = false, create_time = DateTime.Now }
                };

                await _db.Insertable(testPets).ExecuteCommandAsync();
                _logger.LogInformation("创建测试宠物数据完成");

                // 5. 验证数据
                var pastureCount = await _db.Queryable<user_pet>()
                    .Where(p => p.user_id == 1 && p.status == "牧场")
                    .CountAsync();
                
                var carryCount = await _db.Queryable<user_pet>()
                    .Where(p => p.user_id == 1 && p.status == "携带")
                    .CountAsync();

                var mainPetCount = await _db.Queryable<user_pet>()
                    .Where(p => p.user_id == 1 && p.is_main == true)
                    .CountAsync();

                return Ok(new
                {
                    success = true,
                    message = "牧场测试数据初始化成功",
                    data = new
                    {
                        userId = 1,
                        pastureCount = pastureCount,
                        carryCount = carryCount,
                        mainPetCount = mainPetCount,
                        totalPets = pastureCount + carryCount
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化牧场测试数据失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "初始化失败: " + ex.Message
                });
            }
        }

        /// <summary>
        /// 获取测试数据状态
        /// </summary>
        /// <returns>测试数据状态</returns>
        [HttpGet("pasture-data-status")]
        public async Task<IActionResult> GetPastureDataStatus()
        {
            try
            {
                var user = await _db.Queryable<user>().FirstAsync(u => u.id == 1);
                
                var pastureCount = await _db.Queryable<user_pet>()
                    .Where(p => p.user_id == 1 && p.status == "牧场")
                    .CountAsync();
                
                var carryCount = await _db.Queryable<user_pet>()
                    .Where(p => p.user_id == 1 && p.status == "携带")
                    .CountAsync();

                var mainPetCount = await _db.Queryable<user_pet>()
                    .Where(p => p.user_id == 1 && p.is_main == true)
                    .CountAsync();

                var pets = await _db.Queryable<user_pet>()
                    .Where(p => p.user_id == 1)
                    .Select(p => new { p.id, p.custom_name, p.level, p.element, p.status, p.is_main })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    data = new
                    {
                        userExists = user != null,
                        userId = user?.id,
                        nickname = user?.nickname,
                        pastureCapacity = user?.pasture_capacity,
                        pastureCount = pastureCount,
                        carryCount = carryCount,
                        mainPetCount = mainPetCount,
                        totalPets = pastureCount + carryCount,
                        pets = pets
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取测试数据状态失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取状态失败: " + ex.Message
                });
            }
        }

        /// <summary>
        /// 清理测试数据
        /// </summary>
        /// <returns>清理结果</returns>
        [HttpDelete("cleanup-pasture-data")]
        public async Task<IActionResult> CleanupPastureData()
        {
            try
            {
                _logger.LogInformation("开始清理牧场测试数据");

                // 删除测试宠物
                var deletedPets = await _db.Deleteable<user_pet>().Where(p => p.user_id == 1).ExecuteCommandAsync();
                
                _logger.LogInformation($"清理完成，删除了 {deletedPets} 只宠物");

                return Ok(new
                {
                    success = true,
                    message = $"清理完成，删除了 {deletedPets} 只宠物"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理牧场测试数据失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "清理失败: " + ex.Message
                });
            }
        }
    }
}
