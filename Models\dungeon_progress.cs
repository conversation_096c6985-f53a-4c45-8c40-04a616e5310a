﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///副本进度表
    ///</summary>
    [SugarTable("dungeon_progress")]
    public partial class dungeon_progress
    {
           public dungeon_progress(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:用户ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:副本ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string dungeon_id {get;set;}

           /// <summary>
           /// Desc:副本进度
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? progress {get;set;}

           /// <summary>
           /// Desc:重置次数
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? reset_count {get;set;}

           /// <summary>
           /// Desc:最后重置时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? last_reset_time {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? update_time {get;set;}

    }
}
