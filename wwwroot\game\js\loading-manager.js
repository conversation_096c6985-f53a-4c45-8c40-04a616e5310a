/**
 * 加载状态管理器
 * 提供统一的加载状态指示和用户反馈
 */

class LoadingManager {
    constructor() {
        this.activeRequests = new Set();
        // 可抑制的加载提示关键字集合（包含即抑制显示）
        this.suppressedMessages = new Set();
        this.init();
    }

    init() {
        // 创建加载遮罩层
        this.createLoadingOverlay();
        // 创建加载指示器
        this.createLoadingIndicator();
        // 创建消息提示容器
        this.createMessageContainer();
    }

    createLoadingOverlay() {
        const overlay = document.createElement('div');
        overlay.id = 'loading-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 99999999;
            display: none;
            justify-content: center;
            align-items: center;
        `;

        // 确保 document.body 存在
        if (document.body) {
            document.body.appendChild(overlay);
        } else {
            // 如果 body 还没有加载，等待 DOM 加载完成
            document.addEventListener('DOMContentLoaded', () => {
                document.body.appendChild(overlay);
            });
        }
        this.overlay = overlay;
    }

    createLoadingIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'loading-indicator';
        indicator.innerHTML = `
            <div style="
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                text-align: center;
                min-width: 200px;
            ">
                <div class="spinner" style="
                    width: 40px;
                    height: 40px;
                    border: 4px solid #f3f3f3;
                    border-top: 4px solid #3498db;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 15px;
                "></div>
                <div id="loading-text" style="
                    color: #333;
                    font-size: 14px;
                    font-weight: 500;
                ">正在加载...</div>
            </div>
            <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `;
        this.overlay.appendChild(indicator);
        this.indicator = indicator;
    }

    createMessageContainer() {
        const container = document.createElement('div');
        container.id = 'message-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 999999999;
            max-width: 350px;
        `;

        // 确保 document.body 存在
        if (document.body) {
            document.body.appendChild(container);
        } else {
            // 如果 body 还没有加载，等待 DOM 加载完成
            document.addEventListener('DOMContentLoaded', () => {
                document.body.appendChild(container);
            });
        }
        this.messageContainer = container;
    }

    // 显示加载状态
    showLoading(message = '正在加载...', requestId = null) {
        // 如果配置了抑制关键字且命中，则不显示加载遮罩
        if (this.suppressedMessages && this.suppressedMessages.size > 0) {
            for (const key of this.suppressedMessages) {
                if (message && message.indexOf(key) !== -1) {
                    return; // 直接忽略此次显示
                }
            }
        }

        if (requestId) {
            this.activeRequests.add(requestId);
        }

        document.getElementById('loading-text').textContent = message;
        this.overlay.style.display = 'flex';
    }

    // 隐藏加载状态
    hideLoading(requestId = null) {
        if (requestId) {
            this.activeRequests.delete(requestId);
            // 如果还有其他请求在进行，不隐藏加载状态
            if (this.activeRequests.size > 0) {
                return;
            }
        }
        
        this.overlay.style.display = 'none';
    }

    // 显示消息提示
    showMessage(message, type = 'info', duration = 3000) {
        const messageEl = document.createElement('div');
        const colors = {
            success: { bg: '#d4edda', border: '#c3e6cb', text: '#155724' },
            error: { bg: '#f8d7da', border: '#f5c6cb', text: '#721c24' },
            warning: { bg: '#fff3cd', border: '#ffeaa7', text: '#856404' },
            info: { bg: '#d1ecf1', border: '#bee5eb', text: '#0c5460' }
        };
        
        const color = colors[type] || colors.info;
        
        messageEl.style.cssText = `
            background: ${color.bg};
            border: 1px solid ${color.border};
            color: ${color.text};
            padding: 12px 16px;
            margin-bottom: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            font-size: 14px;
            line-height: 1.4;
            animation: slideIn 0.3s ease-out;
            cursor: pointer;
        `;
        
        messageEl.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>${message}</span>
                <span style="margin-left: 10px; font-weight: bold; opacity: 0.7;">×</span>
            </div>
            <style>
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOut {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            </style>
        `;
        
        // 点击关闭
        messageEl.onclick = () => this.removeMessage(messageEl);
        
        this.messageContainer.appendChild(messageEl);
        
        // 自动关闭
        if (duration > 0) {
            setTimeout(() => this.removeMessage(messageEl), duration);
        }
        
        return messageEl;
    }

    removeMessage(messageEl) {
        if (messageEl && messageEl.parentNode) {
            messageEl.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 300);
        }
    }

    // 显示成功消息
    showSuccess(message, duration = 3000) {
        return this.showMessage(message, 'success', duration);
    }

    // 显示错误消息
    showError(message, duration = 5000) {
        return this.showMessage(message, 'error', duration);
    }

    // 显示警告消息
    showWarning(message, duration = 4000) {
        return this.showMessage(message, 'warning', duration);
    }

    // 显示信息消息
    showInfo(message, duration = 3000) {
        return this.showMessage(message, 'info', duration);
    }

    // 包装API调用，自动处理加载状态
    async wrapApiCall(apiCall, loadingMessage = '正在处理...', requestId = null) {
        const id = requestId || Date.now().toString();
        
        try {
            this.showLoading(loadingMessage, id);
            const result = await apiCall();
            return result;
        } catch (error) {
            console.error('API调用失败:', error);
            this.showError('操作失败，请重试');
            throw error;
        } finally {
            this.hideLoading(id);
        }
    }

    // 添加/移除抑制关键字的方法
    suppress(messageKeyword) {
        if (messageKeyword) this.suppressedMessages.add(messageKeyword);
    }
    unsuppress(messageKeyword) {
        if (messageKeyword) this.suppressedMessages.delete(messageKeyword);
    }
    clearSuppress() {
        this.suppressedMessages.clear();
    }
}

// 创建全局实例
window.loadingManager = new LoadingManager();

console.log('加载状态管理器已初始化');
