using System.ComponentModel.DataAnnotations;

namespace WebApplication_HM.DTOs.RequestDTO
{
    /// <summary>
    /// 游戏页面通用请求DTO
    /// </summary>
    public class GamePageRequestDTO
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public int UserId { get; set; }

        /// <summary>
        /// 页码（从0开始）
        /// </summary>
        public int Page { get; set; } = 0;

        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string SearchTerm { get; set; } = "";
    }

    /// <summary>
    /// 使用道具请求DTO（游戏页面专用）
    /// </summary>
    public class UseItemGameRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public int UserId { get; set; }

        /// <summary>
        /// 道具ID
        /// </summary>
        [Required(ErrorMessage = "道具ID不能为空")]
        public string ItemId { get; set; }

        /// <summary>
        /// 使用数量
        /// </summary>
        public int Quantity { get; set; } = 1;
    }

    /// <summary>
    /// 删除道具请求DTO（游戏页面专用）
    /// </summary>
    public class DeleteItemGameRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public int UserId { get; set; }

        /// <summary>
        /// 道具ID
        /// </summary>
        [Required(ErrorMessage = "道具ID不能为空")]
        public string ItemId { get; set; }

        /// <summary>
        /// 道具类型
        /// </summary>
        public string ItemType { get; set; } = "道具";
    }

    /// <summary>
    /// 移动道具到仓库请求DTO（游戏页面专用）
    /// </summary>
    public class MoveToDepotGameRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public int UserId { get; set; }

        /// <summary>
        /// 道具ID
        /// </summary>
        [Required(ErrorMessage = "道具ID不能为空")]
        public string ItemId { get; set; }
    }

    /// <summary>
    /// 每日礼包请求DTO
    /// </summary>
    public class DailyGiftRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public int UserId { get; set; }

        /// <summary>
        /// 礼包类型（1=每日礼包，2=限时福利）
        /// </summary>
        public int GiftType { get; set; } = 1;
    }

    /// <summary>
    /// 任务操作请求DTO
    /// </summary>
    public class TaskOperationRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public int UserId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        [Required(ErrorMessage = "任务ID不能为空")]
        public int TaskId { get; set; }

        /// <summary>
        /// 操作类型（get=接受，set=完成，delete=放弃）
        /// </summary>
        [Required(ErrorMessage = "操作类型不能为空")]
        public string Operation { get; set; }
    }
}
