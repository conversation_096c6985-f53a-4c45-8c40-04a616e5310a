using WebApplication_HM.DTOs;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;
using WebApplication_HM.Sugar;
using SqlSugar;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 宠物进化服务实现
    /// </summary>
    public class PetEvolutionService : IPetEvolutionService
    {
        private readonly DbContext _dbContext;
        private readonly ILogger<PetEvolutionService> _logger;
        private readonly ILevelService _levelService;
        private readonly IGameEventTriggerService _gameEventTrigger;
        private readonly Random _random;

        public PetEvolutionService(DbContext dbContext, ILogger<PetEvolutionService> logger, ILevelService levelService, IGameEventTriggerService gameEventTrigger)
        {
            _dbContext = dbContext;
            _logger = logger;
            _levelService = levelService;
            _gameEventTrigger = gameEventTrigger;
            _random = new Random();
        }

        /// <summary>
        /// 执行宠物进化
        /// </summary>
        public async Task<EvolutionResultDto> EvolvePetAsync(int userId, EvolutionRequestDto request)
        {
            try
            {
                _logger.LogInformation($"开始执行宠物进化 - 用户ID: {userId}, 宠物ID: {request.UserPetId}, 进化类型: {request.EvolutionType}");

                // 1. 验证宠物存在性和所有权
                var userPet = await ValidatePetOwnershipAsync(userId, request.UserPetId);
                if (userPet == null)
                {
                    return new EvolutionResultDto { Success = false, Message = "宠物不存在或不属于您！" };
                }

                // 2. 获取进化配置
                var evolutionConfig = await GetEvolutionConfigAsync(userPet.pet_no, request.EvolutionType);
                if (evolutionConfig == null)
                {
                    return new EvolutionResultDto { Success = false, Message = "进化配置不存在！" };
                }

                // 3. 验证进化条件
                var (isValid, errorMessage) = await ValidateEvolutionConditionsAsync(userId, userPet, evolutionConfig);
                if (!isValid)
                {
                    return new EvolutionResultDto { Success = false, Message = errorMessage };
                }

                // 4. 执行进化逻辑
                return await ExecuteEvolutionAsync(userId, userPet, evolutionConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"宠物进化异常: {ex.Message}");
                return new EvolutionResultDto { Success = false, Message = "进化失败，请稍后重试！" };
            }
        }

        /// <summary>
        /// 获取宠物进化信息
        /// </summary>
        public async Task<PetEvolutionInfoDto> GetPetEvolutionInfoAsync(int userId, int userPetId)
        {
            var userPet = await ValidatePetOwnershipAsync(userId, userPetId);
            if (userPet == null) return null;

            var petConfig = await _dbContext.Db.Queryable<pet_config>()
                .FirstAsync(x => x.pet_no == userPet.pet_no);

            var evolutionInfo = new PetEvolutionInfoDto
            {
                PetId = userPet.id,
                PetName = userPet.custom_name ?? petConfig?.name ?? "未知宠物",
                PetNo = userPet.pet_no,
                Level = await CalculateLevelAsync(userPet.exp ?? 0),
                Growth = userPet.growth ?? 0,
                EvolveCount = userPet.evolve_count ?? 0,
                Element = GetPetAttribute(userPet.pet_no)
            };

            // 获取可用的进化路线
            var configA = await GetEvolutionConfigAsync(userPet.pet_no, "A");
            var configB = await GetEvolutionConfigAsync(userPet.pet_no, "B");

            if (configA != null)
            {
                var canEvolveA = await ValidateEvolutionConditionsAsync(userId, userPet, configA);
                evolutionInfo.AvailableEvolutions.Add(await ConvertToEvolutionConfigDto(configA, canEvolveA.IsValid, canEvolveA.ErrorMessage));
            }

            if (configB != null)
            {
                var canEvolveB = await ValidateEvolutionConditionsAsync(userId, userPet, configB);
                evolutionInfo.AvailableEvolutions.Add(await ConvertToEvolutionConfigDto(configB, canEvolveB.IsValid, canEvolveB.ErrorMessage));
            }

            return evolutionInfo;
        }

        /// <summary>
        /// 获取进化配置
        /// </summary>
        public async Task<pet_evolution_config> GetEvolutionConfigAsync(int petNo, string evolutionType)
        {
            // 先查询数据库配置
            var config = await _dbContext.Db.Queryable<pet_evolution_config>()
                .FirstAsync(x => x.pet_no == petNo && x.evolution_type == evolutionType);

            if (config != null) return config;

            // 如果没有配置，生成默认配置
            return await GenerateDefaultConfigAsync(petNo, evolutionType);
        }

        /// <summary>
        /// 获取宠物进化历史
        /// </summary>
        public async Task<List<EvolutionHistoryDto>> GetEvolutionHistoryAsync(int userId, int userPetId)
        {
            var histories = await _dbContext.Db.Queryable<pet_evolution_log>()
                .Where(x => x.user_id == userId && x.user_pet_id == userPetId)
                .OrderBy(x => x.create_time, OrderByType.Desc)
                .ToListAsync();

            var result = new List<EvolutionHistoryDto>();
            foreach (var history in histories)
            {
                var itemName = "未知道具";
                if (!string.IsNullOrEmpty(history.used_item_id))
                {
                    var item = await _dbContext.Db.Queryable<item_config>()
                        .FirstAsync(x => x.item_no.ToString() == history.used_item_id);
                    if (item != null) itemName = item.name;
                }

                result.Add(new EvolutionHistoryDto
                {
                    Id = history.id,
                    EvolutionType = history.evolution_type,
                    BeforePetNo = history.before_pet_no,
                    AfterPetNo = history.after_pet_no,
                    GrowthIncrease = history.growth_increase,
                    CostGold = history.cost_gold,
                    UsedItemName = itemName,
                    IsSuccess = history.is_success,
                    CreateTime = history.create_time ?? DateTime.Now
                });
            }

            return result;
        }

        /// <summary>
        /// 验证进化条件
        /// </summary>
        public async Task<(bool IsValid, string ErrorMessage)> ValidateEvolutionConditionsAsync(int userId, user_pet userPet, pet_evolution_config config)
        {
            // 1. 验证进化次数
            if ((userPet.evolve_count ?? 0) >= 10)
            {
                return (false, "宠物已进化十次！无法继续进化");
            }

            // 2. 验证等级要求
            var currentLevel = await CalculateLevelAsync(userPet.exp ?? 0);
            if (currentLevel < config.required_level)
            {
                return (false, "进化等级不足！");
            }

            // 3. 验证金币
            var user = await _dbContext.Db.Queryable<user>().FirstAsync(x => x.id == userId);
            if (user.gold < config.cost_gold)
            {
                return (false, "金币不足！");
            }

            // 4. 验证道具
            var hasItem = await CheckUserHasItemAsync(userId, config.required_item_id, config.required_item_count ?? 1);
            if (!hasItem)
            {
                return (false, "进化道具不足！");
            }

            return (true, string.Empty);
        }

        /// <summary>
        /// 计算宠物等级（使用新的等级服务）
        /// </summary>
        public async Task<int> CalculateLevelAsync(long exp)
        {
            try
            {
                return await _levelService.CalculateLevelAsync(exp, "pet");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算宠物等级失败，经验值: {Exp}", exp);
                // 降级到简化计算
                if (exp < 1000) return 1;
                if (exp < 5000) return 10;
                if (exp < 20000) return 20;
                if (exp < 50000) return 30;
                if (exp < 100000) return 40;
                if (exp < 200000) return 50;
                if (exp < 400000) return 60;
                if (exp < 800000) return 70;
                if (exp < 1600000) return 80;
                if (exp < 3200000) return 90;
                return 100;
            }
        }

        /// <summary>
        /// 计算宠物等级（同步版本，用于兼容现有代码）
        /// </summary>
        public int CalculateLevel(long exp)
        {
            try
            {
                return CalculateLevelAsync(exp).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算宠物等级失败，经验值: {Exp}", exp);
                // 降级到简化计算
                if (exp < 1000) return 1;
                if (exp < 5000) return 10;
                if (exp < 20000) return 20;
                if (exp < 50000) return 30;
                if (exp < 100000) return 40;
                if (exp < 200000) return 50;
                if (exp < 400000) return 60;
                if (exp < 800000) return 70;
                if (exp < 1600000) return 80;
                if (exp < 3200000) return 90;
                return 100;
            }
        }

        /// <summary>
        /// 生成默认进化配置
        /// </summary>
        public async Task<pet_evolution_config> GenerateDefaultConfigAsync(int petNo, string evolutionType)
        {
            var petConfig = await _dbContext.Db.Queryable<pet_config>().FirstAsync(x => x.pet_no == petNo);
            if (petConfig == null) return null;

            var config = new pet_evolution_config
            {
                pet_no = petNo,
                evolution_type = evolutionType,
                target_pet_no = petNo, // 默认进化后还是同一个形象
                cost_gold = 1000,
                required_item_count = 1,
                success_rate = 100.00m
            };

            // 根据宠物属性设置默认值
            if (IsGodElement(petConfig.attribute))
            {
                config.required_level = 60;
                config.required_item_id = evolutionType == "A" ? "2016110545" : "2016110546";
                config.growth_min = evolutionType == "A" ? 0.100m : 0.300m;
                config.growth_max = evolutionType == "A" ? 0.300m : 0.600m;
            }
            else
            {
                config.required_level = 40;
                config.required_item_id = evolutionType == "A" ? "2016110512" : "2016110513";
                config.growth_min = evolutionType == "A" ? 0.100m : 0.500m;
                config.growth_max = evolutionType == "A" ? 0.500m : 1.000m;
            }

            return config;
        }

        /// <summary>
        /// 验证宠物所有权
        /// </summary>
        private async Task<user_pet> ValidatePetOwnershipAsync(int userId, int userPetId)
        {
            return await _dbContext.Db.Queryable<user_pet>()
                .FirstAsync(x => x.id == userPetId && x.user_id == userId);
        }

        /// <summary>
        /// 判断是否为神系宠物
        /// </summary>
        private bool IsGodElement(string attribute)
        {
            var godElements = new[] { "神", "神圣", "聖", "佛", "魔", "人", "鬼", "巫", "萌", "仙", "灵", "次元" };
            return godElements.Contains(attribute);
        }

        /// <summary>
        /// 检查用户是否拥有足够道具
        /// </summary>
        private async Task<bool> CheckUserHasItemAsync(int userId, string itemId, int requiredCount)
        {
            var userItem = await _dbContext.Db.Queryable<user_item>()
                .FirstAsync(x => x.user_id == userId && x.item_id == itemId);

            return userItem != null && userItem.item_count >= requiredCount;
        }

        /// <summary>
        /// 执行进化逻辑
        /// </summary>
        private async Task<EvolutionResultDto> ExecuteEvolutionAsync(int userId, user_pet userPet, pet_evolution_config config)
        {
            _dbContext.Db.Ado.BeginTran();
            try
            {
                // 1. 消耗金币
                await ConsumeGoldAsync(userId, config.cost_gold ?? 0);

                // 2. 消耗道具
                await ConsumeItemAsync(userId, config.required_item_id, config.required_item_count ?? 1);

                // 3. 计算成长增加
                var growthIncrease = CalculateGrowthIncrease(userPet, config);

                // 4. 更新宠物数据
                await UpdatePetDataAsync(userPet, config, growthIncrease);

                // 5. 记录进化日志
                await LogEvolutionAsync(userId, userPet, config, growthIncrease);

                _dbContext.Db.Ado.CommitTran();

                _logger.LogInformation($"宠物进化成功 - 宠物ID: {userPet.id}, 成长增加: {growthIncrease}");

                // 🎯 触发宠物进化任务事件
                try
                {
                    // 触发宠物进化事件（使用道具获得事件作为替代）
                    await _gameEventTrigger.OnItemObtainedAsync(userId, $"pet_{config.target_pet_no}", 1);
                    _logger.LogDebug("触发宠物进化事件成功: UserId={UserId}, PetNo={PetNo}", userId, config.target_pet_no);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "触发宠物进化事件失败: UserId={UserId}, PetNo={PetNo}", userId, config.target_pet_no);
                    // 不影响主流程，只记录错误
                }

                return new EvolutionResultDto
                {
                    Success = true,
                    Message = "恭喜您，宝贝进化成功！！",
                    GrowthIncrease = growthIncrease,
                    NewGrowth = (userPet.growth ?? 0) + growthIncrease,
                    BeforePetNo = userPet.pet_no,
                    AfterPetNo = config.target_pet_no,
                    CostGold = config.cost_gold ?? 0,
                    UsedItemId = config.required_item_id
                };
            }
            catch (Exception ex)
            {
                _dbContext.Db.Ado.RollbackTran();
                _logger.LogError(ex, $"进化事务回滚: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 消耗金币
        /// </summary>
        private async Task ConsumeGoldAsync(int userId, long goldAmount)
        {
            await _dbContext.Db.Updateable<user>()
                .SetColumns(x => new user { gold = x.gold - goldAmount })
                .Where(x => x.id == userId)
                .ExecuteCommandAsync();
        }

        /// <summary>
        /// 消耗道具
        /// </summary>
        private async Task ConsumeItemAsync(int userId, string itemId, int itemCount)
        {
            await _dbContext.Db.Updateable<user_item>()
                .SetColumns(x => new user_item { item_count = x.item_count - itemCount })
                .Where(x => x.user_id == userId && x.item_id == itemId)
                .ExecuteCommandAsync();
        }

        /// <summary>
        /// 计算成长增加值
        /// </summary>
        private decimal CalculateGrowthIncrease(user_pet userPet, pet_evolution_config config)
        {
            // 获取效果系数 (默认为1.0)
            var effectMultiplier = 1.0m;

            // 生成随机成长增加值
            var minGrowth = (double)config.growth_min;
            var maxGrowth = (double)config.growth_max;
            var randomValue = _random.NextDouble() * (maxGrowth - minGrowth) + minGrowth;

            // 应用效果系数
            var finalGrowthIncrease = (decimal)randomValue * effectMultiplier;

            return Math.Round(finalGrowthIncrease, 6);
        }

        /// <summary>
        /// 更新宠物数据
        /// </summary>
        private async Task UpdatePetDataAsync(user_pet userPet, pet_evolution_config config, decimal growthIncrease)
        {
            await _dbContext.Db.Updateable<user_pet>()
                .SetColumns(x => new user_pet
                {
                    growth = x.growth + growthIncrease,
                    evolve_count = x.evolve_count + 1,
                    image = config.target_pet_no
                })
                .Where(x => x.id == userPet.id)
                .ExecuteCommandAsync();
        }

        /// <summary>
        /// 记录进化日志
        /// </summary>
        private async Task LogEvolutionAsync(int userId, user_pet userPet, pet_evolution_config config, decimal growthIncrease)
        {
            var log = new pet_evolution_log
            {
                user_id = userId,
                user_pet_id = userPet.id,
                evolution_type = config.evolution_type,
                before_pet_no = userPet.pet_no,
                after_pet_no = config.target_pet_no,
                before_growth = userPet.growth ?? 0,
                after_growth = (userPet.growth ?? 0) + growthIncrease,
                growth_increase = growthIncrease,
                used_item_id = config.required_item_id,
                cost_gold = config.cost_gold ?? 0,
                is_success = true,
                create_time = DateTime.Now
            };

            await _dbContext.Db.Insertable(log).ExecuteCommandAsync();
        }

        /// <summary>
        /// 转换为进化配置DTO
        /// </summary>
        private async Task<EvolutionConfigDto> ConvertToEvolutionConfigDto(pet_evolution_config config, bool canEvolve, string reason)
        {
            var itemName = "未知道具";
            if (!string.IsNullOrEmpty(config.required_item_id))
            {
                var item = await _dbContext.Db.Queryable<item_config>()
                    .FirstAsync(x => x.item_no.ToString() == config.required_item_id);
                if (item != null) itemName = item.name;
            }

            return new EvolutionConfigDto
            {
                EvolutionType = config.evolution_type,
                EvolutionTypeName = config.evolution_type == "A" ? "A路线" : "B路线",
                TargetPetNo = config.target_pet_no,
                RequiredLevel = config.required_level,
                CostGold = config.cost_gold ?? 0,
                RequiredItemId = config.required_item_id,
                ItemName = itemName,
                RequiredItemCount = config.required_item_count ?? 1,
                GrowthMin = config.growth_min ?? 0,
                GrowthMax = config.growth_max ?? 0,
                SuccessRate = config.success_rate ?? 0,
                CanEvolve = canEvolve,
                CannotEvolveReason = reason
            };
        }

        /// <summary>
        /// 获取宠物的五行属性（通过pet_no关联pet_config表）
        /// </summary>
        /// <param name="petNo">宠物编号</param>
        /// <returns>五行属性</returns>
        private string GetPetAttribute(int petNo)
        {
            try
            {
                var petConfig = _dbContext.Db.Queryable<pet_config>().Where(x => x.pet_no == petNo).First();
                return petConfig?.attribute ?? "无";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物属性失败，宠物编号: {PetNo}", petNo);
                return "无";
            }
        }
    }
}
