using System.ComponentModel.DataAnnotations;

namespace WebApplication_HM.DTOs.RequestDTO
{
    /// <summary>
    /// 捕捉宠物请求DTO
    /// </summary>
    public class CaptureRequestDTO
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public int UserId { get; set; }

        /// <summary>
        /// 地图ID
        /// </summary>
        [Required(ErrorMessage = "地图ID不能为空")]
        public int MapId { get; set; }

        /// <summary>
        /// 怪物ID
        /// </summary>
        [Required(ErrorMessage = "怪物ID不能为空")]
        public int MonsterId { get; set; }
    }
}
