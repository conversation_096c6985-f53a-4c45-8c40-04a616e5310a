using SqlSugar;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;

namespace WebApplication_HM.Services.TaskHandlers
{
    /// <summary>
    /// 任务处理器基类
    /// </summary>
    public abstract class BaseTaskHandler : ITaskProgressHandler
    {
        protected readonly ISqlSugarClient _db;
        protected readonly ILogger _logger;

        protected BaseTaskHandler(ISqlSugarClient db, ILogger logger)
        {
            _db = db;
            _logger = logger;
        }

        /// <summary>
        /// 支持的目标类型
        /// </summary>
        public abstract string SupportedObjectiveType { get; }

        /// <summary>
        /// 检查进度
        /// </summary>
        public abstract Task<int> CheckProgressAsync(int userId, task_objective objective);

        /// <summary>
        /// 更新进度
        /// </summary>
        public abstract Task<int> UpdateProgressAsync(int userId, task_objective objective, int amount);

        /// <summary>
        /// 验证目标参数
        /// </summary>
        protected virtual bool ValidateObjective(task_objective objective)
        {
            if (objective == null)
            {
                _logger.LogWarning("任务目标为空");
                return false;
            }

            if (objective.objective_type != SupportedObjectiveType)
            {
                _logger.LogWarning("任务目标类型不匹配: 期望 {Expected}, 实际 {Actual}", 
                    SupportedObjectiveType, objective.objective_type);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 获取用户任务进度
        /// </summary>
        protected async Task<user_task_progress> GetUserTaskProgressAsync(int userTaskId, int objectiveId)
        {
            try
            {
                return await _db.Queryable<user_task_progress>()
                    .Where(utp => utp.user_task_id == userTaskId && utp.objective_id == objectiveId)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户任务进度失败: UserTaskId={UserTaskId}, ObjectiveId={ObjectiveId}", 
                    userTaskId, objectiveId);
                return null;
            }
        }

        /// <summary>
        /// 更新用户任务进度
        /// </summary>
        protected async Task<bool> UpdateUserTaskProgressAsync(user_task_progress progress)
        {
            try
            {
                progress.updated_at = DateTime.Now;
                var result = await _db.Updateable(progress).ExecuteCommandAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户任务进度失败: ProgressId={ProgressId}", progress.progress_id);
                return false;
            }
        }

        /// <summary>
        /// 检查进度是否达到目标
        /// </summary>
        protected bool IsProgressCompleted(int currentAmount, int targetAmount)
        {
            return currentAmount >= targetAmount;
        }

        /// <summary>
        /// 安全地增加进度值
        /// </summary>
        protected int SafeAddProgress(int currentAmount, int addAmount, int maxAmount)
        {
            return Math.Min(currentAmount + addAmount, maxAmount);
        }

        /// <summary>
        /// 记录处理器日志
        /// </summary>
        protected void LogHandlerAction(string action, int userId, task_objective objective, object additionalData = null)
        {
            _logger.LogInformation("{Handler} - {Action}: UserId={UserId}, ObjectiveId={ObjectiveId}, TargetId={TargetId}, TargetAmount={TargetAmount}, Data={Data}",
                GetType().Name, action, userId, objective.objective_id, objective.target_id, objective.target_amount, additionalData);
        }
    }
}
