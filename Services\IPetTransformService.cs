using WebApplication_HM.DTOs;
using WebApplication_HM.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 百变宠物服务接口
    /// </summary>
    public interface IPetTransformService
    {
        /// <summary>
        /// 获取可用于百变的宠物列表
        /// </summary>
        /// <returns>可变换宠物列表</returns>
        Task<List<TransformablePetDto>> GetTransformablePetsAsync();

        /// <summary>
        /// 获取神宠列表
        /// </summary>
        /// <returns>神宠列表</returns>
        Task<List<TransformablePetDto>> GetGodPetsAsync();

        /// <summary>
        /// 获取神圣宠物列表
        /// </summary>
        /// <returns>神圣宠物列表</returns>
        Task<List<TransformablePetDto>> GetHolyPetsAsync();

        /// <summary>
        /// 随机生成神宠
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>变换结果</returns>
        Task<PetTransformResultDto> RandomGenerateGodPetAsync(RandomGodPetRequestDto request);

        /// <summary>
        /// 百变宠物变换
        /// </summary>
        /// <param name="request">变换请求</param>
        /// <returns>变换结果</returns>
        Task<PetTransformResultDto> TransformPetAsync(PetTransformRequestDto request);

        /// <summary>
        /// 验证宠物是否可用于百变
        /// </summary>
        /// <param name="petNo">宠物编号</param>
        /// <returns>是否可变换</returns>
        Task<bool> IsTransformablePetAsync(int petNo);

        /// <summary>
        /// 获取百变宠物选择界面数据
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>选择界面数据</returns>
        Task<PetTransformSelectionDto> GetTransformSelectionDataAsync(int userId);

        /// <summary>
        /// 获取用户百变宠物统计
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>统计数据</returns>
        Task<PetTransformStatsDto> GetUserTransformStatsAsync(int userId);

        /// <summary>
        /// 获取百变宠物配置
        /// </summary>
        /// <param name="transformType">变换类型</param>
        /// <returns>配置信息</returns>
        Task<PetTransformConfigDto?> GetTransformConfigAsync(PetTransformType transformType);

        /// <summary>
        /// 验证变换条件
        /// </summary>
        /// <param name="request">变换请求</param>
        /// <returns>验证结果</returns>
        Task<(bool IsValid, string ErrorMessage)> ValidateTransformConditionsAsync(PetTransformRequestDto request);

        /// <summary>
        /// 检查用户冷却时间
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="transformType">变换类型</param>
        /// <returns>是否在冷却中</returns>
        Task<(bool InCooldown, long RemainingTime)> CheckCooldownAsync(int userId, PetTransformType transformType);

        /// <summary>
        /// 计算神宠概率
        /// </summary>
        /// <param name="sourcePet">源宠物</param>
        /// <returns>神宠概率</returns>
        Task<decimal> CalculateGodPetProbabilityAsync(user_pet sourcePet);

        /// <summary>
        /// 执行宠物变换
        /// </summary>
        /// <param name="request">变换请求</param>
        /// <param name="targetPetConfig">目标宠物配置</param>
        /// <returns>变换结果</returns>
        Task<PetTransformResultDto> ExecuteTransformAsync(PetTransformRequestDto request, pet_config targetPetConfig);

        /// <summary>
        /// 记录变换日志
        /// </summary>
        /// <param name="log">变换日志</param>
        /// <returns>是否成功</returns>
        Task<bool> LogTransformAsync(pet_transform_log log);

        /// <summary>
        /// 初始化默认配置
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> InitializeDefaultConfigAsync();
    }
}
