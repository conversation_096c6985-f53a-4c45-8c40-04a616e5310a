.main_box {
    background-image: url(../img/slcw/bg.png);
    width: 788px;
    height: 311px;
}
body,html{
    padding: 0px;
    margin: 0px;
}
.zlbtn{
    width: 83px;
    height:90px;
    background-image: url(../img/slcw/btn_zl.png);
    cursor: pointer;
}
.zlbtn:hover{
    background-image: url(../img/slcw/btn_zl_hover.png);
}
.cfbtn{
    width: 83px;
    height:90px;
    background-image: url(../img/slcw/btn_cf.png);
    cursor: pointer;
}
.cfbtn:hover{
    background-image: url(../img/slcw/btn_cf_hover.png);
}
.jbbtn{
    width: 83px;
    height:90px;
    background-image: url(../img/slcw/btn_jb.png);
    cursor: pointer;
}
.jbbtn:hover{
    background-image: url(../img/slcw/btn_jb_hover.png);
}
.btns{
    position: absolute;
    left:80px;
    top:35px;
}
.jblist .jbbtn{
    width: 126px;
    height: 44px;

}
.jblist{
    position: absolute;
    left: 570px;
    top: 75px;

}
.image{
    position: absolute;
    width: 311px;
    height: 311px;
    left:220px;
    cursor: pointer;
}
.yingbi{
    display:none;
}
.jbicon{
    cursor: pointer;
}
.box{
    width: 379px;
    height: 243px;
    background-image:url(../img/slcw/window.png) ;
    position: absolute;
    z-index: 999;
    left: 196px;
    top:50px;
    color:#fff;
    display: none;

}
.jbBox,.cfBox,.zlBox{
    z-index:1000;
}
.cfGift{
    width: 40px;
    height: 36px;
    position: absolute;
    left: 550px;
    top: 266px;
    cursor: pointer;
    display: none;
}

.cfGiftLV1{
    background-image:url(../img/slcw/jbbx/1.png)  ;
}
.cfGiftLV1_hide{
    background-image:url(../img/slcw/jbbx/1_hide.png)  ;
}
.cfGiftLV2{
    background-image:url(../img/slcw/jbbx/2.png)  ;
}
.cfGiftLV2_hide{
    background-image:url(../img/slcw/jbbx/2_hide.png)  ;
}
.cfGiftLV3{
    background-image:url(../img/slcw/jbbx/3.png)  ;
}
.cfGiftLV3_hide{
    background-image:url(../img/slcw/jbbx/3_hide.png)  ;
}
.cfGiftLV4{
    background-image:url(../img/slcw/jbbx/4.png)  ;
}
.cfGiftLV4_hide{
    background-image:url(../img/slcw/jbbx/4_hide.png)  ;
}
.cfGiftLV5{
    background-image:url(../img/slcw/jbbx/5.png)  ;
}
.cfGiftLV5_hide{
    background-image:url(../img/slcw/jbbx/5_hide.png)  ;
}

.title{
    font-weight: bold;
    font-size: 16px;
    text-align: center;
    width: 307px;
    margin-top: 50px;
    margin-left: 37px;
}
.sxList{
    font-size:12px;
    margin-left: 55px;
    line-height: 20px;
}
.box1,.box2{
    float:left;
    width: 50%;;
}
.boxLine div{
    float:left;
}
.c{
    clear: both;
    height: 1px;
    width: 1px;
}
.infoTJ{
    display: none;
}
.boxLeft{
    float:left;   
    margin-left: 40px;
    margin-top: 80px;
    width: 47px;
}
.boxRight{
    float:left;
    margin-top: 70px;
}
.input{
    background-image: url(../img/slcw/input.png);
    width: 180px;
    height: 18px;
    padding-top: 4px;
    padding-bottom: 5px;
    text-indent: 5px;
    text-align: center;
    background-color: transparent;
    outline: none;
    border: none;
}
.useBtn{
    background-image: url(../img/slcw/use.png);
    width: 61px;
    height: 33px;
    position: absolute;
    left: 275px;
    top: 88px;
    cursor: pointer;

}
.BoxInfo{
    margin-top: 5px;
}
.boxSR{
    
    color: #000;
    
    font-weight: bold;
}
.BoxUseTitle,.BoxUseText{
    float: left;
    font-size: 14px;
    line-height: 23px;
    font-weight: bold;
    color: #000;
}

/* 阴影效果 - 支持现代浏览器 */
.text-with-shadow {
    text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.5); /* 水平偏移 阴影模糊值 阴影颜色 */
}
/* IE6 兼容 - 使用滤镜效果 */
.text-with-shadow-ie6 {
    zoom: 1; /* 触发hasLayout */  
    text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.5); /* 水平偏移 阴影模糊值 阴影颜色 */

    filter: progid:DXImageTransform.Microsoft.Shadow(color=#333333, direction=135, strength=3); /* IE6 阴影滤镜 */
}


.close{
    width: 35px;
    height: 41px;
    cursor: pointer;
    background-image: url(../img/slcw/close.png);
    position: absolute;
    left: 315px;
    top: 8px;
}

.close:hover{
    background-image: url(../img/slcw/close_hover.png);
}
._left{
    position: absolute;
    background-image: url(../img/slcw/syy.png);
    width: 43px;
    height: 30px;
    top: 275px;
    left: 20px;
    cursor: pointer;
}
._left:hover{
    left: 15px;
}
._right:hover{
    left: 725px;
}
._right{
    position: absolute;
    background-image: url(../img/slcw/xyy.png);
    width: 43px;
    height: 30px;
    top: 275px;
    left: 720px;
    cursor: pointer;
}
.returnPage {
    background-image: url(../img/yg/close.png);
    width: 40px;
    height: 40px;
    position: absolute;
    top: 10px;
    left: 735px;
    background-repeat: no-repeat;
    background-position-x: 5px;
    background-position-y: 5px;
    cursor: pointer;
}.returnPage:hover{

    background-image: url(../img/yg/close_hover.png);
    
    background-position-x: 0px;
    
    background-position-y: 0px;
}
