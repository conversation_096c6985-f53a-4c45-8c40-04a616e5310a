﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///宠物合成公式表
    ///</summary>
    [SugarTable("pet_synthesis_formula")]
    public partial class pet_synthesis_formula
    {
           public pet_synthesis_formula(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:主宠物编号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int main_pet_no {get;set;}

           /// <summary>
           /// Desc:副宠物编号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int sub_pet_no {get;set;}

           /// <summary>
           /// Desc:主宠最小成长要求
           /// Default:0.000000
           /// Nullable:True
           /// </summary>           
           public decimal? main_growth_min {get;set;}

           /// <summary>
           /// Desc:副宠最小成长要求
           /// Default:0.000000
           /// Nullable:True
           /// </summary>           
           public decimal? sub_growth_min {get;set;}

           /// <summary>
           /// Desc:合成结果宠物编号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int result_pet_no {get;set;}

           /// <summary>
           /// Desc:基础成功率(%)
           /// Default:50.00
           /// Nullable:True
           /// </summary>           
           public decimal? base_success_rate {get;set;}

           /// <summary>
           /// Desc:所需等级
           /// Default:40
           /// Nullable:True
           /// </summary>           
           public int? required_level {get;set;}

           /// <summary>
           /// Desc:消耗金币
           /// Default:50000
           /// Nullable:True
           /// </summary>           
           public long? cost_gold {get;set;}

           /// <summary>
           /// Desc:公式类型
           /// Default:FIXED
           /// Nullable:True
           /// </summary>           
           public string? formula_type {get;set;}

           /// <summary>
           /// Desc:是否启用
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? is_active {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

    }
}
