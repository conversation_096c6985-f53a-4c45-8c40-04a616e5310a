namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 游戏事件通知DTO
    /// </summary>
    public class GameEventNoticeDTO
    {
        /// <summary>
        /// 消息类型
        /// </summary>
        public string Type { get; set; } = "game_event";

        /// <summary>
        /// 事件类型 (player_levelup/rare_drop/first_kill/realm_breakthrough/equipment_enhance)
        /// </summary>
        public string EventType { get; set; }

        /// <summary>
        /// 玩家ID
        /// </summary>
        public int PlayerId { get; set; }

        /// <summary>
        /// 玩家昵称
        /// </summary>
        public string PlayerName { get; set; }

        /// <summary>
        /// 事件描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 是否广播给所有玩家
        /// </summary>
        public bool Broadcast { get; set; } = false;

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; } = DateTime.Now;
    }
} 