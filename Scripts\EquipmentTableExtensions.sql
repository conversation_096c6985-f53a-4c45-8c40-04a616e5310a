-- 装备模块表结构扩展脚本
-- 用于补充user_equipment表缺失的字段

-- 检查并添加user_equipment表的缺失字段
-- 1. 五行属性字段
SELECT CASE 
    WHEN COUNT(*) = 0 THEN 'ALTER TABLE user_equipment ADD COLUMN element VARCHAR(10) COMMENT ''五行属性（金/木/水/火/土/雷/风）'';'
    ELSE 'SELECT ''element字段已存在'';'
END as sql_command
FROM pragma_table_info('user_equipment') 
WHERE name = 'element';

-- 2. 关联宠物ID字段
SELECT CASE 
    WHEN COUNT(*) = 0 THEN 'ALTER TABLE user_equipment ADD COLUMN pet_id INTEGER COMMENT ''关联宠物ID（装备到哪个宠物）'';'
    ELSE 'SELECT ''pet_id字段已存在'';'
END as sql_command
FROM pragma_table_info('user_equipment') 
WHERE name = 'pet_id';

-- 3. 宝石槽位数字段
SELECT CASE 
    WHEN COUNT(*) = 0 THEN 'ALTER TABLE user_equipment ADD COLUMN gemstone_slots INTEGER DEFAULT 1 COMMENT ''宝石槽位数量（1-3个）'';'
    ELSE 'SELECT ''gemstone_slots字段已存在'';'
END as sql_command
FROM pragma_table_info('user_equipment') 
WHERE name = 'gemstone_slots';

-- 4. 套装ID字段
SELECT CASE 
    WHEN COUNT(*) = 0 THEN 'ALTER TABLE user_equipment ADD COLUMN suit_id VARCHAR(50) COMMENT ''套装ID（关联套装系统）'';'
    ELSE 'SELECT ''suit_id字段已存在'';'
END as sql_command
FROM pragma_table_info('user_equipment') 
WHERE name = 'suit_id';

-- 5. 历史属性字段（灵饰专用）
SELECT CASE 
    WHEN COUNT(*) = 0 THEN 'ALTER TABLE user_equipment ADD COLUMN lssx VARCHAR(255) COMMENT ''历史属性（灵饰装备专用，每个属性提供2%加成）'';'
    ELSE 'SELECT ''lssx字段已存在'';'
END as sql_command
FROM pragma_table_info('user_equipment') 
WHERE name = 'lssx';

-- 6. 特殊效果字段
SELECT CASE 
    WHEN COUNT(*) = 0 THEN 'ALTER TABLE user_equipment ADD COLUMN special_effect TEXT COMMENT ''特殊效果（法宝等特殊装备的SpecialAffect）'';'
    ELSE 'SELECT ''special_effect字段已存在'';'
END as sql_command
FROM pragma_table_info('user_equipment') 
WHERE name = 'special_effect';

-- 实际执行的ALTER TABLE语句（需要根据上面的检查结果手动执行）
-- 如果字段不存在，请执行以下语句：

-- ALTER TABLE user_equipment ADD COLUMN element VARCHAR(10);
-- ALTER TABLE user_equipment ADD COLUMN pet_id INTEGER;
-- ALTER TABLE user_equipment ADD COLUMN gemstone_slots INTEGER DEFAULT 1;
-- ALTER TABLE user_equipment ADD COLUMN suit_id VARCHAR(50);
-- ALTER TABLE user_equipment ADD COLUMN lssx VARCHAR(255);
-- ALTER TABLE user_equipment ADD COLUMN special_effect TEXT;

-- 检查user_pet表是否有element字段（用于五行限制检查）
SELECT CASE 
    WHEN COUNT(*) = 0 THEN 'ALTER TABLE user_pet ADD COLUMN element VARCHAR(10) COMMENT ''宠物五行属性'';'
    ELSE 'SELECT ''user_pet.element字段已存在'';'
END as sql_command
FROM pragma_table_info('user_pet') 
WHERE name = 'element';

-- 如果user_pet表没有element字段，请执行：
-- ALTER TABLE user_pet ADD COLUMN element VARCHAR(10);

-- 创建装备相关索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_user_equipment_element ON user_equipment(element);
CREATE INDEX IF NOT EXISTS idx_user_equipment_pet_id ON user_equipment(pet_id);
CREATE INDEX IF NOT EXISTS idx_user_equipment_suit_id ON user_equipment(suit_id);
CREATE INDEX IF NOT EXISTS idx_user_equipment_equipped ON user_equipment(is_equipped);

-- 验证表结构
SELECT 'user_equipment表结构验证:' as info;
SELECT name, type, "notnull", dflt_value 
FROM pragma_table_info('user_equipment') 
WHERE name IN ('element', 'pet_id', 'gemstone_slots', 'suit_id', 'lssx', 'special_effect')
ORDER BY name;

SELECT 'user_pet表结构验证:' as info;
SELECT name, type, "notnull", dflt_value 
FROM pragma_table_info('user_pet') 
WHERE name = 'element';

-- 检查装备相关表是否存在
SELECT 'gemstone_config表' as table_name, 
       CASE WHEN COUNT(*) > 0 THEN '存在' ELSE '不存在' END as status
FROM sqlite_master 
WHERE type='table' AND name='gemstone_config'
UNION ALL
SELECT 'equipment_gemstone表' as table_name,
       CASE WHEN COUNT(*) > 0 THEN '存在' ELSE '不存在' END as status
FROM sqlite_master 
WHERE type='table' AND name='equipment_gemstone'
UNION ALL
SELECT 'suit_config表' as table_name,
       CASE WHEN COUNT(*) > 0 THEN '存在' ELSE '不存在' END as status
FROM sqlite_master 
WHERE type='table' AND name='suit_config'
UNION ALL
SELECT 'suit_attribute表' as table_name,
       CASE WHEN COUNT(*) > 0 THEN '存在' ELSE '不存在' END as status
FROM sqlite_master 
WHERE type='table' AND name='suit_attribute'
UNION ALL
SELECT 'equipment_operation_log表' as table_name,
       CASE WHEN COUNT(*) > 0 THEN '存在' ELSE '不存在' END as status
FROM sqlite_master 
WHERE type='table' AND name='equipment_operation_log';

-- 统计现有数据
SELECT 'gemstone_config' as table_name, COUNT(*) as record_count 
FROM gemstone_config
UNION ALL
SELECT 'suit_config' as table_name, COUNT(*) as record_count 
FROM suit_config
UNION ALL
SELECT 'suit_attribute' as table_name, COUNT(*) as record_count 
FROM suit_attribute;
