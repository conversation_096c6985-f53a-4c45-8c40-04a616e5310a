namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 背包结果DTO
    /// </summary>
    public class BagResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 背包物品列表
        /// </summary>
        public List<BagItemDTO> Items { get; set; } = new List<BagItemDTO>();

        /// <summary>
        /// 背包容量上限
        /// </summary>
        public int MaxCapacity { get; set; }

        /// <summary>
        /// 当前已用容量
        /// </summary>
        public int UsedCapacity { get; set; }

        /// <summary>
        /// 剩余容量
        /// </summary>
        public int RemainingCapacity => MaxCapacity - UsedCapacity;
    }
} 