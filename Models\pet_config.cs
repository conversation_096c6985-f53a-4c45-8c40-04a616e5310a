﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///宠物配置表
    ///</summary>
    [SugarTable("pet_config")]
    public partial class pet_config
    {
           public pet_config(){


           }
           /// <summary>
           /// Desc:自增长主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:宠物编号（原始编号）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int pet_no {get;set;}

           /// <summary>
           /// Desc:宠物名称
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string name {get;set;}

           /// <summary>
           /// Desc:宠物属性（如金、木、水、火、土、神等）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string attribute {get;set;}

           /// <summary>
           /// Desc:技能编号（可为空，多个技能用英文逗号分隔）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? skill {get;set;}

           /// <summary>
           /// Desc:是否启用
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? is_active {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

    }
}
