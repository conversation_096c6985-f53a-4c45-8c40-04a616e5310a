<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>牧场 updatePet 函数测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-section h3 { margin-top: 0; color: #333; }
        .test-button { background: #4CAF50; color: white; border: none; padding: 8px 16px; margin: 5px; border-radius: 4px; cursor: pointer; }
        .test-button:hover { background: #45a049; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .pet-display { border: 1px solid #ccc; padding: 10px; margin: 10px 0; border-radius: 5px; background: #f9f9f9; }
        .pet-item { display: inline-block; margin: 5px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; background: white; }
        .main-pet { border-color: #ff6b6b; background: #ffe0e0; }
        .carry-pet { border-color: #4ecdc4; background: #e0f7f5; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎒 牧场 updatePet 函数测试</h1>
        
        <!-- API测试 -->
        <div class="test-section">
            <h3>🔗 API接口测试</h3>
            <button class="test-button" onclick="testGetCarryPetsAPI()">测试 GetCarryPets API</button>
            <button class="test-button" onclick="testGetCarryPetsData()">测试 getCarryPetsData 函数</button>
            <div id="apiResults"></div>
        </div>

        <!-- updatePet函数测试 -->
        <div class="test-section">
            <h3>🎮 updatePet 函数测试</h3>
            <button class="test-button" onclick="testUpdatePetFunction()">测试 updatePet 函数</button>
            <button class="test-button" onclick="testUpdatePetWithMockData()">使用模拟数据测试</button>
            <div id="updatePetResults"></div>
            
            <!-- 模拟的宠物显示区域 -->
            <div class="pet-display">
                <h4>携带宠物显示区域：</h4>
                <div class="背包宠物" id="mockPetDisplay">
                    <!-- 这里会显示宠物 -->
                </div>
            </div>
        </div>

        <!-- 数据格式对比 -->
        <div class="test-section">
            <h3>📊 数据格式对比</h3>
            <button class="test-button" onclick="compareDataFormats()">对比数据格式</button>
            <div id="formatResults"></div>
        </div>
    </div>

    <script src="../js/cache-manager.js"></script>
    <script src="../js/loading-manager.js"></script>
    <script src="../js/auth-manager.js"></script>
    <script src="../js/pasture-api-adapter.js"></script>

    <script>
        const API_BASE_URL = 'http://localhost:5000/api';
        const TEST_USER_ID = 1;

        function addResult(sectionId, message, type = 'info', data = null) {
            const section = document.getElementById(sectionId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            
            const time = new Date().toLocaleTimeString();
            let content = `[${time}] ${message}`;
            
            if (data) {
                content += '\n' + JSON.stringify(data, null, 2);
            }
            
            div.textContent = content;
            section.appendChild(div);
            section.scrollTop = section.scrollHeight;
        }

        // 测试原始 GetCarryPets API
        async function testGetCarryPetsAPI() {
            try {
                addResult('apiResults', '🔄 测试 GetCarryPets API...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/Player/GetCarryPets?userId=${TEST_USER_ID}`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('apiResults', `✅ GetCarryPets API 成功，携带宠物数量: ${result.pets.length}`, 'success', {
                        petCount: result.pets.length,
                        pets: result.pets.map(p => ({
                            id: p.id,
                            name: p.name,
                            level: p.level,
                            element: p.element,
                            isMain: p.isMain
                        }))
                    });
                } else {
                    addResult('apiResults', '❌ GetCarryPets API 失败', 'error', result);
                }
            } catch (error) {
                addResult('apiResults', `💥 GetCarryPets API 异常: ${error.message}`, 'error');
            }
        }

        // 测试适配器的 getCarryPetsData 函数
        async function testGetCarryPetsData() {
            try {
                addResult('apiResults', '🔄 测试 getCarryPetsData 函数...', 'info');
                
                if (typeof window.external.getCarryPetsData === 'function') {
                    const result = await window.external.getCarryPetsData();
                    const pets = JSON.parse(result);
                    
                    addResult('apiResults', `✅ getCarryPetsData 成功，携带宠物数量: ${pets.length}`, 'success', {
                        petCount: pets.length,
                        pets: pets.map(p => ({
                            宠物序号: p.宠物序号,
                            宠物名字: p.宠物名字,
                            等级: p.等级,
                            五行: p.五行,
                            状态: p.状态,
                            是否主宠: p.是否主宠
                        }))
                    });
                } else {
                    addResult('apiResults', '❌ getCarryPetsData 函数不存在', 'error');
                }
            } catch (error) {
                addResult('apiResults', `💥 getCarryPetsData 异常: ${error.message}`, 'error');
            }
        }

        // 模拟 updatePet 函数
        function updatePet(json) {
            try {
                console.log('🎮 调用 updatePet 函数，数据:', json);
                
                const j = JSON.parse(json);
                const container = document.getElementById('mockPetDisplay');
                container.innerHTML = '';
                
                let html = '';
                for (let i = 0; i < j.length && i < 3; i++) { // 最多显示3只宠物
                    const pet = j[i];
                    const isMain = pet.状态 == "0" || pet.是否主宠;
                    const cssClass = isMain ? 'pet-item main-pet' : 'pet-item carry-pet';
                    
                    html += `
                        <div class="${cssClass}">
                            <strong>${pet.宠物名字}</strong><br>
                            等级: ${pet.等级}<br>
                            五行: ${pet.五行}<br>
                            状态: ${isMain ? '主战' : '携带'}<br>
                            形象: ${pet.形象}
                        </div>
                    `;
                }
                
                // 填充空位
                for (let i = j.length; i < 3; i++) {
                    html += '<div class="pet-item">空位</div>';
                }
                
                container.innerHTML = html;
                
                addResult('updatePetResults', `✅ updatePet 函数执行成功，显示 ${j.length} 只宠物`, 'success');
                return true;
            } catch (error) {
                addResult('updatePetResults', `💥 updatePet 函数执行失败: ${error.message}`, 'error');
                return false;
            }
        }

        // 测试 updatePet 函数
        async function testUpdatePetFunction() {
            try {
                addResult('updatePetResults', '🔄 测试 updatePet 函数...', 'info');
                
                if (typeof window.external.getCarryPetsData === 'function') {
                    const carryData = await window.external.getCarryPetsData();
                    const success = updatePet(carryData);
                    
                    if (success) {
                        addResult('updatePetResults', '✅ updatePet 函数测试成功', 'success');
                    }
                } else {
                    addResult('updatePetResults', '❌ getCarryPetsData 函数不存在', 'error');
                }
            } catch (error) {
                addResult('updatePetResults', `💥 updatePet 函数测试异常: ${error.message}`, 'error');
            }
        }

        // 使用模拟数据测试 updatePet 函数
        function testUpdatePetWithMockData() {
            try {
                addResult('updatePetResults', '🔄 使用模拟数据测试 updatePet...', 'info');
                
                const mockData = [
                    {
                        宠物序号: 1,
                        宠物名字: "风精灵",
                        等级: 25,
                        五行: "风",
                        形象: 269,
                        状态: "0", // 主战宠物
                        是否主宠: true
                    },
                    {
                        宠物序号: 2,
                        宠物名字: "光天使",
                        等级: 22,
                        五行: "光",
                        形象: 270,
                        状态: "1", // 普通携带
                        是否主宠: false
                    },
                    {
                        宠物序号: 3,
                        宠物名字: "暗影兽",
                        等级: 19,
                        五行: "暗",
                        形象: 271,
                        状态: "1", // 普通携带
                        是否主宠: false
                    }
                ];
                
                const success = updatePet(JSON.stringify(mockData));
                
                if (success) {
                    addResult('updatePetResults', '✅ 模拟数据测试成功', 'success', mockData);
                }
            } catch (error) {
                addResult('updatePetResults', `💥 模拟数据测试失败: ${error.message}`, 'error');
            }
        }

        // 对比数据格式
        async function compareDataFormats() {
            try {
                addResult('formatResults', '🔄 对比数据格式...', 'info');
                
                // 获取 API 原始数据
                const apiResponse = await fetch(`${API_BASE_URL}/Player/GetCarryPets?userId=${TEST_USER_ID}`);
                const apiResult = await apiResponse.json();
                
                // 获取适配器转换后的数据
                const adaptedData = await window.external.getCarryPetsData();
                const adaptedPets = JSON.parse(adaptedData);
                
                const comparison = {
                    原始API格式: apiResult.pets?.[0] || {},
                    转换后格式: adaptedPets[0] || {},
                    字段映射: {
                        'id → 宠物序号': '✓',
                        'name → 宠物名字': '✓',
                        'level → 等级': '✓',
                        'element → 五行': '✓',
                        'petNo → 形象': '✓',
                        'isMain → 状态/是否主宠': '✓'
                    }
                };
                
                addResult('formatResults', '✅ 数据格式对比完成', 'success', comparison);
            } catch (error) {
                addResult('formatResults', `💥 数据格式对比失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                testGetCarryPetsAPI();
            }, 1000);
        });
    </script>
</body>
</html>
