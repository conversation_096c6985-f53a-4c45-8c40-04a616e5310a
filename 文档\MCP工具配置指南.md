# 🛠️ MCP工具配置指南

## 📋 推荐工具列表

### 🥇 高优先级工具

#### 1. SQLite MCP Server
```json
{
  "name": "sqlite",
  "command": "npx",
  "args": ["-y", "@modelcontextprotocol/server-sqlite", "./kddata.db"],
  "description": "直接操作项目数据库",
  "benefits": [
    "查看数据库表结构",
    "验证API操作结果", 
    "快速测试SQL查询"
  ]
}
```

#### 2. Fetch MCP Server
```json
{
  "name": "fetch",
  "command": "npx",
  "args": ["-y", "@modelcontextprotocol/server-fetch"],
  "description": "HTTP请求测试工具",
  "benefits": [
    "测试Controller接口",
    "验证API响应格式",
    "模拟前端请求"
  ]
}
```

#### 3. Regex MCP Server
```json
{
  "name": "regex",
  "command": "npx", 
  "args": ["-y", "@modelcontextprotocol/server-regex"],
  "description": "正则表达式工具",
  "benefits": [
    "提取window.external调用",
    "解析数据格式字符串",
    "验证格式转换逻辑"
  ]
}
```

### 🥈 中优先级工具

#### 4. JSON MCP Server
```json
{
  "name": "json",
  "command": "npx",
  "args": ["-y", "@modelcontextprotocol/server-json"],
  "description": "JSON处理工具",
  "benefits": [
    "格式化API响应",
    "验证JSON结构",
    "数据格式转换"
  ]
}
```

## 🔧 配置方法

### 方法1：Easy MCP Installation 界面配置

1. **打开 Easy MCP Installation**
2. **进入工具管理页面**
3. **搜索并安装以下工具**：
   - `@modelcontextprotocol/server-sqlite`
   - `@modelcontextprotocol/server-fetch`
   - `@modelcontextprotocol/server-regex`
   - `@modelcontextprotocol/server-json`

### 方法2：手动配置文件

如果您的MCP配置文件位于 `~/.config/mcp/config.json`：

```json
{
  "mcpServers": {
    "sqlite": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sqlite", "d:/AI OB/研究/WebApplication_HM/kddata.db"]
    },
    "fetch": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-fetch"]
    },
    "regex": {
      "command": "npx", 
      "args": ["-y", "@modelcontextprotocol/server-regex"]
    },
    "json": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-json"]
    }
  }
}
```

### 方法3：命令行安装

```bash
# 全局安装MCP工具
npm install -g @modelcontextprotocol/server-sqlite
npm install -g @modelcontextprotocol/server-fetch  
npm install -g @modelcontextprotocol/server-regex
npm install -g @modelcontextprotocol/server-json

# 或者使用npx（推荐）
npx @modelcontextprotocol/server-sqlite --help
npx @modelcontextprotocol/server-fetch --help
```

## 🎯 使用场景示例

### 场景1：分析页面调用
```bash
# 使用正则工具提取window.external调用
regex-search "window\.external\.\w+\(" --file "wwwroot/game/pages/Index.html"
```

### 场景2：测试API接口
```bash
# 使用fetch工具测试背包API
fetch-request POST "http://localhost:5000/api/Game/backpack/1" \
  --headers "Content-Type: application/json" \
  --body '{"page":0,"search":""}'
```

### 场景3：查询数据库
```bash
# 使用SQLite工具查看用户道具
sqlite-query "SELECT * FROM user_item WHERE user_id = 1 LIMIT 10;"
```

### 场景4：处理JSON数据
```bash
# 格式化API响应
json-format --input "api_response.json" --output "formatted.json"
```

## 🚀 预期效果

### 开发效率提升
- **数据库查询**：从手动工具 → 直接命令，节省50%时间
- **API测试**：从Postman → 直接命令，节省30%时间  
- **代码分析**：从手动搜索 → 正则工具，节省60%时间

### 准确性提升
- **实时数据验证**：直接查看数据库变化
- **格式验证**：自动检查JSON格式正确性
- **接口测试**：快速验证API响应

## 📝 注意事项

1. **数据库路径**：确保SQLite工具指向正确的数据库文件路径
2. **网络访问**：Fetch工具需要网络权限
3. **Node.js版本**：确保Node.js版本兼容（建议16+）
4. **权限设置**：某些工具可能需要文件系统访问权限

## 🔄 验证安装

安装完成后，可以通过以下方式验证：

```bash
# 检查工具是否可用
npx @modelcontextprotocol/server-sqlite --version
npx @modelcontextprotocol/server-fetch --version

# 测试基本功能
sqlite-query "SELECT name FROM sqlite_master WHERE type='table';"
```

---

**配置完成后，这些工具将显著提高页面-控制器对接的效率和准确性！**
