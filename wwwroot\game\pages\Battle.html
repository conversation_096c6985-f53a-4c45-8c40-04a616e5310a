﻿<!DOCTYPE html
	PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<title>无标题文档</title>
	<script src="./Content/Javascript/petList.js?123"></script>
	<style type="text/css">
		body {
			/* ReSharper disable once InvalidValue */
			filter: gray;
			margin: 0;
			font-size: 12px;
			margin-left: 8px;
			margin-top: 8px;
		}

		.yingbi {
			POSITION: ABSOLUTE;
			width: 1px;

			height: 1px;

		}

		#info {
			position: absolute;
			left: 8px;
			top: 8px;
			border: 0;
			z-index: 98
		}

		.怪物名字 {
			white-space: nowrap
		}

		#pname {
			padding-top: 3px;
			text-align: center;
			position: absolute;
			z-index: 101;
			left: 8px;
			top: 2px;
			border: 0;
			height: 19px;
			width: 147px;
			background: url(img/zd01.png);
			background-size: 147px 19px;
		}

		#ico {
			position: absolute;
			z-index: 101;
			left: 5px;
			top: 21px;
			border: 0;
			height: 47px;
			width: 90px;
			background: url(img/zd02.png);
			background-size: 90px 47px;

		}

		.c {
			opacity: 0.4;
			filter: progid:DXImageTransform.Microsoft.Alpha(style=0, opacity=40, finishOpacity=100);
		}

		#info_page {
			position: absolute;
			z-index: 999;
			left: 0;
			top: 0;
			border: 0;
			height: 48px;
			width: 160px;
		}

		#hp {
			position: absolute;
			z-index: 100;
			left: 70px;
			top: 24px;
			border: 0;
			height: 16px;
			width: 160px;
		}

		table {
			display: table;
			border-collapse: separate;
			border-spacing: 0;
			border-color: grey;
		}

		td,
		th {
			display: table-cell;
			vertical-align: inherit;
		}

		#mp {
			position: absolute;
			z-index: 100;
			left: 70px;
			top: 41px;
			border: 0;
			height: 16px;
			width: 160px;
		}

		#exp {
			position: absolute;
			z-index: 100;
			left: 70px;
			top: 59px;
			border: 0;
			height: 11px;
			width: 160px;
		}

		#hp_show {
			position: absolute;
			left: 106px;
			top: 27px;
			color: rgb(0, 0, 0);
			z-index: 100000;
			font-size: 0.8em;
		}

		#mp_show {
			position: absolute;
			left: 106px;
			top: 42px;
			color: rgb(0, 0, 0);
			z-index: 100000;
			font-size: 0.8em;
		}

		#exp_show {
			position: absolute;
			left: 106px;
			top: 59px;
			color: rgb(0, 0, 0);
			z-index: 100000;
			font-size: 0.8em;
			padding-left: 2px;
		}

		#petname_label {
			position: absolute;
			left: 2px;
			top: 81px;
			font-size: 12px;
			text-align: center;
			color: rgb(3, 147, 213);
			z-index: 1000;
			width: 100px;
		}

		#InfoIco {
			width: 31px;
			height: 37px;
			z-index: 4;
			top: 0;
			left: 0;
			position: absolute;
			background: url(img/dr02.gif);
		}

		#cw {
			position: absolute;
			left: 10px;
			top: 113px;
		}

		.cw {
			height: 311px;
			width: 788px;
			z-index: 10000;
			POSITION: ABSOLUTE
		}

		#gw {
			position: absolute;
			left: 530px;
			top: 120px;
			width: 250px;
			height: 180px;
			opacity: 100;
		}

		#gwInfo {
			position: absolute;
			left: 620px;
			top: 49px;
			color: rgb(51, 136, 0);
			font-size: 0.8em;
			z-index: 3;
		}

		#gwName {
			width: 114px;
			height: 11px;
			z-index: 5;
			top: 0;
			left: 15px;
			position: absolute;
			padding-left: 6px;
			padding-top: 2px
		}

		#hpIco {
			position: absolute;
			left: 620px;
			top: 49px;
			color: rgb(51, 136, 0);
			font-size: 0.8em;
			z-index: 1;
		}

		#gwimg {
			opacity: 0.7;
			filter: progid:DXImageTransform.Microsoft.Alpha(style=0, opacity=70, finishOpacity=100)
		}

		#hpInfo {
			width: 96px;
			height: 11px;
			position: absolute;
			left: 636px;
			top: 65px;
			z-index: 3;
			padding-left: 13px;
		}

		#gwhpvalue {
			width: 96px;
			height: 11px;
			padding-left: 18px;
			left: 6px;
			position: absolute;
			color: #412804;
			font-size: 10px;
			top: 0;
		}

		#gwhpico {
			width: 96px;
			height: 16px;
			overflow: hidden;
			background-image: url(img/dr03.gif);
			background-position: 100% 0;
			background-repeat: no-repeat;
		}

		#ghp {
			width: auto;
			height: 16px;
			background-image: url(img/dr04.gif);
			background-repeat: repeat-x;
		}

		#timev {
			width: 81px;
			height: 52px;
			background-image: url(img/db.gif);
			position: absolute;
			left: 361px;
			top: 10px;
			padding-top: 25px;
			text-align: center;
			font-weight: bold;
			color: #6A2F06;
			font-size: 25px;
			font-family: 黑体
		}

		#gj {
			position: absolute;
			left: 8px;
			top: 251px;
			background-image: url(img/zdzsk.png);
			background-size: 778px 71px;
			width: 778px;
			height: 71px;
		}

		#ico2 {
			position: absolute;
			width: 37px;
			height: 34px;
			left: 259px;
			top: 7px;
			cursor: pointer;
		}

		#ico3 {
			position: absolute;
			width: 37px;
			height: 34px;
			left: 299px;
			top: 10px;
			cursor: pointer;
		}

		#ico4 {
			position: absolute;
			width: 37px;
			height: 34px;
			left: 340px;
			z-index: 99999999;
			top: 10px;
			cursor: pointer;
		}

		#ico5 {
			position: absolute;
			width: 37px;
			height: 34px;
			left: 384px;
			top: 10px;
			cursor: pointer;
		}

		#ico6 {
			position: absolute;
			width: 37px;
			height: 34px;
			left: 428px;
			top: 10px;
			cursor: pointer;
		}

		#ico7 {
			position: absolute;
			width: 37px;
			height: 34px;
			left: 472px;
			top: 10px;
			cursor: pointer;
		}

		#ico8 {
			position: absolute;
			width: 37px;
			height: 34px;
			left: 513px;
			top: 10px;
			cursor: pointer;
		}

		#state {
			position: absolute;
			left: 620px;
			top: 150px;
			font-weight: bold;
			width: 150px;
			height: auto;
			font-family: 华文新魏;
			font-size: 16px;
			color: yellow;
			z-index: 1000;
		}

		#sh {
			color: #F00;
		}

		.window {
			position: absolute;
			left: 270px;
			height: 160px;
			top: 87px;
			color: #e1e1e1;
			line-height: 1.7;
			width: 246px;
			padding: 10px;
			z-index: 10000;
			overflow-y: auto;
			background-color: #025477;
			opacity: 0.68;
		}

		#gj div {
			cursor: pointer;
		}

		.hc {
			position: absolute;
			top: 0px;
			left: 0px;
			z-index: 99999;
		}
	</style>
</head>

<body>
	<div id="main" width="778px" height="311px" style="width: 778px; height: 311px; position: absolute;">
		<div id="pname"><span class="玩家名字"></span></div>
		<div id="ico">
			<div class="头像"
				style="position: absolute; z-index: 101; left: 23px; top: 1px; border: 0px; height: 36px; width: 36px;">
			</div>
		</div>
		<div id="info_page"></div>
		<div class="hc">
			<img />
		</div>
		<div id="info_page1"></div>
		<div id="hp">
			<table border="0" cellspacing="0" cellpadding="0">
				<tbody>
					<tr>
						<td>
							<img src="img/xthong01.gif" width="156" border="0px" height="16" id="php"
								style="width: 155px;" />
						</td>
						<td width="4">
							<img src="img/xthong00.gif" width="4" border="0" height="16" />
						</td>
					</tr>
				</tbody>
			</table>
		</div>
		<div id="hp_show"><span id="cwdqhp" class="生命">61541</span>/<span id="cwzdhp" class="最大生命">61541</span></div>
		<div id="mp">
			<table border="0" cellspacing="0" cellpadding="0">
				<tbody>
					<tr>
						<td>
							<img src="img/xtlan01.gif" width="155" border="0" height="16" id="pmp"
								style="width: 155px;" />
						</td>
						<td width="5">
							<img src="img/xtlan00.gif" width="5" border="0" height="16" />
						</td>
					</tr>
				</tbody>
			</table>
		</div>
		<div id="mp_show"><span class="魔法"></span>/<span class="最大魔法"></span></div>
		<div id="exp">
			<table border="0" cellspacing="0" cellpadding="0">
				<tbody>
					<tr>
						<td>
							<img src="img/xthuang01.gif" width="156" border="0" height="16px;" id="pexp"
								style="width: 155px;" />
						</td>
						<td width="4">
							<img src="img/xthuang00.gif" width="4" border="0" height="16px;" />
						</td>
					</tr>
				</tbody>
			</table>
		</div>
		<div id="exp_show"><span class="当前经验"></span>/<span class="升级经验"></span></div>
		<div id="petname"
			style="position: absolute; z-index: 100; left: 4px; top: 66px; border: 0px; height: 48px; width: 99px; background: url(img/zd03.png);background-size: 99px 48px;">
		</div>
		<div id="petname_label">
			<span class="宠物名字"></span>
			<br />
			<font color="#097603"><span class="宠物等级"></span>级</font>
		</div>
	</div>
	<div id="gwdiv"></div>
	<div id="gwInfo">
		<div id="InfoIco">
			<div style="z-index: 4; top: 12px; left: 15px; position: absolute">
				<font color="#2A9E49" size="2.5"><b><span class="怪物五行"></span></b></font>
			</div>
		</div>
		<div id="gwName">&nbsp;&nbsp;<span class="怪物名字"></span>&nbsp;LV：<span class="怪物等级"></span></div>
	</div>
	<div id="hpIco">
		<div style="width: 114px; height: 28px; top: 0px; left: 13px; position: absolute">
			<img src="img/dr01.gif" id="gwimg">
		</div>
	</div>
	<div id="hpInfo">
		<div id="gwhpvalue"><span id="gwValue" class="怪物生命"></span>/<span id="gwMaxHp" class="怪物最大生命"></span></div>
		<div id="gwhpico">
			<div id="ghp"></div>
		</div>
	</div>
	<div id="timev">KO</div>
	<div id="gj" style="z-index: 99999; display: block;">
		<div onclick="zidonzhandou(0)"
			style="position: absolute; width: 37px; height: 34px; left: 218px; top: 7px; cursor: pointer;" id="ico1">
			&nbsp;&nbsp;<br>
			&nbsp;&nbsp;
		</div>
		<div onclick="$('#jntool3').toggle();$('#jntool').hide();$('#jntool2').hide();"
			style="position: absolute; width: 37px; height: 34px; left: 259px; top: 7px; cursor: pointer;" id="ico2">
			&nbsp;&nbsp;<br>
			&nbsp;&nbsp;
		</div>
		<div id="ico3" style="position: absolute; width: 37px; height: 34px; left: 299px; top: 10px; cursor: pointer;"
			onclick="gongji(null,'普通攻击')">
			&nbsp;&nbsp;<br>
			&nbsp;&nbsp;
		</div>
		<div id="ico4" style="position: absolute; width: 37px; height: 34px; left: 340px; top: 10px; cursor: pointer;"
			onclick="$('#jntool2').toggle();$('#jntool').hide();$('#jntool3').hide();">
			&nbsp;&nbsp;<br>
			&nbsp;&nbsp;
		</div>
		<div id="ico5" style="position: absolute; width: 37px; height: 34px; left: 384px; top: 10px; cursor: pointer;">
			&nbsp;&nbsp;<br>
			&nbsp;&nbsp;
		</div>
		<div id="ico6" onclick="$('#jntool').toggle();$('#jntool2').hide();$('#jntool3').hide();"
			style="position: absolute; width: 37px; height: 34px; left: 428px; top: 10px; cursor: pointer;">
			&nbsp;&nbsp;<br>
			&nbsp;&nbsp;
		</div>
		<div id="ico7" style="position: absolute; width: 37px; height: 34px; left: 472px; top: 10px; cursor: pointer;">
			&nbsp;&nbsp;<br>
			&nbsp;&nbsp;
		</div>
		<div id="ico8" onclick="window.location.href='BattleMap.html'"
			style="position: absolute; width: 41px; height: 45px; left: 518px; top: 10px; cursor: pointer; cursor: pointer;">
			&nbsp;&nbsp;<br>
			&nbsp;&nbsp;
		</div>
	</div>
	<div id="state"
		style="position: absolute;  left: 300px; top: 150px; font-weight: bold; width: 180px; height: auto; font-family: 华文新魏; font-size: 18px; color: yellow; z-index: 1000; display: none;  padding: 10px; border-radius: 5px;">
		<div style="margin-bottom: 5px;">
			<span id="jn" style="color: #FFD700;"></span><span style="color: #F00; margin-left: 10px;" id="sh"></span>
		</div>
		<div style="color: #F3F; margin: 2px 0;" id="js"></div>
		<div style="color: #9FF; margin: 2px 0;" id="dixiao"></div>
		<div style="color: #6F0; margin: 2px 0;" id="xixue"></div>
		<div style="color: #F0F; margin: 2px 0;" id="ximo"></div>
	</div>
	<div id="returnPage" style="display: none">
		<div style="position: absolute; left: 270px; height: 160px; top: 87px; color: #e1e1e1; line-height: 1.7; width: 246px; padding: 10px; z-index:99999999; overflow-y: auto; background-color: #025477; opacity: 0.68; filter: alpha(opacity=68);"
			class="window"></div>
		<div
			style="position: absolute; left: 270px; height: 160px; top: 87px; color: #e1e1e1; line-height: 1.7; width: 246px; padding: 10px; z-index:99999999; overflow-y: auto;">
			<div class="结果"></div>
			<div class="奖励预览" style="display: none;">
				可获得经验：<span class="预览经验"></span><br>
				可获得金币：<span class="预览金币"></span> 个 可获得元宝：<span class="预览元宝"></span> 个<br>
				可获得物品：<span class="预览物品"></span><br>
				<br>
				<button id="claimRewardBtn" style="background: #4CAF50; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 14px; margin-right: 10px; transition: background-color 0.3s;" onclick="claimBattleReward()" onmouseover="this.style.backgroundColor='#45a049'" onmouseout="this.style.backgroundColor='#4CAF50'">
					<b>领取奖励</b>
				</button>
				<br><br>
			</div>
			<div class="奖励结果" style="display: none;">
				获得经验：<span class="获得经验"></span><br>
				获得金币：<span class="获得金币"></span> 个 获得元宝：<span class="获得元宝"></span> 个<br>
				捕获宠物：0<br>
				获得物品：<span class="获得物品"></span><br>
				<br>
			</div>
			<span style="cursor: pointer;" onclick="closeBattleResultDialog()"><b>继续探险</b></span> <span
				onclick="window.parent.Load(2);" style="cursor: pointer;"><b>返回村庄</b></span><br>
		</div>
	</div>
	<img style="position:absolute;left:999999px" id="hccc" />
	<div id="jntool" class="捕捉面板"
		style="z-index:99999999;display: none; position: absolute; left: 432px; top: 110px; height: 136px; border: 0px; color: rgb(122, 147, 3); font-size: 12px; padding: 3px;">
		<table style="min-width: 274px;" border="0" cellspacing="0" cellpadding="0">
			<tbody>
				<tr>
					<td width="17">
						<img src="img/bk01.gif" width="17" height="123">
					</td>
					<td background="img/bk04.gif">
						<table width="100%" border="0" cellspacing="0" cellpadding="0">
							<tbody>
								<tr></tr>
								<tr class="捕捉球"></tr>
							</tbody>
						</table>
					</td>
					<td width="31">
						<table width="100%" border="0" cellspacing="0" cellpadding="0">
							<tbody>
								<tr>
									<td width="31">
										<img src="img/bk02.gif" width="31" height="31" onclick="$('#jntool').hide();"
											style="cursor: pointer;">
									</td>
								</tr>
								<tr>
									<td>
										<img src="img/bk03.gif" width="31" height="92">
									</td>
								</tr>
							</tbody>
						</table>
					</td>
				</tr>
			</tbody>
		</table>
		<table width="100" border="0" align="left" cellpadding="0" cellspacing="0" style="margin-left: 10px">
			<tbody>
				<tr>
					<td>
						<img src="img/jiantou.gif" width="17" height="16">
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div id="jntool2" class="技能面板"
		style="display: none; position: absolute; left: 343px; top: 110px; height: 136px; border: 0px; color: rgb(122, 147, 3); font-size: 12px; padding: 3px;z-index:99999999">
		<table style="min-width: 274px;" border="0" cellspacing="0" cellpadding="0">
			<tbody>
				<tr>
					<td width="17">
						<img src="img/bk01.gif" width="17" height="123">
					</td>
					<td background="img/bk04.gif">
						<table width="100%" border="0" cellspacing="0" cellpadding="0">
							<tbody>
								<tr></tr>
								<tr class="技能列表"></tr>
							</tbody>
						</table>
					</td>
					<td width="31">
						<table width="100%" border="0" cellspacing="0" cellpadding="0">
							<tbody>
								<tr>
									<td width="31">
										<img src="img/bk02.gif" width="31" height="31" onclick="$('#jntool2').hide()"
											style="cursor: pointer;">
									</td>
								</tr>
								<tr>
									<td>
										<img src="img/bk03.gif" width="31" height="92">
									</td>
								</tr>
							</tbody>
						</table>
					</td>
				</tr>
			</tbody>
		</table>
		<table width="100" border="0" align="left" cellpadding="0" cellspacing="0" style="margin-left: 10px">
			<tbody>
				<tr>
					<td>
						<img src="img/jiantou.gif" width="17" height="16">
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div id="jntool3" class="设置面板"
		style="z-index:99999999;display: none; position: absolute; left: 262px; top: 110px; height: 136px; border: 0px; color: rgb(122, 147, 3); font-size: 12px; padding: 3px;">
		<table style="min-width: 274px;" border="0" cellspacing="0" cellpadding="0">
			<tbody>
				<tr>
					<td width="17">
						<img src="img/bk01.gif" width="17" height="123">
					</td>
					<td background="img/bk04.gif">
						<table width="100%" border="0" cellspacing="0" cellpadding="0">
							<tbody>
								<tr></tr>
								<tr class="技能列表"></tr>
							</tbody>
						</table>
					</td>
					<td width="31">
						<table width="100%" border="0" cellspacing="0" cellpadding="0">
							<tbody>
								<tr>
									<td width="31">
										<img src="img/bk02.gif" width="31" height="31" onclick="$('#jntool2').hide()"
											style="cursor: pointer;">
									</td>
								</tr>
								<tr>
									<td>
										<img src="img/bk03.gif" width="31" height="92">
									</td>
								</tr>
							</tbody>
						</table>
					</td>
				</tr>
			</tbody>
		</table>
		<table width="100" border="0" align="left" cellpadding="0" cellspacing="0" style="margin-left: 10px">
			<tbody>
				<tr>
					<td>
						<img src="img/jiantou.gif" width="17" height="16">
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<script type="text/javascript" src="jquery-1.8.3.min.js"></script>
	<script type="text/javascript">
		// ===== 战斗API适配器 =====
		/**
		 * 战斗API适配器 - 封装所有战斗相关的API调用
		 * 替代原有的window.external调用，实现现代化的异步API通信
		 */
		const BattleAPI = {
			baseUrl: '/api/Player',

			/**
			 * 执行战斗计算
			 * @param {number} mapId - 地图ID
			 * @param {number} petId - 宠物ID
			 * @param {string|null} skillId - 技能ID，null表示普通攻击
			 * @param {number} monsterId - 要战斗的怪物ID
			 * @returns {Promise<Object>} 战斗结果对象
			 */
			async executeBattle(mapId, petId, skillId = null, monsterId = null) {
				try {
					const userId = parent.gameAPI.getCurrentUserId();
					const response = await fetch(`${this.baseUrl}/BattleCalculate`, {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json',
							'Accept': 'application/json'
						},
						body: JSON.stringify({
							UserId: userId,
							MapId: mapId,
							PetId: petId,
							SkillId: skillId,
							MonsterId: monsterId
						})
					});

					if (!response.ok) {
						throw new Error(`HTTP ${response.status}: ${response.statusText}`);
					}

					const result = await response.json();
					return this.transformBattleResult(result);
				} catch (error) {
					console.error('战斗API调用失败:', error);
					throw error;
				}
			},

			/**
			 * 捕捉宠物
			 * @param {number} mapId - 地图ID
			 * @param {number} monsterId - 怪物ID
			 * @returns {Promise<Object>} 捕捉结果对象
			 */
			async capturePet(mapId, monsterId) {
				try {
					const userId = parent.gameAPI.getCurrentUserId();
					const response = await fetch(`${this.baseUrl}/CapturePet`, {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json',
							'Accept': 'application/json'
						},
						body: JSON.stringify({
							UserId: userId,
							MapId: mapId,
							MonsterId: monsterId
						})
					});

					if (!response.ok) {
						throw new Error(`HTTP ${response.status}: ${response.statusText}`);
					}

					return await response.json();
				} catch (error) {
					console.error('捕捉API调用失败:', error);
					throw error;
				}
			},

			/**
			 * 领取战斗奖励
			 * @param {string} battleId - 战斗唯一标识
			 * @returns {Promise<Object>} 奖励领取结果对象
			 */
			async claimBattleReward(battleId) {
				try {
					const userId = parent.gameAPI.getCurrentUserId();
					const response = await fetch(`${this.baseUrl}/ClaimBattleReward`, {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json',
							'Accept': 'application/json'
						},
						body: JSON.stringify({
							UserId: userId,
							BattleId: battleId
						})
					});

					if (!response.ok) {
						throw new Error(`HTTP ${response.status}: ${response.statusText}`);
					}

					const result = await response.json();
					return result;
				} catch (error) {
					console.error('领取奖励API调用失败:', error);
					throw error;
				}
			},

			/**
			 * 转换战斗结果为兼容格式
			 * 将新API返回的标准格式转换为兼容旧系统的格式
			 * @param {Object} apiResult - API返回的战斗结果
			 * @returns {Object} 包含新格式和兼容格式的结果对象
			 */
			transformBattleResult(apiResult) {
				// 直接返回API结果，不需要额外转换
				// 因为我们已经修改了前端代码来直接处理新的数据格式
				return {
					// 保持API原始数据结构
					...apiResult,

					// 添加success标识
					success: true,

					// 确保关键字段存在
					battleRounds: apiResult.battleRounds || [],
					dropItems: apiResult.dropItems || [],

					// 统一字段名（优先使用新字段）
					goldGained: apiResult.goldGained || apiResult.gold || 0,
					experienceGained: apiResult.experienceGained || apiResult.exp || 0,
					yuanbaoGained: apiResult.yuanbaoGained || apiResult.yuanbao || 0,

					// 确保HP字段一致性
					playerCurrentHp: apiResult.playerCurrentHp || apiResult.playerRemainingHp,
					monsterCurrentHp: apiResult.monsterCurrentHp || apiResult.monsterRemainingHp,

					// 保留原始字段以备兼容
					playerRemainingHp: apiResult.playerRemainingHp || apiResult.playerCurrentHp,
					monsterRemainingHp: apiResult.monsterRemainingHp || apiResult.monsterCurrentHp
				};
			}
		};

		// ===== 全局变量（从Battle1.html复制） =====

		// 基础战斗变量
		var ico = "112";           // 玩家宠物图标编号
		var gico = "73";           // 怪物图标编号
		var map = 0;               // 当前地图ID
		var zidong = 0;            // 自动战斗标识（0=手动，1=自动，-1=次数不足）
		var or = true;             // 战斗状态标识（用于控制自动战斗）
		var gj = false;            // 攻击状态锁定（防止重复攻击）
		var t = 10;                // 倒计时变量（秒）
		var isFirstBattle = true;  // 是否为第一次战斗标识
		// 注意：不再需要lastPlayedRound变量，因为每次都播放完整战斗过程

		// ===== 页面参数变量 =====
		var currentMapId = null;   // 当前地图ID（从URL参数获取）
		var currentPetId = null;   // 当前宠物ID（从URL参数获取）
		var currentBattleId = null; // 当前战斗ID（用于安全领取奖励）

		// ===== 怪物管理变量 =====
		var availableMonsters = []; // 当前地图可用的怪物列表
		var currentMonsterIndex = 0; // 当前战斗的怪物索引
		var currentMonsterId = null; // 当前战斗的怪物ID

		// 宠物相关变量
		var 形象;                  // 宠物形象编号（用于动画显示）
		var wuxing = "";           // 宠物五行属性（聖、佛、萌、灵、次元、巫等）
		var cwMp = "";             // 当前宠物魔法值
		var cwMaxMp = "";          // 宠物最大魔法值
		var currentPetNo = null;   // 当前宠物编号（用于战斗API调用）
		var currentPetId = null;   // 当前宠物Id（用于战斗API调用）
		var 怪物调用宠物素材 = false; // 怪物是否使用宠物素材路径
		var isAnimationPlaying = false; // 动画播放状态标志

		// 战斗效果变量
		var jiasheng = 0;          // 加深伤害数值
		var dixiao = 0;            // 抵消伤害数值
		var ximo = 0;              // 吸魔数值
		var xixue = 0;             // 吸血数值
		var jn = "普通攻击";       // 当前使用的技能名称

		// 动画控制变量
		var zI = 1;                // 攻击动画当前帧索引
		var sI = 1;                // 受击动画当前帧索引
		var zCount = 10;           // 攻击动画总帧数
		var sCount = 31;           // 受击动画总帧数
		var png_ = true;           // PNG动画启用标识
		var hc_index = 0;          // 合成动画当前帧索引
		var hc_id = 0;             // 合成动画ID
		var hc_max = 23;           // 合成动画最大帧数
		var hc_time = 55;          // 合成动画时间间隔（毫秒）

		// 其他全局变量
		var gwsh = "";             // 怪物受到伤害（暂存）
		var gwMaxHp;               // 怪物最大生命值
		var gwHp = "";             // 怪物当前生命值
		var jsons;                 // 战斗结果JSON数据（暂存）
		var cwHp = "";             // 宠物当前生命值
		var cwMaxHp = "";          // 宠物最大生命值
		var cwEXP;                 // 宠物当前经验值
		var cwMaxEXP;              // 宠物升级所需经验值
		var Init = 0;              // 动画初始化标识
		var stime = 100;           // 动画播放时间间隔（毫秒）
		var 调用宠物素材 = false;   // 是否使用宠物素材（PNG格式）而非通用图片
		var ys = 0;                // 颜色索引（用于地狱之门特效循环）

		// 地狱之门特效颜色数组（用于魔化宠物的发光效果）
		var 地狱之门颜色 = [
			"#CCFF33", "#BBFF00", "#99DD00", "#88AA00", "#668800",
			"#CCFF99", "#BBFF66", "#99FF33", "#77FF00", "#66DD00",
			"#55AA00", "#227700", "#99FF99", "#66FF66", "#33FF33",
			"#00FF00", "#00DD00", "#00AA00", "#008800"
		];
 
		// ===== 工具函数（从Battle1.html复制） =====
		/**
		 * 禁止后退键功能
		 * 处理键盘事件，禁止后退键（Backspace），密码或单行、多行文本框除外
		 * @param {Event} e - 键盘事件对象
		 * @returns {boolean} 是否允许该键盘事件
		 */
		function banBackSpace(e) {
			var ev = e || window.event;                    // 获取event对象（兼容不同浏览器）
			var obj = ev.target || ev.srcElement;          // 获取事件源元素（兼容不同浏览器）

			var inputType = obj.type || obj.getAttribute('type');  // 获取事件源类型（input类型）

			// 获取作为判断条件的事件类型属性
			var vReadOnly = obj.getAttribute('readonly');  // 只读属性
			var vEnabled = obj.getAttribute('enabled');    // 启用属性
			// 处理null值情况，设置默认值
			vReadOnly = (vReadOnly == null) ? false : vReadOnly;
			vEnabled = (vEnabled == null) ? true : vEnabled;

			// 当敲Backspace键时，事件源类型为密码或单行、多行文本的，
			// 并且readonly属性为true或enabled属性为false的，则退格键失效
			var flag1 = (ev.keyCode == 8 && (inputType == "password" || inputType == "text" || inputType == "textarea")
				&& (vReadOnly == true || vEnabled != true)) ? true : false;

			// 当敲Backspace键时，事件源类型非密码或单行、多行文本的，则退格键失效
			var flag2 = (ev.keyCode == 8 && inputType != "password" && inputType != "text" && inputType != "textarea")
				? true : false;

			//判断
			if (flag2) {
				return false;
			}
			if (flag1) {
				return false;
			}
		}

		//禁止后退键 作用于Firefox、Opera
		document.onkeypress = banBackSpace;
		//禁止后退键  作用于IE、Chrome
		document.onkeydown = banBackSpace;

		/**
		 * 设置战斗背景图片
		 * @param {string} i - 背景图片文件名
		 */
		function setBJ(i) {
			$("#main").css("background-image", "url(Content/Img/Battle/" + i + ")");
		}

		/**
		 * 根据地图ID动态加载背景图片
		 * 支持jpg、gif、png格式，按优先级尝试加载
		 * @param {number|string} mapId - 地图ID
		 */
		function loadBackgroundByMapId(mapId) {
			if (!mapId) {
				console.warn('地图ID为空，无法加载背景');
				return;
			}

			// 支持的图片格式，按优先级排序
			const formats = [ 'jpg', 'gif','png'];
			const basePath = 'Content/Img/Battle/';

			// 尝试加载图片的函数
			function tryLoadImage(format, index = 0) {
				if (index >= formats.length) {
					console.warn(`地图ID ${mapId} 的背景图片不存在，尝试了所有格式: ${formats.join(', ')}`);
					// 可以设置一个默认背景
					setDefaultBackground();
					return;
				}

				const currentFormat = formats[index];
				const imagePath = `${basePath}${mapId}.${currentFormat}`;

				// 创建图片对象测试是否存在
				const img = new Image();

				img.onload = function() {
					// 图片加载成功，设置为背景
					$("#main").css({
						"background-image": `url(${imagePath})`,
						"background-size": "cover",
						"background-position": "center",
						"background-repeat": "no-repeat"
					});
					console.log(`成功加载地图 ${mapId} 的背景图片: ${imagePath}`);
				};

				img.onerror = function() {
					// 当前格式加载失败，尝试下一个格式
					console.log(`格式 ${currentFormat} 加载失败，尝试下一个格式...`);
					tryLoadImage(format, index + 1);
				};

				// 开始加载图片
				img.src = imagePath;
			}

			// 开始尝试加载
			tryLoadImage(formats[0], 0);
		}

		/**
		 * 设置默认背景图片
		 * 当指定地图ID的背景图片不存在时使用
		 */
		function setDefaultBackground() {
			// 可以设置一个默认的背景图片，或者清除背景
			$("#main").css({
				"background-image": "none",
				"background-color": "#000033" // 深蓝色作为默认背景
			});
			console.log('使用默认背景');
		}

		/**
		 * 格式化显示大数字
		 * 将大数字转换为带单位的简化显示（如：1000万 -> 1万）
		 * @param {number} i - 要格式化的数字
		 * @returns {string} 格式化后的字符串
		 */
		function showInt(i) {
			var num = i;  // 默认返回原数字

			// 根据数字大小添加相应的中文单位
			if (i > 1000000000000000000) {          // 大于1000百京
				i = toDecimal(i / 1000000000000000000);
				num = i + "百京";
			}
			else if (i > 10000000000000000) {       // 大于1京
				i = toDecimal(i / 10000000000000000);
				num = i + "京";
			}
			else if (i >= 100000000000000) {        // 大于等于100兆
				i = toDecimal(i / 100000000000000);
				num = i + "百兆";
			}
			else if (i >= 1000000000000) {          // 大于等于1兆
				i = toDecimal(i / 1000000000000);
				num = i + "兆";
			}
			else if (i >= 10000000000) {            // 大于等于100亿
				i = toDecimal(i / 10000000000);
				num = i + "百亿";
			} else if (i >= 100000000) {            // 大于等于1亿
				i = toDecimal(i / 100000000);
				num = i + "亿";
			}
			return num;
		}

		/**
		 * 转换为小数
		 * 将数字转换为保留两位小数的格式
		 * @param {number} x - 要转换的数字
		 * @returns {number} 保留两位小数的数字
		 */
		function toDecimal(x) {
			var f = parseFloat(x);  // 转换为浮点数
			if (isNaN(f)) {         // 如果不是数字则返回undefined
				return;
			}
			f = Math.round(x * 100) / 100;  // 四舍五入保留两位小数
			return f;
		}

		/**
		 * 设置地图和自动战斗状态
		 * @param {number} m - 地图ID
		 * @param {number} z - 自动战斗状态（0=手动，1=自动，-1=次数不足）
		 */
		function SetMap(m, z) {
			// 如果地图ID发生变化，更新背景
			if (map !== m) {
				map = m;
				currentMapId = m;
				// 动态加载新地图的背景
				loadBackgroundByMapId(m);
			} else {
				map = m;
			}

			zidong = z;
			if (z == -1) {
				window.parent.Alert("自动战斗剩余次数不足!");
			}
		}

		/**
		 * 调试信息输出
		 * 用于输出调试信息到父窗口（当前已禁用）
		 * @param {string} i - 调试信息内容
		 */
		function debug(i) {
			return;
			//window.parent.recvMsg("sm|" + new Date().toLocaleString() + "  " + i);
		}

		/**
		 * 自动战斗函数
		 * 启动自动战斗模式，使用预设的技能进行战斗
		 * @param {number} i - 自动战斗参数（当前未使用）
		 */
		function zidonzhandou(i) {
			$("#gj").hide();
			or = false;
			zidong = 1;
			gongji(window.parent.jid, window.parent.jname);
		}

		// ===== 新的异步战斗函数 =====
		/**
		 * 主战斗函数（重构版）
		 * 一次API调用获取完整战斗结果，然后播放完整战斗过程
		 * @param {string|null} id - 技能ID，null表示普通攻击
		 * @param {string} jnname - 技能名称，用于显示
		 */
		async function gongji(id, jnname) {
			if (gj) {
				// 战斗进行中，忽略重复攻击
				
				return;
			}

			try {
				// 设置战斗状态
				gj = true;
				window.parent.gjs(true);

				// 停止倒计时，显示战斗状态
				$("#timev").html("PK");

				// 隐藏所有面板
				$(".捕捉面板").hide();
				$(".技能面板").hide();
				$("#dixiao").hide();
				$("#gj").hide(); // 战斗开始时隐藏攻击按钮

				// 设置技能名称
				jn = jnname || "普通攻击";

				// 显示战斗状态 - 参考Battle1.html的初始显示机制
				showBattleState();

				// 确保使用正确的地图ID
				const mapId = currentMapId || map || window.parent.currentMapId;

				// 使用宠物编号而不是URL参数中的petId
				const petNo = currentPetNo;

				if (!mapId) {
					throw new Error("地图ID未设置，无法进行战斗");
				}

				if (!petNo) {
					throw new Error("宠物编号未设置，无法进行战斗。请确保宠物信息已正确加载。");
				}

				// 获取当前要战斗的怪物ID
				const monsterId = getCurrentMonsterId();

				// 一次性调用战斗API，获取完整战斗结果（使用宠物编号作为PetId参数，传入怪物ID）
				const battleResult = await BattleAPI.executeBattle(mapId, currentPetId, id, monsterId);

				// 验证API返回数据
				if (!battleResult) {
					throw new Error("API返回数据为空");
				}

				// 检查是否有错误信息
				if (battleResult.error || (battleResult.message && !battleResult.isBattleEnd)) {
					throw new Error(battleResult.message || battleResult.error || "战斗API调用失败");
				}

				// 播放完整战斗过程
				await playCompleteBattle(battleResult);

			} catch (error) {
				console.error('战斗执行失败:', error);

				// 错误处理
				handleBattleError(error);

				// 重置战斗状态
				gj = false;
				window.parent.gjs(false);
				$("#state").hide();
				$("#gj").show(); // 战斗结束后显示攻击按钮
			}
		}

		/**
		 * 战斗错误处理
		 * 统一处理战斗过程中的各种错误情况
		 * @param {Error} error - 错误对象
		 */
		function handleBattleError(error) {
			let message = "战斗失败，请重试";

			if (error.message) {
				if (error.message.includes("频繁")) {
					message = "操作过于频繁，请稍后重试";
				} else if (error.message.includes("网络")) {
					message = "网络连接异常，请检查网络";
				} else {
					message = error.message;
				}
			}

			window.parent.Alert(message);
		}

		/**
		 * 播放完整战斗过程
		 * 根据API返回的完整战斗数据，播放所有回合的战斗动画
		 * @param {Object} battleResult - 完整战斗结果对象
		 */
		async function playCompleteBattle(battleResult) {
			

			// 更新全局变量
			cwMaxHp = battleResult.playerMaxHp;
			gwMaxHp = battleResult.monsterMaxHp;
			cwMp = battleResult.remainingMp || cwMp;

			// 更新战斗效果变量（使用总体数据）
			jiasheng = battleResult.damageAmplified || 0;
			dixiao = battleResult.damageReduced || 0;
			xixue = battleResult.lifeSteal || 0;
			ximo = battleResult.manaSteal || 0;

			// 获取所有战斗回合
			const battleRounds = battleResult.battleRounds || [];

			if (battleRounds.length === 0) {
				console.warn('没有战斗回合数据，直接显示结果');
				await handleBattleEnd(battleResult);
				return;
			}

			// 按顺序播放所有回合
			for (let i = 0; i < battleRounds.length; i++) {
				const round = battleRounds[i];
			

				// 确保前一个动画完全结束
				while (isAnimationPlaying) {
				
					await new Promise(resolve => setTimeout(resolve, 100));
				}

				// 显示回合信息
				$("#jn").html(`第${round.round}回合: ${round.description || ''}`);

				if (round.attackerType === "Player") {
					// 玩家攻击回合
		
					await playPlayerAttackAnimation(round, battleResult);
	
				} else if (round.attackerType === "Monster") {
					// 怪物攻击回合
					
					await playMonsterAttackAnimation(round, battleResult);
			
				}

				// 强制等待，确保所有动画和位移完全结束

				await new Promise(resolve => setTimeout(resolve, 1000));

				// 回合间隔（最后一回合不需要间隔）
				if (i < battleRounds.length - 1) {
				
					await new Promise(resolve => setTimeout(resolve, 2000));  // 回合间隔2秒
					
				}
			}

			// 所有回合播放完成后，显示最终战斗结果
			await handleBattleEnd(battleResult);
		}

		/**
		 * 播放完整攻击动画
		 * 使用setInterval连续播放所有攻击动画帧
		 */
		function playCompleteAttackAnimation() {
	

			// 清除之前的动画定时器
			if (Init != 0) {
				clearInterval(Init);
				Init = 0;
			}

			// 重置动画状态
			zI = 1;  // 从1开始
			let frameCount = 0;  // 播放帧计数器

			// 启动连续动画，每100ms播放一帧
			Init = setInterval(function() {
				playz();
				frameCount++;

				// 检查是否播放完一轮动画（播放了zCount帧）
				if (frameCount >= zCount) {
					clearInterval(Init);  // 停止动画
					Init = 0;
		
					// 回到第一帧
					$(".cw").addClass("yingbi").removeClass("cw");
					$(".z1").removeClass("yingbi").addClass("cw");

					// PNG动画完成后，宠物回到原位
					setTimeout(function() {
						$("#cw").css("left", "10px");
					
					}, 200);  // 减少延迟，让回位更及时
				}
			}, 100);
		}

		/**
		 * 播放完整受击动画
		 * 使用setInterval连续播放所有受击动画帧
		 */
		function playCompleteHurtAnimation() {

			// 清除之前的动画定时器
			if (Init != 0) {
				clearInterval(Init);
				Init = 0;
			}

			// 重置动画状态
			sI = 1;  // 从1开始
			let frameCount = 0;  // 播放帧计数器

			// 启动连续动画，使用stime间隔
			Init = setInterval(function() {
				plays();
				frameCount++;

				// 检查是否播放完一轮动画（播放了sCount帧）
				if (frameCount >= sCount) {
					clearInterval(Init);  // 停止动画
					Init = 0;
		
					// 回到攻击第一帧
					$(".cw").addClass("yingbi").removeClass("cw");
					$(".z1").removeClass("yingbi").addClass("cw");
				}
			}, stime);
		}

		/**
		 * 播放新增回合动画
		 * 播放从上次播放位置到当前的所有新回合动画
		 * @param {Object} battleResult - 战斗结果对象
		 */
		async function playLatestRoundAnimation(battleResult) {
			const battleRounds = battleResult.battleRounds;

			if (!battleRounds || battleRounds.length === 0) {
				console.warn('没有找到战斗回合数据');
				return;
			}

			// 计算需要播放的新回合
			const newRounds = battleRounds.filter(round => round.round > lastPlayedRound);

			if (newRounds.length === 0) {
			
				return;
			}

			// 按顺序播放所有新回合
			for (const round of newRounds) {
		
				if (round.attackerType === "Player") {
					// 玩家攻击回合
					await playPlayerAttackAnimation(round, battleResult);
				} else if (round.attackerType === "Monster") {
					// 怪物攻击回合
					await playMonsterAttackAnimation(round, battleResult);
				}

				// 更新已播放的回合数
				lastPlayedRound = round.round;

				// 回合间隔时间（避免动画过快）
				if (newRounds.length > 1) {
					await new Promise(resolve => setTimeout(resolve, 500));
				}
			}
		}

		/**
		 * 玩家攻击动画
		 * 播放玩家攻击动画，更新怪物HP，显示伤害效果
		 * @param {Object} roundData - 当前回合数据
		 * @param {Object} battleResult - 完整战斗结果对象
		 */
		async function playPlayerAttackAnimation(roundData, battleResult) {
		
			// 设置伤害显示数据
			jiasheng = roundData.amplifiedDamage || 0;
			dixiao = roundData.reducedDamage || 0;
			xixue = roundData.lifeSteal || 0;
			ximo = roundData.manaSteal || 0;
			jn = roundData.skillName || "普通攻击";

			// 确保伤害值是数字
			const damage = parseInt(roundData.damage) || 0;
			// 简化判断：如果有伤害值就认为命中
			const isHit = damage > 0 || roundData.isHit === true;
			const isCritical = roundData.isCritical === true;

			// 临时测试数据（后续可以从API获取真实数据）
			if (damage > 50) {
				jiasheng = Math.floor(damage * 0.2);  // 模拟加深伤害
			}
			if (isCritical) {
				xixue = Math.floor(damage * 0.1);     // 模拟吸血
			}

			// 显示伤害信息 - 玩家攻击
			displayDamageInfo(damage, isHit, isCritical, "player");

			// 更新怪物HP条到本回合结束后的值
			const monsterHp = roundData.monsterHpAfter;
			updateMonsterHP(monsterHp, roundData.damage);

			// 更新全局怪物HP变量
			gwHp = monsterHp;

			// 显示伤害数字
			if (roundData.isHit) {
				showDamageNumber(roundData.damage, "monster", roundData.isCritical);
			} else {
				showDamageNumber("MISS", "monster");
			}

			// 播放攻击动画（包含位移效果）

			isAnimationPlaying = true;  // 设置动画播放状态

			// 1. 宠物移动到怪物面前
			var gwLeft = $("#gw").css("left");
			var attackPosition = parseInt(gwLeft) - 80;  // 移动到怪物前80px
			$("#cw").css("left", attackPosition + "px");
	

			if (调用宠物素材) {
				// 播放完整PNG攻击动画
				
				playCompleteAttackAnimation();
			} else {

				// 切换到攻击GIF动画
				if (形象 >= 1000) {
					$("#cw").attr("src", "Content/PetPhoto/g" + 形象 + ".gif");
				} else {
					$("#cw").attr("src", "Content/PetPhoto/g" + 形象 + ".gif");
				}
			

				// 等待攻击动画播放
				await new Promise(resolve => setTimeout(resolve, 2000));

				// 2. 宠物回到原位
				$("#cw").css("left", "10px");


				if (形象 >= 1000) {
					$("#cw").attr("src", "Content/PetPhoto/z" + 形象 + ".gif");
				} else {
					$("#cw").attr("src", "Content/PetPhoto/z" + 形象 + ".gif");
				}
	
			}

			// 额外等待，确保回位完成
			await new Promise(resolve => setTimeout(resolve, 500));

			isAnimationPlaying = false;  // 清除动画播放状态

		}

		/**
		 * 怪物攻击动画
		 * 播放怪物攻击动画，更新玩家HP，显示受伤效果
		 * @param {Object} roundData - 当前回合数据
		 * @param {Object} battleResult - 完整战斗结果对象
		 */
		async function playMonsterAttackAnimation(roundData, battleResult) {

			// 设置伤害显示数据（怪物攻击玩家）
			jiasheng = roundData.amplifiedDamage || 0;
			dixiao = roundData.reducedDamage || 0;
			xixue = roundData.lifeSteal || 0;
			ximo = roundData.manaSteal || 0;
			jn = "怪物攻击";  // 怪物攻击标识

			// 确保伤害值是数字
			const damage = parseInt(roundData.damage) || 0;
			// 简化判断：如果有伤害值就认为命中
			const isHit = damage > 0 || roundData.isHit === true;
			const isCritical = roundData.isCritical === true;


			// 显示伤害信息 - 怪物攻击
			displayDamageInfo(damage, isHit, isCritical, "monster");

			// 更新玩家HP条到本回合结束后的值
			const playerHp = roundData.playerHpAfter;
			updatePlayerHP(playerHp, roundData.damage);

			// 更新全局玩家HP变量
			cwHp = playerHp;

			// 显示伤害数字
			if (roundData.isHit) {
				showDamageNumber(roundData.damage, "player", roundData.isCritical);
			} else {
				showDamageNumber("MISS", "player");
			}

			// 先播放怪物攻击动画（包含位移效果）
		
			// 1. 怪物移动到宠物面前
			var cwLeft = $("#cw").css("left");
			var monsterAttackPosition = parseInt(cwLeft) + 100;  // 移动到宠物后100px
			$("#gw").css("left", monsterAttackPosition + "px");

			if (怪物调用宠物素材) {
				// 怪物使用宠物素材路径
				$("#gw").attr("src", "Content/PetPhoto/g" + gico + ".gif");
			} else {
				// 怪物使用通用素材路径
				$("#gw").attr("src", "Content/gpc/g" + gico + ".gif");
			}
	

			// 等待怪物攻击动画播放
			await new Promise(resolve => setTimeout(resolve, 2000));  // 延长怪物攻击动画时间

			// 2. 怪物回到原位并切换到待机状态
			$("#gw").css("left", "530px");
			
			if (怪物调用宠物素材) {
				$("#gw").attr("src", "Content/PetPhoto/z" + gico + ".gif");
			} else {
				$("#gw").attr("src", "Content/gpc/z" + gico + ".gif");
			}


			// 等待回位完成
			await new Promise(resolve => setTimeout(resolve, 500));

			isAnimationPlaying = false;  // 清除动画播放状态

		}

		/**
		 * 显示伤害信息
		 * 参考Battle1.html的动画配合机制，智能定位和动画配合
		 * @param {number} damage - 伤害值
		 * @param {boolean} isHit - 是否命中
		 * @param {boolean} isCritical - 是否暴击
		 * @param {string} attacker - 攻击者类型 ("player" 或 "monster")
		 */
		function displayDamageInfo(damage, isHit, isCritical, attacker = "player") {
			console.log('显示伤害信息:', {
				damage,
				isHit,
				isCritical,
				attacker,
				jiasheng,
				dixiao,
				xixue,
				ximo,
				damageType: typeof damage,
				hitType: typeof isHit
			});

			// 参考Battle1.html的智能位置计算
			var sLeft = $("#state").css("left");
			var sWidth = $("#state").width();
			var sCount = parseInt(sLeft) + parseInt(sWidth);
			var subLeft = sCount - 778; // 屏幕宽度限制

			// 根据攻击者调整位置，参考Battle1.html的机制
			if (attacker === "player") {
				// 玩家攻击时，显示在怪物附近
				var gwLeft = $("#gw").css("left");
				var targetLeft = parseInt(gwLeft)+50;
				$("#state").css("left", targetLeft + "px");
			} else {
				// 怪物攻击时，显示在固定位置
				$("#state").css("left", "150px");
			}

			// 防止溢出屏幕右侧，参考Battle1.html的防溢出逻辑
			sLeft = $("#state").css("left");
			sWidth = $("#state").width();
			sCount = parseInt(sLeft) + parseInt(sWidth);
			subLeft = sCount - 778;
			if (subLeft > 0) {
				var currentLeft = parseInt($("#state").css("left"));
				$("#state").css("left", (currentLeft - subLeft) + "px");
			}

			// 设置技能名称
			$("#jn").html(jn || "普通攻击");

			// 设置伤害值
			if (!isHit) {
				$("#sh").html("&nbsp;MISS&nbsp;");
				$("#sh").css('color', '#888');
			} else if (damage > 0) {
				$("#sh").html("&nbsp;-" + showInt(damage) + "&nbsp;");
				$("#sh").css('color', isCritical ? '#ff6600' : '#F00');
			} else {
				$("#sh").html("&nbsp;0&nbsp;");
				$("#sh").css('color', '#888');
			}

			// 显示加深伤害
			if (jiasheng == "Max" || parseInt(jiasheng) > 0) {
				$("#js").show();
				$("#js").html("加深伤害 " + showInt(jiasheng));
			} else {
				$("#js").hide();
			}

			// 显示抵消伤害
			if (parseInt(dixiao) > 0) {
				$("#dixiao").show();
				$("#dixiao").html("抵消伤害 " + showInt(dixiao));
			} else {
				$("#dixiao").hide();
			}

			// 显示吸血
			if (xixue == "Max" || parseInt(xixue) > 0) {
				$("#xixue").show();
				$("#xixue").html("吸取生命 " + showInt(xixue));
			} else {
				$("#xixue").hide();
			}

			// 显示吸魔
			if (parseInt(ximo) > 0) {
				$("#ximo").show();
				$("#ximo").html("吸取魔法 " + showInt(ximo));
			} else {
				$("#ximo").hide();
			}

			// 显示状态面板
			$("#state").show();

			// 参考Battle1.html的定时隐藏机制，配合动画节奏
			setTimeout(function() {
				hideDamageInfo();
			}, 2500); // 稍微缩短显示时间，配合动画节奏
		}

		/**
		 * 显示战斗状态面板
		 * 参考Battle1.html的初始显示机制
		 */
		function showBattleState() {
			// 设置初始位置
			$("#state").css("left", "50px");
			$("#state").css("top", "150px");

			// 设置初始内容
			$("#jn").html("战斗开始");
			$("#sh").html("");
			$("#js").hide();
			$("#dixiao").hide();
			$("#xixue").hide();
			$("#ximo").hide();

			// 显示面板
			$("#state").show();

			// 短暂显示后隐藏，为后续动画做准备
			setTimeout(function() {
				$("#state").hide();
			}, 1500);
		}

		/**
		 * 隐藏伤害信息
		 * 参考Battle1.html的隐藏机制，清空所有伤害显示内容并隐藏面板
		 */
		function hideDamageInfo() {
			// 清空所有显示内容
			$("#jn").html("");
			$("#sh").html("");
			$("#js").hide();
			$("#dixiao").hide();
			$("#xixue").hide();
			$("#ximo").hide();

			// 参考Battle1.html的隐藏机制，在动画结束后隐藏面板
			$("#state").hide();
		}

		/**
		 * 更新怪物HP条
		 * 根据当前HP值更新怪物的血条显示
		 * @param {number} currentHp - 当前HP值
		 * @param {number} damage - 造成的伤害值
		 */
		function updateMonsterHP(currentHp, damage) {
			const maxHp = parseInt(gwMaxHp) || 1000;
			const hpPercentage = Math.max(0, currentHp / maxHp);
			const newWidth = Math.floor(96 * hpPercentage); // 96px是HP条的最大宽度

			$("#gwhpico").animate({
				width: newWidth + "px"
			}, 1000);

			// 显示伤害数字
			if (damage > 0) {
				showDamageNumber(damage, "monster");
			}
		}

		/**
		 * 更新玩家HP条
		 * 根据当前HP值更新玩家的血条显示
		 * @param {number} currentHp - 当前HP值
		 * @param {number} damage - 受到的伤害值
		 */
		function updatePlayerHP(currentHp, damage) {
			const maxHp = parseInt(cwMaxHp) || 1000;
			const hpPercentage = Math.max(0, currentHp / maxHp);
			const newWidth = Math.floor(155 * hpPercentage); // 155px是HP条的最大宽度
             
			console.log('更新玩家HP条123:', {
				currentHp,
				maxHp,
				hpPercentage,
				newWidth
			});

			$("#php").animate({
				width: newWidth + "px"
			}, 1000);

			// 显示伤害数字
			if (damage > 0) {
				showDamageNumber(damage, "player");
			}
		}

		/**
		 * 显示伤害数字动画
		 * 在指定位置显示伤害数字，并播放飞出动画
		 * @param {number|string} damage - 伤害数值或"MISS"
		 * @param {string} target - 目标类型（"player"或"monster"）
		 * @param {boolean} isCritical - 是否暴击
		 */
		function showDamageNumber(damage, target, isCritical = false) {
			let displayText = damage;
			let color = target === 'player' ? '#ff0000' : '#ffff00';
			let fontSize = '18px';

			// 根据不同情况设置显示样式
			if (damage === "MISS") {
				color = '#cccccc';
				displayText = "MISS";
			} else if (damage === 0) {
				color = '#888888';
				displayText = "0";
			} else if (isCritical) {
				color = '#ff6600';
				fontSize = '22px';
				displayText = `${damage}!`;
			}

			const damageElement = $(`<div class="damage-number">${displayText}</div>`);
			damageElement.css({
				position: 'absolute',
				color: color,
				fontSize: fontSize,
				fontWeight: 'bold',
				zIndex: 99999,
				left: target === 'player' ? '200px' : '500px',
				top: '200px',
				textShadow: '2px 2px 4px #000000',
				userSelect: 'none'
			});

			$('body').append(damageElement);

			// 动画效果
			damageElement.animate({
				top: '120px',
				opacity: 0
			}, 2000, function() {
				damageElement.remove();
			});
		}

		/**
		 * 战斗结束处理
		 * 处理战斗结束后的各种情况（胜利/失败），显示奖励和结果
		 * @param {Object} battleResult - 战斗结果对象
		 */
		async function handleBattleEnd(battleResult) {

			// 重置战斗状态
			gj = false;
			window.parent.gjs(false);
			$("#state").hide();
			$("#gj").show(); // 战斗结束后显示攻击按钮

			// 隐藏所有面板
			$(".捕捉面板").hide();
			$(".技能面板").hide();

			// 战斗结束后停止倒计时，等待用户在弹框中做出选择
			stopBattleTimer();

			// 更新UI显示最终状态
			updateUI();

			if (battleResult.isWin) {
				// 胜利处理 - 自动领取奖励后显示结果
				console.log('战斗胜利，BattleId:', battleResult.battleId);

				// 存储 BattleId 用于领取奖励
				currentBattleId = battleResult.battleId;

				// 注释掉自动切换怪物，改为在用户点击"继续探险"时切换
				// switchToNextMonster();

				// 自动调用领取奖励接口
				try {
					if (currentBattleId) {
						console.log('战斗结束，开始自动领取奖励，BattleId:', currentBattleId);

						// 调用领取奖励API
						const claimResult = await BattleAPI.claimBattleReward(currentBattleId);

						if (claimResult.success) {
							// 领取成功，显示实际获得的奖励
							$(".结果").html("战斗胜利！");
							$(".获得金币").html(claimResult.goldGained || 0);
							$(".获得元宝").html(claimResult.yuanbaoGained || 0);
							$(".获得经验").html(claimResult.experienceGained || 0);

							// 处理获得的物品
							const itemsGained = claimResult.itemsGained || [];
							let itemsString = "无";
							if (itemsGained.length > 0) {
								itemsString = itemsGained.map(item => `${item.itemName}x${item.quantity}`).join("、");
							}
							$(".获得物品").html(itemsString);

							// 显示奖励结果面板
							$(".奖励预览").hide();
							$(".奖励结果").show();

							// 清空 BattleId，防止重复领取
							currentBattleId = null;

							// 记录领取日志
							debug(`奖励自动领取成功!获得金币:${claimResult.goldGained},获得元宝:${claimResult.yuanbaoGained},获得经验:${claimResult.experienceGained},获得物品:${itemsString}`);

						} else {
							// 领取失败，显示预览奖励和手动领取按钮
							console.error('自动领取奖励失败:', claimResult.message);
							await showRewardPreview(battleResult);
						}
					} else {
						// 没有 BattleId，显示预览奖励
						console.warn('没有 BattleId，无法自动领取奖励');
						await showRewardPreview(battleResult);
					}
				} catch (error) {
					// 自动领取失败，显示预览奖励和手动领取按钮
					console.error('自动领取奖励异常:', error);
					await showRewardPreview(battleResult);
				}

				// 检查是否可以捕捉宠物
				if (battleResult.canCapture && battleResult.captureOptions) {
					await showCaptureOptions(battleResult.captureOptions);
				}

			} else {
				// 失败处理
				let failMessage = battleResult.message || "你的宠物好像有些弱啊！<br><br>赶紧去提升宠物的实力吧！<br><br>噢，对了，氪金可以使你变得强大！<br><br>";

				if (battleResult.isDead === 1) {
					failMessage = "你的宠物在战斗中倒下了！<br><br>" + failMessage;
				}

				$(".结果").html(failMessage);
				$(".奖励预览").hide();
				$(".奖励结果").hide();
			}

			// 显示返回面板
			$("#returnPage").show();

			// 处理自动战斗逻辑
			const autoBattleStatus = battleResult.autoBattleStatus;

			if (autoBattleStatus === 0 || battleResult.isDead === 1) {
				// 自动战斗停止或宠物死亡
				if (battleResult.isDead === 1) {
					window.parent.Alert("你的宠物在战斗中倒下了！");
					setTimeout(() => {
						window.parent.Load(2); // 返回村庄
					}, 2000);
				} else if (autoBattleStatus === 0) {
					// 自动战斗次数用完或其他原因停止
					window.parent.Alert("自动战斗已停止");
				}
			} else {
				// 继续自动战斗
				if (zidong == 1 && battleResult.isDead !== 1) {
					// 自动战斗模式下，奖励已经在 handleBattleEnd 中自动领取了
					// 直接继续下一轮战斗
					setTimeout(function() {
						window.parent.zidong(map);
					}, 3000);
				}
			}
		}

		/**
		 * 显示捕捉选项
		 * 战斗胜利后显示可捕捉的宠物列表
		 * @param {Array} captureOptions - 可捕捉宠物的选项数组
		 */
		async function showCaptureOptions(captureOptions) {
		
			// 构建捕捉面板HTML
			let html = "";
			captureOptions.forEach(option => {
				html += `
					<td height="28px">
						<table style="cursor:pointer;" border="0" cellspacing="0" cellpadding="0">
							<tbody>
								<tr class="buzhuoDIV" onclick="buzhuo(${option.monsterId})">
									<td style="display:none" class="wpid">${option.monsterId}</td>
									<td width="10" class="wpico">
										<img src="Content/PetPhoto/k${option.petNo}.gif" width="25" height="25">
									</td>
									<td width="80" class="wpnm">${option.name}</td>
									<td width="50" class="wplv">Lv.${option.level}</td>
									<td width="60" class="wpsx">${option.element}</td>
								</tr>
							</tbody>
						</table>
					</td>
				`;
			});

			// 更新捕捉面板内容
			$(".捕捉球").html(html);

			// 显示捕捉面板
			$(".捕捉面板").show();
			$(".奖励").hide();
		}

		/**
		 * 显示奖励预览（当自动领取失败时的回退方案）
		 * @param {Object} battleResult - 战斗结果对象
		 */
		async function showRewardPreview(battleResult) {
			$(".结果").html("战斗胜利！");

			// 显示奖励预览（不是实际获得）
			$(".预览金币").html(battleResult.goldGained || 0);
			$(".预览元宝").html(battleResult.yuanbaoGained || 0);
			$(".预览经验").html(battleResult.experienceGained || 0);

			// 处理掉落物品预览
			const dropItems = battleResult.dropItems || [];
			const itemsString = battleResult.itemsGainedString || dropItems.join(",") || "无";
			$(".预览物品").html(itemsString);

			// 显示奖励预览面板（包含领取按钮）
			$(".奖励预览").show();
			$(".奖励结果").hide();

			// 记录战斗日志
			debug(`战斗获胜!可获得奖励:${itemsString},可获得金币:${battleResult.goldGained},可获得元宝:${battleResult.yuanbaoGained},可获得经验:${battleResult.experienceGained}`);
		}

		/**
		 * 领取战斗奖励函数（手动领取，当自动领取失败时使用）
		 * 调用 ClaimBattleReward 接口安全领取奖励
		 */
		async function claimBattleReward() {
			try {
				// 检查是否有有效的 BattleId
				if (!currentBattleId) {
					window.parent.Alert("战斗ID无效，无法领取奖励");
					return;
				}

				// 禁用领取按钮，防止重复点击
				$("#claimRewardBtn").prop("disabled", true).html("领取中...");

				// 调用领取奖励API
				const result = await BattleAPI.claimBattleReward(currentBattleId);

				if (result.success) {
					// 领取成功，显示实际获得的奖励
					$(".获得金币").html(result.goldGained || 0);
					$(".获得元宝").html(result.yuanbaoGained || 0);
					$(".获得经验").html(result.experienceGained || 0);

					// 处理获得的物品
					const itemsGained = result.itemsGained || [];
					let itemsString = "无";
					if (itemsGained.length > 0) {
						itemsString = itemsGained.map(item => `${item.itemName}x${item.quantity}`).join("、");
					}
					$(".获得物品").html(itemsString);

					// 隐藏预览面板，显示结果面板
					$(".奖励预览").hide();
					$(".奖励结果").show();

					// 清空 BattleId，防止重复领取
					currentBattleId = null;

					// 显示成功消息
					window.parent.Alert(result.message || "奖励领取成功！");

					// 记录领取日志
					debug(`奖励领取成功!获得金币:${result.goldGained},获得元宝:${result.yuanbaoGained},获得经验:${result.experienceGained},获得物品:${itemsString}`);

				} else {
					// 领取失败
					window.parent.Alert(result.message || "奖励领取失败，请重试");

					// 重新启用按钮
					$("#claimRewardBtn").prop("disabled", false).html("<b>领取奖励</b>");
				}

			} catch (error) {
				console.error('领取奖励失败:', error);

				// 错误处理
				let message = "网络错误，奖励领取失败";
				if (error.message) {
					if (error.message.includes("过期")) {
						message = "战斗奖励已过期，请重新战斗";
					} else if (error.message.includes("权限")) {
						message = "无权领取此奖励";
					} else {
						message = error.message;
					}
				}

				window.parent.Alert(message);

				// 重新启用按钮
				$("#claimRewardBtn").prop("disabled", false).html("<b>领取奖励</b>");
			}
		}

		/**
		 * 宠物捕捉函数（重构版）
		 * 替代原有的window.external调用，使用异步API进行宠物捕捉
		 * @param {number} monsterId - 要捕捉的怪物ID
		 */
		async function buzhuo(monsterId) {
			try {
			
				// 隐藏捕捉面板，显示等待状态
				$(".捕捉面板").hide();
				$(".结果").html("正在捕捉宠物...");

				// 调用捕捉API
				const result = await BattleAPI.capturePet(map, monsterId);

				if (result.success) {
					// 捕捉成功
					$(".结果").html("捕捉成功,宠物已经放入了您的牧场!!");
					$(".获得金币").html("0");
					$(".获得元宝").html("0");
					$(".获得经验").html("0");
					$(".获得物品").html("无");

					window.parent.Alert("捕捉成功！宠物已放入牧场！");
				} else {
					// 捕捉失败
					$(".结果").html("捕捉失败!!");
					$(".获得金币").html("0");
					$(".获得元宝").html("0");
					$(".获得经验").html("0");
					$(".获得物品").html("无");

					window.parent.Alert(result.message || "捕捉失败");
				}

				// 显示结果面板
				$("#returnPage").show();
				$("#gj").hide();
				$(".奖励").show();

			} catch (error) {
				console.error('捕捉宠物失败:', error);

				// 错误处理
				$(".结果").html("捕捉失败!!");
				$("#returnPage").show();
				$("#gj").hide();
				$(".奖励").show();

				window.parent.Alert("网络错误，捕捉失败");
			}
		}

		// ===== 从Battle1.html复制的其他核心函数 =====
		/**
		 * 读取捕捉球数据
		 * 解析捕捉球JSON数据并生成捕捉界面
		 * @param {string} json - 捕捉球数据的JSON字符串
		 */
		function readBuzhuo(json) {
			var j = $.parseJSON(json);  // 解析捕捉球JSON数据

			// 捕捉球HTML模板
			var html = "<td height=\"28px\">" +
				"<table  style=\"cursor:pointer;\"  style=\"cursor:pointer\"  border=\"0\" cellspacing=\"0\" cellpadding=\"0\">" +
				"<tbody>" +
				"<tr class=\"buzhuoDIV\">" +
				"<td style=\"display:none\" width=\"10\" class=\"wpid\">{ID}</td>" +
				"<td width=\"10\"><img src=\"img/bk05.gif\" width=\"3\" height=\"22\"></td>" +
				"<td align=\"center\" background=\"img/bk.jpg\"><span>{Name}</span></td>" +
				"<td width=\"4\"><img src=\"img/bk06.gif\" width=\"4\" height=\"22\"></td>" +
				"</tr>" +
				"</tbody>" +
				"</table>" +
				"</td>";

			// 遍历捕捉球数据，生成HTML
			for (var i = 0; i < j.length; i++) {
				var ht = html.replace("{ID}", j[i].道具序号);    // 替换道具ID
				ht = ht.replace("{Name}", j[i].道具名字);        // 替换道具名称
				$(".捕捉球").html($(".捕捉球").html() + ht);
			}

			// 绑定捕捉球点击事件
			$(".buzhuoDIV").click(function () {
				var id = $(this).find(".wpid").html();  // 获取道具ID
				buzhuo(id);  // 执行捕捉
			});
		}

		/**
		 * 读取怪物数据
		 * 解析怪物JSON数据并更新怪物显示
		 * @param {string} json - 怪物数据的JSON字符串
		 */
		function readGuaiWu(json) {
			debug("读取怪物信息完毕。");
			var j = $.parseJSON(json);  // 解析怪物JSON数据
			gico = j.形象;              // 设置怪物形象编号
			if (j.宠物素材) {
				怪物调用宠物素材 = true;  // 设置怪物素材标志
				$("#gwdiv").html("<img src=\"Content/PetPhoto/z" + j.形象 + ".gif\" id=\"gw\">");
			} else {
				怪物调用宠物素材 = false; // 设置怪物素材标志
				$("#gwdiv").html("<img src=\"Content/gpc/z" + j.形象 + ".gif\" id=\"gw\">");
			}
		
			if (j.是否水平翻转) {
				$("#gw").css("filter", "FlipH Glow");
			}
			if (j.宠物名字.indexOf("魔化的") != -1) {
				$("#gw").css("filter", "XRay glow(color=#CCFF33,strength=5)");

				setInterval(function () {
					if (ys >= 地狱之门颜色.length) {
						ys = 0;
					}

					$("#gw").css("filter", "XRay glow(color=" + 地狱之门颜色[ys] + ",strength=5)");
					ys++;
				}, 100);
			}
			$(".怪物生命").html(showInt(j.生命));
			$(".怪物最大生命").html(showInt(j.最大生命));
			$(".怪物名字").html(j.宠物名字);
			$(".怪物等级").html(j.等级);
			$(".怪物五行").html(j.五行);
			gwMaxHp = j.最大生命;
			if (zidong == 1) window.parent.checkFightZD();
		}

		/**
		 * 显示PNG动画
		 * 控制PNG格式的宠物动画播放
		 * @param {string} t - 动画类型（"z"=攻击，"s"=受击，"g"=受击）
		 */
		function showPNG(t) {
			if (t == "g") t = "s";

			sI = 1;
			zI = 1;
			if (Init != 0) clearInterval(Init);
			if (t == "s") {
				Init = setInterval("play" + t + "()", stime);
			} else {
				Init = setInterval("play" + t + "()", 100);
			}

			if (形象 == 516) {
				// 特殊处理
			}
		}

		/**
		 * 播放攻击动画
		 * 播放宠物的攻击动画序列（PNG格式）
		 */
		function playz() {
			// 隐藏当前显示的帧
			$(".cw").addClass("yingbi").removeClass("cw");

			// 增加帧索引
			zI++;
			if (zI > zCount) {
				zI = 1;  // 重置到第1帧
			}

			// 显示新的帧
			$(".z" + zI).removeClass("yingbi").addClass("cw");
			
		}

		/**
		 * 播放受击动画
		 * 播放宠物的受击动画序列（PNG格式）
		 */
		function plays() {
			// 隐藏当前显示的帧
			$(".cw").addClass("yingbi").removeClass("cw");

			// 增加帧索引
			sI++;
			if (sI > sCount) {
				// 受击动画结束，回到攻击动画第1帧
				sI = 1;
				zI = 1;
				$(".z1").removeClass("yingbi").addClass("cw");
		
				return;
			}

			// 显示新的受击帧
			$(".s" + sI).removeClass("yingbi").addClass("cw");
	
		}

		/**
		 * 显示Flash宠物动画
		 * 显示Flash格式的宠物动画（兼容旧版本）
		 * @param {string} t - 动画类型（"z"=攻击，"g"=受击）
		 */
		function showFlashPet(t) {
			var petl = PngPet.split('|');  // 分割PNG宠物配置字符串

			// 查找当前宠物的PNG动画配置
			for (var i = 0; i < petl.length; i++) {
				var p = petl[i].split(',');  // 分割单个宠物配置
				if (形象 == p[0]) {          // 如果找到匹配的宠物形象
					zCount = p[1];           // 设置攻击动画帧数
					sCount = p[2];           // 设置受击动画帧数
					showPNG(t);              // 显示PNG动画
					return;
				}
			}

			png_ = true;  // 启用PNG模式

			// Flash动画的位置和样式配置
			var left = "0";                              // 左边距
			var height = "315";                          // 高度
			var top = "0";                               // 上边距
			var f = "z-index:999;position:absolute";     // 基础样式

			// 根据动画类型调整样式
			if (t == "g") { f = "z-index:99999;position:absolute" }  // 受击动画层级更高
			if (形象 < 514) { left = "-250"; height = "200"; top = "100" }  // 旧版宠物位置调整

			// 生成Flash对象HTML代码
			var fhtml = "<div style='margin-top:" + top + "px;margin-left:" + left + "px;" + f + "'><object classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000' id='sw' width='988' height='" + height + "' codebase='http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab'>" +
				"<param name='movie' value='Content/FlashPet/" + t + 形象 + ".swf'>" +
				"<param name='allowScriptAccess' value='always' />  " +
				"<param name='allowFullScreen' value='false' />  " +
				"<param name='quality' value='high' />  " +
				"<param name='wmode' value='transparent' />" +
				"<embed src='Content/FlashPet/" + t + 形象 + ".swf' quality='high' width='988' height='" + height + "' name='sw' align='middle' play='true' loop='false' allowScriptAccess='always' type='application/x-shockwave-flash' wmode='transparent' pluginspage='http://www.macromedia.com/go/getflashplayer'></object></div>";

			$("#info_page1").html(fhtml);  // 插入Flash动画HTML
		}

		/**
		 * Flash启动函数
		 * Flash动画系统的启动函数（兼容性保留）
		 */
		function start_Flash() {
			// Flash启动函数
		}

		/**
		 * 读取宠物数据
		 * 解析宠物JSON数据并更新宠物显示和技能列表
		 * @param {string} j - 宠物数据的JSON字符串
		 */
		function readPet(j) {
			if (zidong == 1) window.parent.checkFightZD();  // 自动战斗状态检查
			
			debug("读取宠物信息完毕。");
			
			var json = $.parseJSON(j);  // 解析宠物JSON数据

			// 如果有指定形象，则使用指定形象
			if (json.指定形象 != null && json.指定形象 != "") json.形象 = json.指定形象;
			形象 = json.形象;  // 设置全局宠物形象变量

			// 保存宠物编号（用于战斗API调用）
			currentPetNo = json.宠物编号 || json.petNo || json.PetNo || 形象;
			currentPetId = json.宠物id;
			
			$(".生命").html(showInt(json.生命));

			$(".魔法").html(showInt(json.魔法));

			// 检查是否为特殊五行宠物，使用PNG动画
			if ((json.五行 == "聖" || json.五行 == "佛" || json.五行 == "萌" || json.五行 == "灵" || json.五行 == "次元" || json.五行 == "巫") && (形象 >= 503 && 形象 != 1000)) {
				wuxing = json.五行;
				调用宠物素材 = true;  // 标记使用宠物素材
				showFlashPet("z");
				if (png_) {

					// 加载动态动画
					for (var i = 0; i <= zCount; i++) {
						$("#info_page").prepend("<img class='yingbi z" + i + "' src='Content/PetPhoto/z" + 形象 + "_" + i + ".png'>");
					}
			
					// 预加载攻击画帧
					for (var i = 0; i <= sCount; i++) {
						$("#info_page").prepend("<img class='yingbi s" + i + "' src='Content/PetPhoto/s" + 形象 + "_" + i + ".png'>");
					}

					// 显示第一帧作为初始状态
					$(".z1").removeClass("yingbi").addClass("cw");
			
				}
			}
			else {
				// 普通宠物，尝试使用PNG动画帧
				调用宠物素材 = true;  // 先尝试使用PNG

				// 加载动态动画
				for (var i = 0; i <= zCount; i++) {
					$("#info_page").prepend("<img class='yingbi z" + i + "' src='Content/PetPhoto/z" + 形象 + "_" + i + ".png'>");
				}
				// 预加载攻击画帧
				for (var i = 0; i <= sCount; i++) {
					$("#info_page").prepend("<img class='yingbi s" + i + "' src='Content/PetPhoto/s" + 形象 + "_" + i + ".png'>");
				}

				// 显示第一帧作为初始状态
				$(".z1").removeClass("yingbi").addClass("cw");

				// 如果PNG加载失败，回退到GIF
				$(".z1").on('error', function() {
				
					调用宠物素材 = false;

					// 清除所有PNG帧
					$("#info_page").find("img[class*='z'], img[class*='s']").remove();

					// 加载GIF动画
					if (json.形象 >= 1000) {
						$("#info_page").html("<img style=\"width: 200px;height: 200px;margin-top: -30px;margin-left: 20px;\" src=\"Content/PetPhoto/z" + json.形象 + ".gif\" id=\"cw\">");
					} else {
						$("#info_page").html("<img src=\"Content/PetPhoto/z" + json.形象 + ".gif\" id=\"cw\">");
					}
					
				});
			}

			if (json.五行 != "聖" && json.五行 != "佛" && json.五行 != "萌" && json.五行 != "灵" && json.五行 != "次元" && json.五行 != "巫" || (json.形象 < 503 && json.形象 != 1000)) {
				$(".头像").html("<img style=\" height:36px; width:36px;\" src=\"Content/PetPhoto/t" + json.形象 + ".gif\" id=\"tx\">");
			} else {
				$(".头像").html("<img style=\" height:36px; width:36px;\" src=\"Content/PetPhoto/t" + json.形象 + ".png\" id=\"tx\">");
			}

			ico = json.形象;
			$(".最大生命").html(showInt(json.最大生命));
			$(".最大魔法").html(showInt(json.最大魔法));
			$(".宠物等级").html(json.等级);
			// 直接显示经验值（使用后端返回的升级经验）
			$(".当前经验").html(showInt(json.当前经验));
			$(".宠物名字").html(json.宠物名字);

			// 技能列表HTML模板
			var html = "<td style='float:left' height=\"28px\" >" +
				"<table  style=\"cursor:pointer;\"  style=\"cursor:pointer\"  border=\"0\" cellspacing=\"0\" cellpadding=\"0\">" +
				"<tbody>" +
				"<tr class=\"jinengDIV\">" +
				"<td style=\"display:none\" width=\"10\" class=\"wpid\">{ID}</td>" +
				"<td style=\"display:none\" width=\"10\" class=\"wpnm\">{Name}</td>" +
				"<td style=\"display:none\" width=\"10\" class=\"wpmp\">{MP}</td>" +
				"<td width=\"10\"><img src=\"img/bk05.gif\" width=\"3\" height=\"22\"></td>" +
				"<td align=\"center\" background=\"img/bk.jpg\"><span>{Name}</span></td>" +
				"<td width=\"4\"><img src=\"img/bk06.gif\" width=\"4\" height=\"22\"></td>" +
				"</tr>" +
				"</tbody>" +
				"</table>" +
				"</td>";

			var 技能 = json.技能显示.split(",");  // 分割技能字符串
			var shtml;  // 技能HTML临时变量

			// 遍历技能列表，生成技能选项
			for (var i = 0; i < 技能.length; i++) {
				var 技能组 = 技能[i].split("|");  // 分割单个技能信息
				if (技能组.length >= 2) {
					shtml = html.replace("{ID}", 技能组[2]);    // 技能ID
					shtml = shtml.replace("{Name}", 技能组[0]);  // 技能名称（第一次替换）
					shtml = shtml.replace("{Name}", 技能组[0]);  // 技能名称（第二次替换）
					shtml = shtml.replace("{MP}", 技能组[3]);    // 技能MP消耗

					// 如果技能不是隐藏的，则添加到技能列表
					if (技能组[4] != "true") {
						$(".技能列表").html($(".技能列表").html() + shtml);
					}
				}
			}

			// 添加普通攻击选项
			shtml = html.replace("{ID}", null);           // 普通攻击无技能ID
			shtml = shtml.replace("{Name}", "普通攻击");   // 技能名称
			shtml = shtml.replace("{Name}", "普通攻击");   // 技能名称（第二次替换）
			shtml = shtml.replace("{MP}", "0");           // 普通攻击不消耗MP
			$(".技能列表").html($(".技能列表").html() + shtml);

			// 绑定技能面板点击事件
			$(".技能面板 .jinengDIV").click(function () {
				var id = $(this).find(".wpid").html();   // 获取技能ID
				var xmp = $(this).find(".wpmp").html();  // 获取技能MP消耗
				$(".技能面板").hide();

				// 检查MP是否足够
				if (parseInt(cwMp) < parseInt(xmp)) {
					window.parent.Alert("主人我魔法值不够啦~~QWQ");
					return;
				}
				gongji(id, $(this).find(".wpnm").html());  // 执行攻击
			});

			// 绑定设置面板点击事件（设置自动战斗技能）
			$(".设置面板 .jinengDIV").click(function () {
				var id = $(this).find(".wpid").html();    // 获取技能ID
				var name = $(this).find(".wpnm").html();  // 获取技能名称
				$(".设置面板").hide();
				window.parent.setJN(id, name);            // 设置自动技能
				window.parent.Alert("设置自动技能成功.");
			});

			// 使用后端返回的升级经验值
			cwMaxEXP = json.升级经验;  // 从后端获取升级经验
			cwHp = json.生命;
			cwMaxHp = json.最大生命;
			cwMp = json.魔法;
			cwMaxMp = json.最大魔法;
			cwEXP = json.当前经验;
			$(".升级经验").html(showInt(cwMaxEXP));
			updateUI();
		}

		/**
		 * 更新用户界面
		 * 更新宠物的HP、MP、EXP条显示
		 */
		function updateUI() {
			// 计算并更新HP条宽度
			var returnWidth = parseInt(cwHp) / parseInt(cwMaxHp);  // HP百分比
			if (returnWidth <= 0) {
				returnWidth = "1px";  // 最小宽度1px
			}

			console.log('更新玩家HP条:', {
				cwHp,
				cwMaxHp,
				returnWidth
			});

			$("#php").width(155 * returnWidth);  // 155px是HP条的最大宽度

			// 计算并更新MP条宽度
			returnWidth = parseInt(cwMp) / parseInt(cwMaxMp);  // MP百分比
			if (returnWidth <= 0) {
				returnWidth = "1px";  // 最小宽度1px
			}
			$("#pmp").width(155 * returnWidth);  // 155px是MP条的最大宽度

			// 计算并更新EXP条宽度
			returnWidth = parseInt(cwEXP) / parseInt(cwMaxEXP);  // EXP百分比
			if (returnWidth <= 0) {
				returnWidth = "1px";  // 最小宽度1px
			}
			$("#pexp").width(155 * returnWidth);  // 155px是EXP条的最大宽度
		}

		/**
		 * 获取Flash影片对象
		 * 跨浏览器获取Flash影片对象的兼容性函数
		 * @param {string} movieName - Flash影片的名称
		 * @returns {Object} Flash影片对象
		 */
		function thisMovie(movieName) {
			if (navigator.appName.indexOf("Microsoft") != -1) {
				return window[movieName];
			} else {
				return document.embeds[movieName];
			}
		}

		/**
		 * 播放状态控制
		 * Flash动画的状态控制函数（兼容性保留）
		 * @param {string} i - 状态标识（"Show", "inAttack", "Attack"）
		 */
		function playState(i) {
			if (i == "Show") {
				$("#gj").show();
			} else if (i == "inAttack") {
				$("#gj").show();
			} else if (i == "Attack") {
				if (parseInt(gwHp) <= 0) {
					$("#returnPage").show();
					$("#gj").hide();
				} else {
					fj();
					thisMovie("sw").inAttack();
				}
			}
		}

		/**
		 * 停止战斗
		 * 检测到异常操作时停止战斗并返回主界面
		 */
		function stopBattle() {
			window.parent.Load(1);
			window.parent.Alert("请不要战斗频繁,如果检测到您正在使用变速齿轮,我们将会做出相应惩罚.");
		}

		// ===== 页面初始化 =====

		/**
		 * 获取当前用户ID
		 */
		function getCurrentUserId() {
			// 从URL参数获取
			const urlParams = new URLSearchParams(window.location.search);
			const userIdFromUrl = urlParams.get('userId');
			if (userIdFromUrl) {
				return parseInt(userIdFromUrl);
			}

			// 从parent获取
			if (typeof parent !== 'undefined' && parent.currentUserId) {
				return parent.currentUserId;
			}

			// 默认用户ID
			return 1;
		}


		/**
		 * 从URL参数获取地图ID和宠物ID
		 * 解析URL中的mapId和petId参数
		 */
		function parseUrlParameters() {
		
			
			try {
				const urlParams = new URLSearchParams(window.location.search);

				// 获取地图ID
				const mapIdParam = urlParams.get('mapId');
				if (mapIdParam) {
					currentMapId = parseInt(mapIdParam);
					map = currentMapId;  // 同时设置旧的map变量
					
					
				} else {
					console.warn('URL中未找到mapId参数');
				}

				// 获取宠物ID
				const petIdParam = urlParams.get('petId');
				if (petIdParam) {
					currentPetId = parseInt(petIdParam);
					
				} else {
					console.warn('URL中未找到petId参数');
				}

				// 尝试从parent获取参数（备用方案）
				if (!currentMapId && typeof parent !== 'undefined' && parent.currentMapId) {
					currentMapId = parent.currentMapId;
					map = currentMapId;
			
					
				}

				if (!currentPetId && typeof parent !== 'undefined' && parent.selectedPetId) {
					currentPetId = parent.selectedPetId;
				
					
				}

			} catch (error) {
				console.error('解析URL参数时出错:', error);
			}
		}

		/**
		 * 强制停止所有动画
		 * 彻底清除所有动画状态和定时器
		 */
		function forceStopAllAnimations() {
	
			// 清除所有可能的定时器ID（防止定时器泄漏）
			for (let i = 1; i <= 10000; i++) {
				clearInterval(i);
			}

			// 重置全局动画变量
			Init = 0;
			zI = 1;
			sI = 1;

		}

		/**
		 * 初始化宠物信息
		 * 获取宠物数据并调用readPet函数进行初始化
		 */
		async function initializePetInfo() {


			try {
				// 获取用户ID
				const userId = getCurrentUserId();


				// 调用API获取宠物信息
				const response = await fetch(`/api/Player/GetMainPet?userId=${userId}`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'Accept': 'application/json'
					}
				});

				if (!response.ok) {
					throw new Error(`API调用失败，状态码: ${response.status}`);
				}

				const result = await response.json();
			

				// 检查API返回的数据结构
				if (!result || !result.success || !result.petInfo) {
					throw new Error('API返回数据格式不正确');
				}

				// 直接使用API返回的petInfo，它已经是正确的格式
				readPet(JSON.stringify(result.petInfo));
			
			} catch (error) {
				console.error('初始化宠物信息失败:', error);
				throw error;
			}
		}

		/**
		 * 初始化怪物信息
		 * 根据地图ID获取怪物数据并调用readGuaiWu函数进行初始化
		 */
		async function initializeMonsterInfo() {

			try {
				// 获取地图ID
				const mapId = currentMapId || map;
				if (!mapId) {
					throw new Error('地图ID未设置，无法获取怪物信息');
				}

				// 调用API获取怪物信息
				const response = await fetch(`/api/Map/${mapId}/monsters`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'Accept': 'application/json'
					}
				});

				if (!response.ok) {
					throw new Error(`怪物API调用失败，状态码: ${response.status}`);
				}

				const monsters = await response.json();

				// 检查API返回的数据结构
				if (!monsters || !Array.isArray(monsters) || monsters.length === 0) {
					throw new Error('怪物API返回数据格式不正确或无怪物数据');
				}

				// 保存所有可用怪物到全局变量
				availableMonsters = monsters;
				currentMonsterIndex = 0; // 从第一个怪物开始

				console.log(`地图 ${mapId} 加载了 ${monsters.length} 个怪物:`, monsters.map(m => m.monsterName || m.宠物名字));

				// 显示第一个怪物
				displayCurrentMonster();

			} catch (error) {
				console.error('初始化怪物信息失败:', error);

				// 使用默认怪物数据作为备用方案
				availableMonsters = [{
					monsterId: 73,
					monsterName: "野生怪物",
					currentLevel: 1,
					element: "无",
					minLevel: 1,
					maxLevel: 1
				}];
				currentMonsterIndex = 0;

				displayCurrentMonster();
			}
		}

		/**
		 * 显示当前怪物
		 * 根据当前怪物索引显示对应的怪物信息
		 */
		function displayCurrentMonster() {
			if (!availableMonsters || availableMonsters.length === 0) {
				console.error('没有可用的怪物数据');
				return;
			}

			const monster = availableMonsters[currentMonsterIndex];
			currentMonsterId = monster.monsterId || monster.形象 || 73;

			console.log(`显示怪物 ${currentMonsterIndex + 1}/${availableMonsters.length}: ${monster.monsterName || monster.宠物名字} (ID: ${currentMonsterId})`);

			// 转换为readGuaiWu期望的格式
			const monsterData = {
				"形象": currentMonsterId,
				"生命": 1000, // 默认生命值，实际应该根据等级计算
				"最大生命": 1000,
				"宠物名字": monster.monsterName || monster.宠物名字 || "野生怪物",
				"等级": monster.currentLevel || Math.floor((monster.minLevel + monster.maxLevel) / 2) || 1,
				"五行": monster.element || monster.五行 || "无",
				"宠物素材": false // 怪物通常不使用宠物素材
			};

			// 调用readGuaiWu函数初始化怪物显示
			readGuaiWu(JSON.stringify(monsterData));
		}

		/**
		 * 获取当前怪物ID
		 * 返回当前要战斗的怪物ID
		 */
		function getCurrentMonsterId() {
			return currentMonsterId;
		}

		/**
		 * 切换到下一个怪物
		 * 战斗结束后切换到下一个怪物，如果到达末尾则回到第一个
		 */
		function switchToNextMonster() {
			if (!availableMonsters || availableMonsters.length === 0) {
				console.error('没有可用的怪物数据，无法切换');
				return;
			}

			// 切换到下一个怪物
			currentMonsterIndex = (currentMonsterIndex + 1) % availableMonsters.length;

			console.log(`切换到下一个怪物: ${currentMonsterIndex + 1}/${availableMonsters.length}`);

			// 显示新的怪物
			displayCurrentMonster();
		}

		/**
		 * 关闭战斗结果弹框
		 * 用户点击"继续探险"时关闭弹框并切换到下一只怪物
		 */
		function closeBattleResultDialog() {
			console.log('用户选择继续探险，关闭弹框并切换到下一只怪物');

			// 隐藏战斗结果弹框 (正确的ID是 returnPage)
			$("#returnPage").hide();

			// 标记不再是第一次战斗，后续战斗使用5秒倒计时
			isFirstBattle = false;

			// 用户选择继续探险时才切换到下一只怪物
			switchToNextMonster();

			// 重置战斗状态，准备下一次战斗
			or = true;  // 允许新的战斗
			gj = false; // 重置战斗进行标志

			// 重置战斗倒计时（现在会使用5秒）
			resetBattleTimer();

			// 启动倒计时器
			startBattleTimer();

			console.log(`切换完成，当前怪物: ${currentMonsterIndex + 1}/${availableMonsters.length} - ${availableMonsters[currentMonsterIndex]?.monsterName || '未知怪物'}`);
		}

		/**
		 * 初始化动画状态
		 * 确保页面加载时动画处于正确的初始状态
		 */
		function initializeAnimationState() {
	
			// 先强制停止所有动画
			forceStopAllAnimations();

			// 停止所有可能的动画
			// 隐藏所有攻击动画帧
			for (let i = 1; i <= 20; i++) {
				$(".z" + i).addClass("yingbi").removeClass("cw");
			}

			// 隐藏所有受击动画帧
			for (let i = 1; i <= 20; i++) {
				$(".s" + i).addClass("yingbi").removeClass("cw");
			}

			// 只显示第一帧作为静态状态
			$(".z1").removeClass("yingbi").addClass("cw");

			// 重置战斗倒计时
			resetBattleTimer();

			// 启动倒计时器（在清除所有定时器后重新启动）
			startBattleTimer();

		}

		/**
		 * 重置战斗倒计时
		 * 第一次战斗10秒，后续战斗5秒
		 */
		function resetBattleTimer() {
			console.log('重置倒计时，当前t值:', t, '是否第一次战斗:', isFirstBattle);

			if (isFirstBattle) {
				t = 10;  // 第一次战斗10秒
				console.log('第一次战斗，设置倒计时为10秒');
			} else {
				t = 5;   // 后续战斗5秒
				console.log('后续战斗，设置倒计时为5秒');
			}

			$("#timev").html(t);  // 更新显示
			console.log('重置完成，新t值:', t);
		}

		// 全局倒计时器变量
		var battleTimerInterval = null;

		/**
		 * 启动战斗倒计时器
		 * 启动每秒执行的倒计时器
		 */
		function startBattleTimer() {
			console.log('启动倒计时器，当前t值:', t, '是否第一次战斗:', isFirstBattle);
			// 先清除可能存在的倒计时器
			if (battleTimerInterval) {
				clearInterval(battleTimerInterval);
				battleTimerInterval = null;
			}

			// 战斗倒计时器 - 每秒更新倒计时显示，倒计时结束时自动攻击
			battleTimerInterval = setInterval(function () {
				// 如果正在战斗中，不进行倒计时，减少日志输出
				if (gj) {
					$("#timev").html("PK");  // 显示战斗状态
					return;
				}
				if (t <= 0) {  // 倒计时结束
					$("#timev").html("PK");  // 显示PK标识
					return;
				}

				t = t - 1;  // 倒计时减1
				$("#timev").html(t);  // 更新倒计时显示

				if (t == 0) {  // 倒计时归零时自动攻击
			          gongji(window.parent.jid, window.parent.jname);
				}
			}, 1000);  // 每1000毫秒（1秒）执行一次
		}

		/**
		 * 停止战斗倒计时器
		 * 清除倒计时器并显示等待状态
		 */
		function stopBattleTimer() {
			if (battleTimerInterval) {
				clearInterval(battleTimerInterval);
				battleTimerInterval = null;
			}
			$("#timev").html("");  // 显示等待状态
			console.log('倒计时已停止，等待用户选择');
		}

		/**
		 * 延迟显示攻击按钮
		 * 页面加载完成后延迟显示战斗操作按钮
		 */
		setTimeout(function () {
			// 先解析URL参数
			parseUrlParameters();

			// 然后初始化动画状态
			initializeAnimationState();

			// 最后显示攻击按钮
			$("#gj").show();
		}, 1055);

		/**
		 * 自动战斗检查
		 * 检查是否需要启动自动战斗模式
		 */
		setTimeout(function () {
			if (map == 0) {
				window.parent.zidong1();
			}
		}, 1000);

		/**
		 * 页面就绪事件处理
		 * 设置自动战斗和倒计时功能
		 */
		$(function () {

			// 首先解析URL参数
			parseUrlParameters();

			// 根据地图ID加载背景图片
			loadBackgroundByMapId(currentMapId || map || window.parent.currentMapId);

			// 异步初始化宠物和怪物信息，然后初始化动画状态
			Promise.all([
				initializePetInfo(),
				initializeMonsterInfo()
			]).then(() => {

				initializeAnimationState();
			}).catch((error) => {

				initializeAnimationState();
			});

			// 添加定期检查，确保动画不会意外启动
			setInterval(function() {
				if (Init != 0 && !or) {  // 如果有动画在运行但不在战斗状态
					
					forceStopAllAnimations();
					initializeAnimationState();
				}
			}, 1000);  // 每秒检查一次

			/**
			 * 自动战斗延迟启动
			 * 页面加载完成后延迟2秒检查是否需要自动战斗
			 */
			setTimeout(function () {
				if (zidong == 1 && or) {  // 如果开启自动战斗且状态允许
					gongji(window.parent.jid, window.parent.jname);  // 使用父窗口设置的技能攻击
				}
			}, 2000);  // 延迟2秒执行

		});
	</script>
	
</body>

</html>