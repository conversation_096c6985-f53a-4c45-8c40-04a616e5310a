﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///怪物配置表
    ///</summary>
    [SugarTable("monster_config")]
    public partial class monster_config
    {
           public monster_config(){


           }
           /// <summary>
           /// Desc:自增长主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:怪物编号（原始编号）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int monster_no {get;set;}

           /// <summary>
           /// Desc:技能编号（可为空）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? skill {get;set;}

           /// <summary>
           /// Desc:怪物名称
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string name {get;set;}

           /// <summary>
           /// Desc:怪物属性（如金、木、水、火、土、神等）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string attribute {get;set;}

    }
}
