using System;
using System.ComponentModel.DataAnnotations;

namespace WebApplication_HM.DTOs
{
    /// <summary>
    /// 进化请求DTO
    /// </summary>
    public class EvolutionRequestDto
    {
        /// <summary>
        /// 用户宠物ID
        /// </summary>
        [Required(ErrorMessage = "宠物ID不能为空")]
        public int UserPetId { get; set; }

        /// <summary>
        /// 进化类型（A或B）
        /// </summary>
        [Required(ErrorMessage = "进化类型不能为空")]
        [RegularExpression("^[AB]$", ErrorMessage = "进化类型只能是A或B")]
        public string EvolutionType { get; set; }
    }

    /// <summary>
    /// 进化结果DTO
    /// </summary>
    public class EvolutionResultDto
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 成长增加值
        /// </summary>
        public decimal GrowthIncrease { get; set; }

        /// <summary>
        /// 新的成长值
        /// </summary>
        public decimal NewGrowth { get; set; }

        /// <summary>
        /// 进化前宠物编号
        /// </summary>
        public int BeforePetNo { get; set; }

        /// <summary>
        /// 进化后宠物编号
        /// </summary>
        public int AfterPetNo { get; set; }

        /// <summary>
        /// 消耗的金币
        /// </summary>
        public long CostGold { get; set; }

        /// <summary>
        /// 使用的道具ID
        /// </summary>
        public string UsedItemId { get; set; }
    }

    /// <summary>
    /// 进化配置DTO
    /// </summary>
    public class EvolutionConfigDto
    {
        /// <summary>
        /// 进化类型
        /// </summary>
        public string EvolutionType { get; set; }

        /// <summary>
        /// 进化类型名称
        /// </summary>
        public string EvolutionTypeName { get; set; }

        /// <summary>
        /// 目标宠物编号
        /// </summary>
        public int TargetPetNo { get; set; }

        /// <summary>
        /// 所需等级
        /// </summary>
        public int RequiredLevel { get; set; }

        /// <summary>
        /// 消耗金币
        /// </summary>
        public long CostGold { get; set; }

        /// <summary>
        /// 所需道具ID
        /// </summary>
        public string RequiredItemId { get; set; }

        /// <summary>
        /// 所需道具名称
        /// </summary>
        public string ItemName { get; set; }

        /// <summary>
        /// 所需道具数量
        /// </summary>
        public int RequiredItemCount { get; set; }

        /// <summary>
        /// 最小成长增加
        /// </summary>
        public decimal GrowthMin { get; set; }

        /// <summary>
        /// 最大成长增加
        /// </summary>
        public decimal GrowthMax { get; set; }

        /// <summary>
        /// 成功率
        /// </summary>
        public decimal SuccessRate { get; set; }

        /// <summary>
        /// 是否可以进化
        /// </summary>
        public bool CanEvolve { get; set; }

        /// <summary>
        /// 不能进化的原因
        /// </summary>
        public string CannotEvolveReason { get; set; }
    }

    /// <summary>
    /// 宠物进化信息DTO
    /// </summary>
    public class PetEvolutionInfoDto
    {
        /// <summary>
        /// 宠物ID
        /// </summary>
        public int PetId { get; set; }

        /// <summary>
        /// 宠物名称
        /// </summary>
        public string PetName { get; set; }

        /// <summary>
        /// 宠物编号
        /// </summary>
        public int PetNo { get; set; }

        /// <summary>
        /// 当前等级
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 当前成长
        /// </summary>
        public decimal Growth { get; set; }

        /// <summary>
        /// 已进化次数
        /// </summary>
        public int EvolveCount { get; set; }

        /// <summary>
        /// 最大进化次数
        /// </summary>
        public int MaxEvolveCount { get; set; } = 10;

        /// <summary>
        /// 宠物属性/五行
        /// </summary>
        public string Element { get; set; }

        /// <summary>
        /// 可用的进化路线
        /// </summary>
        public List<EvolutionConfigDto> AvailableEvolutions { get; set; } = new List<EvolutionConfigDto>();
    }

    /// <summary>
    /// 进化历史记录DTO
    /// </summary>
    public class EvolutionHistoryDto
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 进化类型
        /// </summary>
        public string EvolutionType { get; set; }

        /// <summary>
        /// 进化前宠物编号
        /// </summary>
        public int BeforePetNo { get; set; }

        /// <summary>
        /// 进化后宠物编号
        /// </summary>
        public int AfterPetNo { get; set; }

        /// <summary>
        /// 成长增加值
        /// </summary>
        public decimal GrowthIncrease { get; set; }

        /// <summary>
        /// 消耗金币
        /// </summary>
        public long CostGold { get; set; }

        /// <summary>
        /// 使用道具
        /// </summary>
        public string UsedItemName { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 进化时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }
}
