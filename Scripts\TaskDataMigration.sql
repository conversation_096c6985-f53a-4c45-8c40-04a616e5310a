-- 任务系统数据迁移脚本
-- 说明: 将WindowsFormsApplication7的任务数据迁移到新的数据库结构
-- 创建时间: 2025-07-24

-- 1. 创建临时表用于存储原始任务数据
CREATE TEMPORARY TABLE IF NOT EXISTS temp_original_tasks (
    task_id VARCHAR(50),
    task_name VARCHAR(200),
    task_description TEXT,
    task_objectives JSON,
    required_pet VARCHAR(50),
    reward_config TEXT,
    is_repeatable TINYINT DEFAULT 0,
    prerequisite_task VARCHAR(50),
    is_network_task TINYINT DEFAULT 0,
    sort_order INT DEFAULT 0
);

-- 2. 示例数据插入 - 基于原系统的任务类型
INSERT INTO temp_original_tasks (task_id, task_name, task_description, task_objectives, reward_config, is_repeatable, sort_order) VALUES
-- 新手任务
('TASK_001', '新手指引', '完成基础操作指引', 
 '[{"Type":"等级","Num":"5","ID":"-1"}]', 
 '[{"Type":"道具","Id":"1001","Amount":10},{"Type":"金币","Id":"gold","Amount":1000}]', 
 0, 1),

-- 击杀任务
('TASK_002', '清理怪物', '击杀指定数量的史莱姆', 
 '[{"Type":"击杀","Num":"10","ID":"1001"}]', 
 '[{"Type":"金币","Id":"gold","Amount":500},{"Type":"经验","Id":"exp","Amount":100}]', 
 1, 2),

-- 收集任务
('TASK_003', '收集材料', '收集10个草药', 
 '[{"Type":"收集","Num":"10","ID":"2001"}]', 
 '[{"Type":"道具","Id":"1002","Amount":5}]', 
 1, 3),

-- 复合任务
('TASK_004', '综合训练', '击杀怪物并收集材料', 
 '[{"Type":"击杀","Num":"5","ID":"1002"},{"Type":"收集","Num":"5","ID":"2002"}]', 
 '[{"Type":"装备","Id":"3001","Amount":1},{"Type":"元宝","Id":"diamond","Amount":100}]', 
 0, 4),

-- VIP任务
('TASK_005', 'VIP特权', '达到VIP等级2', 
 '[{"Type":"VIP","Num":"2","ID":"-1"}]', 
 '[{"Type":"道具","Id":"1003","Amount":1},{"Type":"水晶","Id":"crystal","Amount":50}]', 
 0, 5),

-- 宠物成长任务
('TASK_006', '宠物培养', '主宠达到100CC成长', 
 '[{"Type":"主宠达到成长","Num":"100","ID":"-1"}]', 
 '[{"Type":"道具","Id":"1004","Amount":3}]', 
 0, 6),

-- 时间限制任务
('TASK_007', '限时活动', '在指定时间内完成任务', 
 '[{"Type":"时间","Num":"20251231","ID":"-1"},{"Type":"击杀","Num":"20","ID":"1003"}]', 
 '[{"Type":"道具","Id":"1005","Amount":1}]', 
 0, 7),

-- 副本任务
('TASK_008', '挑战地狱', '地狱之门达到10层', 
 '[{"Type":"地狱","Num":"10","ID":"-1"}]', 
 '[{"Type":"装备","Id":"3002","Amount":1}]', 
 0, 8),

-- 一键完成任务
('TASK_009', '快速完成', '使用特殊道具快速完成', 
 '[{"Type":"一键完成道具","Num":"1","ID":"9001"}]', 
 '[{"Type":"金币","Id":"gold","Amount":2000}]', 
 1, 9),

-- 前置任务示例
('TASK_010', '进阶训练', '完成新手指引后的进阶任务', 
 '[{"Type":"等级","Num":"10","ID":"-1"}]', 
 '[{"Type":"道具","Id":"1006","Amount":1}]', 
 0, 10);

-- 设置前置任务关系
UPDATE temp_original_tasks SET prerequisite_task = 'TASK_001' WHERE task_id = 'TASK_010';

-- 3. 迁移任务配置数据
INSERT INTO task_config (
    task_id, task_name, task_description, task_type, is_repeatable, 
    prerequisite_task, required_pet, reward_config, is_network_task, 
    is_active, sort_order, created_at, updated_at
)
SELECT 
    task_id,
    task_name,
    task_description,
    CASE 
        WHEN is_repeatable = 1 THEN 1  -- 循环任务
        WHEN task_name LIKE '%活动%' OR task_name LIKE '%限时%' THEN 2  -- 活动任务
        ELSE 0  -- 普通任务
    END as task_type,
    is_repeatable,
    prerequisite_task,
    CASE WHEN required_pet = '' OR required_pet = '-1' THEN NULL ELSE required_pet END,
    reward_config,
    is_network_task,
    1 as is_active,
    sort_order,
    NOW() as created_at,
    NOW() as updated_at
FROM temp_original_tasks
ON DUPLICATE KEY UPDATE
    task_name = VALUES(task_name),
    task_description = VALUES(task_description),
    task_type = VALUES(task_type),
    is_repeatable = VALUES(is_repeatable),
    prerequisite_task = VALUES(prerequisite_task),
    required_pet = VALUES(required_pet),
    reward_config = VALUES(reward_config),
    is_network_task = VALUES(is_network_task),
    sort_order = VALUES(sort_order),
    updated_at = NOW();

-- 4. 迁移任务目标数据
-- 创建临时表用于解析目标数据
CREATE TEMPORARY TABLE IF NOT EXISTS temp_objectives (
    task_id VARCHAR(50),
    objective_type VARCHAR(50),
    target_id VARCHAR(50),
    target_amount INT,
    objective_order INT,
    objective_description VARCHAR(500)
);

-- 插入解析后的目标数据 (这里需要根据实际的JSON数据进行调整)
INSERT INTO temp_objectives (task_id, objective_type, target_id, target_amount, objective_order, objective_description) VALUES
-- TASK_001 目标
('TASK_001', 'REACH_LEVEL', '-1', 5, 1, '宠物等级达到5级'),

-- TASK_002 目标
('TASK_002', 'KILL_MONSTER', '1001', 10, 1, '击杀史莱姆10个'),

-- TASK_003 目标
('TASK_003', 'COLLECT_ITEM', '2001', 10, 1, '收集草药10个'),

-- TASK_004 目标
('TASK_004', 'KILL_MONSTER', '1002', 5, 1, '击杀哥布林5个'),
('TASK_004', 'COLLECT_ITEM', '2002', 5, 2, '收集铁矿5个'),

-- TASK_005 目标
('TASK_005', 'VIP_LEVEL', '-1', 2, 1, 'VIP等级达到2级'),

-- TASK_006 目标
('TASK_006', 'PET_GROWTH', '-1', 100, 1, '主宠成长达到100CC'),

-- TASK_007 目标
('TASK_007', 'TIME_LIMIT', '-1', 20251231, 1, '在2025年12月31日前完成'),
('TASK_007', 'KILL_MONSTER', '1003', 20, 2, '击杀骷髅20个'),

-- TASK_008 目标
('TASK_008', 'DUNGEON', 'hell', 10, 1, '地狱之门达到10层'),

-- TASK_009 目标
('TASK_009', 'ONE_CLICK', '9001', 1, 1, '使用快速完成道具1个'),

-- TASK_010 目标
('TASK_010', 'REACH_LEVEL', '-1', 10, 1, '宠物等级达到10级');

-- 插入任务目标数据
INSERT INTO task_objective (
    task_id, objective_type, target_id, target_amount, 
    objective_order, objective_description, created_at
)
SELECT 
    task_id,
    objective_type,
    CASE WHEN target_id = '-1' THEN NULL ELSE target_id END,
    target_amount,
    objective_order,
    objective_description,
    NOW()
FROM temp_objectives
ON DUPLICATE KEY UPDATE
    objective_type = VALUES(objective_type),
    target_id = VALUES(target_id),
    target_amount = VALUES(target_amount),
    objective_order = VALUES(objective_order),
    objective_description = VALUES(objective_description);

-- 5. 创建示例用户任务数据 (可选，用于测试)
-- 假设用户ID为1的用户已接取了一些任务
INSERT INTO user_task (user_id, task_id, task_status, accepted_at, completion_count) VALUES
(1, 'TASK_001', 0, DATE_SUB(NOW(), INTERVAL 1 DAY), 1),  -- 已完成
(1, 'TASK_002', 1, DATE_SUB(NOW(), INTERVAL 2 HOUR), 0), -- 进行中
(1, 'TASK_003', 1, DATE_SUB(NOW(), INTERVAL 1 HOUR), 0)  -- 进行中
ON DUPLICATE KEY UPDATE
    task_status = VALUES(task_status),
    completion_count = VALUES(completion_count);

-- 6. 创建示例任务进度数据
INSERT INTO user_task_progress (user_task_id, objective_id, current_amount, is_completed)
SELECT 
    ut.user_task_id,
    to.objective_id,
    CASE 
        WHEN ut.task_status = 0 THEN to.target_amount  -- 已完成任务，进度为目标值
        WHEN ut.task_id = 'TASK_002' THEN 7  -- 击杀任务进度7/10
        WHEN ut.task_id = 'TASK_003' THEN 3  -- 收集任务进度3/10
        ELSE 0
    END as current_amount,
    CASE 
        WHEN ut.task_status = 0 THEN 1  -- 已完成
        ELSE 0  -- 未完成
    END as is_completed
FROM user_task ut
JOIN task_objective to ON ut.task_id = to.task_id
WHERE ut.user_id = 1
ON DUPLICATE KEY UPDATE
    current_amount = VALUES(current_amount),
    is_completed = VALUES(is_completed),
    updated_at = NOW();

-- 7. 创建示例奖励记录
INSERT INTO task_reward_log (user_id, task_id, reward_type, reward_id, reward_amount, reward_description, granted_at) VALUES
(1, 'TASK_001', 'ITEM', '1001', 10, '新手药水 x10', DATE_SUB(NOW(), INTERVAL 1 DAY)),
(1, 'TASK_001', 'CURRENCY', 'gold', 1000, '金币 x1000', DATE_SUB(NOW(), INTERVAL 1 DAY))
ON DUPLICATE KEY UPDATE
    reward_amount = VALUES(reward_amount),
    reward_description = VALUES(reward_description);

-- 8. 数据验证查询
-- 验证任务配置数据
SELECT 
    '任务配置' as table_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_count
FROM task_config

UNION ALL

-- 验证任务目标数据
SELECT 
    '任务目标' as table_name,
    COUNT(*) as record_count,
    COUNT(DISTINCT task_id) as task_count
FROM task_objective

UNION ALL

-- 验证用户任务数据
SELECT 
    '用户任务' as table_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN task_status = 1 THEN 1 END) as in_progress_count
FROM user_task

UNION ALL

-- 验证任务进度数据
SELECT 
    '任务进度' as table_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN is_completed = 1 THEN 1 END) as completed_count
FROM user_task_progress

UNION ALL

-- 验证奖励记录数据
SELECT 
    '奖励记录' as table_name,
    COUNT(*) as record_count,
    COUNT(DISTINCT user_id) as user_count
FROM task_reward_log;

-- 9. 清理临时表
DROP TEMPORARY TABLE IF EXISTS temp_original_tasks;
DROP TEMPORARY TABLE IF EXISTS temp_objectives;

-- 10. 创建数据迁移完成标记
INSERT INTO task_config (task_id, task_name, task_description, task_type, is_active, sort_order, created_at, updated_at) VALUES
('MIGRATION_COMPLETE', '数据迁移完成标记', '标记任务系统数据迁移已完成', 0, 0, 9999, NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- 迁移完成提示
SELECT 
    '任务系统数据迁移完成' as message,
    NOW() as completion_time,
    (SELECT COUNT(*) FROM task_config WHERE is_active = 1) as active_tasks,
    (SELECT COUNT(*) FROM task_objective) as total_objectives,
    (SELECT COUNT(*) FROM user_task) as user_tasks,
    (SELECT COUNT(*) FROM task_reward_log) as reward_logs;
