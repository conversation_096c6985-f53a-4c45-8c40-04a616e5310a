<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>宠物关联查询测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-section h3 { margin-top: 0; color: #333; }
        .test-button { background: #4CAF50; color: white; border: none; padding: 8px 16px; margin: 5px; border-radius: 4px; cursor: pointer; }
        .test-button:hover { background: #45a049; }
        .test-button.synthesis { background: #2196F3; }
        .test-button.nirvana { background: #9C27B0; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .pet-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .pet-table th, .pet-table td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
        .pet-table th { background: #f2f2f2; font-weight: bold; }
        .pet-table .five-element { background: #e8f5e8; }
        .pet-table .god-element { background: #f3e5f5; }
        .comparison-section { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .comparison-box { border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
        .comparison-box h4 { margin-top: 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 宠物关联查询测试</h1>
        
        <div class="test-section">
            <h3>📋 测试说明</h3>
            <div class="info result">
                <strong>关联查询优化</strong>: user_pet 表与 pet_config 表进行 INNER JOIN<br>
                <strong>宠物名称</strong>: 优先使用 custom_name，其次使用 pet_config.name<br>
                <strong>宠物属性</strong>: 直接从 pet_config.attribute 获取（金、木、水、火、土、神等）<br>
                <strong>筛选优化</strong>: 在数据库层面按 pet_config.attribute 筛选，提高效率
            </div>
        </div>

        <!-- API接口测试 -->
        <div class="test-section">
            <h3>🔗 关联查询API测试</h3>
            <button class="test-button synthesis" onclick="testSynthesisJoinQuery()">测试合成宠物关联查询</button>
            <button class="test-button nirvana" onclick="testNirvanaJoinQuery()">测试涅槃宠物关联查询</button>
            <button class="test-button" onclick="compareQueryMethods()">对比查询方法</button>
            <div id="joinQueryResults"></div>
        </div>

        <!-- 数据展示对比 -->
        <div class="comparison-section">
            <div class="comparison-box">
                <h4>🔥 合成宠物（五系）</h4>
                <div id="synthesisDisplay"></div>
            </div>
            <div class="comparison-box">
                <h4>✨ 涅槃宠物（神系）</h4>
                <div id="nirvanaDisplay"></div>
            </div>
        </div>

        <!-- 数据质量验证 -->
        <div class="test-section">
            <h3>📊 数据质量验证</h3>
            <button class="test-button" onclick="validateDataQuality()">验证数据质量</button>
            <div id="qualityResults"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5000/api';
        const TEST_USER_ID = 1;

        function addResult(sectionId, message, type = 'info', data = null) {
            const section = document.getElementById(sectionId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            
            const time = new Date().toLocaleTimeString();
            let content = `[${time}] ${message}`;
            
            if (data) {
                content += '\n' + JSON.stringify(data, null, 2);
            }
            
            div.textContent = content;
            section.appendChild(div);
            section.scrollTop = section.scrollHeight;
        }

        // 测试合成宠物关联查询
        async function testSynthesisJoinQuery() {
            try {
                addResult('joinQueryResults', '🔄 测试合成宠物关联查询...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/Player/synthesis-available?userId=${TEST_USER_ID}`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    const pets = result.data || [];
                    addResult('joinQueryResults', `✅ 合成宠物关联查询成功，返回 ${pets.length} 只五系宠物`, 'success');
                    
                    displayPetsTable('synthesisDisplay', pets, 'five-element');
                    
                    // 验证数据来源
                    const dataSourceInfo = {
                        总数量: pets.length,
                        属性分布: getAttributeDistribution(pets),
                        名称来源: analyzeNameSource(pets),
                        数据完整性: validateDataCompleteness(pets)
                    };
                    
                    addResult('joinQueryResults', '📊 合成宠物数据分析', 'info', dataSourceInfo);
                } else {
                    addResult('joinQueryResults', '❌ 合成宠物关联查询失败', 'error', result);
                }
            } catch (error) {
                addResult('joinQueryResults', `💥 合成宠物关联查询异常: ${error.message}`, 'error');
            }
        }

        // 测试涅槃宠物关联查询
        async function testNirvanaJoinQuery() {
            try {
                addResult('joinQueryResults', '🔄 测试涅槃宠物关联查询...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/Player/nirvana-available?userId=${TEST_USER_ID}`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    const pets = result.data || [];
                    addResult('joinQueryResults', `✅ 涅槃宠物关联查询成功，返回 ${pets.length} 只神系宠物`, 'success');
                    
                    displayPetsTable('nirvanaDisplay', pets, 'god-element');
                    
                    // 验证数据来源
                    const dataSourceInfo = {
                        总数量: pets.length,
                        属性分布: getAttributeDistribution(pets),
                        名称来源: analyzeNameSource(pets),
                        数据完整性: validateDataCompleteness(pets),
                        等级分布: getLevelDistribution(pets)
                    };
                    
                    addResult('joinQueryResults', '📊 涅槃宠物数据分析', 'info', dataSourceInfo);
                } else {
                    addResult('joinQueryResults', '❌ 涅槃宠物关联查询失败', 'error', result);
                }
            } catch (error) {
                addResult('joinQueryResults', `💥 涅槃宠物关联查询异常: ${error.message}`, 'error');
            }
        }

        // 对比查询方法
        async function compareQueryMethods() {
            try {
                addResult('joinQueryResults', '🔄 对比关联查询与分别查询的效果...', 'info');
                
                const startTime = performance.now();
                
                // 同时调用两个接口
                const [synthesisResponse, nirvanaResponse] = await Promise.all([
                    fetch(`${API_BASE_URL}/Player/synthesis-available?userId=${TEST_USER_ID}`),
                    fetch(`${API_BASE_URL}/Player/nirvana-available?userId=${TEST_USER_ID}`)
                ]);
                
                const endTime = performance.now();
                const totalTime = endTime - startTime;
                
                const synthesisResult = await synthesisResponse.json();
                const nirvanaResult = await nirvanaResponse.json();
                
                const synthesisPets = synthesisResult.success ? (synthesisResult.data || []) : [];
                const nirvanaPets = nirvanaResult.success ? (nirvanaResult.data || []) : [];
                
                // 性能和数据质量对比
                const comparison = {
                    查询性能: {
                        总耗时: `${totalTime.toFixed(2)}ms`,
                        平均单次: `${(totalTime / 2).toFixed(2)}ms`
                    },
                    数据质量: {
                        合成宠物数量: synthesisPets.length,
                        涅槃宠物数量: nirvanaPets.length,
                        总宠物数量: synthesisPets.length + nirvanaPets.length,
                        属性覆盖率: calculateAttributeCoverage(synthesisPets, nirvanaPets),
                        名称完整率: calculateNameCompleteness(synthesisPets, nirvanaPets)
                    },
                    关联查询优势: {
                        单次查询: '✅ 减少数据库往返次数',
                        数据一致性: '✅ 避免数据不一致问题',
                        查询效率: '✅ 在数据库层面筛选',
                        内存使用: '✅ 减少内存中的数据处理'
                    }
                };
                
                addResult('joinQueryResults', '✅ 查询方法对比完成', 'success', comparison);
                
            } catch (error) {
                addResult('joinQueryResults', `💥 查询方法对比异常: ${error.message}`, 'error');
            }
        }

        // 验证数据质量
        async function validateDataQuality() {
            try {
                addResult('qualityResults', '🔄 验证数据质量...', 'info');
                
                // 获取两种类型的宠物数据
                const [synthesisResponse, nirvanaResponse] = await Promise.all([
                    fetch(`${API_BASE_URL}/Player/synthesis-available?userId=${TEST_USER_ID}`),
                    fetch(`${API_BASE_URL}/Player/nirvana-available?userId=${TEST_USER_ID}`)
                ]);
                
                const synthesisResult = await synthesisResponse.json();
                const nirvanaResult = await nirvanaResponse.json();
                
                const synthesisPets = synthesisResult.success ? (synthesisResult.data || []) : [];
                const nirvanaPets = nirvanaResult.success ? (nirvanaResult.data || []) : [];
                
                const qualityReport = {
                    数据完整性检查: {
                        合成宠物缺失字段: findMissingFields(synthesisPets),
                        涅槃宠物缺失字段: findMissingFields(nirvanaPets)
                    },
                    属性正确性检查: {
                        合成宠物非五系: findInvalidAttributes(synthesisPets, ['金', '木', '水', '火', '土']),
                        涅槃宠物非神系: findInvalidAttributes(nirvanaPets, ['神', '神圣', '聖', '佛', '魔', '人', '鬼', '巫', '萌', '仙', '灵', '次元'])
                    },
                    名称一致性检查: {
                        合成宠物名称问题: findNameIssues(synthesisPets),
                        涅槃宠物名称问题: findNameIssues(nirvanaPets)
                    },
                    等级要求检查: {
                        涅槃宠物低于60级: nirvanaPets.filter(p => p.等级 < 60).length
                    }
                };
                
                const hasIssues = Object.values(qualityReport).some(category => 
                    Object.values(category).some(issues => 
                        Array.isArray(issues) ? issues.length > 0 : issues > 0
                    )
                );
                
                if (hasIssues) {
                    addResult('qualityResults', '⚠️ 发现数据质量问题', 'warning', qualityReport);
                } else {
                    addResult('qualityResults', '✅ 数据质量验证通过', 'success', qualityReport);
                }
                
            } catch (error) {
                addResult('qualityResults', `💥 数据质量验证异常: ${error.message}`, 'error');
            }
        }

        // 辅助函数
        function displayPetsTable(containerId, pets, cssClass) {
            const container = document.getElementById(containerId);
            
            if (pets.length === 0) {
                container.innerHTML = '<p>暂无数据</p>';
                return;
            }
            
            let html = `
                <table class="pet-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>名称</th>
                            <th>属性</th>
                            <th>等级</th>
                            <th>成长</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            pets.forEach(pet => {
                html += `
                    <tr class="${cssClass}">
                        <td>${pet.宠物序号}</td>
                        <td>${pet.宠物名字}</td>
                        <td>${pet.五行}</td>
                        <td>${pet.等级}</td>
                        <td>${pet.成长}</td>
                        <td>${pet.状态 === '0' ? '主宠' : '普通'}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            container.innerHTML = html;
        }

        function getAttributeDistribution(pets) {
            const distribution = {};
            pets.forEach(pet => {
                const attr = pet.五行 || '未知';
                distribution[attr] = (distribution[attr] || 0) + 1;
            });
            return distribution;
        }

        function analyzeNameSource(pets) {
            let customNames = 0;
            let configNames = 0;
            
            pets.forEach(pet => {
                // 这里简化判断，实际应该根据数据库字段判断
                if (pet.宠物名字 && pet.宠物名字.length > 0) {
                    customNames++;
                } else {
                    configNames++;
                }
            });
            
            return { 自定义名称: customNames, 配置名称: configNames };
        }

        function validateDataCompleteness(pets) {
            const requiredFields = ['宠物序号', '宠物名字', '五行', '等级'];
            let completeCount = 0;
            
            pets.forEach(pet => {
                const isComplete = requiredFields.every(field => 
                    pet[field] !== null && pet[field] !== undefined && pet[field] !== ''
                );
                if (isComplete) completeCount++;
            });
            
            return `${completeCount}/${pets.length} (${((completeCount/pets.length)*100).toFixed(1)}%)`;
        }

        function getLevelDistribution(pets) {
            const distribution = { '60-69级': 0, '70-79级': 0, '80级以上': 0 };
            pets.forEach(pet => {
                const level = pet.等级;
                if (level >= 60 && level < 70) distribution['60-69级']++;
                else if (level >= 70 && level < 80) distribution['70-79级']++;
                else if (level >= 80) distribution['80级以上']++;
            });
            return distribution;
        }

        function calculateAttributeCoverage(synthesisPets, nirvanaPets) {
            const fiveElements = ['金', '木', '水', '火', '土'];
            const godElements = ['神', '神圣', '聖', '佛', '魔', '人', '鬼', '巫', '萌', '仙', '灵', '次元'];
            
            const synthesisAttrs = new Set(synthesisPets.map(p => p.五行));
            const nirvanaAttrs = new Set(nirvanaPets.map(p => p.五行));
            
            const fiveCoverage = fiveElements.filter(attr => synthesisAttrs.has(attr)).length;
            const godCoverage = godElements.filter(attr => nirvanaAttrs.has(attr)).length;
            
            return {
                五系覆盖: `${fiveCoverage}/${fiveElements.length}`,
                神系覆盖: `${godCoverage}/${godElements.length}`
            };
        }

        function calculateNameCompleteness(synthesisPets, nirvanaPets) {
            const allPets = [...synthesisPets, ...nirvanaPets];
            const validNames = allPets.filter(p => p.宠物名字 && p.宠物名字.trim() !== '').length;
            return `${validNames}/${allPets.length} (${((validNames/allPets.length)*100).toFixed(1)}%)`;
        }

        function findMissingFields(pets) {
            const requiredFields = ['宠物序号', '宠物名字', '五行', '等级', '成长'];
            const issues = [];
            
            pets.forEach((pet, index) => {
                const missing = requiredFields.filter(field => 
                    pet[field] === null || pet[field] === undefined || pet[field] === ''
                );
                if (missing.length > 0) {
                    issues.push(`宠物${index + 1}: ${missing.join(', ')}`);
                }
            });
            
            return issues;
        }

        function findInvalidAttributes(pets, validAttributes) {
            return pets.filter(pet => !validAttributes.includes(pet.五行))
                       .map(pet => `${pet.宠物名字}(${pet.五行})`);
        }

        function findNameIssues(pets) {
            return pets.filter(pet => !pet.宠物名字 || pet.宠物名字.trim() === '')
                       .map(pet => `ID:${pet.宠物序号}`);
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                testSynthesisJoinQuery();
            }, 1000);
        });
    </script>
</body>
</html>
