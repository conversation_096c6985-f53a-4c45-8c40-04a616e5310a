using System;
using System.ComponentModel.DataAnnotations;

namespace WebApplication_HM.DTOs
{
    /// <summary>
    /// 合成请求DTO
    /// </summary>
    public class SynthesisRequestDto
    {
        /// <summary>
        /// 主宠ID
        /// </summary>
        [Required(ErrorMessage = "主宠ID不能为空")]
        public int MainPetId { get; set; }

        /// <summary>
        /// 副宠ID
        /// </summary>
        [Required(ErrorMessage = "副宠ID不能为空")]
        public int VicePetId { get; set; }

        /// <summary>
        /// 使用的道具列表
        /// </summary>
        public List<string> UsedItems { get; set; } = new List<string>();
    }

    /// <summary>
    /// 合成结果DTO
    /// </summary>
    public class SynthesisResultDto
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 成长增加值
        /// </summary>
        public decimal GrowthIncrease { get; set; }

        /// <summary>
        /// 新的成长值
        /// </summary>
        public decimal NewGrowth { get; set; }

        /// <summary>
        /// 合成前宠物编号
        /// </summary>
        public int BeforePetNo { get; set; }

        /// <summary>
        /// 合成后宠物编号
        /// </summary>
        public int AfterPetNo { get; set; }

        /// <summary>
        /// 消耗的金币
        /// </summary>
        public long CostGold { get; set; }

        /// <summary>
        /// 使用的道具
        /// </summary>
        public List<string> UsedItems { get; set; } = new List<string>();

        /// <summary>
        /// 是否合成出神宠
        /// </summary>
        public bool IsGodPet { get; set; }

        /// <summary>
        /// 实际成功率
        /// </summary>
        public decimal SuccessRate { get; set; }

        /// <summary>
        /// 是否使用了公式
        /// </summary>
        public bool UsedFormula { get; set; }

        /// <summary>
        /// 公式ID
        /// </summary>
        public int? FormulaId { get; set; }
    }

    /// <summary>
    /// 合成配置DTO
    /// </summary>
    public class SynthesisConfigDto
    {
        /// <summary>
        /// 主宠信息
        /// </summary>
        public PetSynthesisInfoDto MainPet { get; set; }

        /// <summary>
        /// 副宠信息
        /// </summary>
        public PetSynthesisInfoDto VicePet { get; set; }

        /// <summary>
        /// 基础成功率
        /// </summary>
        public decimal BaseSuccessRate { get; set; }

        /// <summary>
        /// 实际成功率
        /// </summary>
        public decimal ActualSuccessRate { get; set; }

        /// <summary>
        /// 消耗金币
        /// </summary>
        public long CostGold { get; set; }

        /// <summary>
        /// 预期成长增加范围
        /// </summary>
        public string ExpectedGrowthRange { get; set; }

        /// <summary>
        /// 是否可以合成
        /// </summary>
        public bool CanSynthesize { get; set; }

        /// <summary>
        /// 不能合成的原因
        /// </summary>
        public string CannotSynthesizeReason { get; set; }

        /// <summary>
        /// 匹配的公式
        /// </summary>
        public SynthesisFormulaDto MatchedFormula { get; set; }

        /// <summary>
        /// 神宠合成概率
        /// </summary>
        public decimal GodPetProbability { get; set; }

        /// <summary>
        /// VIP加成信息
        /// </summary>
        public VipBonusDto VipBonus { get; set; }
    }

    /// <summary>
    /// 宠物合成信息DTO
    /// </summary>
    public class PetSynthesisInfoDto
    {
        /// <summary>
        /// 宠物ID
        /// </summary>
        public int PetId { get; set; }

        /// <summary>
        /// 宠物名称
        /// </summary>
        public string PetName { get; set; }

        /// <summary>
        /// 宠物编号
        /// </summary>
        public int PetNo { get; set; }

        /// <summary>
        /// 当前等级
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 当前成长
        /// </summary>
        public decimal Growth { get; set; }

        /// <summary>
        /// 宠物属性/五行
        /// </summary>
        public string Element { get; set; }

        /// <summary>
        /// 是否为五系宠物
        /// </summary>
        public bool IsFiveElement { get; set; }
    }

    /// <summary>
    /// 合成公式DTO
    /// </summary>
    public class SynthesisFormulaDto
    {
        /// <summary>
        /// 公式ID
        /// </summary>
        public int FormulaId { get; set; }

        /// <summary>
        /// 主宠编号
        /// </summary>
        public int MainPetNo { get; set; }

        /// <summary>
        /// 副宠编号
        /// </summary>
        public int VicePetNo { get; set; }

        /// <summary>
        /// 主宠成长要求
        /// </summary>
        public decimal MainGrowthRequired { get; set; }

        /// <summary>
        /// 副宠成长要求
        /// </summary>
        public decimal ViceGrowthRequired { get; set; }

        /// <summary>
        /// 结果宠物编号
        /// </summary>
        public int ResultPetNo { get; set; }

        /// <summary>
        /// 结果宠物名称
        /// </summary>
        public string ResultPetName { get; set; }

        /// <summary>
        /// 成功率加成
        /// </summary>
        public decimal SuccessRateBonus { get; set; }

        /// <summary>
        /// 成长加成
        /// </summary>
        public decimal GrowthBonus { get; set; }
    }

    /// <summary>
    /// VIP加成DTO
    /// </summary>
    public class VipBonusDto
    {
        /// <summary>
        /// VIP等级
        /// </summary>
        public int VipLevel { get; set; }

        /// <summary>
        /// 是否至尊VIP
        /// </summary>
        public bool IsSupremeVip { get; set; }

        /// <summary>
        /// 成长加成百分比
        /// </summary>
        public decimal GrowthBonusPercent { get; set; }

        /// <summary>
        /// 加成描述
        /// </summary>
        public string BonusDescription { get; set; }
    }

    /// <summary>
    /// 合成历史记录DTO
    /// </summary>
    public class SynthesisHistoryDto
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 主宠名称
        /// </summary>
        public string MainPetName { get; set; }

        /// <summary>
        /// 副宠名称
        /// </summary>
        public string VicePetName { get; set; }

        /// <summary>
        /// 合成前成长
        /// </summary>
        public decimal BeforeGrowth { get; set; }

        /// <summary>
        /// 合成后成长
        /// </summary>
        public decimal? AfterGrowth { get; set; }

        /// <summary>
        /// 成长增加值
        /// </summary>
        public decimal? GrowthIncrease { get; set; }

        /// <summary>
        /// 合成前宠物名称
        /// </summary>
        public string BeforePetName { get; set; }

        /// <summary>
        /// 合成后宠物名称
        /// </summary>
        public string AfterPetName { get; set; }

        /// <summary>
        /// 消耗金币
        /// </summary>
        public long CostGold { get; set; }

        /// <summary>
        /// 使用的道具
        /// </summary>
        public List<string> UsedItems { get; set; } = new List<string>();

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 是否神宠
        /// </summary>
        public bool IsGodPet { get; set; }

        /// <summary>
        /// 成功率
        /// </summary>
        public decimal? SuccessRate { get; set; }

        /// <summary>
        /// 是否使用公式
        /// </summary>
        public bool UsedFormula { get; set; }

        /// <summary>
        /// 合成时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 合成统计DTO
    /// </summary>
    public class SynthesisStatisticsDto
    {
        /// <summary>
        /// 总合成次数
        /// </summary>
        public int TotalSynthesis { get; set; }

        /// <summary>
        /// 成功次数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败次数
        /// </summary>
        public int FailureCount { get; set; }

        /// <summary>
        /// 成功率
        /// </summary>
        public decimal SuccessRate { get; set; }

        /// <summary>
        /// 神宠合成次数
        /// </summary>
        public int GodPetCount { get; set; }

        /// <summary>
        /// 神宠合成率
        /// </summary>
        public decimal GodPetRate { get; set; }

        /// <summary>
        /// 总消耗金币
        /// </summary>
        public long TotalCostGold { get; set; }

        /// <summary>
        /// 平均成长增加
        /// </summary>
        public decimal AverageGrowthIncrease { get; set; }

        /// <summary>
        /// 最大成长增加
        /// </summary>
        public decimal MaxGrowthIncrease { get; set; }

        /// <summary>
        /// 公式使用次数
        /// </summary>
        public int FormulaUsageCount { get; set; }
    }
}
