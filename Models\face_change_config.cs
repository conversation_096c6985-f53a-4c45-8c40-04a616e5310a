﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///变脸配置表
    ///</summary>
    [SugarTable("face_change_config")]
    public partial class face_change_config
    {
           public face_change_config(){


           }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:涅槃兽形象ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string nirvana_pet_image {get;set;}

           /// <summary>
           /// Desc:成功率
           /// Default:
           /// Nullable:False
           /// </summary>           
           public decimal success_rate {get;set;}

           /// <summary>
           /// Desc:属性继承比例
           /// Default:
           /// Nullable:False
           /// </summary>           
           public decimal attribute_ratio {get;set;}

           /// <summary>
           /// Desc:消耗金币
           /// Default:2000000000
           /// Nullable:True
           /// </summary>           
           public long? cost_gold {get;set;}

           /// <summary>
           /// Desc:等级要求
           /// Default:130
           /// Nullable:True
           /// </summary>           
           public int? level_requirement {get;set;}

           /// <summary>
           /// Desc:
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? is_active {get;set;}

           /// <summary>
           /// Desc:
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? created_at {get;set;}

    }
}
