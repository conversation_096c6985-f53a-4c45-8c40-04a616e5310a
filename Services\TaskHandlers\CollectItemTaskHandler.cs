using SqlSugar;
using WebApplication_HM.Models;
using WebApplication_HM.Models.Define;
using TaskStatus = WebApplication_HM.Models.Define.TaskStatus;

namespace WebApplication_HM.Services.TaskHandlers
{
    /// <summary>
    /// 收集道具任务处理器
    /// </summary>
    public class CollectItemTaskHandler : BaseTaskHandler
    {
        public CollectItemTaskHandler(ISqlSugarClient db, ILogger<CollectItemTaskHandler> logger) 
            : base(db, logger)
        {
        }

        public override string SupportedObjectiveType => TaskObjectiveTypes.COLLECT_ITEM;

        /// <summary>
        /// 检查收集道具进度
        /// </summary>
        public override async Task<int> CheckProgressAsync(int userId, task_objective objective)
        {
            if (!ValidateObjective(objective))
                return 0;

            try
            {
                // 收集道具任务通常检查用户背包中的道具数量
                if (string.IsNullOrEmpty(objective.target_id))
                {
                    _logger.LogWarning("收集道具任务缺少目标道具ID: ObjectiveId={ObjectiveId}", objective.objective_id);
                    return 0;
                }

                // 查询用户背包中指定道具的数量
                var itemCountLong = await _db.Queryable<user_item>()
                    .Where(ui => ui.user_id == userId &&
                               ui.item_id == objective.target_id &&
                               ui.item_pos == 1) // 1表示背包
                    .SumAsync(ui => ui.item_count);

                int itemCount = (int)Math.Min(itemCountLong, int.MaxValue);

                LogHandlerAction("检查道具数量", userId, objective, new { ItemCount = itemCount });

                // 返回实际拥有的数量，但不超过任务要求的数量
                return Math.Min(itemCount, objective.target_amount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查收集道具进度失败: UserId={UserId}, ObjectiveId={ObjectiveId}", 
                    userId, objective.objective_id);
                return 0;
            }
        }

        /// <summary>
        /// 更新收集道具进度
        /// </summary>
        public override async Task<int> UpdateProgressAsync(int userId, task_objective objective, int amount)
        {
            if (!ValidateObjective(objective))
                return 0;

            try
            {
                // 收集道具任务的进度更新通常是检查背包中的道具数量
                // 而不是累加，因为道具可能被使用或丢弃
                var currentItemCount = await CheckProgressAsync(userId, objective);

                // 查找用户相关的任务进度
                var taskProgress = await _db.Queryable<user_task, user_task_progress>((ut, utp) => new JoinQueryInfos(
                    JoinType.Inner, ut.user_task_id == utp.user_task_id))
                    .Where((ut, utp) => ut.user_id == userId && 
                                      utp.objective_id == objective.objective_id &&
                                      ut.task_status == (byte)TaskStatus.InProgress)
                    .Select((ut, utp) => new { UserTask = ut, Progress = utp })
                    .FirstAsync();

                if (taskProgress == null)
                {
                    _logger.LogWarning("未找到用户收集道具任务进度: UserId={UserId}, ObjectiveId={ObjectiveId}", 
                        userId, objective.objective_id);
                    return 0;
                }

                var progress = taskProgress.Progress;
                var oldAmount = progress.current_amount ?? 0;

                // 更新进度为当前实际拥有的道具数量
                progress.current_amount = currentItemCount;
                progress.is_completed = (byte)(IsProgressCompleted(currentItemCount, objective.target_amount) ? 1 : 0);

                var updateResult = await UpdateUserTaskProgressAsync(progress);
                
                if (updateResult)
                {
                    LogHandlerAction("更新进度成功", userId, objective, new { 
                        OldAmount = oldAmount, 
                        NewAmount = currentItemCount,
                        IsCompleted = progress.is_completed == 1
                    });
                    
                    return currentItemCount;
                }
                else
                {
                    _logger.LogError("更新收集道具任务进度失败: UserId={UserId}, ObjectiveId={ObjectiveId}", 
                        userId, objective.objective_id);
                    return oldAmount;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新收集道具进度失败: UserId={UserId}, ObjectiveId={ObjectiveId}", 
                    userId, objective.objective_id);
                return 0;
            }
        }

        /// <summary>
        /// 处理道具获得事件
        /// </summary>
        public async Task<int> HandleItemObtainedAsync(int userId, string itemId, int obtainedCount)
        {
            try
            {
                // 查找所有相关的收集道具任务
                var relevantTasks = await _db.Queryable<user_task, task_config, task_objective, user_task_progress>(
                    (ut, tc, to, utp) => new JoinQueryInfos(
                        JoinType.Inner, ut.task_id == tc.task_id,
                        JoinType.Inner, tc.task_id == to.task_id,
                        JoinType.Inner, ut.user_task_id == utp.user_task_id && utp.objective_id == to.objective_id))
                    .Where((ut, tc, to, utp) => ut.user_id == userId &&
                                              ut.task_status == (byte)TaskStatus.InProgress &&
                                              to.objective_type == TaskObjectiveTypes.COLLECT_ITEM &&
                                              to.target_id == itemId &&
                                              utp.is_completed == 0)
                    .Select((ut, tc, to, utp) => new { UserTask = ut, TaskConfig = tc, Objective = to, Progress = utp })
                    .ToListAsync();

                int totalUpdated = 0;

                foreach (var task in relevantTasks)
                {
                    var updatedAmount = await UpdateProgressAsync(userId, task.Objective, obtainedCount);
                    if (updatedAmount > (task.Progress.current_amount ?? 0))
                    {
                        totalUpdated++;
                    }
                }

                if (totalUpdated > 0)
                {
                    _logger.LogInformation("处理道具获得事件: UserId={UserId}, ItemId={ItemId}, ObtainedCount={ObtainedCount}, UpdatedTasks={UpdatedTasks}",
                        userId, itemId, obtainedCount, totalUpdated);
                }

                return totalUpdated;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理道具获得事件失败: UserId={UserId}, ItemId={ItemId}", userId, itemId);
                return 0;
            }
        }

        /// <summary>
        /// 批量检查所有收集道具任务的进度
        /// </summary>
        public async Task<int> RefreshAllCollectItemTasksAsync(int userId)
        {
            try
            {
                // 查找用户所有进行中的收集道具任务
                var collectItemTasks = await _db.Queryable<user_task, task_config, task_objective, user_task_progress>(
                    (ut, tc, to, utp) => new JoinQueryInfos(
                        JoinType.Inner, ut.task_id == tc.task_id,
                        JoinType.Inner, tc.task_id == to.task_id,
                        JoinType.Inner, ut.user_task_id == utp.user_task_id && utp.objective_id == to.objective_id))
                    .Where((ut, tc, to, utp) => ut.user_id == userId &&
                                              ut.task_status == (byte)TaskStatus.InProgress &&
                                              to.objective_type == TaskObjectiveTypes.COLLECT_ITEM &&
                                              utp.is_completed == 0)
                    .Select((ut, tc, to, utp) => new { UserTask = ut, TaskConfig = tc, Objective = to, Progress = utp })
                    .ToListAsync();

                int totalUpdated = 0;

                foreach (var task in collectItemTasks)
                {
                    var updatedAmount = await UpdateProgressAsync(userId, task.Objective, 0); // 传入0表示刷新检查
                    if (updatedAmount != (task.Progress.current_amount ?? 0))
                    {
                        totalUpdated++;
                    }
                }

                if (totalUpdated > 0)
                {
                    _logger.LogInformation("刷新收集道具任务进度: UserId={UserId}, UpdatedTasks={UpdatedTasks}",
                        userId, totalUpdated);
                }

                return totalUpdated;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新收集道具任务进度失败: UserId={UserId}", userId);
                return 0;
            }
        }
    }
}
