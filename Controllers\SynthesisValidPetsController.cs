using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.DTOs;
using WebApplication_HM.Services;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 合成有效宠物管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class SynthesisValidPetsController : ControllerBase
    {
        private readonly ISynthesisValidPetsService _validPetsService;
        private readonly ILogger<SynthesisValidPetsController> _logger;

        public SynthesisValidPetsController(
            ISynthesisValidPetsService validPetsService,
            ILogger<SynthesisValidPetsController> logger)
        {
            _validPetsService = validPetsService;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有可合成宠物ID
        /// </summary>
        /// <returns>可合成宠物ID列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetAllValidPets()
        {
            try
            {
                var validPets = await _validPetsService.GetAllValidPetNosAsync();
                return Ok(new ApiResponse<List<int>>
                {
                    Success = true,
                    Data = validPets,
                    Message = $"获取成功，共{validPets.Count}个可合成宠物"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可合成宠物列表失败");
                return Ok(new ApiResponse<List<int>>
                {
                    Success = false,
                    Message = "获取可合成宠物列表失败",
                    Data = new List<int>()
                });
            }
        }

        /// <summary>
        /// 验证宠物是否可合成
        /// </summary>
        /// <param name="petNo">宠物编号</param>
        /// <returns>验证结果</returns>
        [HttpGet("{petNo}/validate")]
        public async Task<IActionResult> ValidatePet(int petNo)
        {
            try
            {
                var isValid = await _validPetsService.IsValidSynthesisPetAsync(petNo);
                return Ok(new ApiResponse<bool>
                {
                    Success = true,
                    Data = isValid,
                    Message = isValid ? "宠物可以参与合成" : "宠物不能参与合成"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"验证宠物{petNo}是否可合成失败");
                return Ok(new ApiResponse<bool>
                {
                    Success = false,
                    Data = false,
                    Message = "验证失败"
                });
            }
        }

        /// <summary>
        /// 批量验证宠物是否可合成
        /// </summary>
        /// <param name="petNos">宠物编号列表</param>
        /// <returns>验证结果</returns>
        [HttpPost("validate-batch")]
        public async Task<IActionResult> ValidateMultiplePets([FromBody] List<int> petNos)
        {
            try
            {
                if (petNos == null || !petNos.Any())
                {
                    return Ok(new ApiResponse<Dictionary<int, bool>>
                    {
                        Success = false,
                        Message = "宠物编号列表不能为空",
                        Data = new Dictionary<int, bool>()
                    });
                }

                var results = await _validPetsService.ValidateMultiplePetsAsync(petNos);
                return Ok(new ApiResponse<Dictionary<int, bool>>
                {
                    Success = true,
                    Data = results,
                    Message = $"批量验证完成，共验证{petNos.Count}个宠物"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量验证宠物失败");
                return Ok(new ApiResponse<Dictionary<int, bool>>
                {
                    Success = false,
                    Message = "批量验证失败",
                    Data = new Dictionary<int, bool>()
                });
            }
        }

        /// <summary>
        /// 获取可合成宠物数量
        /// </summary>
        /// <returns>数量</returns>
        [HttpGet("count")]
        public async Task<IActionResult> GetValidPetsCount()
        {
            try
            {
                var count = await _validPetsService.GetValidPetsCountAsync();
                return Ok(new ApiResponse<int>
                {
                    Success = true,
                    Data = count,
                    Message = $"当前有{count}个可合成宠物"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可合成宠物数量失败");
                return Ok(new ApiResponse<int>
                {
                    Success = false,
                    Data = 0,
                    Message = "获取数量失败"
                });
            }
        }

        /// <summary>
        /// 刷新缓存
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("refresh")]
        public async Task<IActionResult> RefreshCache()
        {
            try
            {
                await _validPetsService.RefreshValidPetsAsync();
                return Ok(new ApiResponse<object>
                {
                    Success = true,
                    Message = "缓存刷新成功",
                    Data = null
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新缓存失败");
                return Ok(new ApiResponse<object>
                {
                    Success = false,
                    Message = "刷新缓存失败",
                    Data = null
                });
            }
        }

        /// <summary>
        /// 初始化默认数据
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("initialize")]
        public async Task<IActionResult> InitializeDefaultData()
        {
            try
            {
                await _validPetsService.InitializeDefaultDataAsync();
                return Ok(new ApiResponse<object>
                {
                    Success = true,
                    Message = "默认数据初始化成功",
                    Data = null
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化默认数据失败");
                return Ok(new ApiResponse<object>
                {
                    Success = false,
                    Message = "初始化默认数据失败",
                    Data = null
                });
            }
        }
    }
}
