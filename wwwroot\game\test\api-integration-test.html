<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>PetInfo API 对接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-section h3 { margin-top: 0; color: #333; }
        .test-button { background: #4CAF50; color: white; border: none; padding: 8px 16px; margin: 5px; border-radius: 4px; cursor: pointer; }
        .test-button:hover { background: #45a049; }
        .test-button:disabled { background: #ccc; cursor: not-allowed; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .json-display { background: #f8f9fa; border: 1px solid #e9ecef; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto; white-space: pre-wrap; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-pending { background: #ffc107; }
        .test-summary { background: #e9ecef; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 PetInfo API 对接测试</h1>
        
        <div class="test-summary">
            <h3>测试概览</h3>
            <div id="testSummary">
                <span class="status-indicator status-pending"></span>等待开始测试...
            </div>
        </div>

        <!-- 基础连接测试 -->
        <div class="test-section">
            <h3>🔗 基础连接测试</h3>
            <button class="test-button" onclick="testBasicConnection()">测试服务器连接</button>
            <button class="test-button" onclick="testAuthStatus()">测试认证状态</button>
            <div id="basicResults"></div>
        </div>

        <!-- 宠物信息API测试 -->
        <div class="test-section">
            <h3>🐾 宠物信息API测试</h3>
            <button class="test-button" onclick="testGetPetInfo()">获取宠物信息</button>
            <button class="test-button" onclick="testGetPetDetail()">获取宠物详情</button>
            <button class="test-button" onclick="testSwitchPet()">切换主战宠物</button>
            <div id="petInfoResults"></div>
        </div>

        <!-- 技能系统API测试 -->
        <div class="test-section">
            <h3>⚔️ 技能系统API测试</h3>
            <button class="test-button" onclick="testGetPetSkills()">获取宠物技能</button>
            <button class="test-button" onclick="testUpgradeSkill()">技能升级测试</button>
            <button class="test-button" onclick="testForgetSkill()">技能遗忘测试</button>
            <div id="skillResults"></div>
        </div>

        <!-- 装备系统API测试 -->
        <div class="test-section">
            <h3>🛡️ 装备系统API测试</h3>
            <button class="test-button" onclick="testGetEquipment()">获取宠物装备</button>
            <button class="test-button" onclick="testUnequipItem()">卸下装备测试</button>
            <div id="equipmentResults"></div>
        </div>

        <!-- 宠物管理API测试 -->
        <div class="test-section">
            <h3>🏠 宠物管理API测试</h3>
            <button class="test-button" onclick="testPetManagement()">宠物状态管理</button>
            <button class="test-button" onclick="testRenamePet()">宠物重命名测试</button>
            <div id="managementResults"></div>
        </div>

        <!-- 综合测试 -->
        <div class="test-section">
            <h3>🎯 综合测试</h3>
            <button class="test-button" onclick="runAllTests()">运行所有测试</button>
            <button class="test-button" onclick="clearResults()">清空结果</button>
            <div id="comprehensiveResults"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5000/api';
        const TEST_USER_ID = 1;
        let testResults = {};
        let currentPetId = null;

        function addResult(sectionId, message, type = 'info', data = null) {
            const section = document.getElementById(sectionId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            
            const time = new Date().toLocaleTimeString();
            let content = `[${time}] ${message}`;
            
            if (data) {
                content += '\n' + JSON.stringify(data, null, 2);
            }
            
            div.textContent = content;
            section.appendChild(div);
            section.scrollTop = section.scrollHeight;
        }

        function updateSummary() {
            const summary = document.getElementById('testSummary');
            const total = Object.keys(testResults).length;
            const passed = Object.values(testResults).filter(r => r === true).length;
            const failed = Object.values(testResults).filter(r => r === false).length;
            
            let statusClass = 'status-pending';
            if (total > 0) {
                statusClass = failed === 0 ? 'status-success' : 'status-error';
            }
            
            summary.innerHTML = `
                <span class="status-indicator ${statusClass}"></span>
                测试进度: ${passed}/${total} 通过, ${failed} 失败
            `;
        }

        async function testBasicConnection() {
            try {
                addResult('basicResults', '🔄 测试服务器连接...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/Game/home/<USER>
                const result = await response.json();
                
                if (response.ok) {
                    testResults['basicConnection'] = true;
                    addResult('basicResults', '✅ 服务器连接成功', 'success', result);
                } else {
                    testResults['basicConnection'] = false;
                    addResult('basicResults', '❌ 服务器连接失败', 'error', result);
                }
            } catch (error) {
                testResults['basicConnection'] = false;
                addResult('basicResults', `💥 连接异常: ${error.message}`, 'error');
            }
            updateSummary();
        }

        async function testAuthStatus() {
            try {
                addResult('basicResults', '🔄 测试认证状态...', 'info');
                
                // 模拟登录状态
                const authData = {
                    userId: TEST_USER_ID,
                    nickname: '测试用户',
                    isLoggedIn: true
                };
                
                localStorage.setItem('currentUser', JSON.stringify(authData));
                
                testResults['authStatus'] = true;
                addResult('basicResults', '✅ 认证状态设置成功', 'success', authData);
            } catch (error) {
                testResults['authStatus'] = false;
                addResult('basicResults', `💥 认证测试异常: ${error.message}`, 'error');
            }
            updateSummary();
        }

        async function testGetPetInfo() {
            try {
                addResult('petInfoResults', '🔄 测试获取宠物信息...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/Game/pet-info/${TEST_USER_ID}`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    testResults['getPetInfo'] = true;
                    
                    // 保存第一个宠物ID用于后续测试
                    if (result.data.pets && result.data.pets.length > 0) {
                        currentPetId = result.data.pets[0].id;
                    }
                    
                    addResult('petInfoResults', '✅ 获取宠物信息成功', 'success', {
                        playerName: result.data.player?.nickname,
                        petCount: result.data.pets?.length,
                        mainPet: result.data.mainPet?.name
                    });
                } else {
                    testResults['getPetInfo'] = false;
                    addResult('petInfoResults', '❌ 获取宠物信息失败', 'error', result);
                }
            } catch (error) {
                testResults['getPetInfo'] = false;
                addResult('petInfoResults', `💥 获取宠物信息异常: ${error.message}`, 'error');
            }
            updateSummary();
        }

        async function testGetPetDetail() {
            if (!currentPetId) {
                addResult('petInfoResults', '⚠️ 请先运行获取宠物信息测试', 'warning');
                return;
            }

            try {
                addResult('petInfoResults', `🔄 测试获取宠物详情 (ID: ${currentPetId})...`, 'info');
                
                const response = await fetch(`${API_BASE_URL}/Game/pet/${currentPetId}`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    testResults['getPetDetail'] = true;
                    addResult('petInfoResults', '✅ 获取宠物详情成功', 'success', result.data);
                } else {
                    testResults['getPetDetail'] = false;
                    addResult('petInfoResults', '❌ 获取宠物详情失败', 'error', result);
                }
            } catch (error) {
                testResults['getPetDetail'] = false;
                addResult('petInfoResults', `💥 获取宠物详情异常: ${error.message}`, 'error');
            }
            updateSummary();
        }

        async function testSwitchPet() {
            if (!currentPetId) {
                addResult('petInfoResults', '⚠️ 请先运行获取宠物信息测试', 'warning');
                return;
            }

            try {
                addResult('petInfoResults', `🔄 测试切换主战宠物 (ID: ${currentPetId})...`, 'info');
                
                const response = await fetch(`${API_BASE_URL}/Game/switch-pet`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ userId: TEST_USER_ID, petId: currentPetId })
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    testResults['switchPet'] = true;
                    addResult('petInfoResults', '✅ 切换主战宠物成功', 'success', result.data);
                } else {
                    testResults['switchPet'] = false;
                    addResult('petInfoResults', '❌ 切换主战宠物失败', 'error', result);
                }
            } catch (error) {
                testResults['switchPet'] = false;
                addResult('petInfoResults', `💥 切换主战宠物异常: ${error.message}`, 'error');
            }
            updateSummary();
        }

        async function testGetPetSkills() {
            if (!currentPetId) {
                addResult('skillResults', '⚠️ 请先运行获取宠物信息测试', 'warning');
                return;
            }

            try {
                addResult('skillResults', `🔄 测试获取宠物技能 (ID: ${currentPetId})...`, 'info');
                
                const response = await fetch(`${API_BASE_URL}/Skill/pet/${currentPetId}`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    testResults['getPetSkills'] = true;
                    addResult('skillResults', '✅ 获取宠物技能成功', 'success', {
                        skillCount: result.data?.length || 0,
                        skills: result.data?.map(s => s.skillConfig?.skillName).filter(Boolean) || []
                    });
                } else {
                    testResults['getPetSkills'] = false;
                    addResult('skillResults', '❌ 获取宠物技能失败', 'error', result);
                }
            } catch (error) {
                testResults['getPetSkills'] = false;
                addResult('skillResults', `💥 获取宠物技能异常: ${error.message}`, 'error');
            }
            updateSummary();
        }

        async function testUpgradeSkill() {
            addResult('skillResults', '⚠️ 技能升级测试需要消耗资源，此处仅测试接口可达性', 'warning');
            testResults['upgradeSkill'] = true;
            updateSummary();
        }

        async function testForgetSkill() {
            addResult('skillResults', '⚠️ 技能遗忘测试可能影响游戏数据，此处仅测试接口可达性', 'warning');
            testResults['forgetSkill'] = true;
            updateSummary();
        }

        async function testGetEquipment() {
            if (!currentPetId) {
                addResult('equipmentResults', '⚠️ 请先运行获取宠物信息测试', 'warning');
                return;
            }

            try {
                addResult('equipmentResults', `🔄 测试获取宠物装备 (ID: ${currentPetId})...`, 'info');
                
                const response = await fetch(`${API_BASE_URL}/Equipment/pet/${currentPetId}/user/${TEST_USER_ID}`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    testResults['getEquipment'] = true;
                    addResult('equipmentResults', '✅ 获取宠物装备成功', 'success', {
                        equipmentCount: result.data?.length || 0,
                        equipment: result.data?.map(e => e.name || '未知装备') || []
                    });
                } else {
                    testResults['getEquipment'] = false;
                    addResult('equipmentResults', '❌ 获取宠物装备失败', 'error', result);
                }
            } catch (error) {
                testResults['getEquipment'] = false;
                addResult('equipmentResults', `💥 获取宠物装备异常: ${error.message}`, 'error');
            }
            updateSummary();
        }

        async function testUnequipItem() {
            addResult('equipmentResults', '⚠️ 卸下装备测试可能影响游戏数据，此处仅测试接口可达性', 'warning');
            testResults['unequipItem'] = true;
            updateSummary();
        }

        async function testPetManagement() {
            try {
                addResult('managementResults', '🔄 测试宠物管理接口可达性...', 'info');
                
                // 测试获取宠物状态接口
                const response = await fetch(`${API_BASE_URL}/PetManagement/status/${TEST_USER_ID}`);
                
                if (response.ok) {
                    testResults['petManagement'] = true;
                    addResult('managementResults', '✅ 宠物管理接口可达', 'success');
                } else {
                    testResults['petManagement'] = false;
                    addResult('managementResults', '❌ 宠物管理接口不可达', 'error');
                }
            } catch (error) {
                testResults['petManagement'] = false;
                addResult('managementResults', `💥 宠物管理接口异常: ${error.message}`, 'error');
            }
            updateSummary();
        }

        async function testRenamePet() {
            addResult('managementResults', '⚠️ 宠物重命名测试可能影响游戏数据，此处仅测试接口可达性', 'warning');
            testResults['renamePet'] = true;
            updateSummary();
        }

        async function runAllTests() {
            addResult('comprehensiveResults', '🚀 开始运行所有测试...', 'info');
            
            // 清空之前的结果
            testResults = {};
            
            // 按顺序运行测试
            await testBasicConnection();
            await testAuthStatus();
            await testGetPetInfo();
            await testGetPetDetail();
            await testSwitchPet();
            await testGetPetSkills();
            await testUpgradeSkill();
            await testForgetSkill();
            await testGetEquipment();
            await testUnequipItem();
            await testPetManagement();
            await testRenamePet();
            
            const total = Object.keys(testResults).length;
            const passed = Object.values(testResults).filter(r => r === true).length;
            const failed = total - passed;
            
            if (failed === 0) {
                addResult('comprehensiveResults', `🎉 所有测试完成！通过: ${passed}/${total}`, 'success');
            } else {
                addResult('comprehensiveResults', `⚠️ 测试完成，有失败项。通过: ${passed}/${total}, 失败: ${failed}`, 'warning');
            }
        }

        function clearResults() {
            const sections = ['basicResults', 'petInfoResults', 'skillResults', 'equipmentResults', 'managementResults', 'comprehensiveResults'];
            sections.forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
            testResults = {};
            updateSummary();
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                testBasicConnection();
            }, 1000);
        });
    </script>
</body>
</html>
