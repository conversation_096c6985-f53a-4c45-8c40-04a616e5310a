/**
 * 宠物管理系统
 * 替换老系统的 window.external 调用
 */
class PetManagement {
    constructor() {
        this.userId = null;
        this.selectedPetId = null;
        this.selectedPetData = null;
        this.petList = [];
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalPages = 1;
        this.isLoading = false;
    }

    /**
     * 初始化系统
     */
    async init(userId) {
        this.userId = userId;
        console.log('初始化宠物管理系统 - 用户ID:', userId);
        
        // 绑定事件
        this.bindEvents();
        
        // 加载宠物列表
        await this.loadPets();
        
        // 加载容量信息
        await this.loadCapacityInfo();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 筛选和排序事件
        $('#statusFilter').on('change', () => this.loadPets());
        $('#sortBy').on('change', () => this.loadPets());
        $('#sortDirection').on('change', () => this.loadPets());
        
        // 键盘事件
        $(document).on('keydown', (e) => {
            if (e.key === 'Delete' && this.selectedPetId) {
                this.discardPet();
            }
        });
    }

    /**
     * 加载宠物列表
     */
    async loadPets() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading(true);
        
        try {
            const status = $('#statusFilter').val();
            const sortBy = $('#sortBy').val();
            const sortDirection = $('#sortDirection').val();
            
            const url = `/api/PetManagement/pasture/${this.userId}?` +
                `status=${encodeURIComponent(status)}&` +
                `page=${this.currentPage}&` +
                `pageSize=${this.pageSize}&` +
                `sortBy=${sortBy}&` +
                `sortDirection=${sortDirection}`;
            
            const response = await fetch(url);
            const result = await response.json();
            
            if (result.success) {
                this.petList = result.pets;
                this.updatePetTable(result.pets);
                this.updateCapacityDisplay(result.currentCount, result.maxCapacity);
                this.updatePagination(result.currentCount);
                
                console.log('宠物列表加载成功:', result.pets.length, '只宠物');
            } else {
                this.showMessage(result.message || '加载宠物列表失败', 'error');
            }
        } catch (error) {
            console.error('加载宠物列表失败:', error);
            this.showMessage('网络错误，请稍后重试', 'error');
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    /**
     * 更新宠物表格
     */
    updatePetTable(pets) {
        const tbody = $('#petTableBody');
        tbody.empty();
        
        if (pets.length === 0) {
            tbody.append('<tr><td colspan="6" style="text-align: center; color: #6c757d;">暂无宠物</td></tr>');
            return;
        }
        
        pets.forEach(pet => {
            const row = this.createPetRow(pet);
            tbody.append(row);
        });
    }

    /**
     * 创建宠物行
     */
    createPetRow(pet) {
        const displayName = pet.customName || pet.name;
        const mainBadge = pet.isMain ? '<span class="main-pet-badge">主</span>' : '';
        const createTime = new Date(pet.createTime).toLocaleDateString();
        
        const row = $(`
            <tr data-pet-id="${pet.id}" class="pet-row">
                <td>
                    <img src="${pet.imagePath}" class="pet-image" alt="${pet.name}" 
                         onerror="this.src='Content/Img/Pasture/mc05.gif'">
                    ${displayName}
                    ${mainBadge}
                </td>
                <td>
                    <span style="color: ${this.getElementColor(pet.element)}">${pet.element}</span>
                </td>
                <td>LV ${pet.level}</td>
                <td>${pet.growth}</td>
                <td>
                    <span class="status-badge status-${pet.status}">${pet.status}</span>
                </td>
                <td>${createTime}</td>
            </tr>
        `);
        
        // 绑定点击事件
        row.on('click', () => this.selectPet(pet.id, pet));
        
        return row;
    }

    /**
     * 选择宠物
     */
    selectPet(petId, petData) {
        // 清除之前的选中状态
        $('.pet-row').removeClass('selected');
        
        // 设置新的选中状态
        $(`.pet-row[data-pet-id="${petId}"]`).addClass('selected');
        
        this.selectedPetId = petId;
        this.selectedPetData = petData;
        
        // 显示宠物详情
        this.showPetDetail(petData);
        
        // 更新操作按钮状态
        this.updateOperationButtons();
        
        console.log('选中宠物:', petData.name, '(ID:', petId, ')');
    }

    /**
     * 显示宠物详情
     */
    showPetDetail(pet) {
        const panel = $('#petDetailPanel');
        const content = $('#petDetailContent');
        
        const detailHtml = `
            <div style="display: flex; gap: 20px; align-items: flex-start;">
                <img src="${pet.imagePath}" style="width: 64px; height: 64px;" 
                     alt="${pet.name}" onerror="this.src='Content/Img/Pasture/mc05.gif'">
                <div style="flex: 1;">
                    <h4>${pet.customName || pet.name} ${pet.isMain ? '⭐' : ''}</h4>
                    <p style="color: #6c757d; margin: 5px 0;">五行：${pet.element} | 等级：${pet.level} | 成长：${pet.growth}</p>
                </div>
            </div>
            
            <div class="pet-attributes">
                <div class="attribute-group">
                    <h4>基础属性</h4>
                    <div class="attribute-item"><span>生命值</span><span>${pet.hp}/${pet.maxHp}</span></div>
                    <div class="attribute-item"><span>魔法值</span><span>${pet.mp}/${pet.maxMp}</span></div>
                    <div class="attribute-item"><span>攻击力</span><span>${pet.atk}</span></div>
                    <div class="attribute-item"><span>防御力</span><span>${pet.def}</span></div>
                    <div class="attribute-item"><span>速度</span><span>${pet.spd}</span></div>
                </div>
                
                <div class="attribute-group">
                    <h4>战斗属性</h4>
                    <div class="attribute-item"><span>命中</span><span>${pet.hit}</span></div>
                    <div class="attribute-item"><span>闪避</span><span>${pet.dodge}</span></div>
                    <div class="attribute-item"><span>加深伤害</span><span>${pet.deepen}%</span></div>
                    <div class="attribute-item"><span>抵消伤害</span><span>${pet.offset}%</span></div>
                    <div class="attribute-item"><span>吸血比例</span><span>${pet.vamp}%</span></div>
                </div>
                
                <div class="attribute-group">
                    <h4>成长信息</h4>
                    <div class="attribute-item"><span>境界</span><span>${pet.realm}</span></div>
                    <div class="attribute-item"><span>进化次数</span><span>${pet.evolveCount}</span></div>
                    <div class="attribute-item"><span>合成次数</span><span>${pet.synthesisCount}</span></div>
                    <div class="attribute-item"><span>转生次数</span><span>${pet.nirvanaCount}</span></div>
                </div>
            </div>
        `;
        
        content.html(detailHtml);
        panel.addClass('show');
    }

    /**
     * 更新操作按钮状态
     */
    updateOperationButtons() {
        const pet = this.selectedPetData;
        if (!pet) {
            // 没有选中宠物时禁用所有按钮
            $('.operation-buttons button').prop('disabled', true);
            return;
        }
        
        // 根据宠物状态和属性控制按钮可用性
        $('#carryBtn').prop('disabled', pet.status !== '牧场');
        $('#storeBtn').prop('disabled', pet.status !== '携带' || pet.isMain);
        $('#setMainBtn').prop('disabled', pet.isMain || pet.status !== '携带' || pet.hp <= 0);
        $('#renameBtn').prop('disabled', false);
        $('#discardBtn').prop('disabled', pet.isMain);
    }

    /**
     * 携带宠物
     */
    async carryPet() {
        if (!this.selectedPetId) {
            this.showMessage('请先选择一个宠物', 'error');
            return;
        }
        
        if (!confirm('确定要携带这只宠物吗？')) return;
        
        try {
            const response = await fetch('/api/PetManagement/carry', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    userId: this.userId,
                    petId: this.selectedPetId
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showMessage('携带成功', 'success');
                await this.loadPets();
                this.clearSelection();
            } else {
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            console.error('携带宠物失败:', error);
            this.showMessage('操作失败，请稍后重试', 'error');
        }
    }

    /**
     * 存放宠物
     */
    async storePet() {
        if (!this.selectedPetId) {
            this.showMessage('请先选择一个宠物', 'error');
            return;
        }
        
        if (!confirm('确定要存放这只宠物到牧场吗？')) return;
        
        try {
            const response = await fetch('/api/PetManagement/store', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    userId: this.userId,
                    petId: this.selectedPetId
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showMessage('存放成功', 'success');
                await this.loadPets();
                this.clearSelection();
            } else {
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            console.error('存放宠物失败:', error);
            this.showMessage('操作失败，请稍后重试', 'error');
        }
    }

    /**
     * 设置主战宠物
     */
    async setMainPet() {
        if (!this.selectedPetId) {
            this.showMessage('请先选择一个宠物', 'error');
            return;
        }
        
        if (!confirm('确定要设置这只宠物为主战宠物吗？')) return;
        
        try {
            const response = await fetch('/api/PetManagement/setMain', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    userId: this.userId,
                    petId: this.selectedPetId
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showMessage('设置主战宠物成功', 'success');
                await this.loadPets();
                this.clearSelection();
            } else {
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            console.error('设置主战宠物失败:', error);
            this.showMessage('操作失败，请稍后重试', 'error');
        }
    }

    /**
     * 重命名宠物
     */
    async renamePet() {
        if (!this.selectedPetId) {
            this.showMessage('请先选择一个宠物', 'error');
            return;
        }
        
        const currentName = this.selectedPetData.customName || this.selectedPetData.name;
        const newName = prompt('请输入新的宠物名称:', currentName);
        
        if (!newName || newName.trim() === '' || newName === currentName) {
            return;
        }
        
        if (newName.length > 20) {
            this.showMessage('宠物名称不能超过20个字符', 'error');
            return;
        }
        
        try {
            const response = await fetch('/api/PetManagement/rename', {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    userId: this.userId,
                    petId: this.selectedPetId,
                    newName: newName.trim()
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showMessage('重命名成功', 'success');
                await this.loadPets();
                this.clearSelection();
            } else {
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            console.error('重命名宠物失败:', error);
            this.showMessage('操作失败，请稍后重试', 'error');
        }
    }

    /**
     * 丢弃宠物
     */
    async discardPet() {
        if (!this.selectedPetId) {
            this.showMessage('请先选择一个宠物', 'error');
            return;
        }
        
        const petName = this.selectedPetData.customName || this.selectedPetData.name;
        
        if (!confirm(`为了口袋精灵世界的健康发展，丢弃需要花费您10000金币处理费！\n\n注意，您一旦丢弃宠物"${petName}"，就再也找不回来了，您确定要丢弃该宠物吗？`)) {
            return;
        }
        
        const password = prompt('请输入您的密码确认丢弃操作:');
        if (!password) return;
        
        try {
            const response = await fetch('/api/PetManagement/discard', {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    userId: this.userId,
                    petId: this.selectedPetId,
                    confirmPassword: password
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showMessage('丢弃成功，已扣除10000金币处理费', 'success');
                await this.loadPets();
                this.clearSelection();
            } else {
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            console.error('丢弃宠物失败:', error);
            this.showMessage('操作失败，请稍后重试', 'error');
        }
    }

    /**
     * 加载容量信息
     */
    async loadCapacityInfo() {
        try {
            const response = await fetch(`/api/PetManagement/capacity/${this.userId}`);
            const result = await response.json();
            
            if (result.success) {
                this.updateCapacityDisplay(result.data.currentCount, result.data.maxCapacity);
            }
        } catch (error) {
            console.error('加载容量信息失败:', error);
        }
    }

    /**
     * 更新容量显示
     */
    updateCapacityDisplay(currentCount, maxCapacity) {
        $('#currentCount').text(currentCount);
        $('#maxCapacity').text(maxCapacity);
        $('#availableSlots').text(maxCapacity - currentCount);
    }

    /**
     * 清除选择
     */
    clearSelection() {
        this.selectedPetId = null;
        this.selectedPetData = null;
        $('.pet-row').removeClass('selected');
        $('#petDetailPanel').removeClass('show');
        this.updateOperationButtons();
    }

    /**
     * 显示加载状态
     */
    showLoading(show) {
        if (show) {
            $('#petTableBody').html('<tr><td colspan="6" class="loading">正在加载...</td></tr>');
        }
    }

    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        if (typeof showMessage === 'function') {
            showMessage(message, type);
        } else {
            alert(message);
        }
    }

    /**
     * 获取五行颜色
     */
    getElementColor(element) {
        const colors = {
            '金': '#FFD700',
            '木': '#228B22',
            '水': '#1E90FF',
            '火': '#FF4500',
            '土': '#8B4513',
            '神': '#9932CC',
            '神圣': '#FFD700',
            '魔': '#8B0000',
            '巫': '#4B0082'
        };
        return colors[element] || '#6c757d';
    }

    /**
     * 更新分页
     */
    updatePagination(totalCount) {
        this.totalPages = Math.ceil(totalCount / this.pageSize);
        const pagination = $('#pagination');
        pagination.empty();
        
        if (this.totalPages <= 1) return;
        
        // 上一页按钮
        const prevBtn = $(`<button ${this.currentPage <= 1 ? 'disabled' : ''}>上一页</button>`);
        prevBtn.on('click', () => {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.loadPets();
            }
        });
        pagination.append(prevBtn);
        
        // 页码按钮
        for (let i = 1; i <= this.totalPages; i++) {
            const pageBtn = $(`<button class="${i === this.currentPage ? 'active' : ''}">${i}</button>`);
            pageBtn.on('click', () => {
                this.currentPage = i;
                this.loadPets();
            });
            pagination.append(pageBtn);
        }
        
        // 下一页按钮
        const nextBtn = $(`<button ${this.currentPage >= this.totalPages ? 'disabled' : ''}>下一页</button>`);
        nextBtn.on('click', () => {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
                this.loadPets();
            }
        });
        pagination.append(nextBtn);
    }
}

// 导出到全局作用域
window.PetManagement = PetManagement;
