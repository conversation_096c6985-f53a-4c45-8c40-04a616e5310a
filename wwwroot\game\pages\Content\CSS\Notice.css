﻿        body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, textarea, p, blockquote, th, td, em, img {
            padding: 0;
            margin: 0;
            outline: none;
        }

        .task {
            width: 788px;
            height: 319px;
            background: #f2ebc5;
            color: #B06A01;
            font-size: 12px;
        }

        .task_left {
            width: 138px;
            height: 319px;
            float: left;
            background-image: url(/./Content/Img/Notice/ggp_01.jpg);
        }

        .task_right {
            width: 650px;
            height: 319px;
            float: left;
            background-image: url(/./Content/Img/Notice/cangku02.jpg);
        }

        .table_info {
            position: absolute;
            left: 0px;
            width: 280px;
            top: 24px;
            height: 208px;
            overflow: auto;
        }

        .gg {
            position: relative;
            top: 10px;
            left: 10px;
            width: 100px;
        }

        .cmd_content {
            position: relative;
            top: 10px;
            left: 10px;
        }

        .bming {
            position: relative;
            top: 0px;
            width: 280px;
            text-align: center;
            height: 230px;
        }

        #userdeal img {
            cursor: pointer;
        }

        #Layer1 {
            position: absolute;
            width: 39px;
            height: 17px;
            z-index: 1;
            left: 89px;
            top: 281px;
            background-image: url(/./Content/Img/Notice/cangku04.jpg);
        }

        .clearfix:after {
            content: ".";
            display: block;
            height: 0;
            clear: both;
            visibility: hidden;
        }

        .clearfix {
            zoom: 1;
        }

        ol, ul {
            list-style: none;
        }

        .task_nav {
            width: 640px;
            height: 29px;
        }

            .task_nav li {
                float: left;
                height: 29px;
            }

                .task_nav li a {
                    width: 89px;
                    display: block;
                }

        #con_tab_6 li {
            float: left;
            height: 26px;
            margin-right: 21px;
        }

            #con_tab_6 li a {
                width: 80px;
                display: block;
            }

        #con_tab_6 li {
            float: left;
            height: 26px;
            margin-right: 21px;
        }

            #con_tab_6 li a {
                width: 80px;
                display: block;
            }

        #con_tab6_1 li, #con_tab6_2 li, #con_tab6_3 li {
            float: left;
            height: 25px;
            margin: 0px;
        }

        .head1_01 {
            background: url(/./Content/Img/Notice/table_head1.jpg) no-repeat;
            height: 25px;
        }

        .head1_02 {
            background: url(/./Content/Img/Notice/table_head1.jpg) no-repeat -46px 0;
            height: 25px;
        }

        .head1_03 {
            background: url(/./Content/Img/Notice/table_head1.jpg) no-repeat -183px 0;
            height: 25px;
        }

        #con_tab6_1 li.on .head1_01, #con_tab6_2 li.on .head1_01, #con_tab6_3 li.on .head1_01 {
            background: url(/./Content/Img/Notice/table_head1.jpg) no-repeat 0 -25px;
            height: 25px;
        }

        #con_tab6_1 li.on .head1_02, #con_tab6_2 li.on .head1_02, #con_tab6_3 li.on .head1_02 {
            background: url(/./Content/Img/Notice/table_head1.jpg) no-repeat -46px -25px;
            height: 25px;
        }

        #con_tab6_1 li.on .head1_03, #con_tab6_2 li.on .head1_03, #con_tab6_3 li.on .head1_03 {
            background: url(/./Content/Img/Notice/table_head1.jpg) no-repeat -183px -25px;
            height: 25px;
        }

        .head_01 {
            background: url(/./Content/Img/Notice/table_head.jpg) no-repeat;
            height: 25px;
        }

        .head_02 {
            background: url(/./Content/Img/Notice/table_head.jpg) no-repeat -46px 0;
            height: 25px;
        }

        .head_03 {
            background: url(/./Content/Img/Notice/table_head.jpg) no-repeat -146px 0;
            height: 25px;
        }

        .head_04 {
            background: url(/./Content/Img/Notice/table_head.jpg) no-repeat -183px 0;
            height: 25px;
        }

        .head_05 {
            background: url(/./Content/Img/Notice/table_head.jpg) no-repeat -229px 0;
            height: 25px;
        }

        #con_tab6_1 li.on .head_01, #con_tab6_2 li.on .head_01, #con_tab6_3 li.on .head_01 {
            background: url(/./Content/Img/Notice/table_head.jpg) no-repeat 0 -25px;
            height: 25px;
        }

        #con_tab6_1 li.on .head_02, #con_tab6_2 li.on .head_02, #con_tab6_3 li.on .head_02 {
            background: url(/./Content/Img/Notice/table_head.jpg) no-repeat -46px -25px;
            height: 25px;
        }

        #con_tab6_1 li.on .head_03, #con_tab6_2 li.on .head_03, #con_tab6_3 li.on .head_03 {
            background: url(/./Content/Img/Notice/table_head.jpg) no-repeat -146px -25px;
            height: 25px;
        }

        #con_tab6_1 li.on .head_04, #con_tab6_2 li.on .head_04, #con_tab6_3 li.on .head_04 {
            background: url(/./Content/Img/Notice/table_head.jpg) no-repeat -183px -25px;
            height: 25px;
        }

        #con_tab6_1 li.on .head_05, #con_tab6_2 li.on .head_05, #con_tab6_3 li.on .head_05 {
            background: url(/./Content/Img/Notice/table_head.jpg) no-repeat -229px -25px;
            height: 25px;
        }

        .a6_01 {
            background: url(/./Content/Img/Notice/ggp_04_01.jpg) no-repeat 0 -26px;
            height: 26px;
        }

        #con_tab_6 li.on .a6_01 {
            background: url(/./Content/Img/Notice/ggp_04_01.jpg) no-repeat;
            height: 26px;
        }

        .a6_02 {
            background: url(/./Content/Img/Notice/ggp_04_01.jpg) no-repeat -80px -26px;
            height: 26px;
        }

        #con_tab_6 li.on .a6_02 {
            background: url(/./Content/Img/Notice/ggp_04_01.jpg) no-repeat -80px 0;
            height: 26px;
        }

        .a6_03 {
            background: url(/./Content/Img/Notice/ggp_04_01.jpg) no-repeat -160px -26px;
            height: 26px;
        }

        #con_tab_6 li.on .a6_03 {
            background: url(/./Content/Img/Notice/ggp_04_01.jpg) no-repeat -160px 0;
            height: 26px;
        }

        .a01 {
            background: url(/./Content/Img/Notice/ggp_04.jpg) no-repeat;
            height: 29px;
        }

        .task_nav li.on .a01 {
            background: url(/./Content/Img/Notice/ggp_04.jpg) no-repeat 0 -29px;
            height: 29px;
        }

        .a02 {
            background: url(/./Content/Img/Notice/ggp_04.jpg) no-repeat -89px 0;
            height: 29px;
        }

        .task_nav li.on .a02 {
            background: url(/./Content/Img/Notice/ggp_04.jpg) no-repeat -89px -29px;
            height: 29px;
        }

        .a03 {
            background: url(/./Content/Img/Notice/ggp_04.jpg) no-repeat -178px 0;
            height: 29px;
        }

        .task_nav li.on .a03 {
            background: url(/./Content/Img/Notice/ggp_04.jpg) no-repeat -178px -29px;
            height: 29px;
        }

        .a04 {
            background: url(/./Content/Img/Notice/ggp_04.jpg) no-repeat -267px 0;
            height: 29px;
        }

        .task_nav li.on .a04 {
            background: url(/./Content/Img/Notice/ggp_04.jpg) no-repeat -267px -29px;
            height: 29px;
        }

        .a05 {
            background: url(/./Content/Img/Notice/ggp_04.jpg) no-repeat -356px 0;
            height: 29px;
        }

        .task_nav li.on .a05 {
            background: url(/./Content/Img/Notice/ggp_04.jpg) no-repeat -356px -29px;
            height: 29px;
        }

        .a06 {
            background: url(/./Content/Img/Notice/ggp_04.jpg) no-repeat -445px 0;
            height: 29px;
        }

        .task_nav li.on .a06 {
            background: url(/./Content/Img/Notice/ggp_04.jpg) no-repeat -445px -29px;
            height: 29px;
        }

        .box01 {
            width: 292px;
            float: left;
            padding-left: 14px;
        }

        .box02 {
            width: 292px;
            float: left;
            height: 28px;
        }

        .box03 {
            width: 290px;
            float: left;
            border: #D9BD7A 1px solid;
            height: 235px;
            background-color: #FFF;
        }

        .box04 {
            width: 292px;
            float: left;
            height: 20px;
            color: #B06A01;
            padding-top: 4px;
        }

        .bb01 {
            height: 24px;
            float: left;
            padding-top: 3px;
        }

        .bb02 {
            height: 20px;
            float: left;
            padding-top: 8px;
        }

        .dt_list {
            overflow-x: hidden;
            overflow-y: auto;
            background: #fff;
            height: 210px;
            width: 290px;
            scrollbar-face-color: #E1D395;
            scrollbar-highlight-color: #ffffff;
            scrollbar-3dlight-color: #E1D395;
            scrollbar-shadow-color: #ffffff;
            scrollbar-darkshadow-color: #F3EDC9;
            scrollbar-track-color: #F3EDC9;
            scrollbar-arrow-color: #ffffff;
            color: #BF7D1A;
            border-top-width: 1px;
            border-right-width: 0px;
            border-bottom-width: 0px;
            border-left-width: 0px;
            border-top-style: solid;
            border-right-style: none;
            border-bottom-style: none;
            border-left-style: none;
            border-top-color: #D9BD7A;
        }

            .dt_list li {
                line-height: 24px;
                height: 24px;
                overflow: hidden;
            }

                .dt_list li a {
                    overflow: hidden;
                    padding-left: 5px;
                }

                .dt_list li span {
                    padding-right: 5px;
                    float: right;
                    color: #060;
                }

        .dt_task {
            width: 640px;
            height: 265px;
            float: left;
        }

        .dt_list2 {
            height: 235px;
            width: 290px;
            margin: 28px 0 0 0;
            border: #D9BD7A 1px solid;
        }

            .dt_list2 p {
                line-height: 20px;
                color: #BF7D1A;
                font-size: 12px;
            }

                .dt_list2 p b {
                    font-weight: normal;
                    color: #060;
                }

        .part_con {
            text-indent: 2em;
            color: #bf7d1a;
            font-size: 12px;
            line-height: 20px;
        }

        .plus {
            clear: both;
            height: 24px;
            line-height: 24px;
            text-align: right;
        }

            .plus a {
                color: #f00;
            }

        .con {
            display: none;
        }

        .intxt {
            border: #b4a449 1px solid;
            padding: 2px;
            margin: 0 10px 0 200px;
        }

        .stu {
            background: url(/./Content/Img/Notice/i_bnt.gif) no-repeat 0 -34px;
            width: 39px;
            height: 17px;
            cursor: pointer;
            border: 0 none;
            margin: 1px 0;
        }

        .tit01 {
            border-collapse: collapse;
            width: 100%;
            font-size: 12px;
        }

            .tit01 td {
                text-align: center;
                padding: 1px;
                line-height: 20px;
                color: #bf7d1a;
            }

        #textarea {
            background: #fffceb;
            width: 615px;
            height: 235px;
            border: #c6b764 1px solid;
            color: #bf7d1a;
            font-size: 12px;
            margin: 10px 0 0 10px;
            padding: 5px;
            overflow-x: hidden;
            overflow-y: auto;
            scrollbar-face-color: #E1D395;
            scrollbar-highlight-color: #ffffff;
            scrollbar-3dlight-color: #E1D395;
            scrollbar-shadow-color: #ffffff;
            scrollbar-darkshadow-color: #F3EDC9;
            scrollbar-track-color: #F3EDC9;
            scrollbar-arrow-color: #ffffff;
        }

        .menu {
            position: absolute;
            top: 40px;
            left: 138px;
            width: 280px;
            height: 230px;
            border: 1px #996600 solid;
            background: #f9f0d6;
            z-index: 1;
        }

        #cmd {
            position: absolute;
            width: 257px;
            height: 162px;
            background: url(/./Content/Img/Notice/ggp_04_gg.jpg);
            top: 58px;
            left: 450px;
        }

        #help {
            position: absolute;
            width: 45px;
            height: 18px;
            top: 35px;
            left: 700px;
        }

        #userdeal {
            position: absolute;
            width: 280px;
            height: auto;
            top: 240px;
            left: 440px;
        }

        #bg {
            position: absolute;
            height: 220px;
            width: 65px;
            left: 700px;
            top: 80px;
        }

            #bg img {
                padding: 0px;
                border: 0px;
                margin: 0px;
                display: block;
            }

        .sq {
            position: absolute;
            width: auto;
            top: 271px;
            z-index: 2;
        }

        .gangyin {
            position: absolute;
            top: 50px;
            left: 390px;
            width: 70px;
            height: 70px;
            z-index: 2;
        }

        .useyao {
            display: block;
            float: left;
            margin-left: 10px;
            position: relative;
            top: 10px;
            bottom: 10px;
        }

        #zt {
            cursor: pointer;
            color: blue;
            display: block;
            position: relative;
            bottom: 10px;
            top: 10px;
            display: block;
            float: left;
        }

        #sx {
            cursor: pointer;
            color: blue;
            display: block;
            position: relative;
            bottom: 10px;
            top: 10px;
            display: block;
            float: left;
            left: 10px;
        }

        #helpbox {
            width: 620px;
            height: 120px;
            z-index: 5;
            top: 100px;
            position: absolute;
            background: url(/./Content/Img/Notice/tsbg.gif);
        }