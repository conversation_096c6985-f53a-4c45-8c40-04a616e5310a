﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///宠物转生记录表
    ///</summary>
    [SugarTable("pet_nirvana_log")]
    public partial class pet_nirvana_log
    {
           public pet_nirvana_log(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:用户ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:主宠物ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int main_pet_id {get;set;}

           /// <summary>
           /// Desc:副宠物ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int sub_pet_id {get;set;}

           /// <summary>
           /// Desc:涅槃兽ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int nirvana_pet_id {get;set;}

           /// <summary>
           /// Desc:主宠物编号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int main_pet_no {get;set;}

           /// <summary>
           /// Desc:副宠物编号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int sub_pet_no {get;set;}

           /// <summary>
           /// Desc:涅槃兽编号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int nirvana_pet_no {get;set;}

           /// <summary>
           /// Desc:主宠成长
           /// Default:
           /// Nullable:False
           /// </summary>           
           public decimal main_growth {get;set;}

           /// <summary>
           /// Desc:副宠成长
           /// Default:
           /// Nullable:False
           /// </summary>           
           public decimal sub_growth {get;set;}

           /// <summary>
           /// Desc:主宠等级
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int main_level {get;set;}

           /// <summary>
           /// Desc:副宠等级
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int sub_level {get;set;}

           /// <summary>
           /// Desc:转生结果宠物编号
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? result_pet_no {get;set;}

           /// <summary>
           /// Desc:转生结果成长
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? result_growth {get;set;}

           /// <summary>
           /// Desc:实际成功率
           /// Default:
           /// Nullable:False
           /// </summary>           
           public decimal success_rate {get;set;}

           /// <summary>
           /// Desc:使用的辅助道具ID
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? used_item_id {get;set;}

           /// <summary>
           /// Desc:消耗金币
           /// Default:
           /// Nullable:False
           /// </summary>           
           public long cost_gold {get;set;}

           /// <summary>
           /// Desc:是否成功
           /// Default:true
           /// Nullable:False
           /// </summary>           
           public bool is_success {get;set;}

           /// <summary>
           /// Desc:VIP加成(%)
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? vip_bonus {get;set;}

           /// <summary>
           /// Desc:转生类型
           /// Default:NORMAL
           /// Nullable:True
           /// </summary>           
           public string? nirvana_type {get;set;}

           /// <summary>
           /// Desc:转生时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

    }
}
