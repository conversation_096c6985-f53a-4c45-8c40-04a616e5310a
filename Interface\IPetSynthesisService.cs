using WebApplication_HM.DTOs;
using WebApplication_HM.Models;

namespace WebApplication_HM.Interface
{
    /// <summary>
    /// 宠物合成服务接口
    /// </summary>
    public interface IPetSynthesisService
    {
        /// <summary>
        /// 执行宠物合成
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="request">合成请求</param>
        /// <returns>合成结果</returns>
        Task<SynthesisResultDto> SynthesizePetAsync(int userId, SynthesisRequestDto request);

        /// <summary>
        /// 获取合成配置信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="mainPetId">主宠ID</param>
        /// <param name="vicePetId">副宠ID</param>
        /// <returns>合成配置</returns>
        Task<SynthesisConfigDto> GetSynthesisConfigAsync(int userId, int mainPetId, int vicePetId);

        /// <summary>
        /// 获取合成历史
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="petId">宠物ID（可选）</param>
        /// <returns>合成历史列表</returns>
        Task<List<SynthesisHistoryDto>> GetSynthesisHistoryAsync(int userId, int? petId = null);

        /// <summary>
        /// 获取合成统计
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>合成统计</returns>
        Task<SynthesisStatisticsDto> GetSynthesisStatisticsAsync(int userId);

        /// <summary>
        /// 验证合成条件
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="mainPetId">主宠ID</param>
        /// <param name="vicePetId">副宠ID</param>
        /// <returns>验证结果</returns>
        Task<(bool IsValid, string ErrorMessage)> ValidateSynthesisConditionsAsync(int userId, int mainPetId, int vicePetId);

        /// <summary>
        /// 查找合成公式
        /// </summary>
        /// <param name="mainPetNo">主宠编号</param>
        /// <param name="vicePetNo">副宠编号</param>
        /// <param name="mainGrowth">主宠成长</param>
        /// <param name="viceGrowth">副宠成长</param>
        /// <returns>合成公式</returns>
        Task<pet_synthesis_formula> FindFormulaAsync(int mainPetNo, int vicePetNo, decimal mainGrowth, decimal viceGrowth);

        /// <summary>
        /// 计算合成成功率
        /// </summary>
        /// <param name="context">合成上下文</param>
        /// <returns>成功率</returns>
        Task<decimal> CalculateSuccessRateAsync(SynthesisContext context);

        /// <summary>
        /// 计算成长增加值
        /// </summary>
        /// <param name="context">合成上下文</param>
        /// <returns>成长增加值</returns>
        Task<decimal> CalculateGrowthIncreaseAsync(SynthesisContext context);

        /// <summary>
        /// 计算神宠合成概率
        /// </summary>
        /// <param name="currentGrowth">当前成长</param>
        /// <returns>神宠概率</returns>
        Task<decimal> CalculateGodPetProbabilityAsync(decimal currentGrowth);

        /// <summary>
        /// 获取系统配置值
        /// </summary>
        /// <param name="configKey">配置键</param>
        /// <returns>配置值</returns>
        Task<T> GetConfigValueAsync<T>(string configKey);

        /// <summary>
        /// 获取VIP加成信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>VIP加成信息</returns>
        Task<VipBonusDto> GetVipBonusAsync(int userId);
    }

    /// <summary>
    /// 合成公式服务接口
    /// </summary>
    public interface ISynthesisFormulaService
    {
        /// <summary>
        /// 查找匹配的公式
        /// </summary>
        /// <param name="mainPetNo">主宠编号</param>
        /// <param name="vicePetNo">副宠编号</param>
        /// <param name="mainGrowth">主宠成长</param>
        /// <param name="viceGrowth">副宠成长</param>
        /// <returns>匹配的公式</returns>
        Task<pet_synthesis_formula> FindMatchingFormulaAsync(int mainPetNo, int vicePetNo, decimal mainGrowth, decimal viceGrowth);

        /// <summary>
        /// 获取所有公式
        /// </summary>
        /// <returns>公式列表</returns>
        Task<List<pet_synthesis_formula>> GetAllFormulasAsync();

        /// <summary>
        /// 添加公式
        /// </summary>
        /// <param name="formula">公式</param>
        /// <returns>是否成功</returns>
        Task<bool> AddFormulaAsync(pet_synthesis_formula formula);

        /// <summary>
        /// 更新公式
        /// </summary>
        /// <param name="formula">公式</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateFormulaAsync(pet_synthesis_formula formula);

        /// <summary>
        /// 删除公式
        /// </summary>
        /// <param name="formulaId">公式ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteFormulaAsync(int formulaId);

        /// <summary>
        /// 清除公式缓存
        /// </summary>
        Task ClearFormulaCacheAsync();
    }

    /// <summary>
    /// 合成成长计算服务接口
    /// </summary>
    public interface ISynthesisGrowthCalculator
    {
        /// <summary>
        /// 计算成长增加值
        /// </summary>
        /// <param name="context">合成上下文</param>
        /// <returns>成长增加值</returns>
        Task<decimal> CalculateGrowthIncreaseAsync(SynthesisContext context);

        /// <summary>
        /// 计算成长限制系数
        /// </summary>
        /// <param name="currentGrowth">当前成长</param>
        /// <returns>成长限制系数</returns>
        decimal CalculateGrowthLimitFactor(decimal currentGrowth);

        /// <summary>
        /// 计算基础成长增加
        /// </summary>
        /// <param name="context">合成上下文</param>
        /// <returns>基础成长增加</returns>
        decimal CalculateBaseGrowthIncrease(SynthesisContext context);

        /// <summary>
        /// 计算VIP加成系数
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>VIP加成系数</returns>
        Task<decimal> GetVipMultiplierAsync(int userId);

        /// <summary>
        /// 计算道具加成系数
        /// </summary>
        /// <param name="usedItems">使用的道具</param>
        /// <returns>道具加成系数</returns>
        Task<decimal> CalculateItemMultiplierAsync(List<string> usedItems);

        /// <summary>
        /// 获取效果系数
        /// </summary>
        /// <returns>效果系数</returns>
        Task<decimal> GetEffectMultiplierAsync();
    }

    /// <summary>
    /// 神宠合成计算服务接口
    /// </summary>
    public interface IGodPetSynthesisCalculator
    {
        /// <summary>
        /// 判断是否应该合成神宠
        /// </summary>
        /// <param name="currentGrowth">当前成长</param>
        /// <returns>是否合成神宠</returns>
        Task<bool> ShouldBecomeGodPetAsync(decimal currentGrowth);

        /// <summary>
        /// 选择神宠
        /// </summary>
        /// <returns>神宠编号</returns>
        Task<int> SelectGodPetAsync();

        /// <summary>
        /// 计算神宠概率
        /// </summary>
        /// <param name="currentGrowth">当前成长</param>
        /// <returns>神宠概率</returns>
        Task<decimal> CalculateGodPetProbabilityAsync(decimal currentGrowth);

        /// <summary>
        /// 应用神宠成长限制
        /// </summary>
        /// <param name="currentGrowth">当前成长</param>
        /// <param name="growthIncrease">成长增加</param>
        /// <returns>最终成长</returns>
        decimal ApplyGodPetGrowthLimit(decimal currentGrowth, decimal growthIncrease);
    }
}
