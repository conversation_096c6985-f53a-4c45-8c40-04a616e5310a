using Microsoft.Extensions.Caching.Memory;
using SqlSugar;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;
using WebApplication_HM.Sugar;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 等级仓储实现
    /// </summary>
    public class LevelRepository : ILevelRepository
    {
        private readonly DbContext _dbContext;
        private readonly IMemoryCache _cache;
        private readonly ILogger<LevelRepository> _logger;
        
        private const string CACHE_KEY_ALL_LEVELS = "level:all_configs_{0}";
        private const string CACHE_KEY_SYSTEM_CONFIG = "level:system_config_{0}";
        private const int CACHE_EXPIRY_MINUTES = 60;

        public LevelRepository(DbContext dbContext, IMemoryCache cache, ILogger<LevelRepository> logger)
        {
            _dbContext = dbContext;
            _cache = cache;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有等级配置
        /// </summary>
        public async Task<List<LevelConfig>> GetAllLevelConfigsAsync(string systemName = "pet")
        {
            try
            {
                string cacheKey = string.Format(CACHE_KEY_ALL_LEVELS, systemName);
                
                return await _cache.GetOrCreateAsync(cacheKey, async entry =>
                {
                    entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(CACHE_EXPIRY_MINUTES);
                    
                    var configs = await _dbContext.Db.Queryable<LevelConfig>()
                        .Where(x => x.is_active == 1)
                        .OrderBy(x => x.level)
                        .ToListAsync();
                        
                    _logger.LogInformation("从数据库加载等级配置 {Count} 条，系统: {SystemName}", configs.Count, systemName);
                    return configs;
                }) ?? new List<LevelConfig>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取等级配置失败，系统: {SystemName}", systemName);
                return new List<LevelConfig>();
            }
        }

        /// <summary>
        /// 获取指定等级的配置
        /// </summary>
        public async Task<LevelConfig?> GetLevelConfigAsync(int level, string systemName = "pet")
        {
            try
            {
                var configs = await GetAllLevelConfigsAsync(systemName);
                return configs.FirstOrDefault(x => x.level == level);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取等级配置失败，等级: {Level}, 系统: {SystemName}", level, systemName);
                return null;
            }
        }

        /// <summary>
        /// 根据经验计算等级（优化的二分查找算法）
        /// </summary>
        public async Task<int> CalculateLevelAsync(long exp, string systemName = "pet")
        {
            try
            {
                // 边界检查
                if (exp <= 0) return 1;
                
                var configs = await GetAllLevelConfigsAsync(systemName);
                if (configs.Count == 0) return 1;
                
                // 检查是否超过最高等级
                var maxConfig = configs.LastOrDefault();
                if (maxConfig != null && exp >= maxConfig.required_exp)
                {
                    return maxConfig.level;
                }
                
                // 二分查找优化
                int left = 0, right = configs.Count - 1;
                int result = 1;
                
                while (left <= right)
                {
                    int mid = left + (right - left) / 2;
                    
                    if (exp >= configs[mid].required_exp)
                    {
                        result = configs[mid].level;
                        left = mid + 1;
                    }
                    else
                    {
                        right = mid - 1;
                    }
                }
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算等级失败，经验: {Exp}, 系统: {SystemName}", exp, systemName);
                return 1; // 默认返回1级
            }
        }

        /// <summary>
        /// 获取经验系统配置
        /// </summary>
        public async Task<ExpSystemConfig?> GetSystemConfigAsync(string systemName)
        {
            try
            {
                string cacheKey = string.Format(CACHE_KEY_SYSTEM_CONFIG, systemName);
                
                return await _cache.GetOrCreateAsync(cacheKey, async entry =>
                {
                    entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(CACHE_EXPIRY_MINUTES);
                    
                    var config = await _dbContext.Db.Queryable<ExpSystemConfig>()
                        .Where(x => x.system_name == systemName && x.is_active == 1)
                        .FirstAsync();
                        
                    _logger.LogInformation("从数据库加载系统配置，系统: {SystemName}", systemName);
                    return config;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统配置失败，系统: {SystemName}", systemName);
                return null;
            }
        }

        /// <summary>
        /// 获取等级范围内的配置
        /// </summary>
        public async Task<List<LevelConfig>> GetLevelRangeAsync(int startLevel, int endLevel, string systemName = "pet")
        {
            try
            {
                var configs = await GetAllLevelConfigsAsync(systemName);
                return configs.Where(x => x.level >= startLevel && x.level <= endLevel).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取等级范围配置失败，范围: {StartLevel}-{EndLevel}, 系统: {SystemName}", 
                    startLevel, endLevel, systemName);
                return new List<LevelConfig>();
            }
        }

        /// <summary>
        /// 添加等级变更日志
        /// </summary>
        public async Task<bool> AddLevelChangeLogAsync(LevelChangeLog log)
        {
            try
            {
                var result = await _dbContext.Db.Insertable(log).ExecuteCommandAsync();
                
                _logger.LogInformation("等级变更记录: 用户{UserId} 宠物{PetId} {OldLevel}级→{NewLevel}级 原因:{Reason}", 
                    log.user_id, log.pet_id, log.old_level, log.new_level, log.change_reason);
                
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录等级变更失败");
                return false;
            }
        }

        /// <summary>
        /// 获取用户等级变更历史
        /// </summary>
        public async Task<List<LevelChangeLog>> GetLevelChangeHistoryAsync(int userId, int? petId = null, int limit = 50)
        {
            try
            {
                var query = _dbContext.Db.Queryable<LevelChangeLog>()
                    .Where(x => x.user_id == userId);
                
                if (petId.HasValue)
                {
                    query = query.Where(x => x.pet_id == petId.Value);
                }
                
                var logs = await query
                    .OrderBy(x => x.change_time, OrderByType.Desc)
                    .Take(limit)
                    .ToListAsync();
                
                return logs;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取等级变更历史失败，用户ID: {UserId}, 宠物ID: {PetId}", userId, petId);
                return new List<LevelChangeLog>();
            }
        }
    }
}
