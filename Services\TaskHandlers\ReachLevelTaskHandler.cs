using SqlSugar;
using WebApplication_HM.Models;
using WebApplication_HM.Models.Define;
using TaskStatus = WebApplication_HM.Models.Define.TaskStatus;

namespace WebApplication_HM.Services.TaskHandlers
{
    /// <summary>
    /// 达到等级任务处理器
    /// </summary>
    public class ReachLevelTaskHandler : BaseTaskHandler
    {
        public ReachLevelTaskHandler(ISqlSugarClient db, ILogger<ReachLevelTaskHandler> logger) 
            : base(db, logger)
        {
        }

        public override string SupportedObjectiveType => TaskObjectiveTypes.REACH_LEVEL;

        /// <summary>
        /// 检查等级任务进度
        /// </summary>
        public override async Task<int> CheckProgressAsync(int userId, task_objective objective)
        {
            if (!ValidateObjective(objective))
                return 0;

            try
            {
                // 获取用户当前等级
                var user = await _db.Queryable<user>()
                    .Where(u => u.id == userId)
                    .FirstAsync();

                if (user == null)
                {
                    _logger.LogWarning("用户不存在: UserId={UserId}", userId);
                    return 0;
                }

                int currentLevel = user.vip_level ?? 1;

                LogHandlerAction("检查用户等级", userId, objective, new { CurrentLevel = currentLevel });
                
                // 返回当前等级，但不超过任务要求的等级
                return Math.Min(currentLevel, objective.target_amount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查等级任务进度失败: UserId={UserId}, ObjectiveId={ObjectiveId}", 
                    userId, objective.objective_id);
                return 0;
            }
        }

        /// <summary>
        /// 更新等级任务进度
        /// </summary>
        public override async Task<int> UpdateProgressAsync(int userId, task_objective objective, int amount)
        {
            if (!ValidateObjective(objective))
                return 0;

            try
            {
                // 等级任务的进度更新是检查当前等级
                var currentLevel = await CheckProgressAsync(userId, objective);

                // 查找用户相关的任务进度
                var taskProgress = await _db.Queryable<user_task, user_task_progress>((ut, utp) => new JoinQueryInfos(
                    JoinType.Inner, ut.user_task_id == utp.user_task_id))
                    .Where((ut, utp) => ut.user_id == userId && 
                                      utp.objective_id == objective.objective_id &&
                                      ut.task_status == (byte)TaskStatus.InProgress)
                    .Select((ut, utp) => new { UserTask = ut, Progress = utp })
                    .FirstAsync();

                if (taskProgress == null)
                {
                    _logger.LogWarning("未找到用户等级任务进度: UserId={UserId}, ObjectiveId={ObjectiveId}", 
                        userId, objective.objective_id);
                    return 0;
                }

                var progress = taskProgress.Progress;
                var oldAmount = progress.current_amount ?? 0;

                // 更新进度为当前等级
                progress.current_amount = currentLevel;
                progress.is_completed = (byte)(IsProgressCompleted(currentLevel, objective.target_amount) ? 1 : 0);

                var updateResult = await UpdateUserTaskProgressAsync(progress);
                
                if (updateResult)
                {
                    LogHandlerAction("更新进度成功", userId, objective, new { 
                        OldAmount = oldAmount, 
                        NewAmount = currentLevel,
                        TargetLevel = objective.target_amount,
                        IsCompleted = progress.is_completed == 1
                    });
                    
                    return currentLevel;
                }
                else
                {
                    _logger.LogError("更新等级任务进度失败: UserId={UserId}, ObjectiveId={ObjectiveId}", 
                        userId, objective.objective_id);
                    return oldAmount;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新等级任务进度失败: UserId={UserId}, ObjectiveId={ObjectiveId}", 
                    userId, objective.objective_id);
                return 0;
            }
        }

        /// <summary>
        /// 处理用户升级事件
        /// </summary>
        public async Task<int> HandleLevelUpAsync(int userId, int newLevel)
        {
            try
            {
                // 查找所有相关的等级任务
                var relevantTasks = await _db.Queryable<user_task, task_config, task_objective, user_task_progress>(
                    (ut, tc, to, utp) => new JoinQueryInfos(
                        JoinType.Inner, ut.task_id == tc.task_id,
                        JoinType.Inner, tc.task_id == to.task_id,
                        JoinType.Inner, ut.user_task_id == utp.user_task_id && utp.objective_id == to.objective_id))
                    .Where((ut, tc, to, utp) => ut.user_id == userId &&
                                              ut.task_status == (byte)TaskStatus.InProgress &&
                                              to.objective_type == TaskObjectiveTypes.REACH_LEVEL &&
                                              utp.is_completed == 0 &&
                                              to.target_amount <= newLevel) // 只更新目标等级已达到的任务
                    .Select((ut, tc, to, utp) => new { UserTask = ut, TaskConfig = tc, Objective = to, Progress = utp })
                    .ToListAsync();

                int totalUpdated = 0;

                foreach (var task in relevantTasks)
                {
                    var updatedAmount = await UpdateProgressAsync(userId, task.Objective, 0); // 传入0表示刷新检查
                    if (updatedAmount >= task.Objective.target_amount && (task.Progress.current_amount ?? 0) < task.Objective.target_amount)
                    {
                        totalUpdated++;
                    }
                }

                if (totalUpdated > 0)
                {
                    _logger.LogInformation("处理用户升级事件: UserId={UserId}, NewLevel={NewLevel}, UpdatedTasks={UpdatedTasks}",
                        userId, newLevel, totalUpdated);
                }

                return totalUpdated;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理用户升级事件失败: UserId={UserId}, NewLevel={NewLevel}", userId, newLevel);
                return 0;
            }
        }

        /// <summary>
        /// 批量检查所有等级任务的进度
        /// </summary>
        public async Task<int> RefreshAllLevelTasksAsync(int userId)
        {
            try
            {
                // 查找用户所有进行中的等级任务
                var levelTasks = await _db.Queryable<user_task, task_config, task_objective, user_task_progress>(
                    (ut, tc, to, utp) => new JoinQueryInfos(
                        JoinType.Inner, ut.task_id == tc.task_id,
                        JoinType.Inner, tc.task_id == to.task_id,
                        JoinType.Inner, ut.user_task_id == utp.user_task_id && utp.objective_id == to.objective_id))
                    .Where((ut, tc, to, utp) => ut.user_id == userId &&
                                              ut.task_status == (byte)TaskStatus.InProgress &&
                                              to.objective_type == TaskObjectiveTypes.REACH_LEVEL &&
                                              utp.is_completed == 0)
                    .Select((ut, tc, to, utp) => new { UserTask = ut, TaskConfig = tc, Objective = to, Progress = utp })
                    .ToListAsync();

                int totalUpdated = 0;

                foreach (var task in levelTasks)
                {
                    var updatedAmount = await UpdateProgressAsync(userId, task.Objective, 0); // 传入0表示刷新检查
                    if (updatedAmount != (task.Progress.current_amount ?? 0))
                    {
                        totalUpdated++;
                    }
                }

                if (totalUpdated > 0)
                {
                    _logger.LogInformation("刷新等级任务进度: UserId={UserId}, UpdatedTasks={UpdatedTasks}",
                        userId, totalUpdated);
                }

                return totalUpdated;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新等级任务进度失败: UserId={UserId}", userId);
                return 0;
            }
        }
    }
}
