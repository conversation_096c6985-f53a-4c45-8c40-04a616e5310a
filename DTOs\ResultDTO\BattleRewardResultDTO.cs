namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 战斗奖励发放结果DTO
    /// </summary>
    public class BattleRewardResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 获得的经验值
        /// </summary>
        public int Exp { get; set; }

        /// <summary>
        /// 掉落的物品列表
        /// </summary>
        public List<string> DropItems { get; set; } = new();
    }
} 