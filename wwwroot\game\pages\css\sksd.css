.main_box {
    background-image: url(../images/sk.png);
    width: 788px;
    height: 311px;
}

html,
body {
    margin: 0;
    padding: 0;
}

.left {
    width: 139px;
    height: 311px;
    float: left;
}

.right {
    width: 632px;
    height: 311px;
    float: left;
}

.red_btn {
    background-image: url(../images/red_btn.png);
    background-repeat: no-repeat;
}

.task_nav {
    width: 640px;
    height: 29px;
    font-family: kd
}

ol,
ul {
    list-style: none;
}

.task_nav li {
    float: left;
    height: 29px;
}

body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
form,
fieldset,
input,
textarea,
p,
blockquote,
th,
td,
em,
img {
    padding: 0;
    margin: 0;
    outline: none;
}

.a01 {
    background: url(../images/menu.jpg) no-repeat;
    height: 29px;
}

.task_nav li.on .a01 {
    background: url(../images/menu.jpg) no-repeat 0 -29px;
    height: 29px;
}

.a02 {
    background: url(../images/menu.jpg) no-repeat -89px 0;
    height: 29px;
}

.task_nav li.on .a02 {
    background: url(../images/menu.jpg) no-repeat -89px -29px;
    height: 29px;
}

.a03 {
    background: url(../images/menu.jpg) no-repeat -178px 0;
    height: 29px;
}

.task_nav li.on .a03 {
    background: url(../images/menu.jpg) no-repeat -178px -29px;
    height: 29px;
}

.a04 {
    background: url(../images/menu.jpg) no-repeat -267px 0;
    height: 29px;
}

.task_nav li.on .a04 {
    background: url(../images/menu.jpg) no-repeat -267px -29px;
    height: 29px;
}

.a05 {
    background: url(../images/menu.jpg) no-repeat -356px 0;
    height: 29px;
}

.task_nav li.on .a05 {
    background: url(../images/menu.jpg) no-repeat -356px -29px;
    height: 29px;
}

.a06 {
    background: url(../images/menu.jpg) no-repeat -445px 0;
    height: 29px;
}

.task_nav li.on .a06 {
    background: url(../images/menu.jpg) no-repeat -445px -29px;
    height: 29px;
}

.a07 {
    background: url(../images/menu.jpg) no-repeat -410px 0;
    height: 29px;
}

.task_nav li.on .a07 {
    background: url(../images/menu.jpg) no-repeat -410px -29px;
    height: 29px;
}

.task_nav li a {
    width: 89px;
    display: block;
    text-decoration: none;
    color: #C6872A;
    font-weight: bold;
    line-height: 34px;
    text-align: center;
    font-size: 12PX;
}

.tab_1 {
    background-image: url(../images/tab1_back.png);
    width: 620px;
    height: 257px;
    margin-top: 11px;
    margin-left: 12px;
    background-repeat: no-repeat;
}

.sb_box_1 {
    background-image: url(../images/sbg_box1.png);
    width: 220px;
    height: 207px;
    float: left;
    margin-top: 17px;
}

.sb_box_2 {
    background-image: url(../images/sbg_box2.png);
    width: 180px;
    height: 245px;
    float: left;
}

.title_bh {
    background-image: url(../images/png_bh.png);
    width: 31px;
    height: 14px;
    margin-top: 20px;
    margin-left: 93px;
}

.title_wl {
    background-image: url(../images/png_wl.png);
    width: 31px;
    height: 14px;
    margin-top: 20px;
    margin-left: 93px;
}

.title_sb {
    background-image: url(../images/png_sb.png);
    width: 36px;
    height: 16px;
    margin-top: 18px;
    margin-left: 74px;
}

.sb_box_1 .sb_title {
    margin-left: 46px;
    text-align: center;
    width: 130px;
    margin-top: 5px;
    color: #390902;
    font-size: 12px;
    font-weight: bold;
    letter-spacing: 1px;
}

.sb_box_2 .sb_title {
    margin-top: 162px;
    width: 135px;
    text-align: center;
    margin-left: 24px;
    color: #700f01;
    font-size: 15.8px;
    font-weight: bolder;
    letter-spacing: 3px;
    text-indent: 3px;
}

.sb_box_1 .sb_btn1 {
    width: 50px;
    height: 50px;
    margin-top: 98px;
    margin-left: 35px;
    display: inline-block;
    cursor: not-allowed;
    position: absolute;
    z-index: 100;
}

.sb_box_1 .sb_btn2 {
    width: 55px;
    height: 50px;
    margin-top: 98px;
    margin-left: 128px;
    display: inline-block;
    cursor: pointer;
    position: absolute;
    z-index: 100;
}

.sb_box_2 .sb_btn1 {
    width: 30px;
    height: 30px;
    margin: -1px 9px;
    cursor: not-allowed;
    display: inline-block;
}

.sb_box_2 .sb_btn2 {
    width: 30px;
    height: 30px;
    cursor: pointer;
    display: inline-block;
    margin-left: 89px;
    margin-top: -1px;
}

.sb_left {
    float: left;
    margin-top: -24px;
    margin-left: 6px;
    cursor: pointer;
}

.sb_right {
    float: right;
    margin-top: -24px;
    margin-right: 7px;
    cursor: pointer;
}

.sb_next {
    width: 100%;
    height: 34px;
    clear: both;
}

.sb_pageInfo {
    color: #fff;
    position: absolute;
    top: 40px;
    left: 153px;
    font-size: 12px;
}

.tab_2 {
    background-image: url(../images/tab2_back.png);
    width: 620px;
    height: 257px;
    margin-top: 11px;
    margin-left: 12px;
    background-repeat: no-repeat;
}

.petCard_list {
    width: 527px;
    margin-left: 40px;
    height: 257px;
    cursor: pointer;
}

.petCard {
    float: left;
    margin-left: 10px;
    height: 257px;
    width: 94px;
    overflow: hidden;
}

.petCard:nth-child(odd) .pcInfo {
    margin-top: -81px;
}

.petCard:nth-child(even) .pcInfo {
    margin-top: -32px;
}

.petCard:nth-child(odd) .pcInfoBox {
    margin-top: -81px;
}

.petCard:nth-child(even) .pcInfoBox {
    margin-top: -32px;
}

.petCard .pcInfoBox {
    background-image: url(../images/heipf.png);
    height: 222px;
    width: 74px;
    position: absolute;
    color: #C7C7C7;
    font-size: 12px;
    padding-top: 145px;
    padding-left: 15px;
    display: none;
    padding-right: 5px;
}

.pcInfo {
    height: 362px;
    width: 94px;
}

.pf_left {
    float: left;
    margin-top: -38px;
    margin-left: -3px;
    cursor: pointer;
}

.pf_right {
    float: right;
    margin-top: -38px;
    margin-right: 2px;
    cursor: pointer;
}


.pf_right1 {
    float: right;
    margin-top: -38px;
    margin-right: 0px;
    cursor: pointer;
}


.pf_next {
    width: 100%;
    height: 34px;
    clear: both;
}

.tab_3 {
    background-image: url(../images/tab3_back.png);
    width: 620px;
    height: 257px;
    margin-top: 11px;
    margin-left: 12px;
    background-repeat: no-repeat;
}

.hqlh_img {
    height: 257px;
    width: 208px;
    margin-top: -9px;
    float: left;
}

.hqlh_btnlist {
    height: 257px;
    width: 38px;
    float: left;
    padding-left: 100px;
    padding-right: 65px;
}

.hqlh_icoList {
    float: left;
    margin-left: 38px;
}

.hqlh_btnlist .ico_img {
    margin-bottom: 18px;
    cursor: pointer;
}

.hqlh_icoList .ico_row {
    margin-bottom: 16px;
}

.hqlh_icoList .ico_img {
    cursor: pointer;
}

.hqlh_icoList .ico_row .ico_img:nth-child(2) {
    margin-left: 16px;
}

.hq_left {
    float: left;
    margin-top: -41px;
    margin-left: 6px;
    cursor: pointer;
}

.hq_right {
    float: right;
    margin-top: -41px;
    margin-right: 14px;
    cursor: pointer;
}

.hq_right1 {
    float: right;
    margin-top: -41px;
    margin-right: 10px;
    cursor: pointer;
}


.ico_img {
    background-repeat: no-repeat;
    display: inline-block;
    width: 38px;
    height: 38px;
}

.hq_next {
    width: 100%;
    height: 34px;
    clear: both;
}

.hei35 {
    height: 38px;
    width: 38px;
    background-image: url(../images/hei.png);
    position: absolute;
    color: #c7c7c7;
    font-size: 12px;
    text-align: center;
    line-height: 38px;
    display: none;
}

.hq_info {
    background-image: url(../images/xilian_hei.png);
    height: 237px;
    width: 188px;
    margin-top: 12px;
    margin-left: -2px;
    color: #c7c7c7;
    padding: 10px;
    text-align: center;
    cursor: pointer;
    line-height: 19px;
    display: none;
    font-size: 15px;
}

.hq_info_sz {
    text-align: left;
    margin-left: 40px;
}

.hq_info_jc {
    line-height: auto;
    position: absolute;
    margin-left: 10px;
    margin-top: -30px;
    padding: 2px;
    padding-left: 5px;
    padding-right: 5px;
}

.hq_info_jc1 {
    line-height: auto;
    position: absolute;
    margin-left: 142px;
    margin-top: -30px;
    padding: 2px;
    padding-left: 5px;
    padding-right: 5px;
}

.hq_info_jc div {}

._min {
    font-size: 12px;
    line-height: 20px;
    color: aquamarine;
}

._zi {
    color: #ff99ff;
}

.ck {
    position: absolute;
    margin-top: 190px;
    margin-left: -48px;
    color: #C6872A;
}

.sb_img {
    width: 106px;
    height: 127px;
    position: absolute;
    margin-left: 56px;
    margin-top: 11px;
}

.sb_img_2 {
    width: 152px;
    height: 122px;
    position: absolute;
    margin-left: 56px;
    margin-top: 11px;
}

.sb_img_2 {
    width: 152px;
    height: 122px;
    position: absolute;
    margin-left: 16px;
    margin-top: -175px;
}

.sb_box_1 .bhInfoBox {
    background-image: url(../images/heisb_1.png);
    height: 89px;
    width: 85px;
    position: absolute;
    color: #C7C7C7;
    font-size: 12px;
    padding-top: 37px;
    padding-left: 21px;
    display: none;
    cursor: pointer;
    margin-left: 1px;
}

.sb_box_2 .bhInfoBox {
    background-image: url(../images/heisb_2.png);
    height: 80px;
    width: 153px;
    position: absolute;
    color: #C7C7C7;
    font-size: 12px;
    padding-top: 41px;
    text-align: center;
    display: none;
    cursor: pointer;
}

.sb_box_1 .bhInfoBox1 {
    background-image: url(../images/heisb_1.png);
    height: 89px;
    width: 106px;
    position: absolute;
    color: #C7C7C7;
    font-size: 12px;
    padding-top: 37px;
    display: none;
    cursor: pointer;
    margin-left: 1px;
    text-align: center;
}

.sb_box_2 .bhInfoBox1 {
    background-image: url(../images/heisb_2.png);
    height: 80px;
    width: 153px;
    position: absolute;
    color: #C7C7C7;
    font-size: 12px;
    padding-top: 41px;
    text-align: center;
    display: none;
    cursor: pointer;
}

._tishi {
    width: 370px;
    height: 247px;
    position: absolute;
    z-index: 10000;
    background-image: url(../images/tishi.png);
    left: 268px;
    top: 25px;
}

._tishi_bottom {
    width: 70px;
    height: 36px;
    position: absolute;
    z-index: 10000;
    background-image: url(../images/btn.png);
    margin-left: 275px;
    margin-top: 93px;
    text-align: center;
    line-height: 36px;
    color: #884c34;
    cursor: pointer;
}

._tishi_bottom:hover {
    color: #029722;
}

.fq:hover {
    color: red;
}

._tishi_text {
    margin-top: 44px;
    margin-left: 20px;
    font-size: 13px;
}

._ct {
    float: left;
    width: 100px;
    padding-left: 16px;
}

._jt {
    float: left;
    margin-top: 83px;
    margin-right: 7px;
}

.tishi {
    font-weight: bold;
    color: #ff872f;
}

.tip {
    background-image: url(../images/tip.png);
    width: 166px;
    height: 46px;
    line-height: 20px;
    position: absolute;
    color: #fff;
    font-size: 12px;
    padding: 17px;
    background-repeat: no-repeat;
    display: none;
    text-align: center;
}

.tip_qianghua {
    top: 70px;
    left: 51px;
}

.tip_zhuanling {
    top: 130px;
    left: 51px;
}

.tip_xilian {
    top: 190px;
    left: 51px;
}

.tip_ys {
    left: 554px;
    top: 20px;
    z-index: 9999;
}

.tupo-btn {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    margin: 0 28px;
    vertical-align: bottom;
}