﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///用户任务进度表
    ///</summary>
    [SugarTable("user_task_progress")]
    public partial class user_task_progress
    {
           public user_task_progress(){


           }
           /// <summary>
           /// Desc:进度ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int progress_id {get;set;}

           /// <summary>
           /// Desc:用户任务ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_task_id {get;set;}

           /// <summary>
           /// Desc:目标ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int objective_id {get;set;}

           /// <summary>
           /// Desc:当前进度
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? current_amount {get;set;}

           /// <summary>
           /// Desc:是否完成
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public byte? is_completed {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? updated_at {get;set;}

    }
}
