using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.DTOs;
using WebApplication_HM.IServices;
using System.Reflection;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 数据库实体生成控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class DbFirstController : ControllerBase
    {
        private readonly IDbFirstService _dbFirstService;

        public DbFirstController(IDbFirstService dbFirstService)
        {
            _dbFirstService = dbFirstService;
        }

        /// <summary>
        /// 生成数据库实体类
        /// </summary>
        /// <returns>生成结果</returns>
        [HttpPost("generate")]
        public async Task<IActionResult> GenerateEntity()
        {
            var (success, message) = await _dbFirstService.GenerateEntity();

            if (!success)
            {
                return BadRequest(new { success, message });
            }

            return Ok(new { success, message });
        }

        /// <summary>
        /// 根据实体生成数据库表或字段
        /// </summary>
        /// <returns>生成结果</returns>
        [HttpPost("generate-database")]
        public async Task<IActionResult> GenerateDatabase()
        {
            var (success, message) = await _dbFirstService.GenerateDatabase();

            return Ok(new { success, message });
        }

        /// <summary>
        /// 同步指定实体的数据库结构
        /// </summary>
        /// <param name="entityName">实体名称</param>
        /// <returns>同步结果</returns>
        [HttpPost("sync-entity/{entityName}")]
        public async Task<IActionResult> SyncEntity(string entityName)
        {
            try
            {
                // 根据实体名称获取类型
                var entityType = GetEntityTypeByName(entityName);
                if (entityType == null)
                {
                    return BadRequest(new { success = false, message = $"未找到实体: {entityName}" });
                }

                var (success, message) = await _dbFirstService.GenerateDatabaseByType(entityType);

                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"同步实体失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 同步user实体结构
        /// </summary>
        /// <returns>同步结果</returns>
        [HttpPost("sync-user")]
        public async Task<IActionResult> SyncUserEntity()
        {
            var (success, message) = await _dbFirstService.SyncEntityStructure<WebApplication_HM.Models.user>();

            return Ok(new { success, message });
        }

        /// <summary>
        /// 获取所有可同步的实体列表
        /// </summary>
        /// <returns>实体列表</returns>
        [HttpGet("entities")]
        public IActionResult GetEntities()
        {
            try
            {
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                var entityTypes = assembly.GetTypes()
                    .Where(t => t.Namespace == "WebApplication_HM.Models" &&
                               t.IsClass &&
                               !t.IsAbstract &&
                               !t.Name.Contains("DTO") &&
                               !t.Name.Contains("Config") &&
                               t.GetCustomAttributes(typeof(SqlSugar.SugarTable), false).Any())
                    .Select(t => new {
                        Name = t.Name,
                        TableName = GetTableName(t),
                        Properties = t.GetProperties().Where(p => !IsIgnoredProperty(p)).Select(p => new {
                            Name = p.Name,
                            Type = p.PropertyType.Name,
                            ColumnName = GetColumnName(p)
                        }).ToList()
                    })
                    .ToList();

                return Ok(new { success = true, data = entityTypes });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"获取实体列表失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 检查数据库表的实际字段
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <returns>字段列表</returns>
        [HttpGet("check-table/{tableName}")]
        public IActionResult CheckTableColumns(string tableName)
        {
            try
            {
                var dbFirstService = HttpContext.RequestServices.GetRequiredService<IDbFirstService>();
                var db = HttpContext.RequestServices.GetRequiredService<SqlSugar.ISqlSugarClient>();

                // 检查表是否存在
                var tableExists = db.DbMaintenance.IsAnyTable(tableName);
                if (!tableExists)
                {
                    return Ok(new {
                        success = true,
                        tableExists = false,
                        message = $"表 {tableName} 不存在"
                    });
                }

                // 获取表的字段信息
                var columns = db.DbMaintenance.GetColumnInfosByTableName(tableName);
                var columnInfo = columns.Select(c => new {
                    ColumnName = c.DbColumnName,
                    DataType = c.DataType,
                    Length = c.Length,
                    IsNullable = c.IsNullable,
                    IsPrimaryKey = c.IsPrimarykey,
                    DefaultValue = c.DefaultValue
                }).ToList();

                return Ok(new {
                    success = true,
                    tableExists = true,
                    tableName = tableName,
                    columnCount = columnInfo.Count,
                    columns = columnInfo
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"检查表结构失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 比较实体和数据库表结构的差异
        /// </summary>
        /// <param name="entityName">实体名称</param>
        /// <returns>差异比较结果</returns>
        [HttpGet("compare-structure/{entityName}")]
        public IActionResult CompareStructure(string entityName)
        {
            try
            {
                var db = HttpContext.RequestServices.GetRequiredService<SqlSugar.ISqlSugarClient>();

                // 获取实体类型
                var entityType = GetEntityTypeByName(entityName);
                if (entityType == null)
                {
                    return BadRequest(new { success = false, message = $"未找到实体: {entityName}" });
                }

                var tableName = GetTableName(entityType);

                // 检查表是否存在
                var tableExists = db.DbMaintenance.IsAnyTable(tableName);
                if (!tableExists)
                {
                    return Ok(new {
                        success = true,
                        tableExists = false,
                        message = $"表 {tableName} 不存在，需要创建"
                    });
                }

                // 获取数据库字段
                var dbColumns = db.DbMaintenance.GetColumnInfosByTableName(tableName);
                var dbColumnDict = dbColumns.ToDictionary(c => c.DbColumnName.ToLower(), c => c);

                // 获取实体属性
                var properties = entityType.GetProperties()
                    .Where(p => !IsIgnoredProperty(p))
                    .ToList();

                var differences = new List<object>();
                var missingColumns = new List<object>();
                var extraColumns = new List<string>();

                // 检查实体属性对应的数据库字段
                foreach (var property in properties)
                {
                    var columnName = GetColumnName(property);
                    var columnKey = columnName.ToLower();

                    if (!dbColumnDict.ContainsKey(columnKey))
                    {
                        // 字段缺失
                        missingColumns.Add(new {
                            PropertyName = property.Name,
                            ColumnName = columnName,
                            PropertyType = property.PropertyType.Name,
                            ExpectedSqlType = GetExpectedSqlType(property),
                            Status = "Missing"
                        });
                    }
                    else
                    {
                        // 字段存在，检查类型和可空性差异
                        var dbColumn = dbColumnDict[columnKey];
                        var expectedNullable = IsPropertyNullable(property);
                        var expectedSqlType = GetExpectedSqlType(property);

                        if (dbColumn.IsNullable != expectedNullable ||
                            !IsSqlTypeCompatible(dbColumn.DataType, expectedSqlType))
                        {
                            differences.Add(new {
                                PropertyName = property.Name,
                                ColumnName = columnName,
                                PropertyType = property.PropertyType.Name,
                                ExpectedSqlType = expectedSqlType,
                                ExpectedNullable = expectedNullable,
                                ActualSqlType = dbColumn.DataType,
                                ActualNullable = dbColumn.IsNullable,
                                Status = "Different"
                            });
                        }
                    }
                }

                // 检查数据库中多余的字段
                var entityColumnNames = properties.Select(p => GetColumnName(p).ToLower()).ToHashSet();
                extraColumns = dbColumns
                    .Where(c => !entityColumnNames.Contains(c.DbColumnName.ToLower()))
                    .Select(c => c.DbColumnName)
                    .ToList();

                return Ok(new {
                    success = true,
                    entityName = entityName,
                    tableName = tableName,
                    tableExists = true,
                    summary = new {
                        missingColumns = missingColumns.Count,
                        differentColumns = differences.Count,
                        extraColumns = extraColumns.Count,
                        totalIssues = missingColumns.Count + differences.Count
                    },
                    details = new {
                        missingColumns = missingColumns,
                        differentColumns = differences,
                        extraColumns = extraColumns
                    }
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"比较结构失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 获取期望的SQL类型
        /// </summary>
        private string GetExpectedSqlType(System.Reflection.PropertyInfo property)
        {
            var sugarColumnAttr = property.GetCustomAttribute<SqlSugar.SugarColumn>();
            if (sugarColumnAttr != null && !string.IsNullOrEmpty(sugarColumnAttr.ColumnDataType))
            {
                return sugarColumnAttr.ColumnDataType;
            }

            var propertyType = property.PropertyType;
            var underlyingType = Nullable.GetUnderlyingType(propertyType) ?? propertyType;

            return underlyingType.Name.ToLower() switch
            {
                "int32" => "int",
                "int64" => "bigint",
                "decimal" => "decimal",
                "double" => "double",
                "float" => "float",
                "boolean" => "bit",
                "datetime" => "datetime",
                "string" => "varchar",
                _ => "varchar"
            };
        }

        /// <summary>
        /// 检查SQL类型是否兼容
        /// </summary>
        private bool IsSqlTypeCompatible(string actualType, string expectedType)
        {
            actualType = actualType.ToLower();
            expectedType = expectedType.ToLower();

            // 直接匹配
            if (actualType == expectedType) return true;

            // 兼容性匹配
            return (actualType, expectedType) switch
            {
                ("integer", "int") => true,
                ("int", "integer") => true,
                ("varchar", "text") => true,
                ("text", "varchar") => true,
                ("bit", "boolean") => true,
                ("boolean", "bit") => true,
                _ => false
            };
        }

        #region 私有方法

        /// <summary>
        /// 根据实体名称获取类型
        /// </summary>
        /// <param name="entityName">实体名称</param>
        /// <returns>实体类型</returns>
        private Type GetEntityTypeByName(string entityName)
        {
            var assembly = System.Reflection.Assembly.GetExecutingAssembly();
            return assembly.GetTypes()
                .FirstOrDefault(t => t.Namespace == "WebApplication_HM.Models" &&
                                    t.Name.Equals(entityName, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取表名
        /// </summary>
        /// <param name="entityType">实体类型</param>
        /// <returns>表名</returns>
        private string GetTableName(Type entityType)
        {
            var sugarTableAttr = entityType.GetCustomAttribute<SqlSugar.SugarTable>();
            return sugarTableAttr?.TableName ?? entityType.Name.ToLower();
        }

        /// <summary>
        /// 检查属性是否应该被忽略
        /// </summary>
        /// <param name="property">属性信息</param>
        /// <returns>是否忽略</returns>
        private bool IsIgnoredProperty(System.Reflection.PropertyInfo property)
        {
            var sugarColumnAttr = property.GetCustomAttribute<SqlSugar.SugarColumn>();
            if (sugarColumnAttr != null && sugarColumnAttr.IsIgnore)
            {
                return true;
            }

            var navigateAttr = property.GetCustomAttribute<SqlSugar.Navigate>();
            if (navigateAttr != null)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 获取字段名
        /// </summary>
        /// <param name="property">属性信息</param>
        /// <returns>字段名</returns>
        private string GetColumnName(System.Reflection.PropertyInfo property)
        {
            var sugarColumnAttr = property.GetCustomAttribute<SqlSugar.SugarColumn>();
            return sugarColumnAttr?.ColumnName ?? property.Name.ToLower();
        }

        /// <summary>
        /// 检查属性是否可空
        /// </summary>
        /// <param name="property">属性信息</param>
        /// <returns>是否可空</returns>
        private bool IsPropertyNullable(System.Reflection.PropertyInfo property)
        {
            var sugarColumnAttr = property.GetCustomAttribute<SqlSugar.SugarColumn>();
            if (sugarColumnAttr != null)
            {
                return sugarColumnAttr.IsNullable;
            }

            return Nullable.GetUnderlyingType(property.PropertyType) != null ||
                   property.PropertyType == typeof(string);
        }

        #endregion
    }
}