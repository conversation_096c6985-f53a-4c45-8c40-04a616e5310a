﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///用户道具表
    ///</summary>
    [SugarTable("user_item")]
    public partial class user_item
    {
        public user_item()
        {


        }
        /// <summary>
        /// Desc:自增主键
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public int id { get; set; }

        /// <summary>
        /// Desc:所属用户ID
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int user_id { get; set; }

        /// <summary>
        /// Desc:道具ID
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string item_id { get; set; }

        /// <summary>
        /// Desc:道具数量
        /// Default:0
        /// Nullable:False
        /// </summary>           
        public long item_count { get; set; }

        /// <summary>
        /// Desc:道具位置（1.背包  2.仓库 3.丢弃）
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int item_pos { get; set; }

        /// <summary>
        /// Desc:道具序号
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int item_seq { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? create_time { get; set; }

        /// <summary>
        /// 任务结算数量
        /// </summary>
        public int settlement_num { get; set; }

    }
}
