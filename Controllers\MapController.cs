using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.Interface;
using WebApplication_HM.DTOs.RequestDTO;
using WebApplication_HM.DTOs.ResultDTO;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 地图控制器 - 专门处理地图相关功能
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class MapController : ControllerBase
    {
        private readonly IMapService _mapService;
        private readonly ILogger<MapController> _logger;

        public MapController(IMapService mapService, ILogger<MapController> logger)
        {
            _mapService = mapService;
            _logger = logger;
        }

        /// <summary>
        /// 获取用户可用地图列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>地图列表</returns>
        [HttpGet("list/{userId}")]
        public async Task<ActionResult<MapListResultDTO>> GetMapList(int userId)
        {
            try
            {
                _logger.LogInformation($"获取用户地图列表 - UserId: {userId}");

                var result = await _mapService.GetAvailableMapsAsync(userId);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取地图列表失败 - UserId: {userId}");
                return StatusCode(500, new MapListResultDTO 
                { 
                    Success = false, 
                    Message = "获取地图列表失败" 
                });
            }
        }

        /// <summary>
        /// 获取地图详细信息
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <param name="userId">用户ID</param>
        /// <returns>地图详细信息</returns>
        [HttpGet("{mapId}/detail")]
        public async Task<ActionResult<MapDetailResultDTO>> GetMapDetail(int mapId, [FromQuery] int userId)
        {
            try
            {
                _logger.LogInformation($"获取地图详情 - MapId: {mapId}, UserId: {userId}");

                var result = await _mapService.GetMapDetailAsync(mapId, userId);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取地图详情失败 - MapId: {mapId}, UserId: {userId}");
                return StatusCode(500, new MapDetailResultDTO 
                { 
                    Success = false, 
                    Message = "获取地图详情失败" 
                });
            }
        }

        /// <summary>
        /// 获取地图怪物列表
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>怪物列表</returns>
        [HttpGet("{mapId}/monsters")]
        public async Task<ActionResult<List<MapMonsterDTO>>> GetMapMonsters(int mapId)
        {
            try
            {
                _logger.LogInformation($"获取地图怪物列表 - MapId: {mapId}");

                var monsters = await _mapService.GetMapMonstersAsync(mapId);
                return Ok(monsters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取地图怪物失败 - MapId: {mapId}");
                return StatusCode(500, "获取地图怪物失败");
            }
        }

        /// <summary>
        /// 进入地图
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <param name="request">进入地图请求</param>
        /// <returns>进入结果</returns>
        [HttpPost("{mapId}/enter")]
        public async Task<ActionResult<EnterMapResultDTO>> EnterMap(int mapId, [FromBody] EnterMapRequest request)
        {
            try
            {
                _logger.LogInformation($"用户进入地图 - MapId: {mapId}, UserId: {request.UserId}, PetId: {request.PetId}");

                // 验证请求参数
                if (request.UserId <= 0)
                {
                    return BadRequest(new EnterMapResultDTO 
                    { 
                        Success = false, 
                        Message = "无效的用户ID" 
                    });
                }

                if (request.PetId <= 0)
                {
                    return BadRequest(new EnterMapResultDTO 
                    { 
                        Success = false, 
                        Message = "请选择一个宠物" 
                    });
                }

                var result = await _mapService.EnterMapAsync(request.UserId, mapId, request.PetId);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"进入地图失败 - MapId: {mapId}, UserId: {request.UserId}");
                return StatusCode(500, new EnterMapResultDTO 
                { 
                    Success = false, 
                    Message = "进入地图失败" 
                });
            }
        }

        /// <summary>
        /// 解锁地图
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <param name="request">解锁地图请求</param>
        /// <returns>解锁结果</returns>
        [HttpPost("{mapId}/unlock")]
        public async Task<ActionResult<UnlockMapResultDTO>> UnlockMap(int mapId, [FromBody] UnlockMapRequest request)
        {
            try
            {
                _logger.LogInformation($"解锁地图 - MapId: {mapId}, UserId: {request.UserId}, Method: {request.UnlockMethod}");

                var result = await _mapService.UnlockMapAsync(request.UserId, mapId, request.UnlockMethod);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"解锁地图失败 - MapId: {mapId}, UserId: {request.UserId}");
                return StatusCode(500, new UnlockMapResultDTO 
                { 
                    Success = false, 
                    Message = "解锁地图失败" 
                });
            }
        }

        /// <summary>
        /// 获取地图掉落信息
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>掉落信息</returns>
        [HttpGet("{mapId}/drops")]
        public async Task<ActionResult<List<MapDropDTO>>> GetMapDrops(int mapId)
        {
            try
            {
                _logger.LogInformation($"获取地图掉落信息 - MapId: {mapId}");

                var drops = await _mapService.GetMapDropsAsync(mapId);
                return Ok(drops);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取地图掉落失败 - MapId: {mapId}");
                return StatusCode(500, "获取地图掉落失败");
            }
        }

        /// <summary>
        /// 更新地图进度
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <param name="request">进度更新请求</param>
        /// <returns>更新结果</returns>
        [HttpPut("{mapId}/progress")]
        public async Task<ActionResult<UpdateMapProgressResultDTO>> UpdateMapProgress(int mapId, [FromBody] UpdateMapProgressRequest request)
        {
            try
            {
                _logger.LogInformation($"更新地图进度 - MapId: {mapId}, UserId: {request.UserId}");

                var result = await _mapService.UpdateMapProgressAsync(request.UserId, mapId, request.Score, request.CompletionTime);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新地图进度失败 - MapId: {mapId}, UserId: {request.UserId}");
                return StatusCode(500, new UpdateMapProgressResultDTO 
                { 
                    Success = false, 
                    Message = "更新地图进度失败" 
                });
            }
        }
    }
}
