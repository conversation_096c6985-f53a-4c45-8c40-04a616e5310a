namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 装备获得通知DTO
    /// </summary>
    public class EquipmentNotificationDTO
    {
        /// <summary>
        /// 消息类型
        /// </summary>
        public string Type { get; set; } = "equipment_notify";

        /// <summary>
        /// 通知类型 (obtain/upgrade/enhance/unequip)
        /// </summary>
        public string NotifyType { get; set; }

        /// <summary>
        /// 玩家ID
        /// </summary>
        public int PlayerId { get; set; }

        /// <summary>
        /// 玩家名称
        /// </summary>
        public string PlayerName { get; set; }

        /// <summary>
        /// 装备ID
        /// </summary>
        public int EquipmentId { get; set; }

        /// <summary>
        /// 装备名称
        /// </summary>
        public string EquipmentName { get; set; }

        /// <summary>
        /// 装备类型
        /// </summary>
        public string EquipmentType { get; set; }

        /// <summary>
        /// 装备品质
        /// </summary>
        public string Quality { get; set; }

        /// <summary>
        /// 装备等级
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 强化等级
        /// </summary>
        public int EnhanceLevel { get; set; }

        /// <summary>
        /// 装备属性
        /// </summary>
        public EquipmentAttributes Attributes { get; set; }

        /// <summary>
        /// 获得方式
        /// </summary>
        public string ObtainMethod { get; set; }

        /// <summary>
        /// 来源描述
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// 是否稀有
        /// </summary>
        public bool IsRare { get; set; }

        /// <summary>
        /// 是否广播给所有玩家
        /// </summary>
        public bool Broadcast { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 额外消息
        /// </summary>
        public string Message { get; set; }
    }

    /// <summary>
    /// 装备属性信息
    /// </summary>
    public class EquipmentAttributes
    {
        /// <summary>
        /// 攻击力
        /// </summary>
        public int Attack { get; set; }

        /// <summary>
        /// 防御力
        /// </summary>
        public int Defense { get; set; }

        /// <summary>
        /// 生命值
        /// </summary>
        public int Hp { get; set; }

        /// <summary>
        /// 法力值
        /// </summary>
        public int Mp { get; set; }

        /// <summary>
        /// 速度
        /// </summary>
        public int Speed { get; set; }

        /// <summary>
        /// 暴击率
        /// </summary>
        public double CritRate { get; set; }

        /// <summary>
        /// 暴击伤害
        /// </summary>
        public double CritDamage { get; set; }

        /// <summary>
        /// 命中率
        /// </summary>
        public double HitRate { get; set; }

        /// <summary>
        /// 闪避率
        /// </summary>
        public double DodgeRate { get; set; }
    }
} 