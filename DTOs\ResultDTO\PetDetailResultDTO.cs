namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 宠物详情结果DTO
    /// </summary>
    public class PetDetailResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 宠物基础信息
        /// </summary>
        public PetInfoDTO? PetInfo { get; set; }

        /// <summary>
        /// 宠物属性
        /// </summary>
        public AttributeResultDTO? Attributes { get; set; }

        /// <summary>
        /// 宠物技能列表
        /// </summary>
        public List<PetSkillDTO> Skills { get; set; } = new List<PetSkillDTO>();
    }

    /// <summary>
    /// 宠物技能DTO
    /// </summary>
    public class PetSkillDTO
    {
        /// <summary>
        /// 技能ID
        /// </summary>
        public int SkillId { get; set; }

        /// <summary>
        /// 技能名称
        /// </summary>
        public string SkillName { get; set; } = string.Empty;

        /// <summary>
        /// 技能等级
        /// </summary>
        public int SkillLevel { get; set; }

        /// <summary>
        /// 技能描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }
} 