<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>口袋精灵 - 用户登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            backdrop-filter: blur(10px);
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            font-size: 2.5em;
            color: #333;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .logo p {
            color: #666;
            font-size: 1.1em;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn.register {
            background: linear-gradient(45deg, #28a745, #20c997);
        }

        .btn.register:hover {
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        .message {
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: bold;
            text-align: center;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .toggle-form {
            text-align: center;
            margin-top: 20px;
        }

        .toggle-form a {
            color: #667eea;
            text-decoration: none;
            font-weight: bold;
        }

        .toggle-form a:hover {
            text-decoration: underline;
        }

        .register-form {
            display: none;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .remember-me {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .remember-me input {
            width: auto;
            margin-right: 8px;
        }

        .quick-login {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .quick-login h4 {
            text-align: center;
            color: #666;
            margin-bottom: 15px;
        }

        .quick-accounts {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .quick-account {
            padding: 8px 12px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            cursor: pointer;
            text-align: center;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .quick-account:hover {
            background: #e9ecef;
            border-color: #667eea;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>🎮 口袋精灵</h1>
            <p>欢迎回到精灵世界</p>
        </div>

        <!-- 消息显示区域 -->
        <div id="message" class="message" style="display: none;"></div>

        <!-- 登录表单 -->
        <form id="loginForm" class="login-form">
            <div class="form-group">
                <label for="username">账号</label>
                <input type="text" id="username" name="username" required placeholder="请输入账号">
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required placeholder="请输入密码">
            </div>

            <div class="remember-me">
                <input type="checkbox" id="rememberMe" name="rememberMe">
                <label for="rememberMe">记住登录状态</label>
            </div>

            <button type="submit" class="btn" id="loginBtn">
                <span id="loginBtnText">登录</span>
            </button>

            <div class="toggle-form">
                <a href="#" onclick="toggleForm()">没有账号？立即注册</a>
            </div>
        </form>

        <!-- 注册表单 -->
        <form id="registerForm" class="register-form">
            <div class="form-group">
                <label for="regUsername">账号</label>
                <input type="text" id="regUsername" name="regUsername" required placeholder="请输入账号">
            </div>

            <div class="form-group">
                <label for="regPassword">密码</label>
                <input type="password" id="regPassword" name="regPassword" required placeholder="请输入密码">
            </div>

            <div class="form-group">
                <label for="regNickname">昵称</label>
                <input type="text" id="regNickname" name="regNickname" required placeholder="请输入昵称">
            </div>

            <button type="submit" class="btn register" id="registerBtn">
                <span id="registerBtnText">注册</span>
            </button>

            <div class="toggle-form">
                <a href="#" onclick="toggleForm()">已有账号？立即登录</a>
            </div>
        </form>

        <!-- 快速登录 -->
        <div class="quick-login">
            <h4>快速登录</h4>
            <div class="quick-accounts">
                <div class="quick-account" onclick="quickLogin('admin', '123456')">
                    <div>管理员</div>
                    <small>admin/123456</small>
                </div>
                <div class="quick-account" onclick="quickLogin('test', '123456')">
                    <div>测试账号</div>
                    <small>test/123456</small>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let isLoginMode = true;

        // 显示消息
        function showMessage(text, type = 'info') {
            const messageEl = document.getElementById('message');
            messageEl.textContent = text;
            messageEl.className = `message ${type}`;
            messageEl.style.display = 'block';
            
            // 3秒后自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    messageEl.style.display = 'none';
                }, 3000);
            }
        }

        // 切换登录/注册表单
        function toggleForm() {
            const loginForm = document.getElementById('loginForm');
            const registerForm = document.getElementById('registerForm');
            
            if (isLoginMode) {
                loginForm.style.display = 'none';
                registerForm.style.display = 'block';
                isLoginMode = false;
            } else {
                loginForm.style.display = 'block';
                registerForm.style.display = 'none';
                isLoginMode = true;
            }
            
            // 隐藏消息
            document.getElementById('message').style.display = 'none';
        }

        // 快速登录
        function quickLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            showMessage('已填入快速登录信息，点击登录按钮继续', 'success');
        }

        // 设置按钮加载状态
        function setButtonLoading(buttonId, textId, loading) {
            const button = document.getElementById(buttonId);
            const text = document.getElementById(textId);
            
            if (loading) {
                button.disabled = true;
                text.innerHTML = '<span class="loading-spinner"></span>处理中...';
            } else {
                button.disabled = false;
                text.textContent = buttonId === 'loginBtn' ? '登录' : '注册';
            }
        }

        // 登录处理
        async function handleLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            if (!username || !password) {
                showMessage('请输入账号和密码', 'error');
                return;
            }
            
            setButtonLoading('loginBtn', 'loginBtnText', true);
            showMessage('正在登录...', 'loading');
            
            try {
                const response = await fetch('/api/Player/Login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        Number: username,
                        Password: password
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // 保存登录状态
                    const loginData = {
                        userId: result.userId,
                        nickname: result.nickName,
                        username: username,
                        loginTime: new Date().toISOString()
                    };
                    
                    // 根据记住我选项决定存储方式
                    if (rememberMe) {
                        localStorage.setItem('userLogin', JSON.stringify(loginData));
                    } else {
                        sessionStorage.setItem('userLogin', JSON.stringify(loginData));
                    }
                    
                    showMessage(`登录成功！欢迎回来，${result.nickName}`, 'success');
                    
                    // 2秒后跳转到游戏主页
                    setTimeout(() => {
                        window.location.href = '/game';
                    }, 2000);
                    
                } else {
                    showMessage(result.message || '登录失败', 'error');
                }
                
            } catch (error) {
                console.error('登录错误:', error);
                showMessage('网络错误，请稍后重试', 'error');
            } finally {
                setButtonLoading('loginBtn', 'loginBtnText', false);
            }
        }

        // 注册处理
        async function handleRegister(event) {
            event.preventDefault();
            
            const username = document.getElementById('regUsername').value;
            const password = document.getElementById('regPassword').value;
            const nickname = document.getElementById('regNickname').value;
            
            if (!username || !password || !nickname) {
                showMessage('请填写完整信息', 'error');
                return;
            }
            
            if (username.length < 3) {
                showMessage('账号长度至少3位', 'error');
                return;
            }
            
            if (password.length < 6) {
                showMessage('密码长度至少6位', 'error');
                return;
            }
            
            setButtonLoading('registerBtn', 'registerBtnText', true);
            showMessage('正在注册...', 'loading');
            
            try {
                const response = await fetch('/api/Player/Register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        Account: username,
                        Password: password,
                        Nickname: nickname
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showMessage('注册成功！请使用新账号登录', 'success');
                    
                    // 切换到登录表单并填入账号
                    setTimeout(() => {
                        toggleForm();
                        document.getElementById('username').value = username;
                        document.getElementById('password').value = password;
                    }, 1500);
                    
                } else {
                    showMessage(result.message || '注册失败', 'error');
                }
                
            } catch (error) {
                console.error('注册错误:', error);
                showMessage('网络错误，请稍后重试', 'error');
            } finally {
                setButtonLoading('registerBtn', 'registerBtnText', false);
            }
        }

        // 检查登录状态
        function checkLoginStatus() {
            const loginData = localStorage.getItem('userLogin') || sessionStorage.getItem('userLogin');
            if (loginData) {
                try {
                    const userData = JSON.parse(loginData);
                    showMessage(`检测到登录状态：${userData.nickname}`, 'success');
                    
                    // 询问是否直接进入游戏
                    setTimeout(() => {
                        if (confirm('检测到已登录状态，是否直接进入游戏？')) {
                            window.location.href = '/game';
                        }
                    }, 1000);
                } catch (error) {
                    // 清除无效的登录数据
                    localStorage.removeItem('userLogin');
                    sessionStorage.removeItem('userLogin');
                }
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 绑定表单事件
            document.getElementById('loginForm').addEventListener('submit', handleLogin);
            document.getElementById('registerForm').addEventListener('submit', handleRegister);
            
            // 检查登录状态
            checkLoginStatus();
            
            // 自动聚焦到用户名输入框
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>
