using SqlSugar;
using System;

namespace WebApplication_HM.Models
{
    /// <summary>
    /// 百变宠物配置表
    /// </summary>
    [SugarTable("pet_transform_config")]
    public class pet_transform_config
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int id { get; set; }

        /// <summary>
        /// 变换类型
        /// </summary>
        public string transform_type { get; set; } = string.Empty;

        /// <summary>
        /// 消耗金币
        /// </summary>
        public long cost_gold { get; set; } = 0;

        /// <summary>
        /// 所需等级
        /// </summary>
        public int required_level { get; set; } = 1;

        /// <summary>
        /// 成功率（百分比）
        /// </summary>
        public decimal success_rate { get; set; } = 100.00m;

        /// <summary>
        /// 冷却时间（毫秒）
        /// </summary>
        public long cooldown_time { get; set; } = 0;

        /// <summary>
        /// 神宠概率（百分比）
        /// </summary>
        public decimal god_pet_rate { get; set; } = 10.00m;

        /// <summary>
        /// 神圣宠物概率（百分比）
        /// </summary>
        public decimal holy_pet_rate { get; set; } = 1.00m;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool is_active { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime create_time { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime update_time { get; set; } = DateTime.Now;
    }
}
