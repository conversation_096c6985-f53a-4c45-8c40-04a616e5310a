using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WebApplication_HM.DTOs;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 转生反作弊服务
    /// </summary>
    public class NirvanaAntiCheatService : INirvanaAntiCheatService
    {
        private readonly ISqlSugarClient _db;
        private readonly IMemoryCache _cache;
        private readonly ILogger<NirvanaAntiCheatService> _logger;
        private readonly ILevelService _levelService;

        // 反作弊配置
        private const int COOLDOWN_SECONDS = 10; // 转生冷却时间（秒）
        private const int MAX_ATTEMPTS_PER_MINUTE = 6; // 每分钟最大尝试次数
        private const int MAX_ATTEMPTS_PER_HOUR = 50; // 每小时最大尝试次数

        public NirvanaAntiCheatService(ISqlSugarClient db, IMemoryCache cache, ILogger<NirvanaAntiCheatService> logger, ILevelService levelService)
        {
            _db = db;
            _cache = cache;
            _logger = logger;
            _levelService = levelService;
        }

        /// <summary>
        /// 验证转生请求
        /// </summary>
        public async Task<bool> ValidateNirvanaRequestAsync(NirvanaRequestDto request)
        {
            try
            {
                _logger.LogInformation("开始验证转生请求 - 用户ID:{UserId}", request.UserId);

                // 1. 冷却时间检测
                if (!await ValidateCooldownAsync(request.UserId))
                {
                    _logger.LogWarning("转生冷却时间未到 - 用户ID:{UserId}", request.UserId);
                    return false;
                }

                // 2. 宠物有效性检测
                if (!await ValidatePetsAsync(request))
                {
                    _logger.LogWarning("宠物验证失败 - 用户ID:{UserId}", request.UserId);
                    return false;
                }

                // 3. 频率检测
                if (!await ValidateFrequencyAsync(request.UserId))
                {
                    _logger.LogWarning("转生频率过高 - 用户ID:{UserId}", request.UserId);
                    return false;
                }

                _logger.LogInformation("转生请求验证通过 - 用户ID:{UserId}", request.UserId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证转生请求失败 - 用户ID:{UserId}", request.UserId);
                return false;
            }
        }

        /// <summary>
        /// 验证冷却时间
        /// </summary>
        public async Task<bool> ValidateCooldownAsync(int userId)
        {
            try
            {
                // 从用户表获取最后转生时间
                var user = await _db.Queryable<user>().FirstAsync(u => u.id == userId);
                if (user?.nirvana_cd == null)
                    return true;

                var lastNirvanaTime = user.nirvana_cd.Value;
                var cooldownEnd = lastNirvanaTime.AddSeconds(COOLDOWN_SECONDS);

                if (DateTime.Now < cooldownEnd)
                {
                    var remainingSeconds = (cooldownEnd - DateTime.Now).TotalSeconds;
                    _logger.LogWarning("转生冷却中 - 用户ID:{UserId}, 剩余时间:{Seconds}秒", userId, remainingSeconds);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证转生冷却时间失败 - 用户ID:{UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// 验证宠物有效性
        /// </summary>
        public async Task<bool> ValidatePetsAsync(NirvanaRequestDto request)
        {
            try
            {
                // 验证宠物存在且属于用户
                var pets = await _db.Queryable<user_pet>()
                    .Where(p => p.user_id == request.UserId && 
                               (p.id == request.MainPetId || 
                                p.id == request.SubPetId || 
                                p.id == request.NirvanaPetId))
                    .ToListAsync();

                if (pets.Count != 3)
                {
                    _logger.LogWarning("宠物数量不正确 - 用户ID:{UserId}, 找到宠物数:{Count}", request.UserId, pets.Count);
                    return false;
                }

                // 验证宠物状态
                foreach (var pet in pets)
                {
                    // 检查宠物是否在牧场或携带状态
                    if (pet.status != "牧场" && pet.status != "携带")
                    {
                        _logger.LogWarning("宠物状态异常 - 宠物ID:{PetId}, 状态:{Status}", pet.id, pet.status);
                        return false;
                    }

                    // 根据经验值计算正确的等级
                    var petExp = pet.exp ?? 0;
                    var calculatedLevel = await _levelService.CalculateLevelAsync(petExp, "pet");

                    // 检查计算出的等级是否合理（1-130级）
                    if (calculatedLevel < 1 || calculatedLevel > 130)
                    {
                        _logger.LogWarning("宠物等级异常 - 宠物ID:{PetId}, 经验:{Exp}, 计算等级:{Level}",
                            pet.id, petExp, calculatedLevel);
                        return false;
                    }

                    // 验证数据库中的等级字段与计算等级的一致性（允许一定误差）
                    //var storedLevel = Convert.ToInt32(pet.level ?? 1);
                    //var levelDifference = Math.Abs(calculatedLevel - storedLevel);
                    //if (levelDifference > 2) // 允许2级误差
                    //{
                    //    _logger.LogWarning("宠物等级数据不一致 - 宠物ID:{PetId}, 存储等级:{StoredLevel}, 计算等级:{CalculatedLevel}, 经验:{Exp}",
                    //        pet.id, storedLevel, calculatedLevel, petExp);
                    //    return false;
                    //}

                    // 检查宠物成长是否合理
                    var growth = Convert.ToDecimal(pet.growth ?? 0);
                    if (growth < 0 || growth > 20000000)
                    {
                        _logger.LogWarning("宠物成长异常 - 宠物ID:{PetId}, 成长:{Growth}", pet.id, growth);
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证宠物有效性失败 - 用户ID:{UserId}", request.UserId);
                return false;
            }
        }

        /// <summary>
        /// 验证资源
        /// </summary>
        public async Task<bool> ValidateResourcesAsync(NirvanaRequestDto request, long costGold)
        {
            try
            {
                var user = await _db.Queryable<user>().FirstAsync(u => u.id == request.UserId);
                if (user == null)
                    return false;

                var userGold = user.gold ?? 0;
                if (userGold < costGold)
                {
                    _logger.LogWarning("金币不足 - 用户ID:{UserId}, 拥有:{UserGold}, 需要:{CostGold}", 
                        request.UserId, userGold, costGold);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证资源失败 - 用户ID:{UserId}", request.UserId);
                return false;
            }
        }

        /// <summary>
        /// 验证频率
        /// </summary>
        public async Task<bool> ValidateFrequencyAsync(int userId)
        {
            try
            {
                var now = DateTime.Now;
                var oneMinuteAgo = now.AddMinutes(-1);
                var oneHourAgo = now.AddHours(-1);

                // 检查最近1分钟的尝试次数
                var attemptsLastMinute = await _db.Queryable<PetNirvanaLog>()
                    .Where(l => l.UserId == userId && l.CreateTime >= oneMinuteAgo)
                    .CountAsync();

                if (attemptsLastMinute >= MAX_ATTEMPTS_PER_MINUTE)
                {
                    _logger.LogWarning("转生频率过高(分钟) - 用户ID:{UserId}, 次数:{Count}", userId, attemptsLastMinute);
                    return false;
                }

                // 检查最近1小时的尝试次数
                var attemptsLastHour = await _db.Queryable<PetNirvanaLog>()
                    .Where(l => l.UserId == userId && l.CreateTime >= oneHourAgo)
                    .CountAsync();

                if (attemptsLastHour >= MAX_ATTEMPTS_PER_HOUR)
                {
                    _logger.LogWarning("转生频率过高(小时) - 用户ID:{UserId}, 次数:{Count}", userId, attemptsLastHour);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证转生频率失败 - 用户ID:{UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// 记录转生尝试
        /// </summary>
        public async Task RecordNirvanaAttemptAsync(int userId)
        {
            try
            {
                // 更新用户转生冷却时间
                await _db.Updateable<user>()
                    .SetColumns(u => u.nirvana_cd == DateTime.Now)
                    .Where(u => u.id == userId)
                    .ExecuteCommandAsync();

                // 记录到缓存中用于频率检测
                var cacheKey = $"nirvana_attempt_{userId}_{DateTime.Now:yyyyMMddHHmm}";
                _cache.Set(cacheKey, true, TimeSpan.FromHours(1));

                _logger.LogInformation("记录转生尝试 - 用户ID:{UserId}", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录转生尝试失败 - 用户ID:{UserId}", userId);
            }
        }

        /// <summary>
        /// 检测异常行为
        /// </summary>
        public async Task<bool> DetectAbnormalBehaviorAsync(int userId)
        {
            try
            {
                var now = DateTime.Now;
                var oneDayAgo = now.AddDays(-1);

                // 获取最近24小时的转生记录
                var recentLogs = await _db.Queryable<PetNirvanaLog>()
                    .Where(l => l.UserId == userId && l.CreateTime >= oneDayAgo)
                    .OrderBy(l => l.CreateTime)
                    .ToListAsync();

                if (recentLogs.Count == 0)
                    return false;

                // 检测异常高成功率
                var successCount = recentLogs.Count(l => l.IsSuccess == 1);
                var successRate = (decimal)successCount / recentLogs.Count;
                
                if (successRate > 0.8m && recentLogs.Count > 10)
                {
                    _logger.LogWarning("检测到异常高成功率 - 用户ID:{UserId}, 成功率:{Rate}", userId, successRate);
                    return true;
                }

                // 检测异常高频率
                if (recentLogs.Count > 200)
                {
                    _logger.LogWarning("检测到异常高频率转生 - 用户ID:{UserId}, 次数:{Count}", userId, recentLogs.Count);
                    return true;
                }

                // 检测时间间隔异常
                var intervals = new List<double>();
                for (int i = 1; i < recentLogs.Count; i++)
                {
                    var interval = (recentLogs[i].CreateTime - recentLogs[i - 1].CreateTime).TotalSeconds;
                    intervals.Add(interval);
                }

                if (intervals.Any() && intervals.Average() < 5)
                {
                    _logger.LogWarning("检测到异常短时间间隔 - 用户ID:{UserId}, 平均间隔:{Interval}秒", userId, intervals.Average());
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检测异常行为失败 - 用户ID:{UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// 获取用户风险等级
        /// </summary>
        public async Task<string> GetUserRiskLevelAsync(int userId)
        {
            try
            {
                var hasAbnormalBehavior = await DetectAbnormalBehaviorAsync(userId);
                if (hasAbnormalBehavior)
                    return "HIGH";

                var now = DateTime.Now;
                var oneHourAgo = now.AddHours(-1);
                
                var recentAttempts = await _db.Queryable<PetNirvanaLog>()
                    .Where(l => l.UserId == userId && l.CreateTime >= oneHourAgo)
                    .CountAsync();

                if (recentAttempts > 50)
                    return "MEDIUM";

                return "LOW";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户风险等级失败 - 用户ID:{UserId}", userId);
                return "UNKNOWN";
            }
        }
    }
}
