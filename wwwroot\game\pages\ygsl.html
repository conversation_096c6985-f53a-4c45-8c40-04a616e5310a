<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时空神殿</title>
    <link rel="stylesheet" href="css/ygsl.css">
    <script src="js/jquery-1.8.3.min.js"></script>

    <script>
        var lzList = window.parent.ljson;
        $(function () {
            window.external.updateYGSL_Page();
            $(".ICO").mousemove(function () {
                if (no_lz) {
                    window.parent.Alert("你似乎不拥有龙珠~")
                }
                $(".hq_info").show();
            })
            $(".ICO").mouseout(function () {
                $(".hq_info").hide();
            })
        })
        var longzhu = null;
        var no_lz = false;
        function loadLongzhu(JSON) {

            JSON = $.parseJSON(JSON);
            if (JSON.name == null) no_lz = true;
            if (JSON.name == "" || JSON.name == null) {

                return;
            }
            longzhu = JSON;


        }
        var errNum = 0;
        function errImg() {
            errNum++;
            if (errNum == 1)
                $(".ICO img").attr("src", "images\\shenlong\\" + longzhuCalcInfo.name + ".gif");
        }
        var longzhuCalcInfo;
        function LongzhuCalc(JSON) {
            longzhuCalcInfo = $.parseJSON(JSON);
            if (no_lz) longzhuCalcInfo.name = "未激活";
            var src = "images\\shenlong\\" + longzhuCalcInfo.name + ".png";
            errNum = 0;
            $(".ICO").html("<img onerror='errImg()' src='" + src + "'/>")
            var countSX_ARR = new Array();
            for (var c in longzhuCalcInfo.Calc) {
                countSX_ARR.push(c + "：+" + accMul(longzhuCalcInfo.Calc[c], 100) + "%<br>");
            }
            $(".count_sx").html(countSX_ARR.concat(""));

            $(".lv").html("lv." + longzhuCalcInfo.lv);

            $(".lvName").html(longzhuCalcInfo.name);
        }
        function showInfo() {

        }
        //乘法 
        function accMul(arg1, arg2) {
            if (arg1 == null) arg1 = 0;
            if (arg2 == null) arg1 = 0;
            var m = 0,
                s1 = arg1.toString(),
                s2 = arg2.toString();
            try {
                m += s1.split(".")[1].length
            } catch (e) { }
            try {
                m += s2.split(".")[1].length
            } catch (e) { }
            return (Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m)).toFixed(2)
        }
        function openJC(id, tip) {

            //1-神龙宝匣，2-神秘符文，3-龙魂召唤
            var tisMsg;
            if (tip == 1) tisMsg = "(每使用一次消耗500结晶以及道具神龙宝藏钥匙*1个)";
            else if (tip == 2) tisMsg = "(每使用一次消耗500结晶以及道具龙魂召唤石*1个)";
            else if (tip == 3) tisMsg = "(每使用一次消耗500结晶以及道具符文召唤书*1个)";
            else tisMsg = "";
            var res = prompt('请问你要开启几个？' + tisMsg, 1)
            if (checkNum(res)) {
                window.external.buy(8, id, res)
            }
        }
        function checkNum(input) {
            var reg = /^[1-9][0-9]*$/;  // 只允许正整数
            // var reg = /^[0-9]+.?[0-9]*$/; //判断字符串是否为数字 ，判断正整数用/^[1-9]+[0-9]*]*$/
            var num = input;
            if (!reg.test(num)) {
                alert("请输入数字");
                return false;
            }
            return true;
        }

        var lzlh_propIndex = -1;//选择道具
        //选择龙珠炼化
        function lzlh_select(index) {
            // 全部设置为未选
            $('#wclz').removeClass('wclz_xz');
            $('#chlz').removeClass('chlz_xz');
            $('#hclz').removeClass('hclz_xz');
            $('#mxlz').removeClass('mxlz_xz');
            $('#zzlz').removeClass('zzlz_xz');

            // 根据 index 添加对应的类
            if (index == 1) {
                $('#wclz').addClass('wclz_xz');
            } else if (index == 2) {
                $('#chlz').addClass('chlz_xz');
            } else if (index == 3) {
                $('#hclz').addClass('hclz_xz');
            } else if (index == 4) {
                $('#mxlz').addClass('mxlz_xz');
            } else if (index == 5) {
                $('#zzlz').addClass('zzlz_xz');
            }
            lzlh_propIndex = index;
        }


        //龙珠炼化按钮
        function btn_lzlh() {
            var useNum = document.getElementById('lhNum').value;
            if (lzlh_propIndex >= 1 && lzlh_propIndex <= 5) {
                if (checkNum(useNum)) {
                    var msg = window.external.lz_lzlh(lzlh_propIndex, useNum);
                    if (msg) {
                        parent.Alert(msg);
                    }
                }
            } else {
                parent.Alert("请选择要使用的龙珠！");
            }

        }
    </script>
</head>

<body>
    <div class="main_box">
        <div class="returnPage" onclick="window.parent.openNpc(9)"></div>

        <!-- <div class="wenhao" onclick="alert('规则1：\n规则2：\n规则3：')"></div> -->
        <div class="ICO">
            <img src="" onerror="errImg">
        </div>
        <div class="hq_info count_info">
            <div style="margin-top: 38px;font-size:12px;">

            </div>
            <div class="hq_info_jc">
                <div class="_min lv">LV.1</div>

            </div>
            <div class="hq_info_jc1">
                <div class="_min _zi lvName">青龙珠</div>
            </div>
            <div class="hq_info_sz count_sx">
                <div>命中：130%</div>
                <div>攻击：130%</div>
                <div>生命：130%</div>
                <div>防御：130%</div>
                <div>速度：310%</div>
                <div>加深：130%</div>
                <div>吸血：130%</div>
                <div>抵消：130%</div>
                <div>吸魔：130%</div>
            </div>

        </div>
        <div class="tip">
            <span>当前信息</span>
            <span>地狱碎片*100、时之结晶*5000</span>
        </div>
        <div class="btns_left">
            <div class="btn">
                <div class="btnTitle" onclick="$('.lzlh').show()">
                    龙珠炼化
                </div>
            </div>
            <div class="btn end_btn">
                <div class="btnTitle" onclick="openJC('88230001',1)">
                    神龙宝匣
                </div>
            </div>

        </div>
        <div class="btns_right">
            <div class="btn" onclick="openJC('88230002',2)">
                <div class="btnTitle">
                    神秘符文
                </div>
            </div>
            <div class="btn end_btn">
                <div class="btnTitle" onclick="openJC('88230003',3)">
                    龙魂召唤
                </div>
            </div>

        </div>
        <div style="clear: both;"></div>


    </div>
    <!-- 龙珠炼化 -->
    <div class="lzlh" style="display: none;">
        <div class="selectPop">
            <div id="wclz" class="lz_size wclz" onclick="lzlh_select(1)"></div>
            <div id="chlz" class="lz_size chlz" onclick="lzlh_select(2)"></div>
            <div id="hclz" class="lz_size hclz" onclick="lzlh_select(3)"></div>
            <div id="mxlz" class="lz_size mxlz" onclick="lzlh_select(4)"></div>
            <div id="zzlz" class="lz_size zzlz" onclick="lzlh_select(5)"></div>
        </div>
        <div class="use">
            <span style="color: #ed4040;font-weight: bold;">*使用数量</span>
            <div class="inputBox">
                <input type="text" id="lhNum" value="1" class="input" maxlength="4" />
                <div class="useBtn" onclick="btn_lzlh()"></div>
            </div>
            <span style="color: #726b67;font-size: 14px;font-weight: bold;">一次性使用太多会导致经验溢出!</span>
        </div>
        <div class="close" onclick="$('.lzlh').hide();window.external.updateYGSL_Page();"> </div>
    </div>
</body>

</html>