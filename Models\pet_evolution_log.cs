﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///宠物进化记录表
    ///</summary>
    [SugarTable("pet_evolution_log")]
    public partial class pet_evolution_log
    {
           public pet_evolution_log(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:用户ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:用户宠物ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_pet_id {get;set;}

           /// <summary>
           /// Desc:进化路线
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string evolution_type {get;set;}

           /// <summary>
           /// Desc:进化前宠物编号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int before_pet_no {get;set;}

           /// <summary>
           /// Desc:进化后宠物编号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int after_pet_no {get;set;}

           /// <summary>
           /// Desc:进化前成长
           /// Default:
           /// Nullable:False
           /// </summary>           
           public decimal before_growth {get;set;}

           /// <summary>
           /// Desc:进化后成长
           /// Default:
           /// Nullable:False
           /// </summary>           
           public decimal after_growth {get;set;}

           /// <summary>
           /// Desc:成长增加值
           /// Default:
           /// Nullable:False
           /// </summary>           
           public decimal growth_increase {get;set;}

           /// <summary>
           /// Desc:使用的道具ID
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? used_item_id {get;set;}

           /// <summary>
           /// Desc:消耗金币
           /// Default:
           /// Nullable:False
           /// </summary>           
           public long cost_gold {get;set;}

           /// <summary>
           /// Desc:是否成功
           /// Default:true
           /// Nullable:False
           /// </summary>           
           public bool is_success {get;set;}

           /// <summary>
           /// Desc:进化时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

    }
}
