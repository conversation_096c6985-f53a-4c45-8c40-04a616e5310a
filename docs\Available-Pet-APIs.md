# 🐾 可用的宠物管理API端点

## 🎯 **正确的服务器地址**
- **服务器地址**: `http://localhost:5000`
- **API基础路径**: `http://localhost:5000/api`

---

## 📋 **宠物管理API (PetManagementController)**

### **获取宠物列表**
```http
GET /api/PetManagement/pasture/{userId}?status=牧场&page=1&pageSize=50
GET /api/PetManagement/pasture/{userId}?status=携带&page=1&pageSize=50
```

### **宠物状态操作**
```http
# 存放宠物到牧场
POST /api/PetManagement/store
Content-Type: application/json
{
    "userId": 1,
    "petId": 123
}

# 携带宠物
POST /api/PetManagement/carry
Content-Type: application/json
{
    "userId": 1,
    "petId": 123
}

# 设置主战宠物
POST /api/PetManagement/setMain
Content-Type: application/json
{
    "userId": 1,
    "petId": 123
}

# 丢弃宠物
DELETE /api/PetManagement/discard
Content-Type: application/json
{
    "userId": 1,
    "petId": 123
}

# 重命名宠物
PUT /api/PetManagement/rename
Content-Type: application/json
{
    "userId": 1,
    "petId": 123,
    "newName": "新名字"
}
```

### **牧场容量管理**
```http
GET /api/PetManagement/capacity/{userId}
```

---

## 📋 **宠物核心API (PlayerController)**

### **获取宠物列表**
```http
# 获取所有宠物
POST /api/Player/GetUserPets
Content-Type: application/json
{
    "UserId": 1
}

# 按状态获取宠物
GET /api/Player/GetUserPetsByStatus?userId=1&status=牧场
GET /api/Player/GetUserPetsByStatus?userId=1&status=携带

# 获取牧场宠物
GET /api/Player/GetRanchPets?userId=1

# 获取携带宠物
GET /api/Player/GetCarryPets?userId=1
```

### **宠物详细信息**
```http
POST /api/Player/GetPetDetail
Content-Type: application/json
{
    "UserId": 1,
    "PetId": 123
}

POST /api/Player/GetPetAttributes
Content-Type: application/json
{
    "UserId": 1,
    "PetId": 123
}
```

### **主战宠物管理**
```http
POST /api/Player/SetMainPet
Content-Type: application/json
{
    "UserId": 1,
    "PetId": 123
}
```

---

## 📋 **宠物特殊功能API**

### **宠物合成 (PetSynthesisController)**
```http
POST /api/PetSynthesis/synthesize
Content-Type: application/json
{
    "MainPetId": 123,
    "VicePetId": 456
}
```

### **宠物进化 (PetEvolutionController)**
```http
POST /api/PetEvolution/evolve
Content-Type: application/json
{
    "PetId": 123,
    "TargetPetNo": 456,
    "MaterialItems": [
        {"ItemId": "item1", "Quantity": 1}
    ]
}
```

### **宠物变换 (PetTransformController)**
```http
POST /api/PetTransform/transform
Content-Type: application/json
{
    "UserId": 1,
    "PetId": 123,
    "TransformType": "变换类型"
}
```

---

## 📋 **技能管理API (SkillController)**

```http
# 获取宠物技能
GET /api/Skill/pet/{petId}

# 技能升级
POST /api/Skill/pet/{petId}/upgrade
Content-Type: application/json
{
    "skillId": 123
}

# 技能遗忘
DELETE /api/Skill/pet/{petId}/forget
Content-Type: application/json
{
    "skillId": 123
}

# 技能学习
POST /api/Skill/pet/{petId}/learn
Content-Type: application/json
{
    "skillId": 123
}
```

---

## 📋 **装备管理API (EquipmentController)**

```http
# 获取宠物装备
GET /api/Equipment/pet/{petId}/user/{userId}

# 装备穿戴
POST /api/Equipment/equip
Content-Type: application/json
{
    "userEquipmentId": 123,
    "petId": 456
}

# 装备卸下
POST /api/Equipment/unequip
Content-Type: application/json
{
    "userEquipmentId": 123
}
```

---

## 📋 **游戏核心API (GameController)**

```http
# 获取游戏主页数据
GET /api/Game/home/<USER>

# 获取宠物信息页面数据
GET /api/Game/pet-info/{userId}

# 切换主战宠物
POST /api/Game/switch-pet
Content-Type: application/json
{
    "userId": 1,
    "petId": 123
}

# 获取单个宠物详情
GET /api/Game/pet/{petId}
```

---

## 🧪 **测试数据API (TestDataController)**

```http
# 初始化牧场测试数据
POST /api/TestData/init-pasture-data

# 获取测试数据状态
GET /api/TestData/pasture-data-status

# 清理测试数据
DELETE /api/TestData/cleanup-pasture-data
```

---

## 🔧 **修复您的请求**

### **原始错误请求**
```http
❌ POST http://localhost:5078/UserPet/Create
```

### **可能的正确替代方案**

#### **1. 如果要存放宠物到牧场**
```http
✅ POST http://localhost:5000/api/PetManagement/store
Content-Type: application/json
{
    "userId": 1,
    "petId": 123
}
```

#### **2. 如果要创建新宠物（通过道具）**
```http
✅ POST http://localhost:5000/api/Player/UseItem
Content-Type: application/json
{
    "UserId": 1,
    "ItemId": "宠物蛋ID",
    "Quantity": 1
}
```

#### **3. 如果要获取宠物列表**
```http
✅ GET http://localhost:5000/api/Player/GetCarryPets?userId=1
✅ GET http://localhost:5000/api/Player/GetRanchPets?userId=1
```

---

## 🎯 **快速测试**

### **测试服务器连接**
```bash
curl http://localhost:5000/health
```

### **测试API可用性**
```bash
curl "http://localhost:5000/api/Player/GetCarryPets?userId=1"
```

### **测试页面访问**
- 牧场页面: http://localhost:5000/game/pages/Pasture.html
- API测试页面: http://localhost:5000/game/test/pasture-api-test.html

---

## 📞 **故障排除**

1. **确认服务器运行**: 访问 http://localhost:5000/health
2. **检查端口**: 确保使用 5000 端口，不是 5078
3. **验证路由**: 使用上述正确的API端点
4. **查看日志**: 检查服务器控制台输出的错误信息

**🎉 使用正确的端口和API端点，您的请求应该能正常工作！**
