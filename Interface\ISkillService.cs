using WebApplication_HM.DTOs;

namespace WebApplication_HM.Interface
{
    /// <summary>
    /// 技能服务接口
    /// 基于原项目WindowsFormsApplication7的技能系统设计
    /// </summary>
    public interface ISkillService
    {
        #region 技能配置管理

        /// <summary>
        /// 获取所有技能配置
        /// </summary>
        /// <returns>技能配置列表</returns>
        Task<List<SkillConfigDTO>> GetAllSkillsAsync();

        /// <summary>
        /// 根据技能ID获取技能配置
        /// </summary>
        /// <param name="skillId">技能ID</param>
        /// <returns>技能配置</returns>
        Task<SkillConfigDTO?> GetSkillByIdAsync(string skillId);

        /// <summary>
        /// 获取指定五行系别的技能列表
        /// </summary>
        /// <param name="element">五行系别</param>
        /// <returns>技能配置列表</returns>
        Task<List<SkillConfigDTO>> GetSkillsByElementAsync(string element);

        /// <summary>
        /// 获取指定类型的技能列表
        /// </summary>
        /// <param name="skillType">技能类型（ACTIVE/PASSIVE）</param>
        /// <returns>技能配置列表</returns>
        Task<List<SkillConfigDTO>> GetSkillsByTypeAsync(string skillType);

        #endregion

        #region 宠物技能管理

        /// <summary>
        /// 获取宠物的所有技能
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>宠物技能列表</returns>
        Task<List<PetSkillDetailDTO>> GetPetSkillsAsync(int petId);

        /// <summary>
        /// 获取宠物的主动技能
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>主动技能</returns>
        Task<PetSkillDetailDTO?> GetPetActiveSkillAsync(int petId);

        /// <summary>
        /// 获取宠物的被动技能列表
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>被动技能列表</returns>
        Task<List<PetSkillDetailDTO>> GetPetPassiveSkillsAsync(int petId);

        /// <summary>
        /// 宠物学习技能
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="skillId">技能ID</param>
        /// <returns>学习结果</returns>
        Task<SkillLearnResult> LearnSkillAsync(int petId, string skillId);

        /// <summary>
        /// 宠物升级技能
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="skillId">技能ID</param>
        /// <returns>升级结果</returns>
        Task<SkillUpgradeResult> UpgradeSkillAsync(int petId, string skillId);

        /// <summary>
        /// 宠物遗忘技能
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="skillId">技能ID</param>
        /// <returns>是否成功</returns>
        Task<bool> ForgetSkillAsync(int petId, string skillId);

        #endregion

        #region 技能效果计算

        /// <summary>
        /// 计算主动技能的伤害倍数
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="skillId">技能ID</param>
        /// <returns>伤害倍数</returns>
        Task<double> CalculateSkillMultiplierAsync(int petId, string skillId);

        /// <summary>
        /// 计算宠物所有被动技能的效果
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>被动效果字典</returns>
        Task<Dictionary<string, double>> CalculatePassiveEffectsAsync(int petId);

        /// <summary>
        /// 计算技能的魔法消耗
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="skillId">技能ID</param>
        /// <returns>魔法消耗</returns>
        Task<int> CalculateManaCostAsync(int petId, string skillId);

        /// <summary>
        /// 计算宠物技能的完整效果
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="activeSkillId">使用的主动技能ID</param>
        /// <param name="currentMana">当前魔法值</param>
        /// <returns>技能效果结果</returns>
        Task<SkillEffectResult> CalculateSkillEffectsAsync(int petId, string? activeSkillId = null, int currentMana = 0);

        #endregion

        #region 技能验证

        /// <summary>
        /// 检查宠物是否可以学习指定技能
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="skillId">技能ID</param>
        /// <returns>是否可以学习</returns>
        Task<bool> CanLearnSkillAsync(int petId, string skillId);

        /// <summary>
        /// 检查宠物是否可以升级指定技能
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="skillId">技能ID</param>
        /// <returns>是否可以升级</returns>
        Task<bool> CanUpgradeSkillAsync(int petId, string skillId);

        /// <summary>
        /// 检查技能学习的详细验证结果
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="skillId">技能ID</param>
        /// <returns>验证结果消息</returns>
        Task<string> ValidateSkillLearnAsync(int petId, string skillId);

        /// <summary>
        /// 检查技能升级的详细验证结果
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="skillId">技能ID</param>
        /// <returns>验证结果消息</returns>
        Task<string> ValidateSkillUpgradeAsync(int petId, string skillId);

        #endregion

        #region 技能统计

        /// <summary>
        /// 获取宠物技能统计信息
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>技能统计</returns>
        Task<PetSkillStatistics> GetPetSkillStatisticsAsync(int petId);

        #endregion
    }

    /// <summary>
    /// 宠物技能统计信息
    /// </summary>
    public class PetSkillStatistics
    {
        /// <summary>
        /// 主动技能数量
        /// </summary>
        public int ActiveSkillCount { get; set; }

        /// <summary>
        /// 被动技能数量
        /// </summary>
        public int PassiveSkillCount { get; set; }

        /// <summary>
        /// 技能总数
        /// </summary>
        public int TotalSkillCount => ActiveSkillCount + PassiveSkillCount;

        /// <summary>
        /// 是否已达到主动技能上限（1个）
        /// </summary>
        public bool IsActiveSkillFull => ActiveSkillCount >= 1;

        /// <summary>
        /// 是否已达到被动技能上限（15个）
        /// </summary>
        public bool IsPassiveSkillFull => PassiveSkillCount >= 15;

        /// <summary>
        /// 是否已达到技能总数上限（16个）
        /// </summary>
        public bool IsSkillFull => TotalSkillCount >= 16;

        /// <summary>
        /// 平均技能等级
        /// </summary>
        public double AverageSkillLevel { get; set; }

        /// <summary>
        /// 最高技能等级
        /// </summary>
        public int MaxSkillLevel { get; set; }

        /// <summary>
        /// 技能等级分布
        /// </summary>
        public Dictionary<int, int> SkillLevelDistribution { get; set; } = new();
    }
}
