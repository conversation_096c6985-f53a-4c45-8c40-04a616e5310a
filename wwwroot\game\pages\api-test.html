<!DOCTYPE html>
<html>
<head>
    <title>API路由测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        input { padding: 5px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API路由测试</h1>
        
        <div class="test-section">
            <h3>📋 测试参数</h3>
            <label>用户ID: </label>
            <input type="number" id="userId" value="1001" min="1">
            <button onclick="updateUserId()">更新用户ID</button>
        </div>

        <div class="test-section">
            <h3>🔍 宠物API测试</h3>
            <button onclick="testSynthesisAvailable()">测试 synthesis-available</button>
            <button onclick="testGetSynthesisAvailablePets()">测试 GetSynthesisAvailablePets</button>
            <button onclick="testGetUserPets()">测试 GetUserPets</button>
            <div id="pet-api-result"></div>
        </div>

        <div class="test-section">
            <h3>🎒 背包API测试</h3>
            <button onclick="testGetBag()">测试 GetBag</button>
            <div id="bag-api-result"></div>
        </div>

        <div class="test-section">
            <h3>👤 用户API测试</h3>
            <button onclick="testGetCurrentUser()">测试 GetCurrentUser</button>
            <div id="user-api-result"></div>
        </div>

        <div class="test-section">
            <h3>📊 所有API路由测试</h3>
            <button onclick="testAllRoutes()">测试所有路由</button>
            <div id="all-routes-result"></div>
        </div>
    </div>

    <script>
        // 获取当前用户ID
        function getCurrentUserId() {
            // 优先从认证管理器获取
            if (window.authManager && window.authManager.isLoggedIn()) {
                const userId = window.authManager.getCurrentUserId();
                if (userId && userId !== 1) {
                    return userId;
                }
            }

            // 从输入框获取
            const inputUserId = parseInt(document.getElementById('userId').value);
            if (inputUserId && inputUserId > 0) {
                return inputUserId;
            }

            // 开发环境默认值
            console.warn('⚠️ 使用默认测试用户ID: 1001');
            return 1001;
        }

        let currentUserId = getCurrentUserId();

        function updateUserId() {
            currentUserId = getCurrentUserId();
            console.log('用户ID已更新为:', currentUserId);
        }

        // 测试 synthesis-available 路由
        async function testSynthesisAvailable() {
            const resultDiv = document.getElementById('pet-api-result');
            try {
                const url = `/api/Player/synthesis-available?userId=${currentUserId}`;
                console.log('测试URL:', url);
                
                const response = await fetch(url);
                const result = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="result ${response.ok ? 'success' : 'error'}">
                        <h4>synthesis-available 测试结果</h4>
                        <p><strong>URL:</strong> ${url}</p>
                        <p><strong>状态:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>成功:</strong> ${response.ok ? '是' : '否'}</p>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>synthesis-available 测试失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // 测试 GetSynthesisAvailablePets 路由
        async function testGetSynthesisAvailablePets() {
            const resultDiv = document.getElementById('pet-api-result');
            try {
                const url = `/api/Player/GetSynthesisAvailablePets?userId=${currentUserId}`;
                console.log('测试URL:', url);
                
                const response = await fetch(url);
                const result = await response.json();
                
                resultDiv.innerHTML += `
                    <div class="result ${response.ok ? 'success' : 'error'}">
                        <h4>GetSynthesisAvailablePets 测试结果</h4>
                        <p><strong>URL:</strong> ${url}</p>
                        <p><strong>状态:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>成功:</strong> ${response.ok ? '是' : '否'}</p>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML += `
                    <div class="result error">
                        <h4>GetSynthesisAvailablePets 测试失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // 测试 GetUserPets 路由
        async function testGetUserPets() {
            const resultDiv = document.getElementById('pet-api-result');
            try {
                const url = `/api/Player/GetUserPets`;
                console.log('测试URL:', url);
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ UserId: currentUserId })
                });
                const result = await response.json();
                
                resultDiv.innerHTML += `
                    <div class="result ${response.ok ? 'success' : 'error'}">
                        <h4>GetUserPets 测试结果</h4>
                        <p><strong>URL:</strong> ${url}</p>
                        <p><strong>方法:</strong> POST</p>
                        <p><strong>状态:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>成功:</strong> ${response.ok ? '是' : '否'}</p>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML += `
                    <div class="result error">
                        <h4>GetUserPets 测试失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // 测试 GetBag 路由
        async function testGetBag() {
            const resultDiv = document.getElementById('bag-api-result');
            try {
                const url = `/api/Player/GetBag`;
                console.log('测试URL:', url);
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        UserId: currentUserId,
                        PageIndex: 1,
                        PageSize: 10
                    })
                });
                const result = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="result ${response.ok ? 'success' : 'error'}">
                        <h4>GetBag 测试结果</h4>
                        <p><strong>URL:</strong> ${url}</p>
                        <p><strong>方法:</strong> POST</p>
                        <p><strong>状态:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>成功:</strong> ${response.ok ? '是' : '否'}</p>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>GetBag 测试失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // 测试 GetCurrentUser 路由
        async function testGetCurrentUser() {
            const resultDiv = document.getElementById('user-api-result');
            try {
                const url = `/api/Player/GetCurrentUser?userId=${currentUserId}`;
                console.log('测试URL:', url);
                
                const response = await fetch(url);
                const result = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="result ${response.ok ? 'success' : 'error'}">
                        <h4>GetCurrentUser 测试结果</h4>
                        <p><strong>URL:</strong> ${url}</p>
                        <p><strong>状态:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>成功:</strong> ${response.ok ? '是' : '否'}</p>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>GetCurrentUser 测试失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // 测试所有路由
        async function testAllRoutes() {
            const resultDiv = document.getElementById('all-routes-result');
            resultDiv.innerHTML = '<div class="result info"><h4>正在测试所有路由...</h4></div>';
            
            const routes = [
                { name: 'synthesis-available', url: `/api/Player/synthesis-available?userId=${currentUserId}`, method: 'GET' },
                { name: 'GetSynthesisAvailablePets', url: `/api/Player/GetSynthesisAvailablePets?userId=${currentUserId}`, method: 'GET' },
                { name: 'GetUserPets', url: `/api/Player/GetUserPets`, method: 'POST', body: { UserId: currentUserId } },
                { name: 'GetBag', url: `/api/Player/GetBag`, method: 'POST', body: { UserId: currentUserId, PageIndex: 1, PageSize: 10 } },
                { name: 'GetCurrentUser', url: `/api/Player/GetCurrentUser?userId=${currentUserId}`, method: 'GET' }
            ];
            
            let results = [];
            
            for (const route of routes) {
                try {
                    const options = {
                        method: route.method,
                        headers: { 'Content-Type': 'application/json' }
                    };
                    
                    if (route.body) {
                        options.body = JSON.stringify(route.body);
                    }
                    
                    const response = await fetch(route.url, options);
                    const result = await response.json();
                    
                    results.push({
                        name: route.name,
                        url: route.url,
                        method: route.method,
                        status: response.status,
                        statusText: response.statusText,
                        success: response.ok,
                        data: result
                    });
                } catch (error) {
                    results.push({
                        name: route.name,
                        url: route.url,
                        method: route.method,
                        success: false,
                        error: error.message
                    });
                }
            }
            
            let html = '<div class="result info"><h4>所有路由测试结果</h4>';
            results.forEach(result => {
                html += `
                    <div style="margin: 10px 0; padding: 10px; border: 1px solid #ccc; border-radius: 3px;">
                        <h5>${result.name} - ${result.success ? '✅ 成功' : '❌ 失败'}</h5>
                        <p><strong>URL:</strong> ${result.url}</p>
                        <p><strong>方法:</strong> ${result.method}</p>
                        ${result.status ? `<p><strong>状态:</strong> ${result.status} ${result.statusText}</p>` : ''}
                        ${result.error ? `<p><strong>错误:</strong> ${result.error}</p>` : ''}
                    </div>
                `;
            });
            html += '</div>';
            
            resultDiv.innerHTML = html;
        }

        // 页面加载时自动更新用户ID
        window.onload = function() {
            updateUserId();
        };
    </script>
</body>
</html>
