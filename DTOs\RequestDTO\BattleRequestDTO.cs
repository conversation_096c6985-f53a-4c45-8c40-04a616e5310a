namespace WebApplication_HM.DTOs.RequestDTO
{
    /// <summary>
    /// 战斗请求DTO
    /// </summary>
    public class BattleRequestDTO
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }
        
        /// <summary>
        /// 地图ID
        /// </summary>
        public int MapId { get; set; }
        
        /// <summary>
        /// 宠物ID
        /// </summary>
        public int PetId { get; set; }
        
        /// <summary>
        /// 技能ID（可选，null或空字符串表示普通攻击）
        /// </summary>
        public string? SkillId { get; set; }

        /// <summary>
        /// 怪物ID（可选，null表示随机选择地图怪物）
        /// </summary>
        public int? MonsterId { get; set; }

    }
} 