// Vue应用主入口文件

// 等待所有脚本加载完成
window.addEventListener('load', () => {
    console.log('开始初始化Vue战斗应用...')

    // 简单的应用定义，避免复杂的模块导入
    const BattleApp = {
        name: 'BattleApp',
        setup() {
            const { onMounted } = Vue

            // 模拟战斗状态
            const battleState = Vue.reactive({
                playerHp: 1000,
                playerMaxHp: 1000,
                monsterHp: 800,
                monsterMaxHp: 800,
                showDamage: false,
                currentDamage: null,
                battleStatus: 'fighting'
            })

            // 计算属性
            const playerHpPercentage = Vue.computed(() => {
                return (battleState.playerHp / battleState.playerMaxHp) * 100
            })

            const monsterHpPercentage = Vue.computed(() => {
                return (battleState.monsterHp / battleState.monsterMaxHp) * 100
            })

            // 方法
            const attackMonster = () => {
                const damage = Math.floor(Math.random() * 200) + 50
                battleState.monsterHp = Math.max(0, battleState.monsterHp - damage)

                // 显示伤害
                battleState.currentDamage = {
                    damage: damage,
                    isHit: true,
                    isCritical: Math.random() > 0.8,
                    skillName: '普通攻击'
                }
                battleState.showDamage = true

                // 3秒后隐藏
                setTimeout(() => {
                    battleState.showDamage = false
                }, 3000)

                console.log('攻击怪物，造成', damage, '点伤害')
            }

            onMounted(() => {
                console.log('战斗应用已挂载')
            })

            return {
                battleState,
                playerHpPercentage,
                monsterHpPercentage,
                attackMonster
            }
        },
        template: `
            <div class="battle-app">
                <!-- 战斗场景 -->
                <div class="battle-arena">
                    <div class="arena-background">
                        <div class="background-gradient"></div>
                    </div>

                    <!-- 玩家宠物 -->
                    <div class="player-pet">
                        <div class="pet-image">
                            <div class="pet-placeholder">🐱</div>
                        </div>
                        <div class="hp-bar player-hp">
                            <div class="hp-label">玩家</div>
                            <div class="hp-bar-container">
                                <div class="hp-fill" :style="{ width: playerHpPercentage + '%' }"></div>
                            </div>
                            <div class="hp-text">{{ battleState.playerHp }}/{{ battleState.playerMaxHp }}</div>
                        </div>
                    </div>

                    <!-- 怪物 -->
                    <div class="monster" @click="attackMonster" style="cursor: pointer;">
                        <div class="monster-image">
                            <div class="monster-placeholder">👹</div>
                        </div>
                        <div class="hp-bar monster-hp">
                            <div class="hp-label">怪物</div>
                            <div class="hp-bar-container">
                                <div class="hp-fill" :style="{ width: monsterHpPercentage + '%' }"></div>
                            </div>
                            <div class="hp-text">{{ battleState.monsterHp }}/{{ battleState.monsterMaxHp }}</div>
                        </div>
                    </div>

                    <!-- 战斗提示 -->
                    <div class="battle-hints">
                        <p>点击怪物进行攻击</p>
                    </div>
                </div>

                <!-- 伤害显示 -->
                <transition name="damage-fade">
                    <div v-if="battleState.showDamage && battleState.currentDamage" class="damage-display">
                        <div class="skill-name">{{ battleState.currentDamage.skillName }}</div>
                        <div class="damage-value" :class="{ critical: battleState.currentDamage.isCritical }">
                            -{{ battleState.currentDamage.damage }}
                        </div>
                    </div>
                </transition>

                <!-- 战斗结果 -->
                <div v-if="battleState.monsterHp <= 0" class="battle-result">
                    <h2>胜利！</h2>
                    <p>怪物已被击败</p>
                </div>
            </div>
        `
    }

    try {
        // 创建Vue应用
        const { createApp } = Vue
        const app = createApp(BattleApp)

        // 全局错误处理
        app.config.errorHandler = (err, vm, info) => {
            console.error('Vue应用错误:', err, info)
        }

        // 挂载应用
        app.mount('#app')

        console.log('Vue战斗应用已启动')
    } catch (error) {
        console.error('启动Vue应用失败:', error)
        document.getElementById('app').innerHTML = `
            <div style="color: white; text-align: center; padding: 50px;">
                <h2>启动失败</h2>
                <p>错误: ${error.message}</p>
            </div>
        `
    }
})
