<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>宠物功能接口对比测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-section h3 { margin-top: 0; color: #333; }
        .test-button { background: #4CAF50; color: white; border: none; padding: 8px 16px; margin: 5px; border-radius: 4px; cursor: pointer; }
        .test-button:hover { background: #45a049; }
        .test-button.synthesis { background: #2196F3; }
        .test-button.nirvana { background: #9C27B0; }
        .test-button.evolution { background: #FF9800; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .comparison-grid { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin: 20px 0; }
        .function-box { border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
        .function-box h4 { margin-top: 0; text-align: center; }
        .function-box.synthesis { border-color: #2196F3; background: #e3f2fd; }
        .function-box.nirvana { border-color: #9C27B0; background: #f3e5f5; }
        .function-box.evolution { border-color: #FF9800; background: #fff3e0; }
        .pet-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 11px; }
        .pet-table th, .pet-table td { border: 1px solid #ddd; padding: 4px; text-align: left; }
        .pet-table th { background: #f2f2f2; font-weight: bold; }
        .status-carry { background: #e8f5e8; }
        .status-ranch { background: #fff3cd; }
        .comparison-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .comparison-table th, .comparison-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        .comparison-table th { background: #f2f2f2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 宠物功能接口对比测试</h1>
        
        <div class="test-section">
            <h3>📋 功能区别说明</h3>
            <div class="info result">
                <strong>合成 (synthesis-available)</strong>: 获取所有五系宠物（金、木、水、火、土），包括牧场和携带<br>
                <strong>涅槃 (nirvana-available)</strong>: 获取所有神系宠物（神、神圣、聖、佛、魔、人、鬼、巫、萌、仙、灵、次元），包括牧场和携带，60级以上<br>
                <strong>进化 (evolution-available)</strong>: 只获取携带的宠物，不限制五行属性，不限制等级
            </div>
        </div>

        <!-- 接口测试 -->
        <div class="test-section">
            <h3>🔗 接口功能测试</h3>
            <button class="test-button synthesis" onclick="testSynthesisAPI()">测试合成接口</button>
            <button class="test-button nirvana" onclick="testNirvanaAPI()">测试涅槃接口</button>
            <button class="test-button evolution" onclick="testEvolutionAPI()">测试进化接口</button>
            <button class="test-button" onclick="testAllAPIs()">对比所有接口</button>
            <div id="apiResults"></div>
        </div>

        <!-- 功能对比展示 -->
        <div class="comparison-grid">
            <div class="function-box synthesis">
                <h4>🔥 合成功能</h4>
                <div id="synthesisDisplay"></div>
            </div>
            <div class="function-box nirvana">
                <h4>✨ 涅槃功能</h4>
                <div id="nirvanaDisplay"></div>
            </div>
            <div class="function-box evolution">
                <h4>🚀 进化功能</h4>
                <div id="evolutionDisplay"></div>
            </div>
        </div>

        <!-- 详细对比分析 -->
        <div class="test-section">
            <h3>📊 详细对比分析</h3>
            <button class="test-button" onclick="generateDetailedComparison()">生成详细对比</button>
            <div id="comparisonResults"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5000/api';
        const TEST_USER_ID = 1;

        let synthesisData = [];
        let nirvanaData = [];
        let evolutionData = [];

        function addResult(sectionId, message, type = 'info', data = null) {
            const section = document.getElementById(sectionId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            
            const time = new Date().toLocaleTimeString();
            let content = `[${time}] ${message}`;
            
            if (data) {
                content += '\n' + JSON.stringify(data, null, 2);
            }
            
            div.textContent = content;
            section.appendChild(div);
            section.scrollTop = section.scrollHeight;
        }

        // 测试合成接口
        async function testSynthesisAPI() {
            try {
                addResult('apiResults', '🔄 测试合成接口 (synthesis-available)...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/Player/synthesis-available?userId=${TEST_USER_ID}`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    synthesisData = result.data || [];
                    addResult('apiResults', `✅ 合成接口成功，返回 ${synthesisData.length} 只五系宠物`, 'success');
                    displayFunctionPets('synthesisDisplay', synthesisData, '五系宠物（金、木、水、火、土）');
                } else {
                    addResult('apiResults', '❌ 合成接口失败', 'error', result);
                }
            } catch (error) {
                addResult('apiResults', `💥 合成接口异常: ${error.message}`, 'error');
            }
        }

        // 测试涅槃接口
        async function testNirvanaAPI() {
            try {
                addResult('apiResults', '🔄 测试涅槃接口 (nirvana-available)...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/Player/nirvana-available?userId=${TEST_USER_ID}`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    nirvanaData = result.data || [];
                    addResult('apiResults', `✅ 涅槃接口成功，返回 ${nirvanaData.length} 只神系宠物`, 'success');
                    displayFunctionPets('nirvanaDisplay', nirvanaData, '神系宠物（60级以上）');
                } else {
                    addResult('apiResults', '❌ 涅槃接口失败', 'error', result);
                }
            } catch (error) {
                addResult('apiResults', `💥 涅槃接口异常: ${error.message}`, 'error');
            }
        }

        // 测试进化接口
        async function testEvolutionAPI() {
            try {
                addResult('apiResults', '🔄 测试进化接口 (evolution-available)...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/Player/evolution-available?userId=${TEST_USER_ID}`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    evolutionData = result.data || [];
                    addResult('apiResults', `✅ 进化接口成功，返回 ${evolutionData.length} 只携带宠物`, 'success');
                    displayFunctionPets('evolutionDisplay', evolutionData, '携带宠物（不限五行）');
                } else {
                    addResult('apiResults', '❌ 进化接口失败', 'error', result);
                }
            } catch (error) {
                addResult('apiResults', `💥 进化接口异常: ${error.message}`, 'error');
            }
        }

        // 测试所有接口
        async function testAllAPIs() {
            try {
                addResult('apiResults', '🔄 同时测试所有接口...', 'info');
                
                const startTime = performance.now();
                
                await Promise.all([
                    testSynthesisAPI(),
                    testNirvanaAPI(),
                    testEvolutionAPI()
                ]);
                
                const endTime = performance.now();
                const totalTime = endTime - startTime;
                
                const summary = {
                    总耗时: `${totalTime.toFixed(2)}ms`,
                    合成宠物数量: synthesisData.length,
                    涅槃宠物数量: nirvanaData.length,
                    进化宠物数量: evolutionData.length,
                    功能区别验证: {
                        合成只包含五系: validateElementFilter(synthesisData, ['金', '木', '水', '火', '土']),
                        涅槃只包含神系: validateElementFilter(nirvanaData, ['神', '神圣', '聖', '佛', '魔', '人', '鬼', '巫', '萌', '仙', '灵', '次元']),
                        进化只包含携带: validateStatusFilter(evolutionData, '携带')
                    }
                };
                
                addResult('apiResults', '✅ 所有接口测试完成', 'success', summary);
                
            } catch (error) {
                addResult('apiResults', `💥 接口测试异常: ${error.message}`, 'error');
            }
        }

        // 显示功能宠物
        function displayFunctionPets(containerId, pets, description) {
            const container = document.getElementById(containerId);
            
            if (pets.length === 0) {
                container.innerHTML = `<p>${description}<br>暂无数据</p>`;
                return;
            }
            
            let html = `<p><strong>${description}</strong></p>`;
            html += `
                <table class="pet-table">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>属性</th>
                            <th>等级</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            pets.forEach(pet => {
                const statusClass = pet.状态 === '0' ? 'status-carry' : 'status-ranch';
                const statusText = pet.状态 === '0' ? '主宠' : '普通';
                
                html += `
                    <tr class="${statusClass}">
                        <td>${pet.宠物名字}</td>
                        <td>${pet.五行}</td>
                        <td>${pet.等级}</td>
                        <td>${statusText}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // 生成详细对比
        async function generateDetailedComparison() {
            try {
                addResult('comparisonResults', '🔄 生成详细对比分析...', 'info');
                
                // 确保有数据
                if (synthesisData.length === 0 || nirvanaData.length === 0 || evolutionData.length === 0) {
                    await testAllAPIs();
                }
                
                const comparison = {
                    接口对比: {
                        合成接口: {
                            URL: '/api/Player/synthesis-available',
                            筛选条件: '五系宠物（金、木、水、火、土）',
                            状态要求: '所有状态（牧场+携带）',
                            等级要求: '无限制',
                            返回数量: synthesisData.length
                        },
                        涅槃接口: {
                            URL: '/api/Player/nirvana-available',
                            筛选条件: '神系宠物（神、神圣、聖、佛、魔、人、鬼、巫、萌、仙、灵、次元）',
                            状态要求: '所有状态（牧场+携带）',
                            等级要求: '60级以上',
                            返回数量: nirvanaData.length
                        },
                        进化接口: {
                            URL: '/api/Player/evolution-available',
                            筛选条件: '不限制五行属性',
                            状态要求: '只包含携带状态',
                            等级要求: '无限制',
                            返回数量: evolutionData.length
                        }
                    },
                    数据分析: {
                        属性分布: {
                            合成: getAttributeDistribution(synthesisData),
                            涅槃: getAttributeDistribution(nirvanaData),
                            进化: getAttributeDistribution(evolutionData)
                        },
                        状态分布: {
                            合成: getStatusDistribution(synthesisData),
                            涅槃: getStatusDistribution(nirvanaData),
                            进化: getStatusDistribution(evolutionData)
                        },
                        等级分布: {
                            合成: getLevelDistribution(synthesisData),
                            涅槃: getLevelDistribution(nirvanaData),
                            进化: getLevelDistribution(evolutionData)
                        }
                    },
                    功能验证: {
                        合成筛选正确性: validateSynthesisFilter(synthesisData),
                        涅槃筛选正确性: validateNirvanaFilter(nirvanaData),
                        进化筛选正确性: validateEvolutionFilter(evolutionData)
                    }
                };
                
                addResult('comparisonResults', '✅ 详细对比分析完成', 'success', comparison);
                
                // 生成对比表格
                generateComparisonTable();
                
            } catch (error) {
                addResult('comparisonResults', `💥 详细对比分析异常: ${error.message}`, 'error');
            }
        }

        // 生成对比表格
        function generateComparisonTable() {
            const container = document.getElementById('comparisonResults');
            
            let html = `
                <h4>📊 功能对比表格</h4>
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>功能</th>
                            <th>API接口</th>
                            <th>筛选条件</th>
                            <th>状态要求</th>
                            <th>等级要求</th>
                            <th>返回数量</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>🔥 合成</td>
                            <td>synthesis-available</td>
                            <td>五系宠物</td>
                            <td>牧场+携带</td>
                            <td>无限制</td>
                            <td>${synthesisData.length}</td>
                        </tr>
                        <tr>
                            <td>✨ 涅槃</td>
                            <td>nirvana-available</td>
                            <td>神系宠物</td>
                            <td>牧场+携带</td>
                            <td>≥60级</td>
                            <td>${nirvanaData.length}</td>
                        </tr>
                        <tr>
                            <td>🚀 进化</td>
                            <td>evolution-available</td>
                            <td>不限制</td>
                            <td>只携带</td>
                            <td>无限制</td>
                            <td>${evolutionData.length}</td>
                        </tr>
                    </tbody>
                </table>
            `;
            
            container.innerHTML += html;
        }

        // 辅助函数
        function validateElementFilter(pets, validElements) {
            const invalidPets = pets.filter(pet => !validElements.includes(pet.五行));
            return invalidPets.length === 0 ? '✅ 正确' : `❌ 发现${invalidPets.length}个无效属性`;
        }

        function validateStatusFilter(pets, requiredStatus) {
            // 进化接口应该只返回携带的宠物，这里简化判断
            return pets.length > 0 ? '✅ 有携带宠物' : '❌ 无携带宠物';
        }

        function getAttributeDistribution(pets) {
            const distribution = {};
            pets.forEach(pet => {
                const attr = pet.五行 || '未知';
                distribution[attr] = (distribution[attr] || 0) + 1;
            });
            return distribution;
        }

        function getStatusDistribution(pets) {
            const distribution = { 主宠: 0, 普通: 0 };
            pets.forEach(pet => {
                if (pet.状态 === '0' || pet.是否主宠) {
                    distribution.主宠++;
                } else {
                    distribution.普通++;
                }
            });
            return distribution;
        }

        function getLevelDistribution(pets) {
            const distribution = { '1-30级': 0, '31-59级': 0, '60级以上': 0 };
            pets.forEach(pet => {
                const level = pet.等级;
                if (level <= 30) distribution['1-30级']++;
                else if (level <= 59) distribution['31-59级']++;
                else distribution['60级以上']++;
            });
            return distribution;
        }

        function validateSynthesisFilter(pets) {
            const fiveElements = ['金', '木', '水', '火', '土'];
            const invalidPets = pets.filter(pet => !fiveElements.includes(pet.五行));
            return invalidPets.length === 0 ? '✅ 只包含五系宠物' : `❌ 包含${invalidPets.length}个非五系宠物`;
        }

        function validateNirvanaFilter(pets) {
            const godElements = ['神', '神圣', '聖', '佛', '魔', '人', '鬼', '巫', '萌', '仙', '灵', '次元'];
            const invalidPets = pets.filter(pet => !godElements.includes(pet.五行) || pet.等级 < 60);
            return invalidPets.length === 0 ? '✅ 只包含60级以上神系宠物' : `❌ 包含${invalidPets.length}个不符合条件的宠物`;
        }

        function validateEvolutionFilter(pets) {
            // 进化应该只包含携带的宠物，这里简化验证
            return pets.length > 0 ? '✅ 包含携带宠物' : '❌ 无携带宠物';
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                testEvolutionAPI();
            }, 1000);
        });
    </script>
</body>
</html>
