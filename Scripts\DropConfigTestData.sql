-- 掉落配置测试数据
-- 用于测试新的 drop_items_json 字段功能

-- 清理现有测试数据
DELETE FROM drop_config WHERE remark LIKE '%测试数据%';

-- 插入测试掉落配置数据
-- 1. 地图掉落配置（使用 JSON 格式）
INSERT INTO drop_config (map_id, drop_type, monster_id, drop_items_json, remark, create_time) VALUES
(1, '地图掉落', NULL, '[{"ItemId":"2016092301","DropRate":0.1,"MinCount":1,"MaxCount":1},{"ItemId":"2016092302","DropRate":0.1,"MinCount":1,"MaxCount":1}]', '新手训练营掉落配置-测试数据', NOW()),
(2, '地图掉落', NULL, '[{"ItemId":"2016092303","DropRate":0.15,"MinCount":1,"MaxCount":2},{"ItemId":"2016092304","DropRate":0.08,"MinCount":1,"MaxCount":1}]', '彩虹森林掉落配置-测试数据', NOW()),
(3, '地图掉落', NULL, '[{"ItemId":"2016092305","DropRate":0.2,"MinCount":1,"MaxCount":3},{"ItemId":"2016092306","DropRate":0.05,"MinCount":1,"MaxCount":1}]', '火焰山谷掉落配置-测试数据', NOW());

-- 2. 怪物掉落配置（使用 JSON 格式）
INSERT INTO drop_config (map_id, drop_type, monster_id, drop_items_json, remark, create_time) VALUES
(1, '怪物掉落', 1, '[{"ItemId":"2016092307","DropRate":0.25,"MinCount":1,"MaxCount":2},{"ItemId":"2016092308","DropRate":0.12,"MinCount":1,"MaxCount":1}]', '新手训练营怪物1掉落配置-测试数据', NOW()),
(1, '怪物掉落', 2, '[{"ItemId":"2016092309","DropRate":0.3,"MinCount":2,"MaxCount":4},{"ItemId":"2016092310","DropRate":0.15,"MinCount":1,"MaxCount":2}]', '新手训练营怪物2掉落配置-测试数据', NOW()),
(2, '怪物掉落', 3, '[{"ItemId":"2016092311","DropRate":0.18,"MinCount":1,"MaxCount":3},{"ItemId":"2016092312","DropRate":0.08,"MinCount":1,"MaxCount":1}]', '彩虹森林怪物3掉落配置-测试数据', NOW());

-- 3. 兼容性测试：旧格式掉落配置（单个物品）
INSERT INTO drop_config (map_id, drop_type, monster_id, item_id, drop_rate, min_count, max_count, remark, create_time) VALUES
(4, '地图掉落', NULL, '2016092313', 0.2, 1, 2, '冰雪山脉旧格式掉落配置-测试数据', NOW()),
(4, '怪物掉落', 4, '2016092314', 0.35, 1, 3, '冰雪山脉怪物4旧格式掉落配置-测试数据', NOW());

-- 4. 混合配置测试：同时有 JSON 和旧格式（JSON 优先）
INSERT INTO drop_config (map_id, drop_type, monster_id, item_id, drop_rate, min_count, max_count, drop_items_json, remark, create_time) VALUES
(5, '地图掉落', NULL, '2016092315', 0.1, 1, 1, '[{"ItemId":"2016092316","DropRate":0.25,"MinCount":1,"MaxCount":2},{"ItemId":"2016092317","DropRate":0.15,"MinCount":1,"MaxCount":1}]', '黄金海岸混合格式掉落配置-测试数据（JSON优先）', NOW());

-- 5. 创建对应的测试道具配置（如果不存在）
INSERT IGNORE INTO item_config (item_no, name, type, description, quality, price, create_time) VALUES
(2016092301, '新手药水', '消耗品', '恢复少量生命值', '普通', 10, NOW()),
(2016092302, '小经验丹', '消耗品', '增加少量经验值', '普通', 50, NOW()),
(2016092303, '草系精华', '材料', '草系宠物进化材料', '稀有', 100, NOW()),
(2016092304, '风系精华', '材料', '风系宠物进化材料', '稀有', 100, NOW()),
(2016092305, '火系精华', '材料', '火系宠物进化材料', '稀有', 120, NOW()),
(2016092306, '火焰石', '材料', '高级火系材料', '史诗', 500, NOW()),
(2016092307, '初级治疗药', '消耗品', '恢复中等生命值', '普通', 25, NOW()),
(2016092308, '力量药水', '消耗品', '临时增加攻击力', '稀有', 80, NOW()),
(2016092309, '魔法药水', '消耗品', '恢复魔法值', '普通', 30, NOW()),
(2016092310, '敏捷药水', '消耗品', '临时增加速度', '稀有', 75, NOW()),
(2016092311, '中级经验丹', '消耗品', '增加中等经验值', '稀有', 150, NOW()),
(2016092312, '神秘宝石', '材料', '神秘的宝石', '传说', 1000, NOW()),
(2016092313, '冰系精华', '材料', '冰系宠物进化材料', '稀有', 110, NOW()),
(2016092314, '寒冰石', '材料', '高级冰系材料', '史诗', 480, NOW()),
(2016092315, '水系精华', '材料', '水系宠物进化材料', '稀有', 105, NOW()),
(2016092316, '海洋之心', '材料', '传说中的海洋宝石', '传说', 2000, NOW()),
(2016092317, '珍珠', '材料', '美丽的珍珠', '稀有', 200, NOW());

-- 6. 验证插入的数据
SELECT 
    '=== 掉落配置统计 ===' as info,
    COUNT(*) as total_count,
    COUNT(CASE WHEN drop_items_json IS NOT NULL THEN 1 END) as json_format_count,
    COUNT(CASE WHEN item_id IS NOT NULL THEN 1 END) as old_format_count
FROM drop_config 
WHERE remark LIKE '%测试数据%';

-- 7. 查看具体的测试配置
SELECT 
    id, map_id, drop_type, monster_id, 
    CASE 
        WHEN drop_items_json IS NOT NULL THEN 'JSON格式'
        WHEN item_id IS NOT NULL THEN '旧格式'
        ELSE '无配置'
    END as format_type,
    CASE 
        WHEN drop_items_json IS NOT NULL THEN drop_items_json
        WHEN item_id IS NOT NULL THEN CONCAT('{"ItemId":"', item_id, '","DropRate":', drop_rate, ',"MinCount":', min_count, ',"MaxCount":', max_count, '}')
        ELSE NULL
    END as drop_config,
    remark
FROM drop_config 
WHERE remark LIKE '%测试数据%'
ORDER BY map_id, drop_type, monster_id;

COMMIT;
