using System;
using System.ComponentModel.DataAnnotations;

namespace WebApplication_HM.DTOs
{
    /// <summary>
    /// 百变宠物类型枚举
    /// </summary>
    public enum PetTransformType
    {
        /// <summary>
        /// 随机神宠
        /// </summary>
        RandomGod,

        /// <summary>
        /// 随机神圣宠物
        /// </summary>
        RandomHoly,

        /// <summary>
        /// 指定宠物
        /// </summary>
        Specified,

        /// <summary>
        /// 随机普通宠物
        /// </summary>
        RandomNormal
    }

    /// <summary>
    /// 随机神宠请求DTO
    /// </summary>
    public class RandomGodPetRequestDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        public int UserId { get; set; }

        /// <summary>
        /// 源宠物ID（可选，用于消耗源宠物）
        /// </summary>
        public int? SourcePetId { get; set; }

        /// <summary>
        /// 使用的道具ID（可选）
        /// </summary>
        public string? UsedItemId { get; set; }
    }

    /// <summary>
    /// 宠物变换请求DTO
    /// </summary>
    public class PetTransformRequestDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        public int UserId { get; set; }

        /// <summary>
        /// 源宠物ID
        /// </summary>
        [Required]
        public int SourcePetId { get; set; }

        /// <summary>
        /// 目标宠物编号
        /// </summary>
        [Required]
        public int TargetPetNo { get; set; }

        /// <summary>
        /// 变换类型
        /// </summary>
        [Required]
        public PetTransformType TransformType { get; set; }

        /// <summary>
        /// 使用的道具ID（可选）
        /// </summary>
        public string? UsedItemId { get; set; }
    }

    /// <summary>
    /// 宠物变换结果DTO
    /// </summary>
    public class PetTransformResultDto
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 结果宠物编号
        /// </summary>
        public int? ResultPetNo { get; set; }

        /// <summary>
        /// 结果宠物ID
        /// </summary>
        public int? ResultPetId { get; set; }

        /// <summary>
        /// 结果宠物名称
        /// </summary>
        public string? ResultPetName { get; set; }

        /// <summary>
        /// 是否为神宠
        /// </summary>
        public bool IsGodPet { get; set; }

        /// <summary>
        /// 是否为神圣宠物
        /// </summary>
        public bool IsHolyPet { get; set; }

        /// <summary>
        /// 消耗的金币
        /// </summary>
        public long CostGold { get; set; }

        /// <summary>
        /// 使用的道具
        /// </summary>
        public string? UsedItem { get; set; }

        /// <summary>
        /// 变换类型
        /// </summary>
        public PetTransformType TransformType { get; set; }

        /// <summary>
        /// 额外信息
        /// </summary>
        public object? ExtraData { get; set; }
    }

    /// <summary>
    /// 可变换宠物信息DTO
    /// </summary>
    public class TransformablePetDto
    {
        /// <summary>
        /// 宠物编号
        /// </summary>
        public int PetNo { get; set; }

        /// <summary>
        /// 宠物名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 宠物系别
        /// </summary>
        public string Element { get; set; } = string.Empty;

        /// <summary>
        /// 宠物属性（普通/神/神圣）
        /// </summary>
        public string Attribute { get; set; } = string.Empty;

        /// <summary>
        /// 是否为神宠
        /// </summary>
        public bool IsGodPet { get; set; }

        /// <summary>
        /// 是否为神圣宠物
        /// </summary>
        public bool IsHolyPet { get; set; }

        /// <summary>
        /// 宠物图片路径
        /// </summary>
        public string? ImagePath { get; set; }

        /// <summary>
        /// 稀有度等级
        /// </summary>
        public int RarityLevel { get; set; }
    }

    /// <summary>
    /// 百变宠物配置DTO
    /// </summary>
    public class PetTransformConfigDto
    {
        /// <summary>
        /// 变换类型
        /// </summary>
        public PetTransformType TransformType { get; set; }

        /// <summary>
        /// 消耗金币
        /// </summary>
        public long CostGold { get; set; }

        /// <summary>
        /// 所需等级
        /// </summary>
        public int RequiredLevel { get; set; }

        /// <summary>
        /// 成功率（百分比）
        /// </summary>
        public decimal SuccessRate { get; set; }

        /// <summary>
        /// 冷却时间（毫秒）
        /// </summary>
        public long CooldownTime { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 神宠概率（百分比）
        /// </summary>
        public decimal GodPetRate { get; set; }

        /// <summary>
        /// 神圣宠物概率（百分比）
        /// </summary>
        public decimal HolyPetRate { get; set; }
    }

    /// <summary>
    /// 百变宠物统计DTO
    /// </summary>
    public class PetTransformStatsDto
    {
        /// <summary>
        /// 总变换次数
        /// </summary>
        public int TotalTransforms { get; set; }

        /// <summary>
        /// 成功次数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败次数
        /// </summary>
        public int FailureCount { get; set; }

        /// <summary>
        /// 神宠获得次数
        /// </summary>
        public int GodPetCount { get; set; }

        /// <summary>
        /// 神圣宠物获得次数
        /// </summary>
        public int HolyPetCount { get; set; }

        /// <summary>
        /// 总消耗金币
        /// </summary>
        public long TotalCostGold { get; set; }

        /// <summary>
        /// 成功率
        /// </summary>
        public decimal SuccessRate { get; set; }

        /// <summary>
        /// 最后变换时间
        /// </summary>
        public DateTime? LastTransformTime { get; set; }
    }

    /// <summary>
    /// 百变宠物选择界面数据DTO
    /// </summary>
    public class PetTransformSelectionDto
    {
        /// <summary>
        /// 可变换宠物列表
        /// </summary>
        public List<TransformablePetDto> TransformablePets { get; set; } = new List<TransformablePetDto>();

        /// <summary>
        /// 神宠列表
        /// </summary>
        public List<TransformablePetDto> GodPets { get; set; } = new List<TransformablePetDto>();

        /// <summary>
        /// 神圣宠物列表
        /// </summary>
        public List<TransformablePetDto> HolyPets { get; set; } = new List<TransformablePetDto>();

        /// <summary>
        /// 变换配置
        /// </summary>
        public PetTransformConfigDto Config { get; set; } = new PetTransformConfigDto();

        /// <summary>
        /// 用户统计
        /// </summary>
        public PetTransformStatsDto UserStats { get; set; } = new PetTransformStatsDto();
    }
}
