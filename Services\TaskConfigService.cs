using SqlSugar;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 任务配置服务实现
    /// </summary>
    public class TaskConfigService : ITaskConfigService
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<TaskConfigService> _logger;

        public TaskConfigService(ISqlSugarClient db, ILogger<TaskConfigService> logger)
        {
            _db = db;
            _logger = logger;
        }

        #region 任务配置管理

        /// <summary>
        /// 获取任务配置
        /// </summary>
        public async Task<task_config> GetTaskConfigAsync(string taskId)
        {
            try
            {
                return await _db.Queryable<task_config>()
                    .Where(tc => tc.task_id == taskId)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务配置失败，任务ID: {TaskId}", taskId);
                return null;
            }
        }

        /// <summary>
        /// 获取所有激活的任务配置
        /// </summary>
        public async Task<List<task_config>> GetActiveTaskConfigsAsync()
        {
            try
            {
                return await _db.Queryable<task_config>()
                    .Where(tc => tc.is_active == 1)
                    .OrderBy(tc => tc.sort_order)
                    .OrderBy(tc => tc.task_id)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取激活任务配置失败");
                return new List<task_config>();
            }
        }

        /// <summary>
        /// 创建任务配置
        /// </summary>
        public async Task<bool> CreateTaskConfigAsync(task_config taskConfig)
        {
            try
            {
                // 检查任务ID是否已存在
                var existing = await GetTaskConfigAsync(taskConfig.task_id);
                if (existing != null)
                {
                    _logger.LogWarning("任务ID已存在: {TaskId}", taskConfig.task_id);
                    return false;
                }

                taskConfig.created_at = DateTime.Now;
                taskConfig.updated_at = DateTime.Now;

                var result = await _db.Insertable(taskConfig).ExecuteCommandAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建任务配置失败，任务ID: {TaskId}", taskConfig.task_id);
                return false;
            }
        }

        /// <summary>
        /// 更新任务配置
        /// </summary>
        public async Task<bool> UpdateTaskConfigAsync(task_config taskConfig)
        {
            try
            {
                taskConfig.updated_at = DateTime.Now;

                var result = await _db.Updateable(taskConfig)
                    .Where(tc => tc.task_id == taskConfig.task_id)
                    .ExecuteCommandAsync();

                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务配置失败，任务ID: {TaskId}", taskConfig.task_id);
                return false;
            }
        }

        /// <summary>
        /// 删除任务配置
        /// </summary>
        public async Task<bool> DeleteTaskConfigAsync(string taskId)
        {
            try
            {
                // 开始事务
                _db.Ado.BeginTran();

                try
                {
                    // 删除相关的任务目标
                    await _db.Deleteable<task_objective>()
                        .Where(to => to.task_id == taskId)
                        .ExecuteCommandAsync();

                    // 删除任务配置
                    var result = await _db.Deleteable<task_config>()
                        .Where(tc => tc.task_id == taskId)
                        .ExecuteCommandAsync();

                    _db.Ado.CommitTran();

                    return result > 0;
                }
                catch
                {
                    _db.Ado.RollbackTran();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除任务配置失败，任务ID: {TaskId}", taskId);
                return false;
            }
        }

        #endregion

        #region 任务目标管理

        /// <summary>
        /// 获取任务目标列表
        /// </summary>
        public async Task<List<task_objective>> GetTaskObjectivesAsync(string taskId)
        {
            try
            {
                return await _db.Queryable<task_objective>()
                    .Where(to => to.task_id == taskId)
                    .OrderBy(to => to.objective_order)
                    .OrderBy(to => to.objective_id)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务目标失败，任务ID: {TaskId}", taskId);
                return new List<task_objective>();
            }
        }

        /// <summary>
        /// 创建任务目标
        /// </summary>
        public async Task<bool> CreateTaskObjectiveAsync(task_objective objective)
        {
            try
            {
                objective.created_at = DateTime.Now;

                var result = await _db.Insertable(objective).ExecuteCommandAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建任务目标失败，任务ID: {TaskId}", objective.task_id);
                return false;
            }
        }

        /// <summary>
        /// 更新任务目标
        /// </summary>
        public async Task<bool> UpdateTaskObjectiveAsync(task_objective objective)
        {
            try
            {
                var result = await _db.Updateable(objective)
                    .Where(to => to.objective_id == objective.objective_id)
                    .ExecuteCommandAsync();

                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务目标失败，目标ID: {ObjectiveId}", objective.objective_id);
                return false;
            }
        }

        /// <summary>
        /// 删除任务目标
        /// </summary>
        public async Task<bool> DeleteTaskObjectiveAsync(int objectiveId)
        {
            try
            {
                var result = await _db.Deleteable<task_objective>()
                    .Where(to => to.objective_id == objectiveId)
                    .ExecuteCommandAsync();

                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除任务目标失败，目标ID: {ObjectiveId}", objectiveId);
                return false;
            }
        }

        #endregion

        #region 任务类型管理

        /// <summary>
        /// 获取任务类型配置
        /// </summary>
        public async Task<List<task_type_config>> GetTaskTypeConfigsAsync()
        {
            try
            {
                return await _db.Queryable<task_type_config>()
                    .Where(ttc => ttc.is_active == 1)
                    .OrderBy(ttc => ttc.type_id)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务类型配置失败");
                return new List<task_type_config>();
            }
        }

        /// <summary>
        /// 获取任务类型处理器
        /// </summary>
        public async Task<string> GetTaskTypeHandlerAsync(string objectiveType)
        {
            try
            {
                var config = await _db.Queryable<task_type_config>()
                    .Where(ttc => ttc.type_id == objectiveType && ttc.is_active == 1)
                    .FirstAsync();

                return config?.handler_class;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务类型处理器失败，类型: {ObjectiveType}", objectiveType);
                return null;
            }
        }

        #endregion
    }
}
