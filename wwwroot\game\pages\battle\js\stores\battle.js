// 战斗状态管理
const { defineStore } = Pinia
const { ref, computed } = Vue

export const useBattleStore = defineStore('battle', () => {
    // 状态数据
    const battleId = ref(null)
    const currentRound = ref(0)
    const battleRounds = ref([])
    const isPlayerTurn = ref(true)
    const battleStatus = ref('waiting') // waiting, fighting, ended
    const currentDamage = ref(null)
    const showDamage = ref(false)
    const showSkillPanel = ref(false)
    const battleLogs = ref([])
    const lastPlayedRound = ref(0)
    
    // 从原Battle.html迁移的全局变量
    const gwHp = ref(0)      // 怪物当前HP
    const gwMaxHp = ref(0)   // 怪物最大HP
    const cwHp = ref(0)      // 玩家当前HP
    const cwMaxHp = ref(0)   // 玩家最大HP
    const jiasheng = ref(0)  // 加深伤害
    const dixiao = ref(0)    // 抵消伤害
    const xixue = ref(0)     // 吸血
    const ximo = ref(0)      // 吸魔
    const jn = ref('')       // 技能名称
    
    // 计算属性
    const isPlayerAttacking = computed(() => {
        const round = getCurrentRound()
        return round?.attackerType === 'Player'
    })
    
    const isMonsterAttacking = computed(() => {
        const round = getCurrentRound()
        return round?.attackerType === 'Monster'
    })
    
    const playerHpPercentage = computed(() => {
        if (cwMaxHp.value <= 0) return 0
        return Math.max(0, (cwHp.value / cwMaxHp.value) * 100)
    })
    
    const monsterHpPercentage = computed(() => {
        if (gwMaxHp.value <= 0) return 0
        return Math.max(0, (gwHp.value / gwMaxHp.value) * 100)
    })
    
    const battleResult = computed(() => {
        if (battleStatus.value !== 'ended') return null
        const lastRound = battleRounds.value[battleRounds.value.length - 1]
        return lastRound?.result
    })
    
    // 方法
    const initializeBattle = async () => {
        try {
            console.log('初始化战斗系统...')
            
            // 从URL参数获取怪物ID
            const urlParams = new URLSearchParams(window.location.search)
            const monsterId = urlParams.get('monsterId') || '1'
            
            // 初始化玩家和怪物数据
            await loadInitialData(monsterId)
            
            battleStatus.value = 'fighting'
            console.log('战斗系统初始化完成')
        } catch (error) {
            console.error('初始化战斗失败:', error)
        }
    }
    
    const loadInitialData = async (monsterId) => {
        // 模拟加载玩家数据
        cwHp.value = 1000
        cwMaxHp.value = 1000
        
        // 模拟加载怪物数据
        gwHp.value = 800
        gwMaxHp.value = 800
        
        console.log('初始数据加载完成:', {
            playerHp: cwHp.value,
            monsterHp: gwHp.value
        })
    }
    
    const getCurrentRound = () => {
        return battleRounds.value[currentRound.value - 1]
    }
    
    const startBattle = async () => {
        try {
            console.log('开始战斗...')
            battleStatus.value = 'fighting'
            await loadBattleRounds()
        } catch (error) {
            console.error('开始战斗失败:', error)
        }
    }
    
    const loadBattleRounds = async () => {
        try {
            // 这里应该调用实际的API，现在先用模拟数据
            const mockRounds = [
                {
                    round: 1,
                    attackerType: 'Player',
                    damage: 150,
                    isHit: true,
                    isCritical: false,
                    skillName: '普通攻击',
                    playerHpAfter: 1000,
                    monsterHpAfter: 650
                }
            ]
            
            battleRounds.value = mockRounds
            console.log('战斗回合数据加载完成:', mockRounds)
        } catch (error) {
            console.error('加载战斗回合失败:', error)
        }
    }
    
    const playNextRound = () => {
        if (currentRound.value < battleRounds.value.length) {
            const round = battleRounds.value[currentRound.value]
            playRound(round)
            currentRound.value++
        } else {
            checkBattleEnd()
        }
    }
    
    const playRound = (round) => {
        console.log('播放回合:', round)
        
        // 设置伤害显示数据
        jiasheng.value = round.amplifiedDamage || 0
        dixiao.value = round.reducedDamage || 0
        xixue.value = round.lifeSteal || 0
        ximo.value = round.manaSteal || 0
        jn.value = round.skillName || '普通攻击'
        
        // 显示伤害信息
        currentDamage.value = {
            damage: round.damage,
            isHit: round.isHit,
            isCritical: round.isCritical,
            skillName: round.skillName,
            amplifiedDamage: round.amplifiedDamage,
            reducedDamage: round.reducedDamage,
            lifeSteal: round.lifeSteal,
            manaSteal: round.manaSteal
        }
        
        showDamage.value = true
        
        // 更新HP
        if (round.attackerType === 'Player') {
            gwHp.value = round.monsterHpAfter
        } else {
            cwHp.value = round.playerHpAfter
        }
        
        // 添加战斗日志
        battleLogs.value.push({
            round: round.round,
            description: `${round.attackerType === 'Player' ? '玩家' : '怪物'}使用${round.skillName}造成${round.damage}点伤害`,
            timestamp: new Date()
        })
        
        // 3秒后隐藏伤害显示
        setTimeout(() => {
            showDamage.value = false
        }, 3000)
    }
    
    const checkBattleEnd = () => {
        if (cwHp.value <= 0 || gwHp.value <= 0) {
            battleStatus.value = 'ended'
            console.log('战斗结束')
        }
    }
    
    const hideDamageInfo = () => {
        showDamage.value = false
        currentDamage.value = null
    }
    
    // 返回状态和方法
    return {
        // 状态
        battleId,
        currentRound,
        battleRounds,
        isPlayerTurn,
        battleStatus,
        currentDamage,
        showDamage,
        showSkillPanel,
        battleLogs,
        lastPlayedRound,
        gwHp,
        gwMaxHp,
        cwHp,
        cwMaxHp,
        jiasheng,
        dixiao,
        xixue,
        ximo,
        jn,
        
        // 计算属性
        isPlayerAttacking,
        isMonsterAttacking,
        playerHpPercentage,
        monsterHpPercentage,
        battleResult,
        
        // 方法
        initializeBattle,
        startBattle,
        loadBattleRounds,
        playNextRound,
        getCurrentRound,
        hideDamageInfo,
        playRound
    }
})
