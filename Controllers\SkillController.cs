using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.DTOs;
using WebApplication_HM.Interface;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 技能管理控制器
    /// 基于原项目WindowsFormsApplication7的技能系统设计
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class SkillController : ControllerBase
    {
        private readonly ISkillService _skillService;
        private readonly ILogger<SkillController> _logger;

        public SkillController(ISkillService skillService, ILogger<SkillController> logger)
        {
            _skillService = skillService;
            _logger = logger;
        }

        #region 技能配置查询

        /// <summary>
        /// 获取所有技能配置
        /// </summary>
        /// <returns>技能配置列表</returns>
        [HttpGet("configs")]
        public async Task<ActionResult<List<SkillConfigDTO>>> GetAllSkills()
        {
            try
            {
                var skills = await _skillService.GetAllSkillsAsync();
                return Ok(skills);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有技能配置失败");
                return StatusCode(500, "获取技能配置失败");
            }
        }

        /// <summary>
        /// 根据技能ID获取技能配置
        /// </summary>
        /// <param name="skillId">技能ID</param>
        /// <returns>技能配置</returns>
        [HttpGet("configs/{skillId}")]
        public async Task<ActionResult<SkillConfigDTO>> GetSkillById(string skillId)
        {
            try
            {
                var skill = await _skillService.GetSkillByIdAsync(skillId);
                if (skill == null)
                {
                    return NotFound($"技能 {skillId} 不存在");
                }
                return Ok(skill);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取技能配置失败，SkillId: {SkillId}", skillId);
                return StatusCode(500, "获取技能配置失败");
            }
        }

        /// <summary>
        /// 根据五行系别获取技能列表
        /// </summary>
        /// <param name="element">五行系别</param>
        /// <returns>技能配置列表</returns>
        [HttpGet("configs/element/{element}")]
        public async Task<ActionResult<List<SkillConfigDTO>>> GetSkillsByElement(string element)
        {
            try
            {
                var skills = await _skillService.GetSkillsByElementAsync(element);
                return Ok(skills);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取五行技能失败，Element: {Element}", element);
                return StatusCode(500, "获取五行技能失败");
            }
        }

        /// <summary>
        /// 根据技能类型获取技能列表
        /// </summary>
        /// <param name="skillType">技能类型（ACTIVE/PASSIVE）</param>
        /// <returns>技能配置列表</returns>
        [HttpGet("configs/type/{skillType}")]
        public async Task<ActionResult<List<SkillConfigDTO>>> GetSkillsByType(string skillType)
        {
            try
            {
                var skills = await _skillService.GetSkillsByTypeAsync(skillType);
                return Ok(skills);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取技能类型失败，SkillType: {SkillType}", skillType);
                return StatusCode(500, "获取技能类型失败");
            }
        }

        #endregion

        #region 宠物技能管理

        /// <summary>
        /// 获取宠物的所有技能
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>宠物技能列表</returns>
        [HttpGet("pet/{petId}/skills")]
        public async Task<ActionResult<List<PetSkillDetailDTO>>> GetPetSkills(int petId)
        {
            try
            {
                var petSkills = await _skillService.GetPetSkillsAsync(petId);
                return Ok(petSkills);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物技能失败，PetId: {PetId}", petId);
                return StatusCode(500, "获取宠物技能失败");
            }
        }

        /// <summary>
        /// 获取宠物的主动技能
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>主动技能</returns>
        [HttpGet("pet/{petId}/active-skill")]
        public async Task<ActionResult<PetSkillDetailDTO>> GetPetActiveSkill(int petId)
        {
            try
            {
                var activeSkill = await _skillService.GetPetActiveSkillAsync(petId);
                if (activeSkill == null)
                {
                    return NotFound("宠物未掌握主动技能");
                }
                return Ok(activeSkill);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物主动技能失败，PetId: {PetId}", petId);
                return StatusCode(500, "获取宠物主动技能失败");
            }
        }

        /// <summary>
        /// 获取宠物的被动技能列表
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>被动技能列表</returns>
        [HttpGet("pet/{petId}/passive-skills")]
        public async Task<ActionResult<List<PetSkillDetailDTO>>> GetPetPassiveSkills(int petId)
        {
            try
            {
                var passiveSkills = await _skillService.GetPetPassiveSkillsAsync(petId);
                return Ok(passiveSkills);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物被动技能失败，PetId: {PetId}", petId);
                return StatusCode(500, "获取宠物被动技能失败");
            }
        }

        /// <summary>
        /// 宠物学习技能
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="request">学习技能请求</param>
        /// <returns>学习结果</returns>
        [HttpPost("pet/{petId}/learn")]
        public async Task<ActionResult<SkillLearnResult>> LearnSkill(int petId, [FromBody] LearnSkillRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.SkillId))
                {
                    return BadRequest("技能ID不能为空");
                }

                var result = await _skillService.LearnSkillAsync(petId, request.SkillId);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "宠物学习技能失败，PetId: {PetId}, SkillId: {SkillId}", petId, request.SkillId);
                return StatusCode(500, "学习技能失败");
            }
        }

        /// <summary>
        /// 宠物升级技能
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="request">升级技能请求</param>
        /// <returns>升级结果</returns>
        [HttpPost("pet/{petId}/upgrade")]
        public async Task<ActionResult<SkillUpgradeResult>> UpgradeSkill(int petId, [FromBody] UpgradeSkillRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.SkillId))
                {
                    return BadRequest("技能ID不能为空");
                }

                var result = await _skillService.UpgradeSkillAsync(petId, request.SkillId);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "宠物升级技能失败，PetId: {PetId}, SkillId: {SkillId}", petId, request.SkillId);
                return StatusCode(500, "升级技能失败");
            }
        }

        /// <summary>
        /// 宠物遗忘技能
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="request">遗忘技能请求</param>
        /// <returns>遗忘结果</returns>
        [HttpDelete("pet/{petId}/forget")]
        public async Task<ActionResult<bool>> ForgetSkill(int petId, [FromBody] ForgetSkillRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.SkillId))
                {
                    return BadRequest("技能ID不能为空");
                }

                var result = await _skillService.ForgetSkillAsync(petId, request.SkillId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "宠物遗忘技能失败，PetId: {PetId}, SkillId: {SkillId}", petId, request.SkillId);
                return StatusCode(500, "遗忘技能失败");
            }
        }

        #endregion

        #region 技能效果计算

        /// <summary>
        /// 计算主动技能的伤害倍数
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="skillId">技能ID</param>
        /// <returns>伤害倍数</returns>
        [HttpGet("pet/{petId}/skill/{skillId}/multiplier")]
        public async Task<ActionResult<double>> CalculateSkillMultiplier(int petId, string skillId)
        {
            try
            {
                var multiplier = await _skillService.CalculateSkillMultiplierAsync(petId, skillId);
                return Ok(multiplier);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算技能倍数失败，PetId: {PetId}, SkillId: {SkillId}", petId, skillId);
                return StatusCode(500, "计算技能倍数失败");
            }
        }

        /// <summary>
        /// 计算宠物所有被动技能的效果
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>被动效果字典</returns>
        [HttpGet("pet/{petId}/passive-effects")]
        public async Task<ActionResult<Dictionary<string, double>>> CalculatePassiveEffects(int petId)
        {
            try
            {
                var effects = await _skillService.CalculatePassiveEffectsAsync(petId);
                return Ok(effects);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算被动技能效果失败，PetId: {PetId}", petId);
                return StatusCode(500, "计算被动技能效果失败");
            }
        }

        /// <summary>
        /// 计算技能的魔法消耗
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="skillId">技能ID</param>
        /// <returns>魔法消耗</returns>
        [HttpGet("pet/{petId}/skill/{skillId}/mana-cost")]
        public async Task<ActionResult<int>> CalculateManaCost(int petId, string skillId)
        {
            try
            {
                var manaCost = await _skillService.CalculateManaCostAsync(petId, skillId);
                return Ok(manaCost);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算技能魔法消耗失败，PetId: {PetId}, SkillId: {SkillId}", petId, skillId);
                return StatusCode(500, "计算技能魔法消耗失败");
            }
        }

        /// <summary>
        /// 计算宠物技能的完整效果
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="activeSkillId">使用的主动技能ID</param>
        /// <param name="currentMana">当前魔法值</param>
        /// <returns>技能效果结果</returns>
        [HttpGet("pet/{petId}/skill-effects")]
        public async Task<ActionResult<SkillEffectResult>> CalculateSkillEffects(int petId, string? activeSkillId = null, int currentMana = 0)
        {
            try
            {
                var effects = await _skillService.CalculateSkillEffectsAsync(petId, activeSkillId, currentMana);
                return Ok(effects);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算技能效果失败，PetId: {PetId}, ActiveSkillId: {ActiveSkillId}", petId, activeSkillId);
                return StatusCode(500, "计算技能效果失败");
            }
        }

        #endregion

        #region 技能验证和统计

        /// <summary>
        /// 检查宠物是否可以学习指定技能
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="skillId">技能ID</param>
        /// <returns>是否可以学习</returns>
        [HttpGet("pet/{petId}/can-learn/{skillId}")]
        public async Task<ActionResult<bool>> CanLearnSkill(int petId, string skillId)
        {
            try
            {
                var canLearn = await _skillService.CanLearnSkillAsync(petId, skillId);
                return Ok(canLearn);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查技能学习条件失败，PetId: {PetId}, SkillId: {SkillId}", petId, skillId);
                return StatusCode(500, "检查技能学习条件失败");
            }
        }

        /// <summary>
        /// 检查宠物是否可以升级指定技能
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="skillId">技能ID</param>
        /// <returns>是否可以升级</returns>
        [HttpGet("pet/{petId}/can-upgrade/{skillId}")]
        public async Task<ActionResult<bool>> CanUpgradeSkill(int petId, string skillId)
        {
            try
            {
                var canUpgrade = await _skillService.CanUpgradeSkillAsync(petId, skillId);
                return Ok(canUpgrade);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查技能升级条件失败，PetId: {PetId}, SkillId: {SkillId}", petId, skillId);
                return StatusCode(500, "检查技能升级条件失败");
            }
        }

        /// <summary>
        /// 获取宠物技能统计信息
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>技能统计</returns>
        [HttpGet("pet/{petId}/statistics")]
        public async Task<ActionResult<PetSkillStatistics>> GetPetSkillStatistics(int petId)
        {
            try
            {
                var statistics = await _skillService.GetPetSkillStatisticsAsync(petId);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物技能统计失败，PetId: {PetId}", petId);
                return StatusCode(500, "获取宠物技能统计失败");
            }
        }

        #endregion
    }
}
