#!/usr/bin/env node

/**
 * 游戏数据格式转换工具
 * 用于在JSON格式和游戏页面期望的字符串格式之间转换
 */

const fs = require('fs');
const path = require('path');

class GameDataConverter {
    /**
     * 将JSON格式的道具数据转换为游戏页面期望的字符串格式
     * @param {Array} items - JSON格式的道具数组
     * @returns {string} - "名称,图标,ID,,数量|..." 格式的字符串
     */
    static itemsToGameString(items) {
        if (!Array.isArray(items)) {
            throw new Error('输入必须是数组格式');
        }

        return items.map(item => {
            const name = item.itemName || item.name || '未知道具';
            const icon = item.itemIcon || item.icon || 'default';
            const id = item.itemId || item.id || '0';
            const count = item.itemCount || item.count || 0;
            
            return `${name},${icon},${id},,${count}`;
        }).join('|');
    }

    /**
     * 将游戏字符串格式转换为JSON数组
     * @param {string} gameString - "名称,图标,ID,,数量|..." 格式的字符串
     * @returns {Array} - JSON格式的道具数组
     */
    static gameStringToItems(gameString) {
        if (typeof gameString !== 'string') {
            throw new Error('输入必须是字符串格式');
        }

        if (!gameString.trim()) {
            return [];
        }

        return gameString.split('|').map((itemStr, index) => {
            const parts = itemStr.split(',');
            if (parts.length < 5) {
                console.warn(`第${index + 1}个道具格式不正确: ${itemStr}`);
                return null;
            }

            return {
                itemName: parts[0] || '未知道具',
                itemIcon: parts[1] || 'default',
                itemId: parts[2] || '0',
                itemCount: parseInt(parts[4]) || 0
            };
        }).filter(item => item !== null);
    }

    /**
     * 将玩家信息JSON转换为游戏页面格式
     * @param {Object} playerData - 后端返回的玩家数据
     * @returns {Object} - 游戏页面期望的格式
     */
    static playerInfoToGameFormat(playerData) {
        return {
            '账号': playerData.account || playerData.username || '',
            '昵称': playerData.nickname || playerData.name || '',
            '金币': playerData.gold || playerData.money || 0,
            '元宝': playerData.diamond || playerData.gem || 0,
            '等级': playerData.level || 1,
            '经验': playerData.experience || playerData.exp || 0,
            '体力': playerData.stamina || playerData.energy || 0,
            '声望': playerData.reputation || playerData.fame || 0
        };
    }

    /**
     * 验证数据格式是否正确
     * @param {string} format - 数据格式类型 ('items', 'player', 'tasks')
     * @param {any} data - 要验证的数据
     * @returns {Object} - 验证结果
     */
    static validateFormat(format, data) {
        const result = {
            valid: false,
            errors: [],
            warnings: []
        };

        try {
            switch (format) {
                case 'items':
                    if (typeof data === 'string') {
                        // 验证游戏字符串格式
                        const items = this.gameStringToItems(data);
                        result.valid = items.length > 0;
                        if (items.length === 0) {
                            result.errors.push('无法解析任何有效道具');
                        }
                    } else if (Array.isArray(data)) {
                        // 验证JSON数组格式
                        result.valid = data.every(item => 
                            item.hasOwnProperty('itemName') || item.hasOwnProperty('name')
                        );
                        if (!result.valid) {
                            result.errors.push('数组中存在缺少名称字段的道具');
                        }
                    } else {
                        result.errors.push('道具数据必须是字符串或数组格式');
                    }
                    break;

                case 'player':
                    if (typeof data === 'object' && data !== null) {
                        const requiredFields = ['account', 'nickname', 'level'];
                        const missingFields = requiredFields.filter(field => 
                            !data.hasOwnProperty(field) && 
                            !data.hasOwnProperty(field.replace('account', 'username').replace('nickname', 'name'))
                        );
                        
                        result.valid = missingFields.length === 0;
                        if (missingFields.length > 0) {
                            result.errors.push(`缺少必需字段: ${missingFields.join(', ')}`);
                        }
                    } else {
                        result.errors.push('玩家数据必须是对象格式');
                    }
                    break;

                default:
                    result.errors.push(`不支持的格式类型: ${format}`);
            }
        } catch (error) {
            result.errors.push(`验证过程中发生错误: ${error.message}`);
        }

        return result;
    }

    /**
     * 从文件读取数据并转换格式
     * @param {string} inputFile - 输入文件路径
     * @param {string} outputFile - 输出文件路径
     * @param {string} fromFormat - 源格式 ('json', 'gamestring')
     * @param {string} toFormat - 目标格式 ('json', 'gamestring')
     */
    static convertFile(inputFile, outputFile, fromFormat, toFormat) {
        try {
            const inputData = fs.readFileSync(inputFile, 'utf8');
            let result;

            if (fromFormat === 'json' && toFormat === 'gamestring') {
                const jsonData = JSON.parse(inputData);
                result = this.itemsToGameString(jsonData);
            } else if (fromFormat === 'gamestring' && toFormat === 'json') {
                const items = this.gameStringToItems(inputData.trim());
                result = JSON.stringify(items, null, 2);
            } else {
                throw new Error(`不支持的转换: ${fromFormat} → ${toFormat}`);
            }

            fs.writeFileSync(outputFile, result, 'utf8');
            console.log(`✅ 转换完成: ${inputFile} → ${outputFile}`);
        } catch (error) {
            console.error(`❌ 转换失败: ${error.message}`);
        }
    }
}

// 命令行接口
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log(`
🎮 游戏数据格式转换工具

用法:
  node 游戏数据格式转换工具.js <命令> [参数...]

命令:
  convert <输入文件> <输出文件> <源格式> <目标格式>
    - 转换文件格式
    - 格式: json, gamestring
  
  validate <格式类型> <数据文件>
    - 验证数据格式
    - 格式类型: items, player, tasks

  test
    - 运行测试用例

示例:
  node 游戏数据格式转换工具.js convert items.json items.txt json gamestring
  node 游戏数据格式转换工具.js validate items test_data.json
  node 游戏数据格式转换工具.js test
        `);
        process.exit(0);
    }

    const command = args[0];

    switch (command) {
        case 'convert':
            if (args.length !== 5) {
                console.error('❌ convert命令需要4个参数');
                process.exit(1);
            }
            GameDataConverter.convertFile(args[1], args[2], args[3], args[4]);
            break;

        case 'validate':
            if (args.length !== 3) {
                console.error('❌ validate命令需要2个参数');
                process.exit(1);
            }
            try {
                const data = fs.readFileSync(args[2], 'utf8');
                const parsedData = args[1] === 'items' && data.includes('{') ? JSON.parse(data) : data;
                const result = GameDataConverter.validateFormat(args[1], parsedData);
                
                if (result.valid) {
                    console.log('✅ 数据格式验证通过');
                } else {
                    console.log('❌ 数据格式验证失败:');
                    result.errors.forEach(error => console.log(`  - ${error}`));
                }
            } catch (error) {
                console.error(`❌ 验证失败: ${error.message}`);
            }
            break;

        case 'test':
            // 运行测试用例
            console.log('🧪 运行测试用例...');
            
            // 测试道具转换
            const testItems = [
                { itemName: '生命药水', itemIcon: 'potion_hp', itemId: '1001', itemCount: 10 },
                { itemName: '魔法药水', itemIcon: 'potion_mp', itemId: '1002', itemCount: 5 }
            ];
            
            const gameString = GameDataConverter.itemsToGameString(testItems);
            console.log('JSON → 游戏字符串:', gameString);
            
            const backToItems = GameDataConverter.gameStringToItems(gameString);
            console.log('游戏字符串 → JSON:', JSON.stringify(backToItems, null, 2));
            
            console.log('✅ 测试完成');
            break;

        default:
            console.error(`❌ 未知命令: ${command}`);
            process.exit(1);
    }
}

module.exports = GameDataConverter;
