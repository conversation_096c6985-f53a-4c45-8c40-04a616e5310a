using System.Collections.Generic;

namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 战斗宠物信息返回结果DTO - 专为Battle.html设计
    /// </summary>
    public class BattlePetInfoResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 返回消息
        /// </summary>
        public string Message { get; set; } = "";

        /// <summary>
        /// 战斗宠物详细信息 - Battle.html期望的格式
        /// </summary>
        public BattlePetInfoDTO? PetInfo { get; set; }

        /// <summary>
        /// 宠物列表 - 兼容原有格式
        /// </summary>
        public List<PetInfoDTO> Pets { get; set; } = new List<PetInfoDTO>();
    }

    /// <summary>
    /// 战斗宠物信息DTO - 匹配Battle.html的readPet函数期望格式
    /// </summary>
    public class BattlePetInfoDTO
    {

        public int 宠物id { get; set; }

        /// <summary>
        /// 宠物形象编号
        /// </summary>
        public int 形象 { get; set; }

        /// <summary>
        /// 指定形象编号（可为null）
        /// </summary>
        public int? 指定形象 { get; set; }

        /// <summary>
        /// 当前生命值
        /// </summary>
        public long 生命 { get; set; }

        /// <summary>
        /// 最大生命值
        /// </summary>
        public long 最大生命 { get; set; }

        /// <summary>
        /// 当前魔法值
        /// </summary>
        public long 魔法 { get; set; }

        /// <summary>
        /// 最大魔法值
        /// </summary>
        public long 最大魔法 { get; set; }

        /// <summary>
        /// 宠物等级
        /// </summary>
        public int 等级 { get; set; }

        /// <summary>
        /// 宠物名字
        /// </summary>
        public string 宠物名字 { get; set; } = "";

        /// <summary>
        /// 五行属性
        /// </summary>
        public string 五行 { get; set; } = "";

        /// <summary>
        /// 当前经验值
        /// </summary>
        public long 当前经验 { get; set; }

        /// <summary>
        /// 升级所需经验值
        /// </summary>
        public long 升级经验 { get; set; }

        /// <summary>
        /// 技能显示字符串
        /// 格式：技能名|类型|技能ID|消耗|是否可用,技能名2|类型2|技能ID2|消耗2|是否可用2
        /// </summary>
        public string 技能显示 { get; set; } = "";

        /// <summary>
        /// 攻击力
        /// </summary>
        public long 攻击 { get; set; }

        /// <summary>
        /// 防御力
        /// </summary>
        public long 防御 { get; set; }

        /// <summary>
        /// 速度
        /// </summary>
        public long 速度 { get; set; }

        /// <summary>
        /// 闪避
        /// </summary>
        public long 闪避 { get; set; }

        /// <summary>
        /// 成长值
        /// </summary>
        public decimal 成长 { get; set; }

        /// <summary>
        /// 境界
        /// </summary>
        public string 境界 { get; set; } = "";

        /// <summary>
        /// 命中
        /// </summary>
        public long 命中 { get; set; }

        /// <summary>
        /// 加深伤害百分比
        /// </summary>
        public double 加深伤害 { get; set; }

        /// <summary>
        /// 抵消伤害百分比
        /// </summary>
        public double 抵消伤害 { get; set; }

        /// <summary>
        /// 吸血比例
        /// </summary>
        public double 吸血比例 { get; set; }

        /// <summary>
        /// 吸魔比例
        /// </summary>
        public double 吸魔比例 { get; set; }
    }
}
