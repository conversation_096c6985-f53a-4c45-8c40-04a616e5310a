using Microsoft.Extensions.Caching.Memory;
using SqlSugar;
using WebApplication_HM.DTOs;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;
using Newtonsoft.Json;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 技能服务实现
    /// 基于原项目WindowsFormsApplication7的技能系统逻辑
    /// </summary>
    public class SkillService : ISkillService
    {
        private readonly ISqlSugarClient _db;
        private readonly IMemoryCache _cache;
        private readonly ILogger<SkillService> _logger;

        // 缓存键常量
        private const string SKILL_CONFIG_CACHE_KEY = "skill_configs_all";
        private const string PET_SKILLS_CACHE_KEY = "pet_skills_{0}";

        // 缓存过期时间
        private static readonly TimeSpan SKILL_CONFIG_CACHE_EXPIRY = TimeSpan.FromHours(1);
        private static readonly TimeSpan PET_SKILLS_CACHE_EXPIRY = TimeSpan.FromMinutes(10);

        // 技能限制常量（基于原项目）
        private const int MAX_ACTIVE_SKILLS = 1;    // 最多1个主动技能
        private const int MAX_PASSIVE_SKILLS = 15;  // 最多15个被动技能
        private const int MAX_SKILL_LEVEL = 18;     // 最高技能等级18

        public SkillService(ISqlSugarClient db, IMemoryCache cache, ILogger<SkillService> logger)
        {
            _db = db;
            _cache = cache;
            _logger = logger;
        }

        #region 技能配置管理

        public async Task<List<SkillConfigDTO>> GetAllSkillsAsync()
        {
            try
            {
                // 尝试从缓存获取
                if (_cache.TryGetValue(SKILL_CONFIG_CACHE_KEY, out List<SkillConfigDTO>? cachedSkills) && cachedSkills != null)
                {
                    return cachedSkills;
                }

                // 从数据库查询
                var skills = await _db.Queryable<skill>().ToListAsync();
                var skillDTOs = skills.Select(ConvertToSkillConfigDTO).ToList();

                // 缓存结果
                _cache.Set(SKILL_CONFIG_CACHE_KEY, skillDTOs, SKILL_CONFIG_CACHE_EXPIRY);

                return skillDTOs;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有技能配置失败");
                return new List<SkillConfigDTO>();
            }
        }

        public async Task<SkillConfigDTO?> GetSkillByIdAsync(string skillId)
        {
            try
            {
                var allSkills = await GetAllSkillsAsync();
                return allSkills.FirstOrDefault(s => s.SkillId == skillId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取技能配置失败，SkillId: {SkillId}", skillId);
                return null;
            }
        }

        public async Task<List<SkillConfigDTO>> GetSkillsByElementAsync(string element)
        {
            try
            {
                var allSkills = await GetAllSkillsAsync();
                return allSkills.Where(s => string.IsNullOrEmpty(s.ElementLimit) || s.ElementLimit.Contains(element)).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取五行技能失败，Element: {Element}", element);
                return new List<SkillConfigDTO>();
            }
        }

        public async Task<List<SkillConfigDTO>> GetSkillsByTypeAsync(string skillType)
        {
            try
            {
                var allSkills = await GetAllSkillsAsync();
                return allSkills.Where(s => s.SkillType == skillType).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取技能类型失败，SkillType: {SkillType}", skillType);
                return new List<SkillConfigDTO>();
            }
        }

        #endregion

        #region 宠物技能管理

        public async Task<List<PetSkillDetailDTO>> GetPetSkillsAsync(int petId)
        {
            try
            {
                var cacheKey = string.Format(PET_SKILLS_CACHE_KEY, petId);

                // 尝试从缓存获取
                if (_cache.TryGetValue(cacheKey, out List<PetSkillDetailDTO>? cachedPetSkills) && cachedPetSkills != null)
                {
                    return cachedPetSkills;
                }

                // 从数据库查询
                var petSkills = await _db.Queryable<user_pet_skill>()
                    .Where(ups => ups.user_pet_id == petId)
                    .ToListAsync();

                var petSkillDTOs = new List<PetSkillDetailDTO>();
                foreach (var petSkill in petSkills)
                {
                    var skillConfig = await GetSkillByIdAsync(petSkill.skill_id.ToString());
                    if (skillConfig != null)
                    {
                        petSkillDTOs.Add(ConvertToPetSkillDetailDTO(petSkill, skillConfig));
                    }
                }

                // 缓存结果
                _cache.Set(cacheKey, petSkillDTOs, PET_SKILLS_CACHE_EXPIRY);

                return petSkillDTOs;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物技能失败，PetId: {PetId}", petId);
                return new List<PetSkillDetailDTO>();
            }
        }

        public async Task<PetSkillDetailDTO?> GetPetActiveSkillAsync(int petId)
        {
            try
            {
                var petSkills = await GetPetSkillsAsync(petId);
                return petSkills.FirstOrDefault(ps => ps.SkillConfig?.SkillType == "ACTIVE");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物主动技能失败，PetId: {PetId}", petId);
                return null;
            }
        }

        public async Task<List<PetSkillDetailDTO>> GetPetPassiveSkillsAsync(int petId)
        {
            try
            {
                var petSkills = await GetPetSkillsAsync(petId);
                return petSkills.Where(ps => ps.SkillConfig?.SkillType == "PASSIVE").ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物被动技能失败，PetId: {PetId}", petId);
                return new List<PetSkillDetailDTO>();
            }
        }

        public async Task<SkillLearnResult> LearnSkillAsync(int petId, string skillId)
        {
            try
            {
                // 验证技能学习条件
                var validationResult = await ValidateSkillLearnAsync(petId, skillId);
                if (validationResult != "成功")
                {
                    return SkillLearnResult.CreateFailure(validationResult);
                }

                // 开始事务
                _db.Ado.BeginTran();

                try
                {
                    // 创建新的宠物技能记录
                    var newPetSkill = new user_pet_skill
                    {
                        user_pet_id = petId,
                        skill_id = int.Parse(skillId),
                        skill_level = 0,
                        create_time = DateTime.Now
                    };

                    var insertResult = await _db.Insertable(newPetSkill).ExecuteReturnEntityAsync();
                    
                    _db.Ado.CommitTran();

                    // 清除缓存
                    ClearPetSkillsCache(petId);

                    // 获取技能配置
                    var skillConfig = await GetSkillByIdAsync(skillId);
                    var learnedSkill = ConvertToPetSkillDetailDTO(insertResult, skillConfig!);

                    _logger.LogInformation("宠物学习技能成功，PetId: {PetId}, SkillId: {SkillId}", petId, skillId);
                    return SkillLearnResult.CreateSuccess(learnedSkill);
                }
                catch (Exception)
                {
                    _db.Ado.RollbackTran();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "宠物学习技能失败，PetId: {PetId}, SkillId: {SkillId}", petId, skillId);
                return SkillLearnResult.CreateFailure("学习技能失败");
            }
        }

        public async Task<SkillUpgradeResult> UpgradeSkillAsync(int petId, string skillId)
        {
            try
            {
                // 验证技能升级条件
                var validationResult = await ValidateSkillUpgradeAsync(petId, skillId);
                if (validationResult != "成功")
                {
                    return SkillUpgradeResult.CreateFailure(validationResult);
                }

                // 开始事务
                _db.Ado.BeginTran();

                try
                {
                    // 更新技能等级
                    var updateResult = await _db.Updateable<user_pet_skill>()
                        .SetColumns(ups => ups.skill_level == ups.skill_level + 1)
                        .Where(ups => ups.user_pet_id == petId && ups.skill_id == int.Parse(skillId))
                        .ExecuteCommandAsync();

                    if (updateResult <= 0)
                    {
                        _db.Ado.RollbackTran();
                        return SkillUpgradeResult.CreateFailure("技能升级失败");
                    }

                    _db.Ado.CommitTran();

                    // 清除缓存
                    ClearPetSkillsCache(petId);

                    // 获取升级后的技能信息
                    var updatedPetSkills = await GetPetSkillsAsync(petId);
                    var upgradedSkill = updatedPetSkills.FirstOrDefault(ps => ps.SkillId == skillId);

                    if (upgradedSkill != null)
                    {
                        _logger.LogInformation("宠物技能升级成功，PetId: {PetId}, SkillId: {SkillId}, NewLevel: {NewLevel}", 
                            petId, skillId, upgradedSkill.SkillLevel);
                        return SkillUpgradeResult.CreateSuccess(upgradedSkill);
                    }

                    return SkillUpgradeResult.CreateFailure("获取升级后技能信息失败");
                }
                catch (Exception)
                {
                    _db.Ado.RollbackTran();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "宠物技能升级失败，PetId: {PetId}, SkillId: {SkillId}", petId, skillId);
                return SkillUpgradeResult.CreateFailure("技能升级失败");
            }
        }

        public async Task<bool> ForgetSkillAsync(int petId, string skillId)
        {
            try
            {
                // 开始事务
                _db.Ado.BeginTran();

                try
                {
                    // 删除技能记录
                    var deleteResult = await _db.Deleteable<user_pet_skill>()
                        .Where(ups => ups.user_pet_id == petId && ups.skill_id == int.Parse(skillId))
                        .ExecuteCommandAsync();

                    if (deleteResult <= 0)
                    {
                        _db.Ado.RollbackTran();
                        return false;
                    }

                    // 记录遗忘日志
                    var forgetLog = new skill_forget_log
                    {
                        user_id = await GetUserIdByPetIdAsync(petId),
                        pet_id = petId,
                        skill_id = skillId,
                        forget_time = DateTime.Now
                    };

                    await _db.Insertable(forgetLog).ExecuteCommandAsync();

                    _db.Ado.CommitTran();

                    // 清除缓存
                    ClearPetSkillsCache(petId);

                    _logger.LogInformation("宠物遗忘技能成功，PetId: {PetId}, SkillId: {SkillId}", petId, skillId);
                    return true;
                }
                catch (Exception)
                {
                    _db.Ado.RollbackTran();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "宠物遗忘技能失败，PetId: {PetId}, SkillId: {SkillId}", petId, skillId);
                return false;
            }
        }

        #endregion

        #region 技能效果计算

        public async Task<double> CalculateSkillMultiplierAsync(int petId, string skillId)
        {
            try
            {
                var petSkills = await GetPetSkillsAsync(petId);
                var targetSkill = petSkills.FirstOrDefault(ps => ps.SkillId == skillId);

                if (targetSkill?.SkillConfig == null)
                {
                    return 1.0; // 默认倍数
                }

                // 基于原项目Fight.cs第577行的计算逻辑
                // 技能加成 = 1 + 技能百分比 + 技能等级 * 0.02
                return 1.0 + (double)targetSkill.SkillConfig.SkillPercent + (targetSkill.SkillLevel * 0.02);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算技能倍数失败，PetId: {PetId}, SkillId: {SkillId}", petId, skillId);
                return 1.0;
            }
        }

        public async Task<Dictionary<string, double>> CalculatePassiveEffectsAsync(int petId)
        {
            try
            {
                var passiveSkills = await GetPetPassiveSkillsAsync(petId);
                var effects = new Dictionary<string, double>();

                foreach (var skill in passiveSkills)
                {
                    if (skill.SkillConfig == null || !skill.SkillConfig.IsBuff) continue;

                    var effectType = skill.SkillConfig.EffectType;
                    if (string.IsNullOrEmpty(effectType) || effectType == "null") continue;

                    // 基于原项目PetInfo.cs第186行的计算逻辑
                    // 被动效果 = 基础效果 + 技能等级 * 0.005
                    var effectValue = skill.CalculateEffectValue();

                    if (effects.ContainsKey(effectType))
                    {
                        effects[effectType] += effectValue;
                    }
                    else
                    {
                        effects[effectType] = effectValue;
                    }
                }

                return effects;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算被动技能效果失败，PetId: {PetId}", petId);
                return new Dictionary<string, double>();
            }
        }

        public async Task<int> CalculateManaCostAsync(int petId, string skillId)
        {
            try
            {
                var petSkills = await GetPetSkillsAsync(petId);
                var targetSkill = petSkills.FirstOrDefault(ps => ps.SkillId == skillId);

                if (targetSkill?.SkillConfig == null)
                {
                    return 0;
                }

                // 基于原项目Fight.cs第569行的计算逻辑
                // 耗蓝量 = (技能等级 + 1) * 基础耗蓝量
                return targetSkill.CalculateManaCost();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算技能魔法消耗失败，PetId: {PetId}, SkillId: {SkillId}", petId, skillId);
                return 0;
            }
        }

        public async Task<SkillEffectResult> CalculateSkillEffectsAsync(int petId, string? activeSkillId = null, int currentMana = 0)
        {
            try
            {
                var result = new SkillEffectResult();

                // 计算主动技能倍数
                if (!string.IsNullOrEmpty(activeSkillId))
                {
                    result.ActiveSkillMultiplier = await CalculateSkillMultiplierAsync(petId, activeSkillId);
                    result.ManaCost = await CalculateManaCostAsync(petId, activeSkillId);
                    result.HasSufficientMana = currentMana >= result.ManaCost;
                }

                // 计算被动技能效果
                result.PassiveEffects = await CalculatePassiveEffectsAsync(petId);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算技能效果失败，PetId: {PetId}, ActiveSkillId: {ActiveSkillId}", petId, activeSkillId);
                return new SkillEffectResult();
            }
        }

        #endregion

        #region 技能验证

        public async Task<bool> CanLearnSkillAsync(int petId, string skillId)
        {
            var validationResult = await ValidateSkillLearnAsync(petId, skillId);
            return validationResult == "成功";
        }

        public async Task<bool> CanUpgradeSkillAsync(int petId, string skillId)
        {
            var validationResult = await ValidateSkillUpgradeAsync(petId, skillId);
            return validationResult == "成功";
        }

        public async Task<string> ValidateSkillLearnAsync(int petId, string skillId)
        {
            try
            {
                // 1. 验证宠物存在
                var pet = await _db.Queryable<user_pet>().Where(up => up.id == petId).FirstAsync();
                if (pet == null)
                {
                    return "宠物不存在";
                }

                // 2. 验证技能存在
                var skillConfig = await GetSkillByIdAsync(skillId);
                if (skillConfig == null)
                {
                    return "游戏内部错误！";
                }

                // 3. 检查是否已学习
                var petSkills = await GetPetSkillsAsync(petId);
                if (petSkills.Any(ps => ps.SkillId == skillId))
                {
                    return "宠物已经掌握此技能了！";
                }

                // 4. 验证五行限制（基于原项目DataProcess.cs第9374-9380行）
                if (!string.IsNullOrEmpty(skillConfig.ElementLimit) && skillConfig.ElementLimit != "null")
                {
                    var petElement = GetPetAttribute(pet.pet_no);
                    if (petElement != skillConfig.ElementLimit)
                    {
                        return $"该技能只能{skillConfig.ElementLimit}系学习。";
                    }
                }

                // 巫系特殊限制
                if (GetPetAttribute(pet.pet_no) == "巫" && skillConfig.ElementLimit != "巫")
                {
                    return "巫系宠物只能学习巫系专属技能！";
                }

                // 5. 检查技能数量限制（基于原项目DataProcess.cs第9384-9400行）
                var activeSkillCount = petSkills.Count(ps => ps.SkillConfig?.SkillType == "ACTIVE");
                var passiveSkillCount = petSkills.Count(ps => ps.SkillConfig?.SkillType == "PASSIVE");

                if (skillConfig.SkillType == "ACTIVE")
                {
                    if (activeSkillCount >= MAX_ACTIVE_SKILLS)
                    {
                        return "宠物只能学习一个主动技能！";
                    }
                }
                else if (skillConfig.SkillType == "PASSIVE")
                {
                    if (passiveSkillCount >= MAX_PASSIVE_SKILLS)
                    {
                        return "宠物技能已满，学习失败";
                    }
                }

                return "成功";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证技能学习失败，PetId: {PetId}, SkillId: {SkillId}", petId, skillId);
                return "验证失败";
            }
        }

        public async Task<string> ValidateSkillUpgradeAsync(int petId, string skillId)
        {
            try
            {
                // 1. 验证宠物技能存在
                var petSkills = await GetPetSkillsAsync(petId);
                var targetSkill = petSkills.FirstOrDefault(ps => ps.SkillId == skillId);

                if (targetSkill == null)
                {
                    return "宠物未掌握此技能";
                }

                // 2. 检查技能等级上限（基于原项目DataProcess.cs第9424-9426行）
                if (targetSkill.SkillLevel >= MAX_SKILL_LEVEL)
                {
                    return "技能已达到最高等级";
                }

                return "成功";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证技能升级失败，PetId: {PetId}, SkillId: {SkillId}", petId, skillId);
                return "验证失败";
            }
        }

        #endregion

        #region 技能统计

        public async Task<PetSkillStatistics> GetPetSkillStatisticsAsync(int petId)
        {
            try
            {
                var petSkills = await GetPetSkillsAsync(petId);

                var statistics = new PetSkillStatistics
                {
                    ActiveSkillCount = petSkills.Count(ps => ps.SkillConfig?.SkillType == "ACTIVE"),
                    PassiveSkillCount = petSkills.Count(ps => ps.SkillConfig?.SkillType == "PASSIVE")
                };

                if (petSkills.Any())
                {
                    statistics.AverageSkillLevel = petSkills.Average(ps => ps.SkillLevel);
                    statistics.MaxSkillLevel = petSkills.Max(ps => ps.SkillLevel);

                    // 技能等级分布
                    statistics.SkillLevelDistribution = petSkills
                        .GroupBy(ps => ps.SkillLevel)
                        .ToDictionary(g => g.Key, g => g.Count());
                }

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物技能统计失败，PetId: {PetId}", petId);
                return new PetSkillStatistics();
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 转换skill实体为SkillConfigDTO
        /// </summary>
        private static SkillConfigDTO ConvertToSkillConfigDTO(skill skillEntity)
        {
            return new SkillConfigDTO
            {
                SkillId = skillEntity.skill_id,
                SkillName = skillEntity.skill_name,
                SkillPercent = skillEntity.skill_percent,
                EffectType = skillEntity.effect_type?.ToString() ?? "",
                EffectValue = skillEntity.effect_value?.ToString() ?? "",
                ManaCost = skillEntity.mana_cost,
                BuffInfo = skillEntity.buff_info?.ToString() ?? "",
                ElementLimit = skillEntity.element_limit?.ToString() ?? "",
                SkillType = skillEntity.skill_type ?? ""
            };
        }

        /// <summary>
        /// 转换user_pet_skill实体为PetSkillDetailDTO
        /// </summary>
        private static PetSkillDetailDTO ConvertToPetSkillDetailDTO(user_pet_skill petSkillEntity, SkillConfigDTO skillConfig)
        {
            return new PetSkillDetailDTO
            {
                Id = petSkillEntity.id,
                UserPetId = petSkillEntity.user_pet_id,
                SkillId = petSkillEntity.skill_id.ToString(),
                SkillLevel = petSkillEntity.skill_level ?? 0,
                SkillConfig = skillConfig,
                CreateTime = petSkillEntity.create_time ?? DateTime.Now
            };
        }

        /// <summary>
        /// 清除宠物技能缓存
        /// </summary>
        private void ClearPetSkillsCache(int petId)
        {
            var cacheKey = string.Format(PET_SKILLS_CACHE_KEY, petId);
            _cache.Remove(cacheKey);
        }

        /// <summary>
        /// 根据宠物ID获取用户ID
        /// </summary>
        private async Task<int> GetUserIdByPetIdAsync(int petId)
        {
            try
            {
                var pet = await _db.Queryable<user_pet>()
                    .Where(up => up.id == petId)
                    .FirstAsync();
                return pet?.user_id ?? 0;
            }
            catch
            {
                return 0;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取宠物的五行属性（通过pet_no关联pet_config表）
        /// </summary>
        /// <param name="petNo">宠物编号</param>
        /// <returns>五行属性</returns>
        private string GetPetAttribute(int petNo)
        {
            try
            {
                var petConfig = _db.Queryable<pet_config>().Where(x => x.pet_no == petNo).First();
                return petConfig?.attribute ?? "无";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物属性失败，宠物编号: {PetNo}", petNo);
                return "无";
            }
        }

        #endregion
    }
}
