-- 初始化地图数据脚本

-- 创建地图配置表（如果不存在）
CREATE TABLE IF NOT EXISTS `map_config` (
  `map_id` int NOT NULL,
  `map_name` varchar(100) NOT NULL,
  `map_desc` text,
  `background` varchar(200),
  `ico` varchar(200),
  `type` int DEFAULT 1,
  PRIMARY KEY (`map_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建地图详情表（如果不存在）
CREATE TABLE IF NOT EXISTS `map_detail` (
  `map_id` int NOT NULL,
  `limit_level` int DEFAULT 1,
  `unlock_condition` varchar(200),
  PRIMARY KEY (`map_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建地图怪物表（如果不存在）
CREATE TABLE IF NOT EXISTS `map_monsters` (
  `id` int NOT NULL AUTO_INCREMENT,
  `map_id` int NOT NULL,
  `monster_name` varchar(100) NOT NULL,
  `level_range` varchar(50),
  `element` varchar(50),
  PRIMARY KEY (`id`),
  KEY `idx_map_id` (`map_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建地图掉落表（如果不存在）
CREATE TABLE IF NOT EXISTS `map_drops` (
  `id` int NOT NULL AUTO_INCREMENT,
  `map_id` int NOT NULL,
  `item_name` varchar(100) NOT NULL,
  `drop_rate` decimal(5,4) DEFAULT 0.1000,
  PRIMARY KEY (`id`),
  KEY `idx_map_id` (`map_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入测试地图配置数据
INSERT INTO `map_config` (`map_id`, `map_name`, `map_desc`, `background`, `ico`, `type`) VALUES
(1, '新手训练营', '适合新手练级的安全区域', 'map1.jpg', '1.png', 1),
(2, '彩虹森林', '美丽的森林，有各种可爱的小动物', 'map2.jpg', '2.png', 1),
(3, '火焰山谷', '炎热的山谷，火系宠物的聚集地', 'map3.jpg', '3.png', 1),
(4, '冰雪山脉', '寒冷的雪山，冰系宠物的家园', 'map4.jpg', '4.png', 1),
(5, '黄金海岸', '美丽的海滩，水系宠物的乐园', 'map5.jpg', '5.png', 1),
(6, '神秘沙漠', '神秘的沙漠，土系宠物的领域', 'map6.jpg', '6.png', 1),
(7, '幽灵山谷', '阴森的山谷，暗系宠物出没', 'map7.jpg', '7.png', 1),
(8, '暗黑沼泽', '危险的沼泽地带，毒系宠物聚集', 'map8.jpg', '8.png', 1),
(9, '龙族圣殿', '传说中的龙族圣地', 'map9.jpg', '9.png', 1),
(10, '天空之城', '漂浮在空中的神秘城市', 'map10.jpg', '10.png', 1),
(11, '远古遗迹', '古老文明的遗迹', 'map11.jpg', '16.png', 1)
ON DUPLICATE KEY UPDATE 
  `map_name` = VALUES(`map_name`),
  `map_desc` = VALUES(`map_desc`),
  `background` = VALUES(`background`),
  `ico` = VALUES(`ico`),
  `type` = VALUES(`type`);

-- 插入地图详情数据
INSERT INTO `map_detail` (`map_id`, `limit_level`, `unlock_condition`) VALUES
(1, 1, '无条件'),
(2, 5, '等级达到5级'),
(3, 10, '等级达到10级'),
(4, 15, '等级达到15级'),
(5, 20, '等级达到20级'),
(6, 25, '等级达到25级'),
(7, 30, '等级达到30级'),
(8, 35, '等级达到35级'),
(9, 40, '等级达到40级'),
(10, 45, '等级达到45级'),
(11, 50, '等级达到50级')
ON DUPLICATE KEY UPDATE 
  `limit_level` = VALUES(`limit_level`),
  `unlock_condition` = VALUES(`unlock_condition`);

-- 插入地图怪物数据
INSERT INTO `map_monsters` (`map_id`, `monster_name`, `level_range`, `element`) VALUES
(1, '小兔子', '1-3', '普通'),
(1, '小鸟', '2-4', '风'),
(2, '花精灵', '5-8', '草'),
(2, '蝴蝶', '6-9', '风'),
(3, '火蜥蜴', '10-13', '火'),
(3, '岩浆兽', '12-15', '火'),
(4, '雪狼', '15-18', '冰'),
(4, '冰熊', '17-20', '冰'),
(5, '海星', '20-23', '水'),
(5, '水母', '22-25', '水'),
(6, '沙虫', '25-28', '土'),
(6, '仙人掌', '27-30', '草'),
(7, '幽灵', '30-33', '暗'),
(7, '骷髅', '32-35', '暗'),
(8, '毒蛇', '35-38', '毒'),
(8, '沼泽怪', '37-40', '毒'),
(9, '小龙', '40-43', '龙'),
(9, '龙卫士', '42-45', '龙'),
(10, '天使', '45-48', '光'),
(10, '风神', '47-50', '风'),
(11, '古代守护者', '50-55', '神秘')
ON DUPLICATE KEY UPDATE 
  `monster_name` = VALUES(`monster_name`),
  `level_range` = VALUES(`level_range`),
  `element` = VALUES(`element`);

-- 插入地图掉落数据
INSERT INTO `map_drops` (`map_id`, `item_name`, `drop_rate`) VALUES
(1, '新手药水', 0.3000),
(1, '小经验丹', 0.2000),
(2, '草系精华', 0.2500),
(2, '风系精华', 0.2000),
(3, '火系精华', 0.2500),
(3, '火焰石', 0.1500),
(4, '冰系精华', 0.2500),
(4, '寒冰石', 0.1500),
(5, '水系精华', 0.2500),
(5, '海洋之心', 0.1000),
(6, '土系精华', 0.2500),
(6, '大地之石', 0.1500),
(7, '暗系精华', 0.2500),
(7, '暗影宝石', 0.1000),
(8, '毒系精华', 0.2500),
(8, '毒液瓶', 0.1500),
(9, '龙鳞', 0.1500),
(9, '龙血', 0.0800),
(10, '光系精华', 0.2000),
(10, '天空之翼', 0.0500),
(11, '远古符文', 0.1000),
(11, '神秘宝箱', 0.0300)
ON DUPLICATE KEY UPDATE 
  `item_name` = VALUES(`item_name`),
  `drop_rate` = VALUES(`drop_rate`);

-- 创建用户地图进度表（如果不存在）
CREATE TABLE IF NOT EXISTS `user_map_progress` (
  `user_id` int NOT NULL,
  `map_id` int NOT NULL,
  `is_unlocked` tinyint(1) DEFAULT 0,
  `best_score` int DEFAULT 0,
  `completion_count` int DEFAULT 0,
  `last_completion_time` datetime DEFAULT NULL,
  PRIMARY KEY (`user_id`, `map_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_map_id` (`map_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 为测试用户(ID=1)解锁前几个地图
INSERT INTO `user_map_progress` (`user_id`, `map_id`, `is_unlocked`, `best_score`, `completion_count`) VALUES
(1, 1, 1, 100, 5),
(1, 2, 1, 150, 3),
(1, 3, 1, 200, 2),
(1, 4, 0, 0, 0),
(1, 5, 0, 0, 0),
(1, 6, 0, 0, 0),
(1, 7, 0, 0, 0),
(1, 8, 0, 0, 0),
(1, 9, 0, 0, 0),
(1, 10, 0, 0, 0),
(1, 11, 0, 0, 0)
ON DUPLICATE KEY UPDATE 
  `is_unlocked` = VALUES(`is_unlocked`),
  `best_score` = VALUES(`best_score`),
  `completion_count` = VALUES(`completion_count`);

SELECT '地图数据初始化完成！' as message;
