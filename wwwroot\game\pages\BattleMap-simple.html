<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>战斗地图 - 简化版</title>
    <script src="jquery-1.8.3.min.js"></script>
    <script src="/game/js/game-api-adapter.js"></script>
    
    <style type="text/css">
        body {
            margin: 15px;
            font-size: 12px;
            font-family: Arial, sans-serif;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            font-size: 16px;
        }
        
        .debug-info {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .map-list {
            margin: 20px 0;
        }
        
        .map-item {
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            background: #f9f9f9;
        }
    </style>
</head>

<body>
    <h1>战斗地图 - 简化版调试</h1>
    
    <div id="loading" class="loading">正在初始化...</div>
    <div id="debug" class="debug-info"></div>
    <div id="mapContainer" class="map-list" style="display: none;"></div>
    
    <script type="text/javascript">
        let debugInfo = '';
        
        function addDebug(message) {
            debugInfo += new Date().toLocaleTimeString() + ': ' + message + '\n';
            document.getElementById('debug').textContent = debugInfo;
            console.log(message);
        }
        
        function updateLoading(message) {
            document.getElementById('loading').textContent = message;
            addDebug('Loading: ' + message);
        }
        
        // 页面加载时初始化
        $(document).ready(function() {
            addDebug('页面DOM加载完成');
            
            // 检查jQuery
            if (typeof $ === 'undefined') {
                updateLoading('jQuery未加载');
                return;
            }
            addDebug('jQuery已加载');
            
            // 等待gameAPI加载
            let checkCount = 0;
            function waitForGameAPI() {
                checkCount++;
                addDebug('检查gameAPI第' + checkCount + '次');
                
                if (typeof gameAPI !== 'undefined') {
                    addDebug('gameAPI已加载');
                    initializePage();
                } else if (checkCount < 50) { // 最多等待5秒
                    setTimeout(waitForGameAPI, 100);
                } else {
                    updateLoading('gameAPI加载超时');
                    addDebug('gameAPI加载超时，停止等待');
                }
            }
            
            waitForGameAPI();
        });
        
        async function initializePage() {
            try {
                updateLoading('正在初始化...');
                
                // 设置用户ID
                if (gameAPI.setCurrentUserId) {
                    gameAPI.setCurrentUserId(1);
                    addDebug('已设置用户ID: 1');
                } else {
                    addDebug('setCurrentUserId方法不存在');
                }
                
                // 获取用户ID
                const userId = gameAPI.getCurrentUserId();
                addDebug('当前用户ID: ' + userId);
                
                // 测试API调用
                await testMapAPI(userId);
                
            } catch (error) {
                updateLoading('初始化失败');
                addDebug('初始化错误: ' + error.message);
            }
        }
        
        async function testMapAPI(userId) {
            try {
                updateLoading('正在调用地图API...');
                addDebug('开始调用getMapList(' + userId + ')');
                
                const result = await gameAPI.getMapList(userId);
                addDebug('API调用完成，结果类型: ' + typeof result);
                addDebug('API结果: ' + JSON.stringify(result, null, 2));
                
                if (result && result.success) {
                    addDebug('API调用成功');
                    
                    if (result.maps && Array.isArray(result.maps)) {
                        addDebug('地图数组长度: ' + result.maps.length);
                        displayMaps(result.maps);
                    } else {
                        addDebug('maps字段不存在或不是数组');
                        updateLoading('地图数据格式错误');
                    }
                } else {
                    const errorMsg = result ? result.message : '未知错误';
                    addDebug('API调用失败: ' + errorMsg);
                    updateLoading('API调用失败: ' + errorMsg);
                }
                
            } catch (error) {
                addDebug('API调用异常: ' + error.message);
                updateLoading('API调用异常: ' + error.message);
            }
        }
        
        function displayMaps(maps) {
            const container = document.getElementById('mapContainer');
            const loading = document.getElementById('loading');
            
            if (maps.length === 0) {
                updateLoading('没有找到地图数据');
                return;
            }
            
            // 隐藏loading，显示地图列表
            loading.style.display = 'none';
            container.style.display = 'block';
            
            let html = '<h2>找到 ' + maps.length + ' 个地图:</h2>';
            
            maps.forEach(function(map, index) {
                html += '<div class="map-item">';
                html += '<strong>地图 ' + (index + 1) + ':</strong><br>';
                html += 'ID: ' + (map.mapId || 'N/A') + '<br>';
                html += '名称: ' + (map.mapName || 'N/A') + '<br>';
                html += '类型: ' + (map.mapType || 'N/A') + '<br>';
                html += '描述: ' + (map.mapDesc || 'N/A') + '<br>';
                html += '解锁状态: ' + (map.isUnlocked ? '已解锁' : '未解锁') + '<br>';
                html += '</div>';
            });
            
            container.innerHTML = html;
            addDebug('地图列表显示完成');
        }
    </script>
</body>
</html>
