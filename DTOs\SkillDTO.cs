using System.ComponentModel.DataAnnotations;

namespace WebApplication_HM.DTOs
{
    /// <summary>
    /// 技能配置DTO
    /// </summary>
    public class SkillConfigDTO
    {
        /// <summary>
        /// 技能ID
        /// </summary>
        public string SkillId { get; set; } = string.Empty;

        /// <summary>
        /// 技能名称
        /// </summary>
        public string SkillName { get; set; } = string.Empty;

        /// <summary>
        /// 技能百分比（主动技能伤害倍数）
        /// </summary>
        public decimal SkillPercent { get; set; }

        /// <summary>
        /// 技能效果类型
        /// </summary>
        public string EffectType { get; set; } = string.Empty;

        /// <summary>
        /// 效果数值（JSON格式）
        /// </summary>
        public string EffectValue { get; set; } = string.Empty;

        /// <summary>
        /// 耗蓝量
        /// </summary>
        public int ManaCost { get; set; }

        /// <summary>
        /// BUFF信息
        /// </summary>
        public string BuffInfo { get; set; } = string.Empty;

        /// <summary>
        /// 五行限制
        /// </summary>
        public string ElementLimit { get; set; } = string.Empty;

        /// <summary>
        /// 技能类型（ACTIVE-主动技能，PASSIVE-被动技能）
        /// </summary>
        public string SkillType { get; set; } = string.Empty;

        /// <summary>
        /// 是否为BUFF技能
        /// </summary>
        public bool IsBuff => EffectType != "null" && !string.IsNullOrEmpty(EffectType);
    }

    /// <summary>
    /// 宠物技能详情DTO（技能系统专用）
    /// </summary>
    public class PetSkillDetailDTO
    {
        /// <summary>
        /// 宠物技能记录ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 用户宠物ID
        /// </summary>
        public int UserPetId { get; set; }

        /// <summary>
        /// 技能ID
        /// </summary>
        public string SkillId { get; set; } = string.Empty;

        /// <summary>
        /// 技能等级（0-18）
        /// </summary>
        public int SkillLevel { get; set; }

        /// <summary>
        /// 技能配置信息
        /// </summary>
        public SkillConfigDTO? SkillConfig { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 计算当前技能的实际效果值
        /// </summary>
        public double CalculateEffectValue()
        {
            if (SkillConfig == null) return 0;

            if (SkillConfig.IsBuff && !string.IsNullOrEmpty(SkillConfig.EffectValue))
            {
                // 被动技能：基础效果 + 技能等级 * 0.005
                if (double.TryParse(SkillConfig.EffectValue, out double baseEffect))
                {
                    return baseEffect + (SkillLevel * 0.005);
                }
            }
            else
            {
                // 主动技能：基础百分比 + 技能等级 * 0.02
                return (double)SkillConfig.SkillPercent + (SkillLevel * 0.02);
            }

            return 0;
        }

        /// <summary>
        /// 计算当前技能的魔法消耗
        /// </summary>
        public int CalculateManaCost()
        {
            if (SkillConfig == null) return 0;
            return (SkillLevel + 1) * SkillConfig.ManaCost;
        }
    }

    /// <summary>
    /// 技能学习请求DTO
    /// </summary>
    public class LearnSkillRequest
    {
        /// <summary>
        /// 技能ID
        /// </summary>
        [Required]
        public string SkillId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 技能升级请求DTO
    /// </summary>
    public class UpgradeSkillRequest
    {
        /// <summary>
        /// 技能ID
        /// </summary>
        [Required]
        public string SkillId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 技能遗忘请求DTO
    /// </summary>
    public class ForgetSkillRequest
    {
        /// <summary>
        /// 技能ID
        /// </summary>
        [Required]
        public string SkillId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 技能学习结果DTO
    /// </summary>
    public class SkillLearnResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 学习的技能信息
        /// </summary>
        public PetSkillDetailDTO? LearnedSkill { get; set; }

        public static SkillLearnResult CreateSuccess(PetSkillDetailDTO skill, string message = "技能学习成功")
        {
            return new SkillLearnResult
            {
                Success = true,
                Message = message,
                LearnedSkill = skill
            };
        }

        public static SkillLearnResult CreateFailure(string message)
        {
            return new SkillLearnResult
            {
                Success = false,
                Message = message
            };
        }
    }

    /// <summary>
    /// 技能升级结果DTO
    /// </summary>
    public class SkillUpgradeResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 升级后的技能等级
        /// </summary>
        public int NewLevel { get; set; }

        /// <summary>
        /// 升级后的技能信息
        /// </summary>
        public PetSkillDetailDTO? UpgradedSkill { get; set; }

        public static SkillUpgradeResult CreateSuccess(PetSkillDetailDTO skill, string message = "技能升级成功")
        {
            return new SkillUpgradeResult
            {
                Success = true,
                Message = message,
                NewLevel = skill.SkillLevel,
                UpgradedSkill = skill
            };
        }

        public static SkillUpgradeResult CreateFailure(string message)
        {
            return new SkillUpgradeResult
            {
                Success = false,
                Message = message
            };
        }
    }

    /// <summary>
    /// 技能效果计算结果DTO
    /// </summary>
    public class SkillEffectResult
    {
        /// <summary>
        /// 主动技能伤害倍数
        /// </summary>
        public double ActiveSkillMultiplier { get; set; } = 1.0;

        /// <summary>
        /// 被动技能效果字典
        /// Key: 效果类型（攻击、防御、生命等）
        /// Value: 效果数值
        /// </summary>
        public Dictionary<string, double> PassiveEffects { get; set; } = new();

        /// <summary>
        /// 魔法消耗
        /// </summary>
        public int ManaCost { get; set; }

        /// <summary>
        /// 是否有足够魔法
        /// </summary>
        public bool HasSufficientMana { get; set; }
    }
}
