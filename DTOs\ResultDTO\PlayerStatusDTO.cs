namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 玩家状态同步DTO
    /// </summary>
    public class PlayerStatusDTO
    {
        /// <summary>
        /// 消息类型
        /// </summary>
        public string Type { get; set; } = "player_status";

        /// <summary>
        /// 玩家ID
        /// </summary>
        public int PlayerId { get; set; }

        /// <summary>
        /// 玩家昵称
        /// </summary>
        public string Nickname { get; set; }

        /// <summary>
        /// 在线状态 (online/offline/battle/idle)
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// VIP等级
        /// </summary>
        public int VipLevel { get; set; }

        /// <summary>
        /// 金币
        /// </summary>
        public long Gold { get; set; }

        /// <summary>
        /// 元宝
        /// </summary>
        public int Yuanbao { get; set; }

        /// <summary>
        /// 水晶
        /// </summary>
        public int Crystal { get; set; }

        /// <summary>
        /// 主宠物ID
        /// </summary>
        public int? MainPetId { get; set; }

        /// <summary>
        /// 主宠物名称
        /// </summary>
        public string MainPetName { get; set; }

        /// <summary>
        /// 主宠物境界
        /// </summary>
        public string MainPetRealm { get; set; }

        /// <summary>
        /// 当前所在地图ID
        /// </summary>
        public int? CurrentMapId { get; set; }

        /// <summary>
        /// 当前所在地图名称
        /// </summary>
        public string CurrentMapName { get; set; }

        /// <summary>
        /// 是否在战斗中
        /// </summary>
        public bool InBattle { get; set; }

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime LastActiveTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 状态更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.Now;
    }
} 