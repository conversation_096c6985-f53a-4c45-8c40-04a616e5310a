using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using WebApplication_HM.DTOs;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 转生计算服务
    /// </summary>
    public class NirvanaCalculationService : INirvanaCalculationService
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<NirvanaCalculationService> _logger;
        private readonly ILevelService _levelService;

        public NirvanaCalculationService(ISqlSugarClient db, ILogger<NirvanaCalculationService> logger, ILevelService levelService)
        {
            _db = db;
            _logger = logger;
            _levelService = levelService;
        }

        /// <summary>
        /// 计算转生成长获得（完全基于老系统逻辑）
        /// </summary>
        public async Task<decimal> CalculateGrowthGainAsync(user_pet mainPet, user_pet subPet, decimal effectBonus, user user, string script1 = "", string script2 = "")
        {
            try
            {
                // 获取基础数据
                var mainGrowth = Convert.ToDouble(mainPet.growth);
                var subGrowth = Convert.ToDouble(subPet.growth);
                var mainLevel = await _levelService.CalculateLevelAsync(mainPet.exp ?? 0, "pet");
                var subLevel = await _levelService.CalculateLevelAsync(subPet.exp ?? 0, "pet");

                // 处理道具脚本效果（对应老系统的效果加成）
                double 效果加成 = 0.0;
                if (!string.IsNullOrEmpty(script1))
                {
                    效果加成 += ParseScriptEffect(script1);
                }
                if (!string.IsNullOrEmpty(script2))
                {
                    效果加成 += ParseScriptEffect(script2);
                }

                _logger.LogInformation("转生成长计算开始 - 主宠成长:{MainGrowth}, 副宠成长:{SubGrowth}, 道具效果加成:{EffectBonus}",
                    mainGrowth, subGrowth, 效果加成);

                // 1. 计算成长限制系数（基于老系统逻辑）
                double growthLimitCoeff;
                if (mainGrowth > 10)
                {
                    growthLimitCoeff = (20.0 - Math.Log10(mainGrowth)) * 5.0 * mainGrowth;
                }
                else
                {
                    growthLimitCoeff = 200.0;
                }

                // 2. 计算成长加成系数（基于老系统逻辑）
                double growthBonusCoeff;
                if (mainGrowth < 70.0)
                {
                    growthBonusCoeff = 0.3;
                    if (subGrowth < 70.0)
                    {
                        growthBonusCoeff += 0.2;
                    }
                    else
                    {
                        growthBonusCoeff += 0.1;
                    }
                }
                else if (mainGrowth < 90.0)
                {
                    growthBonusCoeff = 0.3 - (mainGrowth - 70.0) * 0.01;
                }
                else if (mainGrowth < 10000.0)
                {
                    growthBonusCoeff = 0.1;
                }
                else
                {
                    growthBonusCoeff = 0.0;
                }

                // 3. 计算基础获得成长（老系统公式）
                var baseGrowthGain = mainGrowth * (1 + (mainLevel - 50.0) / growthLimitCoeff * 0.025) +
                                   subGrowth * (0.1 + 0.001 * (subLevel - 50.0)) - mainGrowth;

                // 4. 计算成长方法A和B（使用道具效果加成）
                double methodA = (1 + growthBonusCoeff) * (1 + 效果加成);
                double methodB = 1 + growthBonusCoeff + 效果加成;

                // 5. VIP加成
                double vipBonus = CalculateVipBonusDouble(user);

                // 6. 选择较小的方法计算最终成长
                double finalGrowthGain;
                if (methodA >= methodB)
                {
                    finalGrowthGain = baseGrowthGain * methodB * vipBonus;
                }
                else
                {
                    finalGrowthGain = baseGrowthGain * methodA * vipBonus;
                }

                // 7. 五行特殊加成
                finalGrowthGain = ApplyElementBonus(finalGrowthGain, mainPet);

                _logger.LogInformation("转生成长计算完成 - 基础成长:{BaseGrowth}, 最终成长:{FinalGrowth}",
                    baseGrowthGain, finalGrowthGain);

                return Math.Max(0, (decimal)finalGrowthGain);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "转生成长计算失败");
                return 0;
            }
        }

        /// <summary>
        /// 计算成长限制系数
        /// </summary>
        private decimal CalculateGrowthLimitCoeff(decimal mainGrowth)
        {
            if (mainGrowth > 10)
            {
                return (20 - (decimal)Math.Log10((double)mainGrowth)) * 5 * mainGrowth;
            }
            return 200;
        }

        /// <summary>
        /// 计算最终成长
        /// </summary>
        private decimal CalculateFinalGrowth(decimal baseGrowth, decimal effectBonus, user user, decimal mainGrowth)
        {
            // 计算成长加成系数
            decimal growthAdditionCoeff = baseGrowth / mainGrowth;
            decimal methodA = (1 + growthAdditionCoeff) * (1 + effectBonus);
            decimal methodB = 1 + growthAdditionCoeff + effectBonus;

            // VIP加成
            decimal vipBonus = CalculateVipBonus(user);

            // 最终成长计算
            decimal finalGrowth = baseGrowth * Math.Min(methodA, methodB) * vipBonus;

            // 五行特殊加成（如果需要）
            // finalGrowth = ApplyElementBonus(finalGrowth, mainPet.Element);

            return finalGrowth;
        }

        /// <summary>
        /// 计算VIP加成（基于老系统逻辑）
        /// </summary>
        public decimal CalculateVipBonus(user user)
        {
            try
            {
                var vipLevel = user.vip_level ?? 0;
                decimal vipBonus = 1.0m;

                // 检查是否为至尊VIP或星辰VIP
                bool isSupremeVip = user.supreme_vip == true;
                bool isStarVip = user.star_vip == true && user.star_vip_expire.HasValue && user.star_vip_expire > DateTime.Now;

                if (isSupremeVip || isStarVip)
                {
                    // 至尊VIP和星辰VIP：0.5% * VIP等级
                    vipBonus = 1 + 0.005m * vipLevel;
                }
                else if (vipLevel > 0)
                {
                    // 普通VIP：0.25% * VIP等级
                    vipBonus = 1 + 0.0025m * vipLevel;
                }

                return vipBonus;
            }
            catch
            {
                return 1.0m; // 默认无加成
            }
        }

        /// <summary>
        /// 计算转生成功率（基于老系统逻辑）
        /// </summary>
        public async Task<decimal> CalculateSuccessRateAsync(NirvanaRequestDto request, PetNirvanaConfig config)
        {
            try
            {
                // 基础成功率为30%（老系统默认值）
                decimal baseSuccessRate = 0.30m;
                decimal successRateBonus = 0m;
                bool guaranteedSuccess = false;

                // 道具效果加成
                if (!string.IsNullOrEmpty(request.UsedItemId))
                {
                    var propEffect = await GetPropEffectAsync(request.UsedItemId);
                    successRateBonus += propEffect.SuccessRateBonus;
                    guaranteedSuccess = propEffect.GuaranteedSuccess;
                }

                // 如果有保证成功的道具
                if (guaranteedSuccess)
                {
                    return 1.0m; // 100%成功
                }

                // 计算最终成功率
                decimal finalSuccessRate = baseSuccessRate + successRateBonus;

                // 确保成功率在合理范围内
                return Math.Min(Math.Max(finalSuccessRate, 0.01m), 0.95m);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算成功率失败");
                return 0.30m; // 默认30%成功率
            }
        }

        /// <summary>
        /// 验证转生条件
        /// </summary>
        public async Task<(bool isValid, List<string> errors)> ValidateNirvanaConditionsAsync(NirvanaRequestDto request)
        {
            var errors = new List<string>();

            try
            {
                // 1. 验证用户存在
                var user = await _db.Queryable<user>().FirstAsync(u => u.id == request.UserId);
                if (user == null)
                {
                    errors.Add("用户不存在");
                    return (false, errors);
                }

                // 2. 验证宠物存在且属于用户
                var mainPet = await _db.Queryable<user_pet>()
                    .FirstAsync(p => p.id == request.MainPetId && p.user_id == request.UserId);
                if (mainPet == null)
                {
                    errors.Add("主宠物不存在或不属于当前用户");
                }

                var subPet = await _db.Queryable<user_pet>()
                    .FirstAsync(p => p.id == request.SubPetId && p.user_id == request.UserId);
                if (subPet == null)
                {
                    errors.Add("副宠物不存在或不属于当前用户");
                }

                var nirvanaPet = await _db.Queryable<user_pet>()
                    .FirstAsync(p => p.id == request.NirvanaPetId && p.user_id == request.UserId);
                if (nirvanaPet == null)
                {
                    errors.Add("涅槃兽不存在或不属于当前用户");
                }

                if (errors.Any()) return (false, errors);

                // 3. 验证宠物不能相同
                if (request.MainPetId == request.SubPetId || 
                    request.MainPetId == request.NirvanaPetId || 
                    request.SubPetId == request.NirvanaPetId)
                {
                    errors.Add("转生宠物不能相同");
                }

                // 4. 验证转生配置存在
                //var config = await _db.Queryable<PetNirvanaConfig>()
                //    .FirstAsync(c => c.MainPetNo == mainPet.pet_no && 
                //                   c.SubPetNo == subPet.pet_no && 
                //                   c.NirvanaPetNo == nirvanaPet.pet_no && 
                //                   c.IsActive == 1);
                //if (config == null)
                //{
                //    errors.Add("不存在对应的转生配置");
                //}

                // 5. 验证等级要求（使用LevelService计算真实等级）
                var mainLevel = await _levelService.CalculateLevelAsync(mainPet.exp ?? 0, "pet");
                var subLevel = await _levelService.CalculateLevelAsync(subPet.exp ?? 0, "pet");
                var nirvanaLevel = await _levelService.CalculateLevelAsync(nirvanaPet.exp ?? 0, "pet");

                _logger.LogInformation("转生等级验证 - 主宠: {MainLevel}, 副宠: {SubLevel}, 涅槃兽: {NirvanaLevel}",
                    mainLevel, subLevel, nirvanaLevel);

                if (mainLevel < 60)
                {
                    errors.Add($"主宠等级没有达到60级! 当前等级: {mainLevel}");
                }

                if (subLevel < 60)
                {
                    errors.Add($"副宠等级没有达到60级! 当前等级: {subLevel}");
                }

                if (nirvanaLevel < 60)
                {
                    errors.Add($"涅槃兽等级没有达到60级! 当前等级: {nirvanaLevel}");
                }

                // 6. 验证特殊宠物转生规则（基于老系统逻辑）
                await ValidateSpecialPetRulesAsync(mainPet, subPet, nirvanaPet, errors);

                // 7. 验证金币是否足够（按照老系统固定500,000金币要求）
                var userGold = user.gold ?? 0;
                const long requiredGold = 500000; // 老系统固定要求

                if (userGold < requiredGold)
                {
                    errors.Add("金币不足!涅槃一次需要500000金币噢!");
                }

                return (errors.Count == 0, errors);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证转生条件失败");
                errors.Add("验证转生条件时发生错误");
                return (false, errors);
            }
        }

        /// <summary>
        /// 计算转生消耗金币
        /// </summary>
        public long CalculateCostGold(PetNirvanaConfig config, user user)
        {
            // 基础消耗
            long baseCost = config.CostGold;

            // VIP折扣（如果有）
            if (user.supreme_vip == true)
            {
                baseCost = (long)(baseCost * 0.9m); // 至尊VIP 9折
            }
            else if ((user.vip_level ?? 0) > 0)
            {
                baseCost = (long)(baseCost * 0.95m); // 普通VIP 95折
            }

            return baseCost;
        }

        /// <summary>
        /// 获取道具效果（基于老系统道具配置）
        /// </summary>
        public async Task<PropEffect> GetPropEffectAsync(string itemId)
        {
            try
            {
                // 查询道具配置
                var itemConfig = await _db.Queryable<item_config>()
                    .FirstAsync(i => i.item_no.ToString() == itemId);

                if (itemConfig == null)
                {
                    return new PropEffect();
                }

                // 根据道具名称或ID判断效果
                var effect = new PropEffect();

                // 设置道具脚本（从配置表获取）
                effect.Script = itemConfig.script ?? "";

                // 转生相关道具效果配置
                switch (itemConfig.item_no)
                {
                    case 521: // 涅槃石
                        effect.SuccessRateBonus = 0.1m; // 10%成功率加成
                        break;
                    case 522: // 高级涅槃石
                        effect.SuccessRateBonus = 0.2m; // 20%成功率加成
                        break;
                    case 523: // 超级涅槃石
                        effect.SuccessRateBonus = 0.3m; // 30%成功率加成
                        break;
                    case 524: // 护宠仙石
                        effect.FailureProtection = true; // 失败保护
                        break;
                    case 525: // 转生保证书
                        effect.GuaranteedSuccess = true; // 保证成功
                        break;
                    default:
                        // 根据道具名称判断
                        if (itemConfig.name?.Contains("涅槃") == true)
                        {
                            effect.SuccessRateBonus = 0.05m; // 默认5%加成
                        }
                        else if (itemConfig.name?.Contains("护宠") == true)
                        {
                            effect.FailureProtection = true;
                        }
                        break;
                }

                return effect;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取道具效果失败 - 道具ID:{ItemId}", itemId);
                return new PropEffect();
            }
        }

        /// <summary>
        /// 验证特殊宠物转生规则（基于老系统逻辑）
        /// </summary>
        private async Task ValidateSpecialPetRulesAsync(user_pet mainPet, user_pet subPet, user_pet nirvanaPet, List<string> errors)
        {
            try
            {
                // 获取宠物的五行属性（需要从宠物配置表获取）
                var mainPetConfig = await _db.Queryable<pet_config>()
                    .FirstAsync(p => p.pet_no == mainPet.pet_no);
                var subPetConfig = await _db.Queryable<pet_config>()
                    .FirstAsync(p => p.pet_no == subPet.pet_no);

                if (mainPetConfig == null || subPetConfig == null)
                {
                    errors.Add("无法获取宠物配置信息");
                    return;
                }

                // 1. 巫系宠物特殊规则（完全按照老系统逻辑）
                if (mainPetConfig.attribute == "巫")
                {
                    if (subPetConfig.attribute != "巫")
                    {
                        errors.Add("巫系宠物只能和巫系宠物涅槃！");
                        return;
                    }
                    // 注：老系统中巫系不需要特定的涅槃兽，这里保持一致
                }

                // 2. 检查是否为涅槃兽
                if (mainPet.name?.Contains("涅槃兽") == true || subPet.name?.Contains("涅槃兽") == true)
                {
                    errors.Add("当前主宠或副宠无法参与涅槃!");
                    return;
                }

                // 3. 特定形象限制（基于老系统的形象编号）
                if (mainPet.pet_no == 577 || mainPet.pet_no == 578 || mainPet.pet_no == 581)
                {
                    errors.Add("当前主宠无法参与涅槃!");
                    return;
                }

                // 4. 特定宠物组合限制
                if (mainPet.pet_no == 610) // 特定宠物
                {
                    if (subPet.pet_no != 609)
                    {
                        errors.Add("当前主宠只能和亓玥·宝宝转生!");
                        return;
                    }
                }

                if (mainPet.pet_no == 609) // 亓玥·宝宝
                {
                    errors.Add("当前主宠只能作为副宠!");
                    return;
                }

                // 5. 验证涅槃兽（按照老系统逻辑）
                if (nirvanaPet.name?.Contains("涅槃兽") != true&&nirvanaPet.pet_no!=103)
                {
                    errors.Add("请放入涅槃兽!");
                    return;
                }

                // 6. 验证宠物五行等级（不能是普通宠物）
                var mainElementOrder = GetElementOrder(mainPetConfig.attribute);
                var subElementOrder = GetElementOrder(subPetConfig.attribute);

                if (mainElementOrder <= 5 || subElementOrder <= 5)
                {
                    errors.Add("不能放入普通宠物!");
                    return;
                }

                // 7. 验证等级要求（60级以上）
                var mainLevel = await _levelService.CalculateLevelAsync(mainPet.exp ?? 0, "pet");
                var subLevel = await _levelService.CalculateLevelAsync(subPet.exp ?? 0, "pet");
                var nirvanaLevel = await _levelService.CalculateLevelAsync(nirvanaPet.exp ?? 0, "pet");

                _logger.LogInformation("特殊宠物转生等级验证 - 主宠: {MainLevel}, 副宠: {SubLevel}, 涅槃兽: {NirvanaLevel}",
                    mainLevel, subLevel, nirvanaLevel);

                if (mainLevel < 60)
                {
                    errors.Add($"主宠等级没有达到60级! 当前等级: {mainLevel}");
                    return;
                }

                if (subLevel < 60)
                {
                    errors.Add($"副宠等级没有达到60级! 当前等级: {subLevel}");
                    return;
                }

                if (nirvanaLevel < 60)
                {
                    errors.Add($"涅槃兽等级没有达到60级! 当前等级: {nirvanaLevel}");
                    return;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证特殊宠物规则失败");
                errors.Add("验证特殊规则时发生错误");
            }
        }

        /// <summary>
        /// 获取五行序号（完全按照老系统的五行序号枚举）
        /// </summary>
        private int GetElementOrder(string element)
        {
            return element switch
            {
                "金" => 1,
                "木" => 2,
                "水" => 3,
                "火" => 4,
                "土" => 5,
                "神" => 6,
                "神圣" => 7,
                "聖" => 8,
                "佛" => 9,
                "魔" => 10,
                "人" => 11,
                "鬼" => 12,
                "巫" => 13,
                "萌" => 14,
                "仙" => 15,
                "灵" => 16,
                "次元" => 17,
                _ => 0
            };
        }

        /// <summary>
        /// 计算VIP加成（Double版本，用于成长计算）
        /// </summary>
        private double CalculateVipBonusDouble(user user)
        {
            try
            {
                var vipLevel = user.vip_level ?? 0;
                double vipBonus = 1.0;

                // 检查是否为至尊VIP或星辰VIP
                bool isSupremeVip = user.supreme_vip == true;
                bool isStarVip = user.star_vip == true && user.star_vip_expire.HasValue && user.star_vip_expire > DateTime.Now;

                if (isSupremeVip || isStarVip)
                {
                    // 至尊VIP和星辰VIP：0.5% * VIP等级
                    vipBonus = 1 + 0.005 * vipLevel;
                }
                else if (vipLevel > 0)
                {
                    // 普通VIP：0.25% * VIP等级
                    vipBonus = 1 + 0.0025 * vipLevel;
                }

                return vipBonus;
            }
            catch
            {
                return 1.0; // 默认无加成
            }
        }

        /// <summary>
        /// 应用五行特殊加成（基于老系统逻辑）
        /// </summary>
        private double ApplyElementBonus(double growthGain, user_pet mainPet)
        {
            try
            {
                // 获取宠物五行属性
                var petConfig = _db.Queryable<pet_config>()
                    .First(p => p.pet_no == mainPet.pet_no);

                if (petConfig?.attribute == null) return growthGain;

                // 五行特殊加成
                return petConfig.attribute switch
                {
                    "神" => growthGain * 0.9,      // 神系 90%
                    "神圣" => growthGain * 0.9,    // 神圣系 90%
                    "佛" => growthGain * 0.9,      // 佛系 90%
                    "聖" => growthGain * 0.9,      // 聖系 90%
                    "次元" => growthGain * 0.93,   // 次元系 93%
                    _ => growthGain                // 其他无加成
                };
            }
            catch
            {
                return growthGain; // 异常时返回原值
            }
        }

        /// <summary>
        /// 解析道具脚本效果
        /// 根据老系统的道具脚本格式解析效果加成
        /// </summary>
        /// <param name="script">道具脚本</param>
        /// <returns>效果加成值</returns>
        private double ParseScriptEffect(string script)
        {
            if (string.IsNullOrEmpty(script))
                return 0.0;

            try
            {
                // 老系统中常见的道具脚本效果格式：
                // "涅槃成功率+10%" -> 成功率加成
                // "涅槃成长+5%" -> 成长加成
                // "失败不消失" -> 失败保护
                // "肯定成功" -> 100%成功率

                double effectBonus = 0.0;

                // 解析成长加成
                if (script.Contains("涅槃成长") || script.Contains("成长"))
                {
                    var match = System.Text.RegularExpressions.Regex.Match(script, @"(\d+)%");
                    if (match.Success)
                    {
                        if (double.TryParse(match.Groups[1].Value, out double percentage))
                        {
                            effectBonus += percentage / 100.0; // 转换为小数
                        }
                    }
                }

                // 解析固定加成值
                if (script.Contains("加成") || script.Contains("增加"))
                {
                    var match = System.Text.RegularExpressions.Regex.Match(script, @"(\d+\.?\d*)");
                    if (match.Success)
                    {
                        if (double.TryParse(match.Groups[1].Value, out double value))
                        {
                            // 如果是百分比形式，转换为小数
                            if (script.Contains("%"))
                            {
                                effectBonus += value / 100.0;
                            }
                            else
                            {
                                effectBonus += value;
                            }
                        }
                    }
                }

                // 特殊道具效果
                if (script.Contains("涅槃石"))
                {
                    effectBonus += 0.05; // 涅槃石通常提供5%加成
                }
                else if (script.Contains("护宠"))
                {
                    effectBonus += 0.02; // 护宠道具提供2%加成
                }
                else if (script.Contains("转生保证书"))
                {
                    effectBonus += 0.1; // 转生保证书提供10%加成
                }

                _logger.LogDebug("道具脚本解析 - 脚本:{Script}, 效果加成:{EffectBonus}", script, effectBonus);
                return effectBonus;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "解析道具脚本失败 - 脚本:{Script}", script);
                return 0.0;
            }
        }

    }
}
