﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///境界配置表
    ///</summary>
    [SugarTable("realm_config")]
    public partial class realm_config
    {
           public realm_config(){


           }
           /// <summary>
           /// Desc:自增主键ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:境界ID（唯一业务标识）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int realm_id {get;set;}

           /// <summary>
           /// Desc:境界名称
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string realm_name {get;set;}

           /// <summary>
           /// Desc:属性加成比例
           /// Default:0.0000
           /// Nullable:True
           /// </summary>           
           public decimal? attribute_bonus {get;set;}

           /// <summary>
           /// Desc:加成类型
           /// Default:PERCENTAGE
           /// Nullable:True
           /// </summary>           
           public string? bonus_type {get;set;}

           /// <summary>
           /// Desc:境界描述
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? description {get;set;}

           /// <summary>
           /// Desc:等级要求
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int level_requirement {get;set;}

           /// <summary>
           /// Desc:生命值加成
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int hp_bonus {get;set;}

           /// <summary>
           /// Desc:攻击力加成
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int attack_bonus {get;set;}

           /// <summary>
           /// Desc:防御力加成
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int defense_bonus {get;set;}

           /// <summary>
           /// Desc:境界等级 (对应原项目PetStates的Key)
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int realm_level {get;set;}

           /// <summary>
           /// Desc:属性加成倍率
           /// Default:0.000000
           /// Nullable:False
           /// </summary>           
           public decimal attribute_bonus_rate {get;set;}

           /// <summary>
           /// Desc:升级成功率(%)
           /// Default:0.000000
           /// Nullable:False
           /// </summary>           
           public decimal upgrade_success_rate {get;set;}

           /// <summary>
           /// Desc:升级消耗金币
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public long upgrade_cost_gold {get;set;}

           /// <summary>
           /// Desc:升级所需道具
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? upgrade_item_required {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? created_at {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? updated_at {get;set;}

    }
}
