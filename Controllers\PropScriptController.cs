using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.Services;
using WebApplication_HM.Services.PropScript;
using WebApplication_HM.DTOs.Common;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 道具脚本控制器
    /// </summary>
    [ApiController]
    [Route("api/prop-script")]
    public class PropScriptController : ControllerBase
    {
        private readonly PropService _propService;
        private readonly ILogger<PropScriptController> _logger;

        public PropScriptController(PropService propService, ILogger<PropScriptController> logger)
        {
            _propService = propService;
            _logger = logger;
        }

        /// <summary>
        /// 使用道具（支持脚本执行）
        /// </summary>
        /// <param name="request">脚本执行请求</param>
        /// <returns>执行结果</returns>
        [HttpPost("use")]
        public ApiResult<PropScriptResult> UseItemWithScript([FromBody] PropScriptExecuteRequest request)
        {
            try
            {
                _logger.LogInformation($"使用道具脚本: UserId={request.UserId}, ItemId={request.ItemId}");

                var result = _propService.UseItemWithScript(request);

                if (result.Success)
                {
                    _logger.LogInformation($"道具脚本执行成功: {result.Message}");
                    return ApiResult<PropScriptResult>.CreateSuccess(result);
                }
                else
                {
                    _logger.LogWarning($"道具脚本执行失败: {result.Message}");
                    return ApiResult<PropScriptResult>.CreateError(result.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"使用道具脚本异常: {System.Text.Json.JsonSerializer.Serialize(request)}");
                return ApiResult<PropScriptResult>.CreateError("使用道具脚本异常");
            }
        }

        /// <summary>
        /// 执行多脚本选择
        /// </summary>
        /// <param name="request">多脚本选择请求</param>
        /// <returns>执行结果</returns>
        [HttpPost("multi-select")]
        public ApiResult<PropScriptResult> ExecuteMultiScriptSelection([FromBody] MultiScriptSelectRequest request)
        {
            try
            {
                _logger.LogInformation($"执行多脚本选择: UserId={request.UserId}, ItemId={request.ItemId}, Option={request.SelectedOption}");

                var result = _propService.ExecuteMultiScriptSelection(request);

                if (result.Success)
                {
                    _logger.LogInformation($"多脚本选择执行成功: {result.Message}");
                    return ApiResult<PropScriptResult>.CreateSuccess(result);
                }
                else
                {
                    _logger.LogWarning($"多脚本选择执行失败: {result.Message}");
                    return ApiResult<PropScriptResult>.CreateError(result.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"执行多脚本选择异常: {System.Text.Json.JsonSerializer.Serialize(request)}");
                return ApiResult<PropScriptResult>.CreateError("执行多脚本选择异常");
            }
        }

        /// <summary>
        /// 获取用户的多脚本选择状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>多脚本状态</returns>
        [HttpGet("multi-state/{userId}")]
        public ApiResult<MultiScriptState> GetUserMultiScriptState(int userId)
        {
            try
            {
                _logger.LogInformation($"获取用户多脚本状态: UserId={userId}");

                var state = _propService.GetUserMultiScriptState(userId);

                if (state != null)
                {
                    return ApiResult<MultiScriptState>.CreateSuccess(state);
                }
                else
                {
                    return ApiResult<MultiScriptState>.CreateError("没有找到多脚本选择状态");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取用户多脚本状态异常: UserId={userId}");
                return ApiResult<MultiScriptState>.CreateError("获取多脚本状态异常");
            }
        }

        /// <summary>
        /// 清除用户的多脚本选择状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("multi-state/{userId}")]
        public ApiResult<bool> ClearUserMultiScriptState(int userId)
        {
            try
            {
                _logger.LogInformation($"清除用户多脚本状态: UserId={userId}");

                _propService.ClearUserMultiScriptState(userId);

                return ApiResult<bool>.CreateSuccess(true, "多脚本状态已清除");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"清除用户多脚本状态异常: UserId={userId}");
                return ApiResult<bool>.CreateError("清除多脚本状态异常");
            }
        }

        /// <summary>
        /// 验证道具脚本格式
        /// </summary>
        /// <param name="script">脚本内容</param>
        /// <returns>验证结果</returns>
        [HttpPost("validate")]
        public ApiResult<ScriptValidationResult> ValidateScript([FromBody] string script)
        {
            try
            {
                _logger.LogInformation($"验证道具脚本: Script={script}");

                var result = _propService.ValidateScript(script);

                return ApiResult<ScriptValidationResult>.CreateSuccess(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"验证道具脚本异常: Script={script}");
                return ApiResult<ScriptValidationResult>.CreateError("验证脚本异常");
            }
        }

        /// <summary>
        /// 获取支持的脚本类型列表
        /// </summary>
        /// <returns>脚本类型列表</returns>
        [HttpGet("script-types")]
        public ApiResult<List<ScriptTypeInfo>> GetSupportedScriptTypes()
        {
            try
            {
                var scriptTypes = new List<ScriptTypeInfo>
                {
                    // 基础脚本类型
                    new() { Type = "扩展道具格子", Description = "扩展道具格子|数量|最小容量|最大容量", Example = "扩展道具格子|20|240|300" },
                    new() { Type = "扩展牧场格子", Description = "扩展牧场格子|数量|最小容量|最大容量", Example = "扩展牧场格子|10|80|120" },
                    new() { Type = "学习技能", Description = "学习技能|技能ID", Example = "学习技能|skill_001" },
                    new() { Type = "宠物当前经验", Description = "宠物当前经验|经验值", Example = "宠物当前经验|10000" },
                    new() { Type = "巫族宠物经验", Description = "巫族宠物经验|经验值", Example = "巫族宠物经验|5000" },
                    new() { Type = "龙珠经验值", Description = "龙珠经验值|基础经验|最大经验", Example = "龙珠经验值|1000|2000" },
                    new() { Type = "龙珠突破", Description = "龙珠突破|龙珠名称", Example = "龙珠突破|青龙珠" },
                    new() { Type = "合成道具", Description = "合成道具|材料JSON|结果道具ID", Example = "合成道具|{\"item1\":2,\"item2\":1}|result_item" },
                    new() { Type = "扣除并获得道具", Description = "扣除并获得道具|扣除道具ID|获得道具ID|扣除数量|获得数量", Example = "扣除并获得道具|old_item|new_item|1|1" },
                    new() { Type = "召唤宠物", Description = "召唤宠物|宠物ID", Example = "召唤宠物|pet_001" },
                    new() { Type = "镶嵌宝石", Description = "镶嵌宝石|宝石类型", Example = "镶嵌宝石|红宝石" },
                    new() { Type = "获得称号", Description = "获得称号|称号ID", Example = "获得称号|title_001" },
                    new() { Type = "魂宠召唤", Description = "魂宠召唤|魂宠ID", Example = "魂宠召唤|soul_pet_001" },
                    new() { Type = "多脚本选择", Description = "多脚本选择!{\"选项1\":\"脚本1\",\"选项2\":\"脚本2\"}", Example = "多脚本选择!{\"增加经验\":\"宠物当前经验|10000\",\"增加金币\":\"获得金币|5000\"}" },

                    // 新增的高优先级脚本类型
                    new() { Type = "获得元宝", Description = "获得元宝|数量", Example = "获得元宝|100" },
                    new() { Type = "获得金币", Description = "获得金币|数量", Example = "获得金币|50000" },
                    new() { Type = "获得道具", Description = "获得道具|道具ID|数量", Example = "获得道具|2016092601|5" },
                    new() { Type = "获得多个道具", Description = "获得多个道具|道具ID1|数量1|道具ID2|数量2", Example = "获得多个道具|2016092601|5|2016092602|3" },
                    new() { Type = "一定概率获得", Description = "一定概率获得|道具ID,权重|道具ID,权重", Example = "一定概率获得|2016092601,5|2016092602,3|2016092603,2" },
                    new() { Type = "随机获得", Description = "随机获得|道具ID1|道具ID2|道具ID3", Example = "随机获得|2016092601|2016092602|2016092603" },
                    new() { Type = "召唤宠物", Description = "召唤宠物|材料配置,宠物配置", Example = "召唤宠物|2016092601|1|2016092602|2,1001|25" }
                };

                return ApiResult<List<ScriptTypeInfo>>.CreateSuccess(scriptTypes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取支持的脚本类型异常");
                return ApiResult<List<ScriptTypeInfo>>.CreateError("获取脚本类型异常");
            }
        }

        /// <summary>
        /// 测试脚本执行（仅用于开发调试）
        /// </summary>
        /// <param name="request">测试请求</param>
        /// <returns>测试结果</returns>
        [HttpPost("test")]
        public ApiResult<PropScriptResult> TestScript([FromBody] ScriptTestRequest request)
        {
            try
            {
                _logger.LogInformation($"测试脚本执行: Script={request.Script}");

                // 创建测试用的脚本执行请求
                var executeRequest = new PropScriptExecuteRequest
                {
                    UserId = request.UserId,
                    ItemId = request.ItemId,
                    UseCount = 1,
                    SpecificScript = request.Script
                };

                var result = _propService.UseItemWithScript(executeRequest);

                return ApiResult<PropScriptResult>.CreateSuccess(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"测试脚本执行异常: {System.Text.Json.JsonSerializer.Serialize(request)}");
                return ApiResult<PropScriptResult>.CreateError("测试脚本执行异常");
            }
        }
    }

    /// <summary>
    /// 脚本类型信息
    /// </summary>
    public class ScriptTypeInfo
    {
        /// <summary>
        /// 脚本类型
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 示例
        /// </summary>
        public string Example { get; set; } = string.Empty;
    }

    /// <summary>
    /// 脚本测试请求
    /// </summary>
    public class ScriptTestRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 道具ID
        /// </summary>
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// 脚本内容
        /// </summary>
        public string Script { get; set; } = string.Empty;
    }
}
