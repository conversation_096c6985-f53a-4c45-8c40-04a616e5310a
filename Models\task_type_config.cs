﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///任务类型配置表
    ///</summary>
    [SugarTable("task_type_config")]
    public partial class task_type_config
    {
           public task_type_config(){


           }
           /// <summary>
           /// Desc:类型ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string type_id {get;set;}

           /// <summary>
           /// Desc:类型名称
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string type_name {get;set;}

           /// <summary>
           /// Desc:类型描述
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? type_description {get;set;}

           /// <summary>
           /// Desc:处理器类名
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? handler_class {get;set;}

           /// <summary>
           /// Desc:是否激活
           /// Default:1
           /// Nullable:True
           /// </summary>           
           public byte? is_active {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? created_at {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? updated_at {get;set;}

    }
}
