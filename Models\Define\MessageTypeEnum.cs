using System.ComponentModel;

namespace WebApplication_HM.Models.Define;

// 获取枚举值
// var headType = HeadType.Config;

// 获取Description值（需要写一个扩展方法）
// string configValue = headType.GetDescription(); // 将返回 "Cf"

/// <summary>
/// TCP请求头部类型
/// </summary>
public enum HeadType
{
    [Description("Err")]
    Error,      // TCP请求头部_请求未知类型
    
    [Description("Cf")]
    Config,     // TCP请求头部_配置类型
    
    [Description("Ac")]
    Account,    // TCP请求头部_账号类型
    
    [Description("It")]
    Item,       // TCP请求头部_道具请求
    
    [Description("Pl")]
    Player,     // TCP请求头部_玩家请求
    
    [Description("Key")]
    Key,        // TCP请求头部_密钥请求
    
    [Description("Msg")]
    Message     // TCP请求头部_消息请求
}

/// <summary>
/// 配置相关子类型
/// </summary>
public enum ConfigSubType
{
    [Description("C_HE")]
    Hero,       // TCP请求[配置]子类型_角色配置
    
    [Description("C_SK")]
    Skill,      // TCP请求[配置]子类型_技能配置
    
    [Description("C_TA")]
    Talent,     // TCP请求[配置]子类型_协作配置
    
    [Description("C_FB")]
    FaBao,      // TCP请求[配置]子类型_装备配置
    
    [Description("C_IT")]
    Item,       // TCP请求[配置]子类型_道具配置
    
    [Description("C_CL")]
    Class,      // TCP请求[配置]子类型_境界配置
    
    [Description("C_JD")]
    JdClass,    // TCP请求[配置]子类型_金丹配置
    
    [Description("C_LV")]
    Level,      // TCP请求[配置]子类型_等级配置
    
    [Description("C_CD")]
    CardPool    // TCP请求[配置]子类型_卡池配置
}

/// <summary>
/// 账号相关子类型
/// </summary>
public enum AccountSubType
{
    [Description("L")]
    Login,      // TCP请求[账号]子类型_登录类型
    
    [Description("R")]
    Register,   // TCP请求[账号]子类型_注册类型
    
    [Description("LD")]
    LoginDrop   // TCP请求[账号]子类型_账号在其他地方登录
}

/// <summary>
/// 任务状态枚举
/// </summary>
public enum TaskStatus : byte
{
    Completed = 0,      // 已完成
    InProgress = 1,     // 进行中
    Abandoned = 2       // 已放弃
}

/// <summary>
/// 任务类型枚举
/// </summary>
public enum TaskType : byte
{
    Normal = 0,         // 普通任务
    Repeatable = 1,     // 循环任务
    Event = 2           // 活动任务
}



/// <summary>
/// 任务目标类型常量（与数据库中的值保持一致）
/// </summary>
public static class TaskObjectiveTypes
{
    public const string KILL_MONSTER = "KILL_MONSTER";
    public const string COLLECT_ITEM = "COLLECT_ITEM";
    public const string REACH_LEVEL = "REACH_LEVEL";
    public const string CURRENCY = "CURRENCY";
    public const string PET_GROWTH = "PET_GROWTH";
    public const string COLLECT_EQUIPMENT = "COLLECT_EQUIPMENT";
    public const string VIP_LEVEL = "VIP_LEVEL";
    public const string TIME_LIMIT = "TIME_LIMIT";
    public const string DUNGEON = "DUNGEON";
    public const string CARD = "CARD";
    public const string ONE_CLICK = "ONE_CLICK";
    public const string SPECIAL = "SPECIAL";
}

/// <summary>
/// 奖励类型常量
/// </summary>
public static class RewardTypes
{
    public const string ITEM = "道具";
    public const string CURRENCY = "货币";
    public const string EQUIPMENT = "装备";
    public const string PET = "宠物";
    public const string EXPERIENCE = "经验";
    public const string VIP_POINTS = "VIP积分";
    public const string SPECIAL = "特殊";
}

/// <summary>
/// 道具相关子类型
/// </summary>
public enum ItemSubType
{
    [Description("Items")]
    GetItems,       // TCP请求[道具]子类型_获取道具列表
    
    [Description("CItem")]
    GetClassItem,   // TCP请求[道具]子类型_获取境界中可消耗的角色和道具
    
    [Description("UpItem")]
    GetLevelUpItem  // TCP请求[道具]子类型_获取升级中可消耗的角色和道具
}

/// <summary>
/// 玩家角色相关子类型
/// </summary>
public enum PlayerHeroSubType
{
    [Description("CHP")]
    ChangeHeroPos,  // TCP请求[玩家]子类型-角色_更改角色站位/下阵/上阵/更换
    
    [Description("HXZ")]
    XiaZhen,        // TCP请求[玩家]子类型-角色_下阵请求
    
    [Description("ZM")]
    DrawACard,      // TCP请求[玩家]子类型-角色_招募请求
    
    [Description("GH")]
    GetHeros,       // TCP请求[玩家]子类型-角色_获取已有角色
    
    [Description("HJJ")]
    JingJie,        // TCP请求[玩家]子类型-角色_境界消耗请求
    
    [Description("HSJ")]
    Shengji,        // TCP请求[玩家]子类型-角色_升级消耗请求
    
    [Description("P_CD")]
    CountDown       // TCP请求[玩家]子类型-角色_获取累计时间请求
}

/// <summary>
/// WebSocket消息类型枚举
/// </summary>
public static class MessageTypeEnum
{
    /// <summary>
    /// 登录消息
    /// </summary>
    public const string Login = "L";
    
    /// <summary>
    /// 登录成功响应
    /// </summary>
    public const string LoginSuccess = "La";
    
    /// <summary>
    /// 聊天消息
    /// </summary>
    public const string Chat = "chat";
    
    /// <summary>
    /// 私聊消息
    /// </summary>
    public const string Private = "private";
    
    /// <summary>
    /// 实时战斗推送
    /// </summary>
    public const string RealTimeBattle = "battle";
    
    /// <summary>
    /// 玩家状态同步
    /// </summary>
    public const string PlayerStatus = "player_status";
    
    /// <summary>
    /// 系统公告
    /// </summary>
    public const string SystemNotice = "system_notice";
    
    /// <summary>
    /// 境界突破通知
    /// </summary>
    public const string RealmBreakthrough = "realm_breakthrough";
    
    /// <summary>
    /// 装备获得通知
    /// </summary>
    public const string EquipmentNotify = "equipment_notify";
    
    /// <summary>
    /// 被挤下线
    /// </summary>
    public const string ForceLogout = "force_logout";
    
    /// <summary>
    /// 心跳检测
    /// </summary>
    public const string Heartbeat = "heartbeat";
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public const string Error = "error";
} 