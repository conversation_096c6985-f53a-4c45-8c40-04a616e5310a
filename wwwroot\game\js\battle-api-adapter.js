/**
 * 战斗API适配器
 * 负责处理战斗相关的API调用和数据转换
 * 作者：AI Assistant
 * 创建时间：2025-01-01
 */

const BattleAPI = {
    baseUrl: '/api/Player',
    
    /**
     * 执行战斗计算
     * @param {number} mapId - 地图ID
     * @param {number} petId - 宠物ID
     * @param {string|null} skillId - 技能ID
     * @returns {Promise<Object>} 战斗结果
     */
    async executeBattle(mapId, petId, skillId = null) {
        try {
            console.log(`[BattleAPI] 发起战斗请求: 地图${mapId}, 宠物${petId}, 技能${skillId}`);
            
            const userId = this.getCurrentUserId();
            const requestData = {
                UserId: userId,
                MapId: mapId,
                PetId: petId,
                SkillId: skillId
            };
            
            console.log('[BattleAPI] 请求数据:', requestData);
            
            const response = await fetch(`${this.baseUrl}/BattleCalculate`, {
                method: 'POST',
                headers: { 
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            console.log('[BattleAPI] 战斗响应:', result);
            
            return this.transformBattleResult(result);
        } catch (error) {
            console.error('[BattleAPI] 战斗API调用失败:', error);
            throw error;
        }
    },
    
    // 捕捉功能暂时禁用
    // TODO: 后续实现捕捉功能
    
    /**
     * 获取当前用户ID
     * @returns {number} 用户ID
     */
    getCurrentUserId() {
        if (typeof parent !== 'undefined' && parent.gameAPI && parent.gameAPI.getCurrentUserId) {
            const userId = parent.gameAPI.getCurrentUserId();
            if (userId && userId > 0) {
                return userId;
            }
        }

        // 备用方案：从其他地方获取用户ID
        if (typeof window.userId !== 'undefined' && window.userId > 0) {
            return window.userId;
        }

        // 测试模式：返回默认用户ID
        console.warn('[BattleAPI] 使用测试用户ID，请确保在生产环境中正确登录');
        return 1;
    },
    
    /**
     * 转换战斗结果为兼容格式
     * @param {Object} apiResult - API返回的原始结果
     * @returns {Object} 转换后的结果
     */
    transformBattleResult(apiResult) {
        // 数据验证
        if (!apiResult || typeof apiResult !== 'object') {
            throw new Error('无效的战斗结果数据');
        }
        
        const currentRound = apiResult.battleRounds?.[apiResult.currentRound - 1] || {};
        
        return {
            // 新格式（用于新逻辑）
            success: apiResult.success !== false,
            isBattleEnd: apiResult.isBattleEnd || false,
            isWin: apiResult.isWin || false,
            currentRound: apiResult.currentRound || 0,
            playerCurrentHp: apiResult.playerCurrentHp || 0,
            monsterCurrentHp: apiResult.monsterCurrentHp || 0,
            playerCurrentMp: apiResult.playerCurrentMp || 0,
            playerMaxHp: apiResult.playerMaxHp || 1000,
            monsterMaxHp: apiResult.monsterMaxHp || 1000,
            battleRounds: apiResult.battleRounds || [],
            exp: apiResult.exp || 0,
            gold: apiResult.gold || 0,
            yuanbao: apiResult.yuanbao || 0,
            dropItems: apiResult.dropItems || [],
            message: apiResult.message || '',
            canCapture: apiResult.canCapture || false,
            captureOptions: apiResult.captureOptions || [],
            
            // 兼容格式（用于现有逻辑）
            legacy: {
                "战斗是否结束": apiResult.isBattleEnd ? "1" : "0",
                "advance": currentRound.isPlayerTurn ? "1" : "0",
                "输出": currentRound.playerDamage || 0,
                "受到伤害": currentRound.monsterDamage || 0,
                "己方剩余HP": apiResult.playerCurrentHp || 0,
                "对方剩余HP": apiResult.monsterCurrentHp || 0,
                "剩余魔法": apiResult.playerCurrentMp || 0,
                "加深伤害": currentRound.amplifiedDamage || 0,
                "抵消伤害": currentRound.reducedDamage || 0,
                "吸血": currentRound.lifeSteal || 0,
                "吸魔": currentRound.manaSteal || 0,
                "获得经验": apiResult.exp || 0,
                "获得金币": apiResult.gold || 0,
                "获得元宝": apiResult.yuanbao || 0,
                "获得道具": apiResult.dropItems?.join(",") || ""
            }
        };
    },
    
    /**
     * 验证战斗结果数据
     * @param {Object} data - 战斗结果数据
     * @returns {boolean} 验证结果
     */
    validateBattleResult(data) {
        const required = ['isBattleEnd', 'isWin', 'playerCurrentHp', 'monsterCurrentHp'];
        
        for (const field of required) {
            if (!data.hasOwnProperty(field)) {
                throw new Error(`缺少必需字段: ${field}`);
            }
        }
        
        // 数值验证
        if (data.playerCurrentHp < 0 || data.monsterCurrentHp < 0) {
            throw new Error('HP值不能为负数');
        }
        
        return true;
    }
};

/**
 * 数据转换工具
 */
const DataConverter = {
    /**
     * 将新格式转换为旧格式（用于兼容）
     * @param {Object} newData - 新格式数据
     * @returns {Object} 旧格式数据
     */
    newToLegacy(newData) {
        const currentRound = newData.battleRounds?.[newData.currentRound - 1] || {};
        
        return {
            "战斗是否结束": newData.isBattleEnd ? "1" : "0",
            "advance": currentRound.isPlayerTurn ? "1" : "0",
            "输出": currentRound.playerDamage || 0,
            "受到伤害": currentRound.monsterDamage || 0,
            "己方剩余HP": newData.playerCurrentHp,
            "对方剩余HP": newData.monsterCurrentHp,
            "剩余魔法": newData.playerCurrentMp || 0,
            "加深伤害": currentRound.amplifiedDamage || 0,
            "抵消伤害": currentRound.reducedDamage || 0,
            "吸血": currentRound.lifeSteal || 0,
            "吸魔": currentRound.manaSteal || 0,
            "获得经验": newData.exp || 0,
            "获得金币": newData.gold || 0,
            "获得元宝": newData.yuanbao || 0,
            "获得道具": newData.dropItems?.join(",") || ""
        };
    },
    
    /**
     * 标准化技能数据
     * @param {string} skillString - 技能字符串
     * @returns {Object} 标准化的技能对象
     */
    standardizeSkill(skillString) {
        const parts = skillString.split("|");
        return {
            id: parts[0] || "",
            name: parts[1] || "未知技能",
            mpCost: parseInt(parts[2]) || 0,
            description: parts[3] || ""
        };
    },
    
    /**
     * 标准化宠物数据
     * @param {Object} petData - 宠物数据
     * @returns {Object} 标准化的宠物对象
     */
    standardizePet(petData) {
        return {
            id: petData.id || petData.ID,
            name: petData.name || petData.Name || "未知宠物",
            level: petData.level || petData.Level || 1,
            hp: petData.hp || petData.HP || 100,
            mp: petData.mp || petData.MP || 100,
            maxHp: petData.maxHp || petData.MaxHP || 100,
            maxMp: petData.maxMp || petData.MaxMP || 100,
            petNo: petData.petNo || petData.PetNo || 1,
            element: petData.element || petData.Element || ""
        };
    }
};

/**
 * 错误处理工具
 */
const ErrorHandler = {
    /**
     * 处理API错误
     * @param {Error} error - 错误对象
     * @param {string} operation - 操作名称
     */
    handleAPIError(error, operation) {
        console.error(`[ErrorHandler] ${operation}失败:`, error);
        
        let message = "操作失败，请重试";
        
        if (error.status) {
            switch (error.status) {
                case 400:
                    message = "请求参数错误";
                    break;
                case 401:
                    message = "用户未登录，请重新登录";
                    break;
                case 403:
                    message = "权限不足";
                    break;
                case 429:
                    message = "操作过于频繁，请稍后重试";
                    break;
                case 500:
                    message = "服务器内部错误";
                    break;
                case 503:
                    message = "服务暂时不可用";
                    break;
                default:
                    message = `服务器错误 (${error.status})`;
            }
        } else if (error.message) {
            if (error.message.includes("网络") || error.message.includes("Network")) {
                message = "网络连接异常，请检查网络";
            } else if (error.message.includes("超时") || error.message.includes("timeout")) {
                message = "请求超时，请重试";
            } else {
                message = error.message;
            }
        }
        
        // 显示错误信息
        if (typeof parent !== 'undefined' && parent.Alert) {
            parent.Alert(message);
        } else {
            alert(message);
        }
        
        // 记录错误日志
        if (typeof debug === 'function') {
            debug(`${operation}错误: ${error.message || error}`);
        }
        
        return message;
    }
};

// 导出到全局作用域（兼容现有代码）
if (typeof window !== 'undefined') {
    window.BattleAPI = BattleAPI;
    window.DataConverter = DataConverter;
    window.ErrorHandler = ErrorHandler;
}

console.log('[BattleAPI] 战斗API适配器已加载');
