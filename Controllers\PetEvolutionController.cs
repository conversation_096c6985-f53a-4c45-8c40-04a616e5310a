using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.DTOs;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 宠物进化控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class PetEvolutionController : ControllerBase
    {
        private readonly IPetEvolutionService _evolutionService;
        private readonly ILogger<PetEvolutionController> _logger;

        public PetEvolutionController(IPetEvolutionService evolutionService, ILogger<PetEvolutionController> logger)
        {
            _evolutionService = evolutionService;
            _logger = logger;
        }

        /// <summary>
        /// 执行宠物进化
        /// </summary>
        /// <param name="request">进化请求</param>
        /// <returns>进化结果</returns>
        [HttpPost("evolve")]
        public async Task<IActionResult> EvolvePet([FromBody] EvolutionRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ApiResponse<object>
                    {
                        Success = false,
                        Message = "请求参数无效",
                        Data = ModelState
                    });
                }

                var userId = GetCurrentUserId(); // 从JWT或Session获取用户ID
                if (userId == null)
                {
                    return CreateUnauthorizedResponse();
                }

                var result = await _evolutionService.EvolvePetAsync(userId.Value, request);

                if (result.Success)
                {
                    return Ok(new ApiResponse<EvolutionResultDto>
                    {
                        Success = true,
                        Message = "进化成功！",
                        Data = result
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<object>
                    {
                        Success = false,
                        Message = result.Message
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "宠物进化异常");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = "系统错误，请稍后重试"
                });
            }
        }

        /// <summary>
        /// 获取宠物进化信息
        /// </summary>
        /// <param name="userPetId">用户宠物ID</param>
        /// <returns>进化信息</returns>
        [HttpGet("{userPetId}/info")]
        public async Task<IActionResult> GetPetEvolutionInfo(int userPetId)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return CreateUnauthorizedResponse();
                }

                var info = await _evolutionService.GetPetEvolutionInfoAsync(userId.Value, userPetId);

                    if (info == null)
                {
                    return NotFound(new ApiResponse<object>
                    {
                        Success = false,
                        Message = "宠物不存在或不属于您",
                        ErrorCode = "PET_NOT_FOUND"
                    });
                }

                return Ok(new ApiResponse<PetEvolutionInfoDto>
                {
                    Success = true,
                    Message = "获取成功",
                    Data = info
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物进化信息异常");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = "系统错误，请稍后重试",
                    ErrorCode = "INTERNAL_ERROR"
                });
            }
        }

        /// <summary>
        /// 获取宠物进化历史
        /// </summary>
        /// <param name="userPetId">用户宠物ID</param>
        /// <returns>进化历史</returns>
        [HttpGet("{userPetId}/history")]
        public async Task<IActionResult> GetEvolutionHistory(int userPetId)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return CreateUnauthorizedResponse();
                }

                var history = await _evolutionService.GetEvolutionHistoryAsync(userId.Value, userPetId);

                return Ok(new ApiResponse<List<EvolutionHistoryDto>>
                {
                    Success = true,
                    Message = "获取成功",
                    Data = history
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取进化历史异常");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = "系统错误，请稍后重试",
                    ErrorCode = "INTERNAL_ERROR"
                });
            }
        }

        /// <summary>
        /// 获取进化配置
        /// </summary>
        /// <param name="petNo">宠物编号</param>
        /// <param name="evolutionType">进化类型</param>
        /// <returns>进化配置</returns>
        [HttpGet("config/{petNo}/{evolutionType}")]
        public async Task<IActionResult> GetEvolutionConfig(int petNo, string evolutionType)
        {
            try
            {
                if (evolutionType != "A" && evolutionType != "B")
                {
                    return BadRequest(new ApiResponse<object>
                    {
                        Success = false,
                        Message = "进化类型只能是A或B"
                    });
                }

                var config = await _evolutionService.GetEvolutionConfigAsync(petNo, evolutionType);

                if (config == null)
                {
                    return NotFound(new ApiResponse<object>
                    {
                        Success = false,
                        Message = "进化配置不存在"
                    });
                }

                return Ok(new ApiResponse<object>
                {
                    Success = true,
                    Message = "获取成功",
                    Data = new
                    {
                        config.pet_no,
                        config.evolution_type,
                        config.target_pet_no,
                        config.required_level,
                        config.required_item_id,
                        config.required_item_count,
                        config.cost_gold,
                        config.growth_min,
                        config.growth_max,
                        config.success_rate
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取进化配置异常");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = "系统错误，请稍后重试"
                });
            }
        }

        /// <summary>
        /// 批量获取宠物进化信息
        /// </summary>
        /// <param name="userPetIds">用户宠物ID列表</param>
        /// <returns>进化信息列表</returns>
        [HttpPost("batch-info")]
        public async Task<IActionResult> GetBatchPetEvolutionInfo([FromBody] List<int> userPetIds)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return CreateUnauthorizedResponse();
                }

                var results = new List<PetEvolutionInfoDto>();

                foreach (var petId in userPetIds)
                {
                    var info = await _evolutionService.GetPetEvolutionInfoAsync(userId.Value, petId);
                    if (info != null)
                    {
                        results.Add(info);
                    }
                }

                return Ok(new ApiResponse<List<PetEvolutionInfoDto>>
                {
                    Success = true,
                    Message = "获取成功",
                    Data = results
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量获取宠物进化信息异常");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = "系统错误，请稍后重试",
                    ErrorCode = "INTERNAL_ERROR"
                });
            }
        }

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        /// <returns>用户ID，如果未登录则返回null</returns>
        private int? GetCurrentUserId()
        {
            // 这里应该从JWT Token或Session中获取用户ID
            // 临时返回固定值用于测试
            var userIdClaim = User.FindFirst("user_id")?.Value;
            if (int.TryParse(userIdClaim, out int userId))
            {
                return userId;
            }

            // 如果没有找到用户ID，可以从Header中获取（用于测试）
            if (Request.Headers.ContainsKey("X-User-Id"))
            {
                if (int.TryParse(Request.Headers["X-User-Id"], out int headerUserId))
                {
                    return headerUserId;
                }
            }

            // 返回null表示未登录，而不是抛出异常
            return null;
        }

        /// <summary>
        /// 创建未授权响应
        /// </summary>
        /// <returns>未授权响应</returns>
        private IActionResult CreateUnauthorizedResponse()
        {
            return Unauthorized(new ApiResponse<object>
            {
                Success = false,
                Message = "用户未登录或Token无效",
                ErrorCode = "UNAUTHORIZED",
                Data = new {
                    RequireLogin = true,
                    LoginUrl = "/login" // 前端可以使用这个URL跳转到登录页面
                }
            });
        }
    }

    /// <summary>
    /// API响应模型
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    public class ApiResponse<T>
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string ErrorCode { get; set; }

        /// <summary>
        /// 数据
        /// </summary>
        public T Data { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public long Timestamp { get; set; } = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
    }
}
