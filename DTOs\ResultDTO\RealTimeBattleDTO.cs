namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 实时战斗推送消息DTO
    /// </summary>
    public class RealTimeBattleDTO
    {
        /// <summary>
        /// 消息类型
        /// </summary>
        public string Type { get; set; } = "battle";

        /// <summary>
        /// 战斗ID
        /// </summary>
        public string BattleId { get; set; }

        /// <summary>
        /// 玩家ID
        /// </summary>
        public int PlayerId { get; set; }

        /// <summary>
        /// 玩家名称
        /// </summary>
        public string PlayerName { get; set; }

        /// <summary>
        /// 地图ID
        /// </summary>
        public int MapId { get; set; }

        /// <summary>
        /// 地图名称
        /// </summary>
        public string MapName { get; set; }

        /// <summary>
        /// 战斗状态 (start/fighting/end)
        /// </summary>
        public string BattleStatus { get; set; }

        /// <summary>
        /// 回合数
        /// </summary>
        public int Round { get; set; }

        /// <summary>
        /// 战斗结果 (win/lose/fighting)
        /// </summary>
        public string Result { get; set; }

        /// <summary>
        /// 玩家当前生命值
        /// </summary>
        public int PlayerCurrentHp { get; set; }

        /// <summary>
        /// 玩家最大生命值
        /// </summary>
        public int PlayerMaxHp { get; set; }

        /// <summary>
        /// 怪物当前生命值
        /// </summary>
        public int MonsterCurrentHp { get; set; }

        /// <summary>
        /// 怪物最大生命值
        /// </summary>
        public int MonsterMaxHp { get; set; }

        /// <summary>
        /// 本回合伤害描述
        /// </summary>
        public string DamageDescription { get; set; }

        /// <summary>
        /// 战斗奖励（战斗结束时）
        /// </summary>
        public BattleRewardInfo? Reward { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 战斗奖励信息
    /// </summary>
    public class BattleRewardInfo
    {
        /// <summary>
        /// 获得经验
        /// </summary>
        public int Experience { get; set; }

        /// <summary>
        /// 获得金币
        /// </summary>
        public int Gold { get; set; }

        /// <summary>
        /// 是否升级
        /// </summary>
        public bool LevelUp { get; set; }

        /// <summary>
        /// 掉落道具列表
        /// </summary>
        public List<DropItemInfo> DropItems { get; set; } = new List<DropItemInfo>();
    }

    /// <summary>
    /// 掉落道具信息
    /// </summary>
    public class DropItemInfo
    {
        /// <summary>
        /// 道具ID
        /// </summary>
        public int ItemId { get; set; }

        /// <summary>
        /// 道具名称
        /// </summary>
        public string ItemName { get; set; }

        /// <summary>
        /// 掉落数量
        /// </summary>
        public int Count { get; set; }
    }
} 