using WebApplication_HM.Interface;
using WebApplication_HM.Models;
using SqlSugar;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 道具数据访问实现
    /// </summary>
    public class PropRepository : IPropRepository
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<PropRepository> _logger;
        
        public PropRepository(ISqlSugarClient db, ILogger<PropRepository> logger)
        {
            _db = db;
            _logger = logger;
        }

        #region 用户道具管理

        /// <summary>
        /// 获取用户所有道具（对应原GetPAP方法）
        /// </summary>
        public async Task<List<PropInfo>> GetUserItemsAsync(int userId)
        {
            try
            {
                var userItems = await _db.Queryable<user_item, item_config>((ui, ic) => new JoinQueryInfos(
                    JoinType.Left, ui.item_id == ic.item_no.ToString()))
                    .Where((ui, ic) => ui.user_id == userId)
                    .Select((ui, ic) => new { ui, ic })
                    .ToListAsync();

                return userItems.Select(x => PropInfo.FromUserItem(x.ui, x.ic)).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户道具列表失败，用户ID: {UserId}", userId);
                return new List<PropInfo>();
            }
        }

        /// <summary>
        /// 按道具ID获取用户道具（对应原GetAP_ID方法）
        /// </summary>
        public async Task<PropInfo> GetUserItemByIdAsync(int userId, string itemId)
        {
            try
            {
                var userItem = await _db.Queryable<user_item>()
                    .Where(x => x.item_id == itemId && x.user_id == userId)
                    .FirstAsync();
                
                if (userItem == null) return null;
                
                var config = await _db.Queryable<item_config>()
                    .Where(x => x.item_no.ToString() == itemId)
                    .FirstAsync();
                    
                return PropInfo.FromUserItem(userItem, config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "按ID获取用户道具失败，道具ID: {ItemId}, 用户ID: {UserId}", itemId, userId);
                return null;
            }
        }

        /// <summary>
        /// 按序号获取道具（对应原GetAP_XH方法）
        /// </summary>
        public async Task<PropInfo> GetItemBySeqAsync(int itemSeq)
        {
            try
            {
                var userItem = await _db.Queryable<user_item>()
                    .Where(x => x.item_seq == itemSeq)
                    .FirstAsync();

                if (userItem == null) return null;

                item_config config = null;
                try
                {
                    // 尝试将item_id转换为数字并查找配置
                    if (int.TryParse(userItem.item_id, out int itemNo))
                    {
                        config = await _db.Queryable<item_config>()
                            .Where(x => x.item_no == itemNo)
                            .FirstAsync();
                    }
                }
                catch (Exception configEx)
                {
                    _logger.LogWarning(configEx, "获取道具配置失败，道具ID: {ItemId}", userItem.item_id);
                }

                return PropInfo.FromUserItem(userItem, config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "按序号获取道具失败，序号: {ItemSeq}", itemSeq);
                return null;
            }
        }

        /// <summary>
        /// 获取用户指定位置的道具（对应原GetALPP方法）
        /// </summary>
        public async Task<List<PropInfo>> GetUserItemsByPositionAsync(int userId, int position)
        {
            try
            {
                // 先获取用户道具
                var userItems = await _db.Queryable<user_item>()
                    .Where(ui => ui.user_id == userId && ui.item_pos == position)
                    .ToListAsync();

                var result = new List<PropInfo>();

                // 为每个道具获取配置信息
                foreach (var userItem in userItems)
                {
                    item_config config = null;
                    try
                    {
                        // 尝试将item_id转换为数字并查找配置
                        if (int.TryParse(userItem.item_id, out int itemNo))
                        {
                            config = await _db.Queryable<item_config>()
                                .Where(ic => ic.item_no == itemNo)
                                .FirstAsync();
                        }
                    }
                    catch (Exception configEx)
                    {
                        _logger.LogWarning(configEx, "获取道具配置失败，道具ID: {ItemId}", userItem.item_id);
                    }

                    result.Add(PropInfo.FromUserItem(userItem, config));
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户指定位置道具失败，用户ID: {UserId}, 位置: {Position}", userId, position);
                return new List<PropInfo>();
            }
        }

        /// <summary>
        /// 添加道具到用户背包（对应原AddPlayerProp方法）
        /// </summary>
        public async Task<bool> AddUserItemAsync(int userId, string itemId, long count, int? position = null)
        {
            try
            {
                // 验证道具是否存在
                var config = await _db.Queryable<item_config>()
                    .Where(x => x.item_no.ToString() == itemId)
                    .FirstAsync();
                
                if (config == null)
                {
                    _logger.LogWarning("尝试添加不存在的道具，道具ID: {ItemId}", itemId);
                    return false;
                }

                // 检查用户是否已有该道具
                var existingItem = await _db.Queryable<user_item>()
                    .Where(x => x.user_id == userId && x.item_id == itemId)
                    .FirstAsync();

                if (existingItem != null)
                {
                    // 更新数量
                    existingItem.item_count += count;
                    return await _db.Updateable(existingItem).ExecuteCommandAsync() > 0;
                }
                else
                {
                    // 生成新的序号
                    var newSeq = await GenerateNewItemSeqAsync(userId);
                    
                    var newItem = new user_item
                    {
                        user_id = userId,
                        item_id = itemId,
                        item_count = count,
                        item_pos = position ?? 1, // 道具位置不能为空，默认为1（背包）
                        item_seq = newSeq
                    };
                    
                    return await _db.Insertable(newItem).ExecuteCommandAsync() > 0;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加道具失败，用户ID: {UserId}, 道具ID: {ItemId}, 数量: {Count}", userId, itemId, count);
                return false;
            }
        }

        /// <summary>
        /// 修改或删除道具（对应原ReviseOrDeletePP方法）
        /// </summary>
        public async Task<bool> UpdateOrDeleteItemAsync(int itemSeq, long newCount)
        {
            try
            {
                var userItem = await _db.Queryable<user_item>()
                    .Where(x => x.item_seq == itemSeq)
                    .FirstAsync();
                
                if (userItem == null) return false;

                if (newCount <= 0)
                {
                    // 删除道具
                    return await _db.Deleteable<user_item>()
                        .Where(x => x.item_seq == itemSeq)
                        .ExecuteCommandAsync() > 0;
                }
                else
                {
                    // 修改数量
                    userItem.item_count = newCount;
                    return await _db.Updateable(userItem).ExecuteCommandAsync() > 0;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "修改或删除道具失败，序号: {ItemSeq}, 新数量: {NewCount}", itemSeq, newCount);
                return false;
            }
        }

        /// <summary>
        /// 删除道具
        /// </summary>
        public async Task<bool> DeleteItemAsync(int itemSeq)
        {
            try
            {
                return await _db.Deleteable<user_item>()
                    .Where(x => x.item_seq == itemSeq)
                    .ExecuteCommandAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除道具失败，序号: {ItemSeq}", itemSeq);
                return false;
            }
        }

        /// <summary>
        /// 更新道具数量
        /// </summary>
        public async Task<bool> UpdateItemCountAsync(int itemSeq, long newCount)
        {
            try
            {
                return await _db.Updateable<user_item>()
                    .SetColumns(x => x.item_count == newCount)
                    .Where(x => x.item_seq == itemSeq)
                    .ExecuteCommandAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新道具数量失败，序号: {ItemSeq}, 新数量: {NewCount}", itemSeq, newCount);
                return false;
            }
        }

        /// <summary>
        /// 更新道具位置
        /// </summary>
        public async Task<bool> UpdateItemPositionAsync(int itemSeq, int newPosition)
        {
            try
            {
                return await _db.Updateable<user_item>()
                    .SetColumns(x => x.item_pos == newPosition)
                    .Where(x => x.item_seq == itemSeq)
                    .ExecuteCommandAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新道具位置失败，序号: {ItemSeq}, 新位置: {NewPosition}", itemSeq, newPosition);
                return false;
            }
        }

        #endregion

        #region 道具配置管理

        /// <summary>
        /// 获取道具配置信息
        /// </summary>
        public async Task<PropConfig> GetItemConfigAsync(string itemId)
        {
            try
            {
                var config = await _db.Queryable<item_config>()
                    .Where(x => x.item_no.ToString() == itemId)
                    .FirstAsync();
                
                if (config == null) return null;

                var script = await _db.Queryable<item_script>()
                    .Where(x => x.item_no.ToString() == itemId)
                    .FirstAsync();
                
                return PropConfig.FromItemConfig(config, script);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取道具配置失败，道具ID: {ItemId}", itemId);
                return null;
            }
        }

        /// <summary>
        /// 获取道具配置信息（按编号）
        /// </summary>
        public async Task<PropConfig> GetItemConfigByNoAsync(int itemNo)
        {
            try
            {
                var config = await _db.Queryable<item_config>()
                    .Where(x => x.item_no == itemNo)
                    .FirstAsync();
                
                if (config == null) return null;

                var script = await _db.Queryable<item_script>()
                    .Where(x => x.item_no == itemNo)
                    .FirstAsync();
                
                return PropConfig.FromItemConfig(config, script);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取道具配置失败，道具编号: {ItemNo}", itemNo);
                return null;
            }
        }

        /// <summary>
        /// 获取所有道具配置
        /// </summary>
        public async Task<List<PropConfig>> GetAllItemConfigsAsync()
        {
            try
            {
                var configs = await _db.Queryable<item_config, item_script>((ic, iss) => new JoinQueryInfos(
                    JoinType.Left, ic.item_no == iss.item_no))
                    .Select((ic, iss) => new { ic, iss })
                    .ToListAsync();

                return configs.Select(x => PropConfig.FromItemConfig(x.ic, x.iss)).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有道具配置失败");
                return new List<PropConfig>();
            }
        }

        #endregion

        #region 道具脚本管理

        /// <summary>
        /// 获取道具脚本信息
        /// </summary>
        public async Task<PropScriptInfo> GetItemScriptAsync(string itemId)
        {
            try
            {
                var script = await _db.Queryable<item_script>()
                    .Where(x => x.item_no.ToString() == itemId)
                    .FirstAsync();
                
                return script != null ? PropScriptInfo.FromItemScript(script) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取道具脚本失败，道具ID: {ItemId}", itemId);
                return null;
            }
        }

        /// <summary>
        /// 获取道具脚本信息（按编号）
        /// </summary>
        public async Task<PropScriptInfo> GetItemScriptByNoAsync(int itemNo)
        {
            try
            {
                var script = await _db.Queryable<item_script>()
                    .Where(x => x.item_no == itemNo)
                    .FirstAsync();
                
                return script != null ? PropScriptInfo.FromItemScript(script) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取道具脚本失败，道具编号: {ItemNo}", itemNo);
                return null;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 检查用户是否拥有指定道具
        /// </summary>
        public async Task<bool> HasItemAsync(int userId, string itemId)
        {
            try
            {
                return await _db.Queryable<user_item>()
                    .Where(x => x.user_id == userId && x.item_id == itemId)
                    .AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查用户道具失败，用户ID: {UserId}, 道具ID: {ItemId}", userId, itemId);
                return false;
            }
        }

        /// <summary>
        /// 获取用户道具总数
        /// </summary>
        public async Task<int> GetUserItemCountAsync(int userId)
        {
            try
            {
                return await _db.Queryable<user_item>()
                    .Where(x => x.user_id == userId)
                    .CountAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户道具总数失败，用户ID: {UserId}", userId);
                return 0;
            }
        }

        /// <summary>
        /// 生成新的道具序号
        /// </summary>
        public async Task<int> GenerateNewItemSeqAsync(int userId)
        {
            try
            {
                var maxSeq = await _db.Queryable<user_item>()
                    .Where(x => x.user_id == userId)
                    .MaxAsync(x => (int?)x.item_seq);

                return (maxSeq ?? 0) + 1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成道具序号失败，用户ID: {UserId}", userId);
                return 1;
            }
        }

        #endregion
    }
}
