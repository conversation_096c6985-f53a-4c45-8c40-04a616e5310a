using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 等级缓存服务实现
    /// </summary>
    public class LevelCacheService : ILevelCacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly IDistributedCache? _distributedCache;
        private readonly ILogger<LevelCacheService> _logger;
        
        // 缓存键常量
        private const string CACHE_KEY_ALL_LEVELS = "level:all_configs_{0}";
        private const string CACHE_KEY_LEVEL_CALC = "level:calc_{0}_{1}";
        private const string CACHE_KEY_SYSTEM_CONFIG = "level:system_{0}";
        
        // 缓存过期时间
        private static readonly TimeSpan MEMORY_CACHE_EXPIRY = TimeSpan.FromMinutes(30);
        private static readonly TimeSpan DISTRIBUTED_CACHE_EXPIRY = TimeSpan.FromHours(2);
        private static readonly TimeSpan CALC_CACHE_EXPIRY = TimeSpan.FromMinutes(15);

        public LevelCacheService(
            IMemoryCache memoryCache, 
            IDistributedCache? distributedCache,
            ILogger<LevelCacheService> logger)
        {
            _memoryCache = memoryCache;
            _distributedCache = distributedCache;
            _logger = logger;
        }

        /// <summary>
        /// 获取或设置等级配置缓存
        /// </summary>
        public async Task<List<LevelConfig>> GetOrSetLevelConfigsAsync(string systemName, Func<Task<List<LevelConfig>>> factory)
        {
            try
            {
                string cacheKey = string.Format(CACHE_KEY_ALL_LEVELS, systemName);
                
                // 1. 尝试从内存缓存获取
                if (_memoryCache.TryGetValue(cacheKey, out List<LevelConfig>? memoryResult) && memoryResult != null)
                {
                    _logger.LogDebug("从内存缓存获取等级配置，系统: {SystemName}", systemName);
                    return memoryResult;
                }
                
                // 2. 尝试从分布式缓存获取
                if (_distributedCache != null)
                {
                    var distributedResult = await GetFromDistributedCacheAsync<List<LevelConfig>>(cacheKey);
                    if (distributedResult != null)
                    {
                        // 回填内存缓存
                        _memoryCache.Set(cacheKey, distributedResult, MEMORY_CACHE_EXPIRY);
                        _logger.LogDebug("从分布式缓存获取等级配置，系统: {SystemName}", systemName);
                        return distributedResult;
                    }
                }
                
                // 3. 从数据库加载
                var data = await factory();
                
                // 4. 设置缓存
                _memoryCache.Set(cacheKey, data, MEMORY_CACHE_EXPIRY);
                
                if (_distributedCache != null)
                {
                    await SetDistributedCacheAsync(cacheKey, data, DISTRIBUTED_CACHE_EXPIRY);
                }
                
                _logger.LogInformation("从数据库加载并缓存等级配置，系统: {SystemName}, 数量: {Count}", systemName, data.Count);
                return data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取等级配置缓存失败，系统: {SystemName}", systemName);
                return await factory(); // 降级到直接调用工厂方法
            }
        }

        /// <summary>
        /// 缓存等级计算结果
        /// </summary>
        public async Task<int> GetOrCalculateLevelAsync(long exp, string systemName, Func<Task<int>> calculator)
        {
            try
            {
                // 对经验值进行分段缓存，但确保不同等级有不同缓存键
                // 使用更小的分段，避免不同等级共享缓存
                long expSegment = exp / 1000; // 每1000经验一个缓存段，更精确
                string cacheKey = string.Format(CACHE_KEY_LEVEL_CALC, systemName, expSegment);
                
                var result = await _memoryCache.GetOrCreateAsync(cacheKey, async entry =>
                {
                    entry.AbsoluteExpirationRelativeToNow = CALC_CACHE_EXPIRY;
                    var calcResult = await calculator();
                    _logger.LogDebug("缓存等级计算结果，经验段: {ExpSegment}, 系统: {SystemName}, 等级: {Level}",
                        expSegment, systemName, calcResult);
                    return calcResult;
                });

                return result > 0 ? result : 1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "等级计算缓存失败，经验: {Exp}, 系统: {SystemName}", exp, systemName);
                return await calculator(); // 降级到直接计算
            }
        }

        /// <summary>
        /// 清除缓存
        /// </summary>
        public async Task<bool> ClearCacheAsync(string? systemName = null)
        {
            try
            {
                if (string.IsNullOrEmpty(systemName))
                {
                    // 清除所有等级相关缓存
                    _logger.LogInformation("清除所有等级系统缓存");
                    // 注意：IMemoryCache 没有直接清除所有缓存的方法
                    // 这里只能清除已知的缓存键
                }
                else
                {
                    // 清除指定系统的缓存
                    string configCacheKey = string.Format(CACHE_KEY_ALL_LEVELS, systemName);
                    string systemConfigKey = string.Format(CACHE_KEY_SYSTEM_CONFIG, systemName);
                    
                    _memoryCache.Remove(configCacheKey);
                    _memoryCache.Remove(systemConfigKey);
                    
                    if (_distributedCache != null)
                    {
                        await _distributedCache.RemoveAsync(configCacheKey);
                        await _distributedCache.RemoveAsync(systemConfigKey);
                    }
                    
                    _logger.LogInformation("清除等级系统缓存，系统: {SystemName}", systemName);
                }
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清除缓存失败，系统: {SystemName}", systemName);
                return false;
            }
        }

        /// <summary>
        /// 预热缓存
        /// </summary>
        public async Task<bool> WarmupCacheAsync(string systemName = "pet")
        {
            try
            {
                _logger.LogInformation("开始预热等级系统缓存，系统: {SystemName}", systemName);
                
                // 这里需要注入 ILevelRepository 来预热缓存
                // 由于循环依赖问题，暂时返回 true
                // 实际实现中可以通过工厂模式或其他方式解决
                
                _logger.LogInformation("等级系统缓存预热完成，系统: {SystemName}", systemName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "缓存预热失败，系统: {SystemName}", systemName);
                return false;
            }
        }

        #region 私有方法

        /// <summary>
        /// 从分布式缓存获取数据
        /// </summary>
        private async Task<T?> GetFromDistributedCacheAsync<T>(string key) where T : class
        {
            try
            {
                if (_distributedCache == null) return null;
                
                var cachedData = await _distributedCache.GetStringAsync(key);
                if (string.IsNullOrEmpty(cachedData)) return null;
                
                return JsonConvert.DeserializeObject<T>(cachedData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从分布式缓存获取数据失败，键: {Key}", key);
                return null;
            }
        }

        /// <summary>
        /// 设置分布式缓存数据
        /// </summary>
        private async Task SetDistributedCacheAsync<T>(string key, T data, TimeSpan expiry)
        {
            try
            {
                if (_distributedCache == null) return;
                
                var serializedData = JsonConvert.SerializeObject(data);
                var options = new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expiry
                };
                
                await _distributedCache.SetStringAsync(key, serializedData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置分布式缓存失败，键: {Key}", key);
            }
        }

        #endregion
    }
}
