namespace WebApplication_HM.DTOs.RequestDTO
{
    /// <summary>
    /// 游戏配置查询请求DTO
    /// </summary>
    public class ConfigRequestDTO
    {
        /// <summary>
        /// 配置类型（pet、skill、item、monster等）
        /// </summary>
        public string ConfigType { get; set; } = string.Empty;

        /// <summary>
        /// 是否获取详细信息
        /// </summary>
        public bool IncludeDetails { get; set; } = false;
    }
} 