using Microsoft.Extensions.Caching.Memory;
using System.Data;
using MySqlConnector;
using WebApplication_HM.Interface;
using WebApplication_HM.DTOs.RequestDTO;
using WebApplication_HM.DTOs.ResultDTO;
using WebApplication_HM.IServices;
using Newtonsoft.Json;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 地图服务实现
    /// </summary>
    public class MapService : IMapService
    {
        private readonly string _connectionString;
        private readonly IMemoryCache _cache;
        private readonly ILogger<MapService> _logger;
        private readonly IPlayerService _playerService;
        private readonly ILevelService _levelService;
        private readonly IGameEventTriggerService _gameEventTrigger;

        public MapService(IConfiguration configuration, IMemoryCache cache, ILogger<MapService> logger, IPlayerService playerService, ILevelService levelService, IGameEventTriggerService gameEventTrigger)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") ?? throw new ArgumentNullException("ConnectionString");
            _cache = cache;
            _logger = logger;
            _playerService = playerService;
            _levelService = levelService;
            _gameEventTrigger = gameEventTrigger;
        }

        /// <summary>
        /// 获取用户可用地图列表
        /// </summary>
        public async Task<MapListResultDTO> GetAvailableMapsAsync(int userId)
        {
            try
            {
                // 获取用户信息
                var userProgress = await GetUserMapProgressAsync(userId);
                
                // 获取所有地图配置
                var allMaps = await GetAllMapConfigsAsync();
                
                // 为每个地图设置解锁状态
                var mapInfos = new List<MapInfoDTO>();
                foreach (var map in allMaps)
                {
                    var mapInfo = new MapInfoDTO
                    {
                        MapId = map.MapId,
                        MapName = map.MapName,
                        MapDesc = map.MapDesc,
                        Background = map.Background,
                        Icon = map.Icon,
                        MapType = map.MapType,
                        RecommendLevel = map.RecommendLevel,
                        IsUnlocked = userProgress.UnlockedMaps.Contains(map.MapId),
                        UnlockCondition = await GetMapUnlockConditionAsync(map.MapId)
                    };
                    mapInfos.Add(mapInfo);
                }

                return new MapListResultDTO
                {
                    Success = true,
                    Message = "获取地图列表成功",
                    Maps = mapInfos
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取用户地图列表失败 - UserId: {userId}");
                return new MapListResultDTO
                {
                    Success = false,
                    Message = "获取地图列表失败"
                };
            }
        }

        /// <summary>
        /// 获取地图详细信息
        /// </summary>
        public async Task<MapDetailResultDTO> GetMapDetailAsync(int mapId, int userId)
        {
            try
            {
                // 获取地图基础信息
                var mapInfo = await GetMapInfoAsync(mapId);
                if (mapInfo == null)
                {
                    return new MapDetailResultDTO
                    {
                        Success = false,
                        Message = "地图不存在"
                    };
                }

                // 检查用户是否解锁该地图
                var userProgress = await GetUserMapProgressAsync(userId);
                mapInfo.IsUnlocked = userProgress.UnlockedMaps.Contains(mapId);

                // 获取地图怪物
                var monsters = await GetMapMonstersAsync(mapId);

                // 获取地图掉落
                var drops = await GetMapDropsAsync(mapId);

                return new MapDetailResultDTO
                {
                    Success = true,
                    Message = "获取地图详情成功",
                    MapInfo = mapInfo,
                    Monsters = monsters,
                    Drops = drops
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取地图详情失败 - MapId: {mapId}, UserId: {userId}");
                return new MapDetailResultDTO
                {
                    Success = false,
                    Message = "获取地图详情失败"
                };
            }
        }

        /// <summary>
        /// 检查用户是否可以进入地图
        /// </summary>
        public async Task<bool> CanEnterMapAsync(int userId, int mapId)
        {
            try
            {
                var userProgress = await GetUserMapProgressAsync(userId);
                return userProgress.UnlockedMaps.Contains(mapId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"检查地图进入权限失败 - UserId: {userId}, MapId: {mapId}");
                return false;
            }
        }

        /// <summary>
        /// 获取地图怪物列表
        /// </summary>
        public async Task<List<MapMonsterDTO>> GetMapMonstersAsync(int mapId)
        {
            try
            {
                // 尝试从缓存获取
                var cacheKey = $"map_monsters_{mapId}";
                if (_cache.TryGetValue(cacheKey, out List<MapMonsterDTO>? cachedMonsters) && cachedMonsters != null)
                {
                    return cachedMonsters;
                }

                var monsters = new List<MapMonsterDTO>();

                using var connection = new MySqlConnection(_connectionString);
                await connection.OpenAsync();

                var sql = @"
                    SELECT monster_id, monster_name, growth, element, min_level, max_level
                    FROM map_monster 
                    WHERE map_id = @mapId
                    ORDER BY monster_id";

                using var command = new MySqlCommand(sql, connection);
                command.Parameters.AddWithValue("@mapId", mapId);

                using var reader = await command.ExecuteReaderAsync();
                var random = new Random();
                while (await reader.ReadAsync())
                {
                    var minLevel = reader.GetInt32("min_level");
                    var maxLevel = reader.GetInt32("max_level");

                    // 确保最小等级不大于最大等级
                    if (minLevel > maxLevel)
                    {
                        _logger.LogWarning($"地图 {mapId} 怪物 {reader.GetInt32("monster_id")} 的等级配置错误: minLevel({minLevel}) > maxLevel({maxLevel})，已自动纠正");
                        (minLevel, maxLevel) = (maxLevel, minLevel); // 交换值
                    }

                    // 确保等级值有效（至少为1）
                    minLevel = Math.Max(1, minLevel);
                    maxLevel = Math.Max(1, maxLevel);

                    // 如果两个值相等，确保maxLevel至少比minLevel大1（为了random.Next的要求）
                    if (minLevel == maxLevel)
                    {
                        maxLevel = minLevel + 1;
                    }

                    monsters.Add(new MapMonsterDTO
                    {
                        MonsterId = reader.GetInt32("monster_id"),
                        MonsterName = reader.GetString("monster_name"),
                        Growth = reader.GetInt64("growth"),
                        Element = reader.GetString("element"),
                        MinLevel = minLevel,
                        MaxLevel = maxLevel,
                        CurrentLevel = random.Next(minLevel, maxLevel + 1) // 在最小和最大等级之间随机生成
                    });
                }

                // 缓存结果（1小时）
                _cache.Set(cacheKey, monsters, TimeSpan.FromHours(1));

                return monsters;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取地图怪物失败 - MapId: {mapId}");
                return new List<MapMonsterDTO>();
            }
        }

        /// <summary>
        /// 获取地图掉落信息
        /// </summary>
        public async Task<List<MapDropDTO>> GetMapDropsAsync(int mapId)
        {
            try
            {
                // 尝试从缓存获取
                var cacheKey = $"map_drops_{mapId}";
                if (_cache.TryGetValue(cacheKey, out List<MapDropDTO>? cachedDrops) && cachedDrops != null)
                {
                    return cachedDrops;
                }

                var drops = new List<MapDropDTO>();

                using var connection = new MySqlConnection(_connectionString);
                await connection.OpenAsync();

                // 这里需要根据实际的掉落表结构来查询
                // 暂时返回模拟数据，后续需要根据实际数据库结构调整
                var sql = @"
                    SELECT md.limit_level, md.min_gold, md.max_gold, md.min_yuanbao, md.max_yuanbao
                    FROM map_detail md 
                    WHERE md.map_id = @mapId";

                using var command = new MySqlCommand(sql, connection);
                command.Parameters.AddWithValue("@mapId", mapId);

                using var reader = await command.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    var minGold = reader.GetInt64("min_gold");
                    var maxGold = reader.GetInt64("max_gold");
                    var minYuanbao = reader.GetInt32("min_yuanbao");
                    var maxYuanbao = reader.GetInt32("max_yuanbao");

                    if (minGold > 0 || maxGold > 0)
                    {
                        drops.Add(new MapDropDTO
                        {
                            ItemId = 1, // 金币的道具ID
                            ItemName = "金币",
                            DropRate = 0.8m,
                            MinQuantity = (int)minGold,
                            MaxQuantity = (int)maxGold
                        });
                    }

                    if (minYuanbao > 0 || maxYuanbao > 0)
                    {
                        drops.Add(new MapDropDTO
                        {
                            ItemId = 2, // 元宝的道具ID
                            ItemName = "元宝",
                            DropRate = 0.3m,
                            MinQuantity = minYuanbao,
                            MaxQuantity = maxYuanbao
                        });
                    }
                }

                // 缓存结果（1小时）
                _cache.Set(cacheKey, drops, TimeSpan.FromHours(1));

                return drops;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取地图掉落失败 - MapId: {mapId}");
                return new List<MapDropDTO>();
            }
        }

        /// <summary>
        /// 进入地图
        /// </summary>
        public async Task<EnterMapResultDTO> EnterMapAsync(int userId, int mapId, int petId)
        {
            try
            {
                // 检查用户是否可以进入地图
                //if (!await CanEnterMapAsync(userId, mapId))
                //{
                //    return new EnterMapResultDTO
                //    {
                //        Success = false,
                //        Message = "该地图尚未解锁"
                //    };
                //}

                // 检查宠物是否属于用户
                // TODO: 添加宠物验证逻辑

                // 获取地图战斗信息
                var monsters = await GetMapMonstersAsync(mapId);
                var rewards = await GetMapDropsAsync(mapId);

                var battleInfo = new MapBattleInfoDTO
                {
                    MapId = mapId,
                    UserId = userId,
                    PetId = petId,
                    Monsters = monsters,
                    Rewards = rewards,
                    BattleStartTime = DateTime.Now
                };

                return new EnterMapResultDTO
                {
                    Success = true,
                    Message = "成功进入地图",
                    BattleInfo = battleInfo
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"进入地图失败 - UserId: {userId}, MapId: {mapId}, PetId: {petId}");
                return new EnterMapResultDTO
                {
                    Success = false,
                    Message = "进入地图失败"
                };
            }
        }

        /// <summary>
        /// 解锁地图
        /// </summary>
        public async Task<UnlockMapResultDTO> UnlockMapAsync(int userId, int mapId, string unlockMethod)
        {
            try
            {
                // 检查解锁条件
                var checkResult = await CheckMapUnlockConditionsAsync(userId, mapId);
                if (!checkResult.CanUnlock)
                {
                    return new UnlockMapResultDTO
                    {
                        Success = false,
                        Message = string.Join(", ", checkResult.Reasons)
                    };
                }

                // 更新用户的解锁地图列表
                var userProgress = await GetUserMapProgressAsync(userId);
                if (!userProgress.UnlockedMaps.Contains(mapId))
                {
                    userProgress.UnlockedMaps.Add(mapId);
                    await UpdateUserMapProgressAsync(userId, userProgress);
                }

                // 获取地图信息
                var mapInfo = await GetMapInfoAsync(mapId);
                if (mapInfo != null)
                {
                    mapInfo.IsUnlocked = true;
                }

                return new UnlockMapResultDTO
                {
                    Success = true,
                    Message = "地图解锁成功",
                    MapInfo = mapInfo
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"解锁地图失败 - UserId: {userId}, MapId: {mapId}");
                return new UnlockMapResultDTO
                {
                    Success = false,
                    Message = "解锁地图失败"
                };
            }
        }

        /// <summary>
        /// 更新地图进度
        /// </summary>
        public async Task<UpdateMapProgressResultDTO> UpdateMapProgressAsync(int userId, int mapId, int score, TimeSpan completionTime)
        {
            try
            {
                var userProgress = await GetUserMapProgressAsync(userId);
                
                // 检查是否创造新记录
                var isNewRecord = false;
                var newBestScore = score;

                if (userProgress.MapBestScores.ContainsKey(mapId))
                {
                    if (score > userProgress.MapBestScores[mapId])
                    {
                        userProgress.MapBestScores[mapId] = score;
                        isNewRecord = true;
                    }
                    else
                    {
                        newBestScore = userProgress.MapBestScores[mapId];
                    }
                }
                else
                {
                    userProgress.MapBestScores[mapId] = score;
                    isNewRecord = true;
                }

                // 更新通关次数
                if (userProgress.MapCompletionCounts.ContainsKey(mapId))
                {
                    userProgress.MapCompletionCounts[mapId]++;
                }
                else
                {
                    userProgress.MapCompletionCounts[mapId] = 1;
                }

                // 保存进度
                await UpdateUserMapProgressAsync(userId, userProgress);

                // 🎯 触发任务事件
                try
                {
                    // 触发副本完成事件
                    await _gameEventTrigger.OnDungeonCompletedAsync(userId, mapId.ToString(), 1);

                    // 获取地图怪物信息，触发击杀怪物事件
                    var monsters = await GetMapMonstersAsync(mapId);
                    foreach (var monster in monsters)
                    {
                        // 假设每次战斗击杀1只怪物（实际应该根据战斗结果确定）
                        await _gameEventTrigger.OnMonsterKilledAsync(userId, monster.MonsterId.ToString(), 1);
                    }

                    _logger.LogInformation("地图战斗任务事件触发成功: UserId={UserId}, MapId={MapId}", userId, mapId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "触发地图战斗任务事件失败: UserId={UserId}, MapId={MapId}", userId, mapId);
                    // 不影响主流程，只记录错误
                }

                return new UpdateMapProgressResultDTO
                {
                    Success = true,
                    Message = isNewRecord ? "恭喜！创造了新记录！" : "战斗完成",
                    NewBestScore = newBestScore,
                    IsNewRecord = isNewRecord
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新地图进度失败 - UserId: {userId}, MapId: {mapId}");
                return new UpdateMapProgressResultDTO
                {
                    Success = false,
                    Message = "更新地图进度失败"
                };
            }
        }

        /// <summary>
        /// 获取用户地图进度
        /// </summary>
        public async Task<UserMapProgressDTO> GetUserMapProgressAsync(int userId)
        {
            try
            {
                // 尝试从缓存获取
                var cacheKey = $"user_map_progress_{userId}";
                if (_cache.TryGetValue(cacheKey, out UserMapProgressDTO? cachedProgress) && cachedProgress != null)
                {
                    return cachedProgress;
                }

                using var connection = new MySqlConnection(_connectionString);
                await connection.OpenAsync();

                var sql = "SELECT open_maps FROM user WHERE id = @userId";
                using var command = new MySqlCommand(sql, connection);
                command.Parameters.AddWithValue("@userId", userId);

                var openMapsJson = await command.ExecuteScalarAsync() as string;

                var progress = new UserMapProgressDTO
                {
                    UserId = userId
                };

                if (!string.IsNullOrEmpty(openMapsJson))
                {
                    try
                    {
                        var openMapsData = JsonConvert.DeserializeObject<Dictionary<string, object>>(openMapsJson);

                        if (openMapsData?.ContainsKey("unlockedMaps") == true)
                        {
                            var unlockedMaps = JsonConvert.DeserializeObject<List<int>>(openMapsData["unlockedMaps"].ToString() ?? "[]");
                            progress.UnlockedMaps = unlockedMaps ?? new List<int>();
                        }

                        if (openMapsData?.ContainsKey("bestScores") == true)
                        {
                            var bestScores = JsonConvert.DeserializeObject<Dictionary<int, int>>(openMapsData["bestScores"].ToString() ?? "{}");
                            progress.MapBestScores = bestScores ?? new Dictionary<int, int>();
                        }

                        if (openMapsData?.ContainsKey("completionCounts") == true)
                        {
                            var completionCounts = JsonConvert.DeserializeObject<Dictionary<int, int>>(openMapsData["completionCounts"].ToString() ?? "{}");
                            progress.MapCompletionCounts = completionCounts ?? new Dictionary<int, int>();
                        }
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogWarning(ex, $"解析用户地图进度JSON失败 - UserId: {userId}");
                    }
                }

                // 确保至少解锁第一个地图（新手训练营）
                if (!progress.UnlockedMaps.Contains(1))
                {
                    progress.UnlockedMaps.Add(1);
                    await UpdateUserMapProgressAsync(userId, progress);
                }

                // 缓存结果（5分钟）
                _cache.Set(cacheKey, progress, TimeSpan.FromMinutes(5));

                return progress;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取用户地图进度失败 - UserId: {userId}");
                return new UserMapProgressDTO { UserId = userId, UnlockedMaps = new List<int> { 1 } };
            }
        }

        /// <summary>
        /// 检查地图解锁条件
        /// </summary>
        public async Task<MapUnlockCheckResultDTO> CheckMapUnlockConditionsAsync(int userId, int mapId)
        {
            try
            {
                var result = new MapUnlockCheckResultDTO
                {
                    CanUnlock = true
                };

                // 获取用户信息
                var playerInfo = _playerService.GetPlayerInfo(new PlayerInfoRequestDTO { UserId = userId });
                if (playerInfo?.PlayerInfo == null)
                {
                    result.CanUnlock = false;
                    result.Reasons.Add("用户信息不存在");
                    return result;
                }

                // 获取主战宠物的等级
                var currentLevel = 1;
                var currentGrowth = 0m;

                // 通过PlayerService获取主战宠物信息
                var mainPetInfo = await _playerService.GetMainPet(userId);
                if (mainPetInfo.Success && mainPetInfo.PetInfo != null)
                {
                    currentLevel = mainPetInfo.PetInfo.等级;
                    currentGrowth = mainPetInfo.PetInfo.成长;
                }

                result.CurrentLevel = currentLevel;
                result.CurrentGrowth = currentGrowth;

                // 获取地图详情
                var mapDetail = await GetMapDetailInfoAsync(mapId);
                if (mapDetail == null)
                {
                    result.CanUnlock = false;
                    result.Reasons.Add("地图不存在");
                    return result;
                }

                result.RequiredLevel = mapDetail.LimitLevel;
                result.RequiredGrowth = mapDetail.LimitGrowth;

                // 检查等级要求
                if (currentLevel < mapDetail.LimitLevel)
                {
                    result.CanUnlock = false;
                    result.Reasons.Add($"等级不足，需要{mapDetail.LimitLevel}级，当前{currentLevel}级");
                }

                // 检查成长要求（暂时跳过，因为需要从主宠物获取成长值）
                // TODO: 实现从主宠物获取成长值的逻辑
                /*
                if (playerInfo.PlayerInfo.Growth < mapDetail.LimitGrowth)
                {
                    result.CanUnlock = false;
                    result.Reasons.Add($"成长不足，需要{mapDetail.LimitGrowth}成长");
                }
                */

                // 检查前置地图（简单逻辑：地图ID-1必须已解锁）
                if (mapId > 1)
                {
                    var userProgress = await GetUserMapProgressAsync(userId);
                    var prerequisiteMapId = mapId - 1;
                    if (!userProgress.UnlockedMaps.Contains(prerequisiteMapId))
                    {
                        result.CanUnlock = false;
                        result.Reasons.Add($"需要先完成地图{prerequisiteMapId}");
                        result.MissingPrerequisiteMaps.Add(prerequisiteMapId);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"检查地图解锁条件失败 - UserId: {userId}, MapId: {mapId}");
                return new MapUnlockCheckResultDTO
                {
                    CanUnlock = false,
                    Reasons = new List<string> { "检查解锁条件失败" }
                };
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 获取所有地图配置
        /// </summary>
        private async Task<List<dynamic>> GetAllMapConfigsAsync()
        {
            var cacheKey = "all_map_configs";
            if (_cache.TryGetValue(cacheKey, out List<dynamic>? cachedConfigs) && cachedConfigs != null)
            {
                return cachedConfigs;
            }

            var configs = new List<dynamic>();

            using var connection = new MySqlConnection(_connectionString);
            await connection.OpenAsync();

            var sql = @"
                SELECT mc.map_id, mc.map_name, mc.map_desc, mc.background, mc.ico as icon, mc.type as map_type,
                       md.limit_level as recommend_level
                FROM map_config mc
                LEFT JOIN map_detail md ON mc.map_id = md.map_id
                ORDER BY mc.map_id";

            using var command = new MySqlCommand(sql, connection);
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                configs.Add(new
                {
                    MapId = reader.GetInt32("map_id"),
                    MapName = reader.GetString("map_name"),
                    MapDesc = reader.IsDBNull("map_desc") ? "" : reader.GetString("map_desc"),
                    Background = reader.IsDBNull("background") ? "" : reader.GetString("background"),
                    Icon = reader.IsDBNull("icon") ? "" : reader.GetString("icon"),
                    MapType = reader.IsDBNull("map_type") ? 0 : reader.GetInt32("map_type"),
                    RecommendLevel = reader.IsDBNull("recommend_level") ? 1 : reader.GetInt32("recommend_level")
                });
            }

            // 缓存1小时
            _cache.Set(cacheKey, configs, TimeSpan.FromHours(1));

            return configs;
        }

        /// <summary>
        /// 获取地图信息
        /// </summary>
        private async Task<MapInfoDTO?> GetMapInfoAsync(int mapId)
        {
            var allMaps = await GetAllMapConfigsAsync();
            var mapConfig = allMaps.FirstOrDefault(m => m.MapId == mapId);

            if (mapConfig == null) return null;

            return new MapInfoDTO
            {
                MapId = mapConfig.MapId,
                MapName = mapConfig.MapName,
                MapDesc = mapConfig.MapDesc,
                Background = mapConfig.Background,
                Icon = mapConfig.Icon,
                MapType = mapConfig.MapType,
                RecommendLevel = mapConfig.RecommendLevel,
                UnlockCondition = await GetMapUnlockConditionAsync(mapId)
            };
        }

        /// <summary>
        /// 获取地图详情信息
        /// </summary>
        private async Task<dynamic?> GetMapDetailInfoAsync(int mapId)
        {
            using var connection = new MySqlConnection(_connectionString);
            await connection.OpenAsync();

            var sql = "SELECT * FROM map_detail WHERE map_id = @mapId";
            using var command = new MySqlCommand(sql, connection);
            command.Parameters.AddWithValue("@mapId", mapId);

            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return new
                {
                    MapId = reader.GetInt32("map_id"),
                    LimitLevel = reader.GetInt32("limit_level"),
                    LimitGrowth = reader.GetDecimal("limit_growth"),
                    LimitKey = reader.GetBoolean("limit_key"),
                    MinGold = reader.GetInt64("min_gold"),
                    MaxGold = reader.GetInt64("max_gold"),
                    MinYuanbao = reader.GetInt32("min_yuanbao"),
                    MaxYuanbao = reader.GetInt32("max_yuanbao")
                };
            }

            return null;
        }

        /// <summary>
        /// 获取地图解锁条件描述
        /// </summary>
        private async Task<string> GetMapUnlockConditionAsync(int mapId)
        {
            var mapDetail = await GetMapDetailInfoAsync(mapId);
            if (mapDetail == null) return "";

            var conditions = new List<string>();

            if (mapDetail.LimitLevel > 1)
            {
                conditions.Add($"等级{mapDetail.LimitLevel}级");
            }

            if (mapDetail.LimitGrowth > 0)
            {
                conditions.Add($"成长{mapDetail.LimitGrowth}");
            }

            if (mapId > 1)
            {
                conditions.Add($"完成地图{mapId - 1}");
            }

            return conditions.Count > 0 ? string.Join("，", conditions) : "无限制";
        }

        /// <summary>
        /// 更新用户地图进度
        /// </summary>
        private async Task UpdateUserMapProgressAsync(int userId, UserMapProgressDTO progress)
        {
            try
            {
                var progressData = new
                {
                    unlockedMaps = progress.UnlockedMaps,
                    bestScores = progress.MapBestScores,
                    completionCounts = progress.MapCompletionCounts,
                    lastUpdated = DateTime.Now
                };

                var progressJson = JsonConvert.SerializeObject(progressData);

                using var connection = new MySqlConnection(_connectionString);
                await connection.OpenAsync();

                var sql = "UPDATE user SET open_maps = @openMaps WHERE id = @userId";
                using var command = new MySqlCommand(sql, connection);
                command.Parameters.AddWithValue("@openMaps", progressJson);
                command.Parameters.AddWithValue("@userId", userId);

                await command.ExecuteNonQueryAsync();

                // 清除缓存
                var cacheKey = $"user_map_progress_{userId}";
                _cache.Remove(cacheKey);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新用户地图进度失败 - UserId: {userId}");
                throw;
            }
        }

        #endregion
    }
}
