using WebApplication_HM.Interface;
using WebApplication_HM.Models.Define;
using WebApplication_HM.Models;

namespace WebApplication_HM.Services.TaskHandlers
{
    /// <summary>
    /// 任务处理器工厂
    /// </summary>
    public class TaskHandlerFactory : ITaskHandlerFactory
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<TaskHandlerFactory> _logger;
        private readonly Dictionary<string, Type> _handlerTypes;

        public TaskHandlerFactory(IServiceProvider serviceProvider, ILogger<TaskHandlerFactory> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _handlerTypes = new Dictionary<string, Type>();
            
            RegisterHandlers();
        }

        /// <summary>
        /// 注册所有任务处理器
        /// </summary>
        private void RegisterHandlers()
        {
            try
            {
                // 注册核心任务处理器
                _handlerTypes[TaskObjectiveTypes.KILL_MONSTER] = typeof(KillMonsterTaskHandler);
                _handlerTypes[TaskObjectiveTypes.COLLECT_ITEM] = typeof(CollectItemTaskHandler);
                _handlerTypes[TaskObjectiveTypes.CURRENCY] = typeof(CurrencyTaskHandler);
                _handlerTypes[TaskObjectiveTypes.REACH_LEVEL] = typeof(ReachLevelTaskHandler);

                // 注册其他任务处理器（待实现）
                // _handlerTypes[TaskObjectiveTypes.PET_GROWTH] = typeof(PetGrowthTaskHandler);
                // _handlerTypes[TaskObjectiveTypes.COLLECT_EQUIPMENT] = typeof(CollectEquipmentTaskHandler);
                // _handlerTypes[TaskObjectiveTypes.VIP_LEVEL] = typeof(VipLevelTaskHandler);
                // _handlerTypes[TaskObjectiveTypes.TIME_LIMIT] = typeof(TimeLimitTaskHandler);
                // _handlerTypes[TaskObjectiveTypes.DUNGEON] = typeof(DungeonTaskHandler);
                // _handlerTypes[TaskObjectiveTypes.CARD] = typeof(CardTaskHandler);
                // _handlerTypes[TaskObjectiveTypes.ONE_CLICK] = typeof(OneClickTaskHandler);
                // _handlerTypes[TaskObjectiveTypes.SPECIAL] = typeof(SpecialTaskHandler);

                _logger.LogInformation("任务处理器注册完成，共注册 {Count} 个处理器", _handlerTypes.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注册任务处理器失败");
            }
        }

        /// <summary>
        /// 获取指定类型的任务处理器
        /// </summary>
        public ITaskProgressHandler GetHandler(string objectiveType)
        {
            try
            {
                if (string.IsNullOrEmpty(objectiveType))
                {
                    _logger.LogWarning("任务目标类型为空");
                    return null;
                }

                if (!_handlerTypes.TryGetValue(objectiveType, out Type handlerType))
                {
                    _logger.LogWarning("未找到任务类型 {ObjectiveType} 的处理器", objectiveType);
                    return null;
                }

                var handler = _serviceProvider.GetService(handlerType) as ITaskProgressHandler;
                
                if (handler == null)
                {
                    _logger.LogError("无法创建任务处理器实例: {HandlerType}", handlerType.Name);
                    return null;
                }

                return handler;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务处理器失败: {ObjectiveType}", objectiveType);
                return null;
            }
        }

        /// <summary>
        /// 获取所有已注册的任务处理器
        /// </summary>
        public IEnumerable<ITaskProgressHandler> GetAllHandlers()
        {
            var handlers = new List<ITaskProgressHandler>();

            try
            {
                foreach (var handlerType in _handlerTypes.Values)
                {
                    var handler = _serviceProvider.GetService(handlerType) as ITaskProgressHandler;
                    if (handler != null)
                    {
                        handlers.Add(handler);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有任务处理器失败");
            }

            return handlers;
        }

        /// <summary>
        /// 检查是否支持指定的任务类型
        /// </summary>
        public bool IsSupported(string objectiveType)
        {
            return !string.IsNullOrEmpty(objectiveType) && _handlerTypes.ContainsKey(objectiveType);
        }

        /// <summary>
        /// 获取所有支持的任务类型
        /// </summary>
        public IEnumerable<string> GetSupportedObjectiveTypes()
        {
            return _handlerTypes.Keys.ToList();
        }

        /// <summary>
        /// 动态注册任务处理器
        /// </summary>
        public bool RegisterHandler(string objectiveType, Type handlerType)
        {
            try
            {
                if (string.IsNullOrEmpty(objectiveType) || handlerType == null)
                {
                    _logger.LogWarning("注册任务处理器参数无效: ObjectiveType={ObjectiveType}, HandlerType={HandlerType}", 
                        objectiveType, handlerType?.Name);
                    return false;
                }

                if (!typeof(ITaskProgressHandler).IsAssignableFrom(handlerType))
                {
                    _logger.LogWarning("处理器类型必须实现 ITaskProgressHandler 接口: {HandlerType}", handlerType.Name);
                    return false;
                }

                _handlerTypes[objectiveType] = handlerType;
                _logger.LogInformation("动态注册任务处理器成功: {ObjectiveType} -> {HandlerType}", 
                    objectiveType, handlerType.Name);
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "动态注册任务处理器失败: {ObjectiveType} -> {HandlerType}", 
                    objectiveType, handlerType?.Name);
                return false;
            }
        }

        /// <summary>
        /// 取消注册任务处理器
        /// </summary>
        public bool UnregisterHandler(string objectiveType)
        {
            try
            {
                if (string.IsNullOrEmpty(objectiveType))
                {
                    _logger.LogWarning("取消注册任务处理器的目标类型为空");
                    return false;
                }

                var removed = _handlerTypes.Remove(objectiveType);
                
                if (removed)
                {
                    _logger.LogInformation("取消注册任务处理器成功: {ObjectiveType}", objectiveType);
                }
                else
                {
                    _logger.LogWarning("未找到要取消注册的任务处理器: {ObjectiveType}", objectiveType);
                }

                return removed;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消注册任务处理器失败: {ObjectiveType}", objectiveType);
                return false;
            }
        }

        /// <summary>
        /// 获取处理器统计信息
        /// </summary>
        public TaskHandlerStatistics GetStatistics()
        {
            try
            {
                var statistics = new TaskHandlerStatistics
                {
                    TotalRegistered = _handlerTypes.Count,
                    RegisteredTypes = _handlerTypes.Keys.ToList(),
                    AvailableHandlers = new List<string>()
                };

                // 检查哪些处理器实际可用
                foreach (var kvp in _handlerTypes)
                {
                    try
                    {
                        var handler = _serviceProvider.GetService(kvp.Value);
                        if (handler != null)
                        {
                            statistics.AvailableHandlers.Add(kvp.Key);
                        }
                    }
                    catch
                    {
                        // 忽略无法创建的处理器
                    }
                }

                statistics.AvailableCount = statistics.AvailableHandlers.Count;

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取处理器统计信息失败");
                return new TaskHandlerStatistics();
            }
        }

        /// <summary>
        /// 验证目标完成
        /// </summary>
        public async Task<bool> ValidateCompletionAsync(int userId, task_objective objective)
        {
            try
            {
                var handler = GetHandler(objective.objective_type);
                if (handler == null)
                {
                    _logger.LogWarning("未找到任务类型 {ObjectiveType} 的处理器", objective.objective_type);
                    return false;
                }

                var currentProgress = await handler.CheckProgressAsync(userId, objective);
                return currentProgress >= objective.target_amount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证任务完成失败: UserId={UserId}, ObjectiveId={ObjectiveId}",
                    userId, objective.objective_id);
                return false;
            }
        }
    }

    /// <summary>
    /// 任务处理器统计信息
    /// </summary>
    public class TaskHandlerStatistics
    {
        public int TotalRegistered { get; set; }
        public int AvailableCount { get; set; }
        public List<string> RegisteredTypes { get; set; } = new List<string>();
        public List<string> AvailableHandlers { get; set; } = new List<string>();
    }
}
