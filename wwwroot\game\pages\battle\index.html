<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宠物战斗 - Vue版本</title>
    <link rel="stylesheet" href="css/battle.css">
    <style>
        /* 临时样式，确保页面基本显示 */
        body {
            margin: 0;
            padding: 0;
            font-family: '华文新魏', serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
        }
        
        #app {
            width: 100vw;
            height: 100vh;
        }
        
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: white;
            font-size: 24px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="loading">正在加载战斗系统...</div>
    </div>
    
    <!-- Vue.js 3.x CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Pinia 状态管理 -->
    <script src="https://unpkg.com/pinia@2/dist/pinia.iife.js"></script>
    <!-- Axios HTTP客户端 -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    
    <!-- 应用入口 -->
    <script type="module" src="js/main.js"></script>
</body>
</html>
