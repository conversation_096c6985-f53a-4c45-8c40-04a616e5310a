﻿
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>玩家信息</title>
    <link href="Content/CSS/playerInfo.css" rel="stylesheet" />
    <script src="Content/Javascript/jquery-1.8.3.min.js"></script>
    <script src="/game/js/auth-manager.js"></script>
    <script src="/game/js/game-api-adapter.js"></script>
    <link href="Content/CSS/playerInfo1.css" rel="stylesheet" />
    <style>
        .hc img {
            POSITION: ABSOLUTE;
            TOP: 110px;
            left: -80px;
            pointer-events: none;
        }
        .hc {
            background: url(content/img/PetInfo/pet_2_bg.jpg) center top no-repeat;
            width: 313px;
            margin-top: 52px;
            height: 100%;
            float:left;
        }
        #rb_right {
            width: 151px;
            line-height: 24px;
            padding: 5px 0 5px 10px;
            background: url(../img/PetInfo/pet_c1_r.jpg) left center no-repeat;
            float: right;
            border-left: 1px dashed;
        }
        .yingbi {
            POSITION: ABSOLUTE;
            width: 1px;
            height: 1px;
        }
    </style>
    <script type="text/javascript">
		$(function(){
	
			if(window.external.check()=="true"){
			window.external.updatePlayerInfo_page();
				
			
			}
		});

		function setTab(name, cursel, n) {
            for (i = 1; i <= n; i++) {
                var menu = document.getElementById(name + i);
                var con = document.getElementById("con_" + name + "_" + i);
                menu.className = i == cursel ? "on" : "";
                con.style.display = i == cursel ? "block" : "none";
            }
        }

        function ChangeTitle()
        {
        
        }

        function friend(op) {
            var name = $('nickname').value;
            if (name == '' || name.length < 2) {
                window.parent.Alert('请正确输入玩家的角色名称');
                return false;
            }
          	   window.parent.Alert('好友模式尚未开放');
            //$('f'+op).disabled=true;
        }
        function black(op) {
            var name = $('nicknames').value;
            if (name == '') window.parent.Alert('请正确输入玩家的角色名称');

            window.parent.Alert('黑名单模式尚未开放');



        }
     
		function showUserInfo(json){
			try {
				var j = $.parseJSON(json);
				console.log('显示玩家信息:', j);

				// 使用安全的方式更新DOM元素
				updateElementSafely(".账号", j.账号);
				updateElementSafely(".昵称", j.昵称);
				updateElementSafely(".金币", j.金币);
				updateElementSafely(".元宝", j.元宝);
				updateElementSafely(".水晶", j.水晶);
				updateElementSafely(".等级", j.等级);
				updateElementSafely(".VIP等级", j.VIP等级);
				updateElementSafely(".自动购买体力丹", j.自动购买体力丹);
				updateElementSafely(".自动购买魔力丹", j.自动购买魔力丹);
				updateElementSafely(".自动购买经验丹", j.自动购买经验丹);
				updateElementSafely(".时之券", j.时之券);
				updateElementSafely(".体力上限", j.体力上限);
				updateElementSafely(".自动战斗次数", j.自动战斗次数);
				updateElementSafely(".VIP等级", j.vip);
				updateElementSafely(".性别", j.sex);
				updateElementSafely(".论坛ID", j.论坛ID);
				updateElementSafely(".刷新次数", j.刷新次数);
				updateElementSafely(".账号", j.账号);
				updateElementSafely(".VIP权限", j.VIP权限);

			} catch (error) {
				console.error('显示玩家信息失败:', error);
			}
		}

		// 安全更新DOM元素的函数
		function updateElementSafely(selector, value) {
			try {
				var elements = $(selector);
				if (elements.length > 0 && value !== undefined && value !== null) {
					elements.html(value);
				}
			} catch (error) {
				console.warn('更新元素失败:', selector, error);
			}
		}
		
		</script>
</head>
<body>
    <strong></strong>

    <div class="box">
        <div class="self_l l">
            <div class="self_name"><strong>玩家名称：<span class="昵称">测试</span></strong></div>
            <div class="self_role"><img src="Content/Img/player/main.gif?rd=4564"></div>
        </div>

        <div class="self_r r">
            <ul class="selftab">
                <li onClick="setTab('self',1,3)" id="self1" class="on">
                    <p class="p1">基本</p>
                </li>
                <li onClick="setTab('self',2,3)" id="self2" class="">
                    <p class="p2">好友</p>
                </li>
                <li onClick="setTab('self',3,3)" id="self3" class="">
                    <p class="p3">空间</p>
                </li>

            </ul>
            <div class="self_cont clearfix" id="con_self_1" style="display: block;">
                <div class="mi_box">
                    <ul class="top  clearfix">
                        <li>等级：<span class="等级">1</span></li>
                        <li>角色昵称：<span class="昵称">测试玩家</span></li>
                        <li>性别：<span class="性别">男性</span></li>
                        <li>论坛ID：<span class="论坛ID">暂无</span></li>
                        <li>体力上限：<span class="体力上限">0</span></li>
                        <li>刷新次数：<span class="刷新次数">0</span></li>
                        <li>元宝：<span class="元宝">0</span></li>
                        <li>水晶：<span class="水晶">0</span></li>
                        <li>金币：<span class="金币"></span></li>
                        <li>等级：<span class="等级">0</span></li>
                        <li>时之券：<span class="时之券">0</span></li>
                        <li>自动购买体力丹：<span class="自动购买体力丹">0</span></li>
                        <li>自动购买魔力丹：<span class="自动购买魔力丹">0</span></li>
                        <li>VIP等级：<span class="VIP等级">0</span></li>
                        <li>自动购买经验丹：<span class="自动购买经验丹">0</span></li>
                        <li>VIP权限：<span class="等级">0</span></li>
						<li>高级会员权限：<span class="VIP权限">未开通</span></li>

                    </ul>
                    <ul class="bot">
                        <li class="v">自动战斗次数：<span class="自动战斗次数"></span></li>

                        <li class="v">账号：<span class="账号">无</span></li>
                    </ul>
                </div>
            </div>
            <div class="self_cont clearfix" id="con_self_2" style="display: none;">
                <div class="hc" data-id="0"  style="display:none">
                 
                </div>
                <div id="rb_right" style="display:none">

                    名字：<span class="好友_名字">undefined</span><br>
                    攻击：<span class="好友_攻击">undefined</span><br>
                    魔法：<span class="好友_魔法">undefined</span><br>
                    防御：<span class="好友_防御">undefined</span><br>
                    敏捷：<span class="好友_敏捷">undefined</span><br>
                    命中：<span class="好友_命中">undefined</span><br>
                    技能：<span class="好友_技能">undefined</span><br>
                    速度：<span class="好友_速度">undefined</span><br>
                    吸血：<span class="好友_吸血">undefined</span><br>
                    吸魔：<span class="好友_吸魔">undefined</span><br>
                    等级：<span class="好友_等级">undefined</span><br>
                </div>
            </div>

            <div class="self_cont clearfix" id="con_self_3" style="display: none;">
                <div id="rb_right" style="display:block">
                    魔法道具：<span class="空间_魔法道具">0/0</span><br>
                    皮肤道具：<span class="空间_皮肤">0/0</span><br>
                </div>
            </div>
        </div>
    </div>


    <script type="text/javascript">
        var listtype = 2;
        if (listtype == 1) {
            setTab('self', 2, 3);
        }
        var hc_index = 0;
        var hc_id = 0;
        var hc_max = 23;
        var hc_time = 55;
        function playHC() {

            var id = $(".hc").attr("data-id");
            hc_id = id;
            for (var i = 0; i <= hc_max; i++) {
                $(".hc").prepend("<img class='yingbi hcc" + i + "' src='Content/PetPhoto/h_z" + hc_id + "_" + (i + 1) + ".png'>");
            }
            if (id != 0) {
                setInterval(playHC_AT, hc_time);
            }

        } 
        function showHCInfo(json) {
            if (json != "0") {
                var j = $.parseJSON(json);
                 $(".hc").show();
                $("#rb_right").show();
                $(".hc").attr("data-id", j.图片编号);
                $(".hc").html("");
                hc_max = j.帧数;
                hc_time = j.延迟*1.5;
                $(".好友_名字").html(j.名字);
                $(".好友_攻击").html(j.攻击 * 100 + "%");
                $(".好友_防御").html(j.防御 * 100 + "%");
                $(".好友_吸魔").html(j.吸魔 * 100 + "%");
                $(".好友_吸血").html(j.吸血 * 100 + "%");
                $(".好友_敏捷").html(getINT(j.敏捷));
                $(".好友_魔法").html(getINT(j.魔法));
                $(".好友_命中").html(getINT(j.命中));
                $(".好友_技能").html(getINT(j.技能));
                $(".好友_等级").html(getINT(j.等级));
                $(".好友_攻击").html(getINT(j.攻击));
                $(".好友_速度").html(getINT(j.速度));

                playHC();

               
            }
        }
        function getINT(NUM) {
            if (NUM.indexOf(".") != -1) {
                return NUM * 100 + "%";
            } else {
                return NUM;
            }
        }
        function playHC_AT() {
            hc_index++;
          
            // $(".hc img").attr("src", "Content/PetPhoto/h_z" + hc_id + "_" + hc_index + ".png");
            if (hc_index >= hc_max) {
                hc_index = 0;
            }
            $(".hc1").addClass("yingbi").removeClass("hc1");
            $(".hcc" + hc_index).removeClass("yingbi").addClass("hc1");
        } 
    </script>
</body>
</html>
