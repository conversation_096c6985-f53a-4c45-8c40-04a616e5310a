namespace WebApplication_HM.DTOs.RequestDTO
{
    /// <summary>
    /// 境界提升请求DTO
    /// </summary>
    public class RealmUpgradeRequestDTO
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 宠物ID
        /// </summary>
        public int PetId { get; set; }

        /// <summary>
        /// 升级类型 (修炼丹/玄元丹)
        /// </summary>
        public string UpgradeType { get; set; } = "修炼丹";
    }

    /// <summary>
    /// 获取境界信息请求DTO
    /// </summary>
    public class GetRealmInfoRequestDTO
    {
        /// <summary>
        /// 宠物ID
        /// </summary>
        public int PetId { get; set; }
    }

    /// <summary>
    /// 获取境界历史请求DTO
    /// </summary>
    public class GetRealmHistoryRequestDTO
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 宠物ID (可选)
        /// </summary>
        public int? PetId { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; } = 20;
    }
}