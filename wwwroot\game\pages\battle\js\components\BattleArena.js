// 战斗场景组件
const { computed, ref } = Vue

export default {
    name: 'BattleArena',
    props: {
        battleState: {
            type: Object,
            required: true
        }
    },
    emits: ['animation-complete', 'player-pet-click', 'monster-click'],
    setup(props, { emit }) {
        // 响应式数据
        const playerPetRef = ref(null)
        const monsterRef = ref(null)
        
        // 计算属性
        const playerPetClasses = computed(() => ({
            'attacking': props.battleState.isPlayerAttacking,
            'hit': false, // 暂时简化
            'critical': false
        }))
        
        const monsterClasses = computed(() => ({
            'attacking': props.battleState.isMonsterAttacking,
            'hit': false, // 暂时简化
            'dying': props.battleState.gwHp <= 0
        }))
        
        const playerPetStyle = computed(() => ({
            left: '100px',
            bottom: '100px'
        }))
        
        const monsterStyle = computed(() => ({
            right: '100px',
            bottom: '100px'
        }))
        
        // 方法
        const handlePlayerPetClick = () => {
            emit('player-pet-click')
        }
        
        const handleMonsterClick = () => {
            emit('monster-click')
        }
        
        return {
            playerPetRef,
            monsterRef,
            playerPetClasses,
            monsterClasses,
            playerPetStyle,
            monsterStyle,
            handlePlayerPetClick,
            handleMonsterClick
        }
    },
    template: `
        <div class="battle-arena">
            <!-- 背景 -->
            <div class="arena-background">
                <div class="background-gradient"></div>
            </div>
            
            <!-- 玩家宠物 -->
            <div 
                ref="playerPetRef"
                class="player-pet"
                :class="playerPetClasses"
                :style="playerPetStyle"
                @click="handlePlayerPetClick"
            >
                <div class="pet-image">
                    <div class="pet-placeholder">🐱</div>
                </div>
                <!-- HP条 -->
                <div class="hp-bar player-hp">
                    <div class="hp-label">玩家</div>
                    <div class="hp-bar-container">
                        <div 
                            class="hp-fill"
                            :style="{ width: battleState.playerHpPercentage + '%' }"
                        ></div>
                    </div>
                    <div class="hp-text">{{ battleState.cwHp }}/{{ battleState.cwMaxHp }}</div>
                </div>
            </div>
            
            <!-- 怪物 -->
            <div 
                ref="monsterRef"
                class="monster"
                :class="monsterClasses"
                :style="monsterStyle"
                @click="handleMonsterClick"
            >
                <div class="monster-image">
                    <div class="monster-placeholder">👹</div>
                </div>
                <!-- HP条 -->
                <div class="hp-bar monster-hp">
                    <div class="hp-label">怪物</div>
                    <div class="hp-bar-container">
                        <div 
                            class="hp-fill"
                            :style="{ width: battleState.monsterHpPercentage + '%' }"
                        ></div>
                    </div>
                    <div class="hp-text">{{ battleState.gwHp }}/{{ battleState.gwMaxHp }}</div>
                </div>
            </div>
            
            <!-- 战斗提示 -->
            <div class="battle-hints">
                <p v-if="battleState.battleStatus === 'fighting'">点击怪物进行攻击</p>
                <p v-if="battleState.battleStatus === 'waiting'">准备战斗中...</p>
            </div>
        </div>
    `
}
