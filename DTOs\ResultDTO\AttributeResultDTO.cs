namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 属性计算结果DTO - 包含宠物完整信息
    /// </summary>
    public class AttributeResultDTO
    {
        // 宠物基本信息
        /// <summary>
        /// 宠物ID
        /// </summary>
        public int PetId { get; set; }

        /// <summary>
        /// 宠物编号
        /// </summary>
        public int PetNo { get; set; }

        /// <summary>
        /// 宠物名称
        /// </summary>
        public string PetName { get; set; } = "";

        /// <summary>
        /// 宠物等级
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 五行属性
        /// </summary>
        public string Element { get; set; } = "";

        /// <summary>
        /// 境界等级
        /// </summary>
        public int RealmLevel { get; set; }

        /// <summary>
        /// 境界名称
        /// </summary>
        public string RealmName { get; set; } = "";

        /// <summary>
        /// 进化次数
        /// </summary>
        public int EvolveCount { get; set; }

        /// <summary>
        /// 合成次数
        /// </summary>
        public int SynthesisCount { get; set; }

        /// <summary>
        /// 涅槃次数
        /// </summary>
        public int NirvanaCount { get; set; }

        /// <summary>
        /// 经验值
        /// </summary>
        public long Exp { get; set; }

        /// <summary>
        /// 成长值 (兼容原项目)
        /// </summary>
        public string Growth { get; set; } = "0";

        // 属性值
        /// <summary>
        /// 攻击力
        /// </summary>
        public int Atk { get; set; }

        /// <summary>
        /// 防御力
        /// </summary>
        public int Def { get; set; }

        /// <summary>
        /// 命中
        /// </summary>
        public int Hit { get; set; }

        /// <summary>
        /// 闪避
        /// </summary>
        public int Dodge { get; set; }

        /// <summary>
        /// 速度
        /// </summary>
        public int Spd { get; set; }

        /// <summary>
        /// 生命值
        /// </summary>
        public int Hp { get; set; }

        /// <summary>
        /// 魔法值
        /// </summary>
        public int Mp { get; set; }

        // 扩展属性（装备系统专用）
        /// <summary>
        /// 加深（暴击伤害加成）
        /// </summary>
        public double Deepen { get; set; }

        /// <summary>
        /// 抵消（伤害减免）
        /// </summary>
        public double Offset { get; set; }

        /// <summary>
        /// 吸血（物理吸血）
        /// </summary>
        public double Vamp { get; set; }

        /// <summary>
        /// 吸魔（法术吸血）
        /// </summary>
        public double VampMp { get; set; }

        /// <summary>
        /// 属性详细信息（用于调试和显示）
        /// </summary>
        public AttributeDetailInfo? DetailInfo { get; set; }
    }

    /// <summary>
    /// 属性详细信息
    /// </summary>
    public class AttributeDetailInfo
    {
        /// <summary>
        /// 基础属性（宠物本身）
        /// </summary>
        public Dictionary<string, double> BaseAttributes { get; set; } = new();

        /// <summary>
        /// 装备属性加成
        /// </summary>
        public Dictionary<string, double> EquipmentAttributes { get; set; } = new();

        /// <summary>
        /// 套装属性加成
        /// </summary>
        public Dictionary<string, double> SuitAttributes { get; set; } = new();

        /// <summary>
        /// 宝石属性加成
        /// </summary>
        public Dictionary<string, double> GemstoneAttributes { get; set; } = new();

        /// <summary>
        /// 总属性
        /// </summary>
        public Dictionary<string, double> TotalAttributes { get; set; } = new();
    }
}