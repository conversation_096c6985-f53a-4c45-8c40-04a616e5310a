using System.Collections.Concurrent;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;
using WebApplication_HM.DTOs.ResultDTO;
using WebApplication_HM.Models.Define;
using WebApplication_HM.Interface;

namespace WebApplication_HM.Services.SocketInfo
{
    /// <summary>
    /// 实时通知服务 - 管理所有WebSocket实时推送功能
    /// </summary>
    public class RealTimeNotificationService : IRealTimeService
    {
        /// <summary>
        /// 玩家连接管理（玩家ID -> WebSocket连接）
        /// </summary>
        private static readonly ConcurrentDictionary<int, WebSocket> _playerConnections = new();

        /// <summary>
        /// 连接ID到玩家ID的映射
        /// </summary>
        private static readonly ConcurrentDictionary<string, int> _connectionToPlayer = new();

        /// <summary>
        /// 玩家在线状态管理
        /// </summary>
        private static readonly ConcurrentDictionary<int, PlayerStatusDTO> _playerStatus = new();

        #region 连接管理

        /// <summary>
        /// 添加玩家连接
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="socket">WebSocket连接</param>
        /// <param name="playerName">玩家昵称</param>
        public void AddPlayerConnection(int playerId, WebSocket socket, string playerName)
        {
            string connectionId = socket.GetHashCode().ToString();
            
            // 如果玩家已经有连接，先断开旧连接
            if (_playerConnections.TryGetValue(playerId, out var oldSocket))
            {
                _ = Task.Run(async () =>
                {
                    if (oldSocket.State == WebSocketState.Open)
                    {
                        // 发送被挤下线通知
                        var forceLogoutMsg = new
                        {
                            Type = MessageTypeEnum.ForceLogout,
                            Message = "您的账号在其他地方登录，您已被强制下线"
                        };
                        await SendToSocket(oldSocket, JsonSerializer.Serialize(forceLogoutMsg));
                        await oldSocket.CloseAsync(WebSocketCloseStatus.PolicyViolation, "Account logged in elsewhere", CancellationToken.None);
                    }
                });
            }

            // 添加新连接
            _playerConnections[playerId] = socket;
            _connectionToPlayer[connectionId] = playerId;

            // 更新玩家状态
            var status = new PlayerStatusDTO
            {
                PlayerId = playerId,
                Nickname = playerName,
                Status = "online",
                LastActiveTime = DateTime.Now
            };
            _playerStatus[playerId] = status;

            // 广播玩家上线状态
            _ = Task.Run(() => BroadcastPlayerStatusChange(playerId, "login", "offline", "online", $"{playerName} 上线了"));
        }

        /// <summary>
        /// 移除玩家连接
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        public void RemovePlayerConnection(int playerId)
        {
            if (_playerConnections.TryRemove(playerId, out var socket))
            {
                string connectionId = socket.GetHashCode().ToString();
                _connectionToPlayer.TryRemove(connectionId, out _);

                // 更新玩家状态为离线
                if (_playerStatus.TryGetValue(playerId, out var status))
                {
                    var playerName = status.Nickname;
                    _playerStatus.TryRemove(playerId, out _);
                    
                    // 广播玩家下线状态
                    _ = Task.Run(() => BroadcastPlayerStatusChange(playerId, "logout", "online", "offline", $"{playerName} 下线了"));
                }
            }
        }

        /// <summary>
        /// 通过连接ID获取玩家ID
        /// </summary>
        /// <param name="connectionId">连接ID</param>
        /// <returns>玩家ID</returns>
        public int? GetPlayerIdByConnection(string connectionId)
        {
            return _connectionToPlayer.TryGetValue(connectionId, out var playerId) ? playerId : null;
        }

        #endregion

        #region 实时战斗推送

        /// <summary>
        /// 发送实时战斗推送
        /// </summary>
        /// <param name="battleNotification">战斗通知信息</param>
        public async Task SendBattleNotification(RealTimeBattleDTO battleNotification)
        {
            var message = JsonSerializer.Serialize(battleNotification);
            
            // 发送给参与战斗的玩家
            await SendToPlayer(battleNotification.PlayerId, message);
            
            // 如果是重要战斗事件，广播给所有在线玩家
            if (battleNotification.BattleStatus == "end" && battleNotification.Result == "win")
            {
                var broadcastMsg = new GameEventNoticeDTO
                {
                    EventType = "battle_victory",
                    PlayerId = battleNotification.PlayerId,
                    PlayerName = battleNotification.PlayerName,
                    Description = $"{battleNotification.PlayerName} 在 {battleNotification.MapName} 取得了胜利！",
                    Broadcast = true
                };
                await BroadcastToAll(JsonSerializer.Serialize(broadcastMsg));
            }
        }

        /// <summary>
        /// 发送战斗开始通知
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="battleId">战斗ID</param>
        /// <param name="mapId">地图ID</param>
        /// <param name="mapName">地图名称</param>
        public async Task SendBattleStartNotification(int playerId, string battleId, int mapId, string mapName)
        {
            // 更新玩家状态为战斗中
            await UpdatePlayerStatusWithMap(playerId, "battle", mapId, mapName);

            var notification = new RealTimeBattleDTO
            {
                BattleId = battleId,
                PlayerId = playerId,
                MapId = mapId,
                MapName = mapName,
                BattleStatus = "start",
                Round = 0,
                Result = "fighting"
            };

            await SendBattleNotification(notification);
        }

        #endregion

        #region 玩家状态同步

        /// <summary>
        /// 获取在线玩家列表
        /// </summary>
        /// <returns>在线玩家状态列表</returns>
        public List<PlayerStatusDTO> GetOnlinePlayers()
        {
            return _playerStatus.Values.ToList();
        }

        /// <summary>
        /// 广播玩家状态变更
        /// </summary>
        private async Task BroadcastPlayerStatusChange(int playerId, string changeType, string oldStatus, string newStatus, string description)
        {
            var notification = new PlayerStatusChangeDTO
            {
                PlayerId = playerId,
                Nickname = _playerStatus.TryGetValue(playerId, out var status) ? status.Nickname : "未知玩家",
                ChangeType = changeType,
                OldStatus = oldStatus,
                NewStatus = newStatus,
                Description = description
            };

            await BroadcastToAll(JsonSerializer.Serialize(notification));
        }

        #endregion

        #region 系统公告

        /// <summary>
        /// 发送系统公告
        /// </summary>
        /// <param name="notice">公告信息</param>
        public async Task SendSystemNotice(SystemNoticeDTO notice)
        {
            var message = JsonSerializer.Serialize(notice);

            if (notice.TargetPlayerIds.Any())
            {
                // 发送给指定玩家
                foreach (var playerId in notice.TargetPlayerIds)
                {
                    await SendToPlayer(playerId, message);
                }
            }
            else
            {
                // 全服广播
                await BroadcastToAll(message);
            }
        }

        /// <summary>
        /// 发送游戏事件通知
        /// </summary>
        /// <param name="eventNotice">事件通知</param>
        public async Task SendGameEventNotice(GameEventNoticeDTO eventNotice)
        {
            var message = JsonSerializer.Serialize(eventNotice);

            if (eventNotice.Broadcast)
            {
                await BroadcastToAll(message);
            }
            else
            {
                await SendToPlayer(eventNotice.PlayerId, message);
            }
        }

        #endregion

        #region 境界突破通知

        /// <summary>
        /// 发送境界突破通知
        /// </summary>
        /// <param name="notification">境界突破通知</param>
        public async Task SendRealmBreakthroughNotification(RealmNotificationDTO notification)
        {
            var message = JsonSerializer.Serialize(notification);

            // 发送给玩家自己
            await SendToPlayer(notification.PlayerId, message);

            // 如果是重大突破，广播给所有玩家
            if (notification.MajorBreakthrough || notification.Broadcast)
            {
                var broadcastMsg = new GameEventNoticeDTO
                {
                    EventType = "realm_breakthrough",
                    PlayerId = notification.PlayerId,
                    PlayerName = notification.PlayerName,
                    Description = $"恭喜 {notification.PlayerName} 的 {notification.PetName} 突破到 {notification.NewRealm} 境界！",
                    Broadcast = true
                };
                await BroadcastToAll(JsonSerializer.Serialize(broadcastMsg));
            }
        }

        /// <summary>
        /// 发送境界修炼进度通知
        /// </summary>
        /// <param name="progress">修炼进度</param>
        public async Task SendRealmProgressNotification(RealmProgressDTO progress)
        {
            var message = JsonSerializer.Serialize(progress);
            await SendToPlayer(progress.PlayerId, message);
        }

        #endregion

        #region 装备通知

        /// <summary>
        /// 发送装备获得通知
        /// </summary>
        /// <param name="notification">装备通知</param>
        public async Task SendEquipmentNotification(EquipmentNotificationDTO notification)
        {
            var message = JsonSerializer.Serialize(notification);

            // 发送给玩家自己
            await SendToPlayer(notification.PlayerId, message);

            // 如果是稀有装备，广播给所有玩家
            if (notification.IsRare || notification.Broadcast)
            {
                var broadcastMsg = new GameEventNoticeDTO
                {
                    EventType = "rare_equipment",
                    PlayerId = notification.PlayerId,
                    PlayerName = notification.PlayerName,
                    Description = $"{notification.PlayerName} 获得了 {notification.Quality} 品质的 {notification.EquipmentName}！",
                    Broadcast = true
                };
                await BroadcastToAll(JsonSerializer.Serialize(broadcastMsg));
            }
        }

        /// <summary>
        /// 发送装备强化通知
        /// </summary>
        /// <param name="notification">强化通知</param>
        public async Task SendEquipmentEnhanceNotification(EquipmentEnhanceNotificationDTO notification)
        {
            var message = JsonSerializer.Serialize(notification);
            await SendToPlayer(notification.PlayerId, message);

            // 如果是大成功，可以考虑广播
            if (notification.CriticalSuccess && notification.NewLevel >= 10)
            {
                var broadcastMsg = new GameEventNoticeDTO
                {
                    EventType = "equipment_enhance",
                    PlayerId = notification.PlayerId,
                    PlayerName = "未知玩家", // 需要从其他地方获取玩家名称
                    Description = $"玩家成功将 {notification.EquipmentName} 强化到 +{notification.NewLevel}！",
                    Broadcast = true
                };
                await BroadcastToAll(JsonSerializer.Serialize(broadcastMsg));
            }
        }

        #endregion

        #region 通用发送方法

        /// <summary>
        /// 发送消息给指定玩家（同步版本）
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="message">消息内容</param>
        public void SendMessage(int playerId, string message)
        {
            SendToPlayer(playerId, message).Wait();
        }

        /// <summary>
        /// 广播消息给所有在线玩家（同步版本）
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="messageType">消息类型</param>
        public void BroadcastMessage(string message, string messageType)
        {
            var broadcastMsg = new
            {
                Type = messageType,
                Content = message,
                Timestamp = DateTime.Now
            };
            var jsonMessage = JsonSerializer.Serialize(broadcastMsg);
            BroadcastToAll(jsonMessage).Wait();
        }

        /// <summary>
        /// 发送心跳检测给指定玩家（同步版本）
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        public void SendHeartbeat(int playerId)
        {
            var heartbeatMsg = new
            {
                Type = MessageTypeEnum.Heartbeat,
                Timestamp = DateTime.Now
            };
            var message = JsonSerializer.Serialize(heartbeatMsg);
            SendToPlayer(playerId, message).Wait();
        }

        /// <summary>
        /// 发送系统公告给指定玩家（同步版本）
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="notice">公告信息</param>
        public void SendSystemNotice(int playerId, SystemNoticeDTO notice)
        {
            var message = JsonSerializer.Serialize(notice);
            SendToPlayer(playerId, message).Wait();
        }

        /// <summary>
        /// 广播系统公告（同步版本）
        /// </summary>
        /// <param name="notice">公告信息</param>
        public void BroadcastSystemNotice(SystemNoticeDTO notice)
        {
            var message = JsonSerializer.Serialize(notice);
            BroadcastToAll(message).Wait();
        }

        /// <summary>
        /// 发送游戏事件通知给指定玩家（同步版本）
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="eventNotice">事件通知</param>
        public void SendGameEvent(int playerId, GameEventNoticeDTO eventNotice)
        {
            var message = JsonSerializer.Serialize(eventNotice);
            SendToPlayer(playerId, message).Wait();
        }

        /// <summary>
        /// 广播游戏事件通知（同步版本）
        /// </summary>
        /// <param name="eventNotice">事件通知</param>
        public void BroadcastGameEvent(GameEventNoticeDTO eventNotice)
        {
            var message = JsonSerializer.Serialize(eventNotice);
            BroadcastToAll(message).Wait();
        }

        /// <summary>
        /// 更新玩家状态（同步版本）
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="status">新状态</param>
        public void UpdatePlayerStatus(int playerId, string status)
        {
            UpdatePlayerStatusWithMap(playerId, status).Wait();
        }

        /// <summary>
        /// 更新玩家状态（带地图信息）
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="status">新状态</param>
        /// <param name="mapId">当前地图ID</param>
        /// <param name="mapName">当前地图名称</param>
        public async Task UpdatePlayerStatusWithMap(int playerId, string status, int? mapId = null, string mapName = null)
        {
            if (_playerStatus.TryGetValue(playerId, out var playerStatus))
            {
                var oldStatus = playerStatus.Status;
                playerStatus.Status = status;
                playerStatus.UpdateTime = DateTime.Now;
                playerStatus.LastActiveTime = DateTime.Now;

                if (mapId.HasValue)
                {
                    playerStatus.CurrentMapId = mapId;
                    playerStatus.CurrentMapName = mapName;
                }

                // 更新在线状态
                switch (status.ToLower())
                {
                    case "battle":
                        playerStatus.InBattle = true;
                        break;
                    case "idle":
                    case "online":
                        playerStatus.InBattle = false;
                        break;
                }

                // 广播状态变更
                await BroadcastPlayerStatusChange(playerId, "status_update", oldStatus, status, $"玩家状态更新为: {status}");
            }
        }

        /// <summary>
        /// 发送消息给指定玩家
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="message">消息内容</param>
        public async Task SendToPlayer(int playerId, string message)
        {
            if (_playerConnections.TryGetValue(playerId, out var socket))
            {
                await SendToSocket(socket, message);
            }
        }

        /// <summary>
        /// 广播消息给所有在线玩家
        /// </summary>
        /// <param name="message">消息内容</param>
        public async Task BroadcastToAll(string message)
        {
            var tasks = new List<Task>();
            foreach (var connection in _playerConnections.Values)
            {
                tasks.Add(SendToSocket(connection, message));
            }
            await Task.WhenAll(tasks);
        }

        /// <summary>
        /// 发送消息到指定WebSocket连接
        /// </summary>
        /// <param name="socket">WebSocket连接</param>
        /// <param name="message">消息内容</param>
        private async Task SendToSocket(WebSocket socket, string message)
        {
            if (socket.State == WebSocketState.Open)
            {
                try
                {
                    var buffer = Encoding.UTF8.GetBytes(message);
                    await socket.SendAsync(new ArraySegment<byte>(buffer), WebSocketMessageType.Text, true, CancellationToken.None);
                }
                catch (Exception ex)
                {
                    // 记录日志，但不抛出异常
                    Console.WriteLine($"发送WebSocket消息失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 发送心跳检测
        /// </summary>
        public async Task SendHeartbeat()
        {
            var heartbeatMsg = new
            {
                Type = MessageTypeEnum.Heartbeat,
                Timestamp = DateTime.Now
            };
            var message = JsonSerializer.Serialize(heartbeatMsg);
            await BroadcastToAll(message);
        }

        /// <summary>
        /// 获取在线玩家数量
        /// </summary>
        /// <returns>在线玩家数量</returns>
        public int GetOnlinePlayerCount()
        {
            return _playerConnections.Count;
        }

        #endregion
    }
} 