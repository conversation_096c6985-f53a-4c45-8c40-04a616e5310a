using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WebApplication_HM.DTOs;
using WebApplication_HM.DTOs.Common;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;
using WebApplication_HM.Utils;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 转生服务
    /// </summary>
    public class NirvanaService : INirvanaService
    {
        private readonly ISqlSugarClient _db;
        private readonly INirvanaCalculationService _calculationService;
        private readonly INirvanaConfigService _configService;
        private readonly INirvanaAntiCheatService _antiCheatService;
        private readonly ILevelService _levelService;
        private readonly ILogger<NirvanaService> _logger;

        public NirvanaService(
            ISqlSugarClient db,
            INirvanaCalculationService calculationService,
            INirvanaConfigService configService,
            INirvanaAntiCheatService antiCheatService,
            ILevelService levelService,
            ILogger<NirvanaService> logger)
        {
            _db = db;
            _calculationService = calculationService;
            _configService = configService;
            _antiCheatService = antiCheatService;
            _levelService = levelService;
            _logger = logger;
        }

        /// <summary>
        /// 执行转生
        /// </summary>
        public async Task<ApiResult<NirvanaResultDto>> ExecuteNirvanaAsync(NirvanaRequestDto request)
        {
            try
            {
                _logger.LogInformation("开始执行转生 - 用户ID:{UserId}", request.UserId);

                // 1. 反作弊验证
                if (!await _antiCheatService.ValidateNirvanaRequestAsync(request))
                {
                    return ApiResult<NirvanaResultDto>.CreateError("转生请求验证失败");
                }

                // 2. 条件验证
                var (isValid, errors) = await _calculationService.ValidateNirvanaConditionsAsync(request);
                if (!isValid)
                {
                    return ApiResult<NirvanaResultDto>.CreateError(string.Join(", ", errors));
                }

                // 3. 获取相关数据
                var user = await _db.Queryable<user>().FirstAsync(u => u.id == request.UserId);
                var mainPet = await _db.Queryable<user_pet>().FirstAsync(p => p.id == request.MainPetId);
                var subPet = await _db.Queryable<user_pet>().FirstAsync(p => p.id == request.SubPetId);
                var nirvanaPet = await _db.Queryable<user_pet>().FirstAsync(p => p.id == request.NirvanaPetId);

                // 3.5. 验证冷却时间（按照老系统10秒CD）
                if (user.nirvana_cd.HasValue)
                {
                    var timeSinceLastNirvana = DateTime.Now - user.nirvana_cd.Value;
                    if (timeSinceLastNirvana.TotalMilliseconds < 10000) // 10秒 = 10000毫秒
                    {
                        return ApiResult<NirvanaResultDto>.CreateError("涅槃CD未到!");
                    }
                }

                // 4. 获取转生配置（如果不存在则创建默认配置）
                var config = await _configService.FindMatchingConfigAsync(mainPet.pet_no, subPet.pet_no, nirvanaPet.pet_no);
                if (config == null)
                {
                    // 参考老系统创建默认转生配置
                    config = await CreateDefaultNirvanaConfigAsync(mainPet.pet_no, subPet.pet_no, nirvanaPet.pet_no, mainPet.pet_no);
                    if (config == null)
                    {
                        return ApiResult<NirvanaResultDto>.CreateError("无法创建转生配置，请检查宠物信息");
                    }

                    _logger.LogInformation("创建默认转生配置 - 主宠:{MainPet}, 副宠:{SubPet}, 涅槃兽:{NirvanaPet}, 结果:{ResultPet}",
                        mainPet.pet_no, subPet.pet_no, nirvanaPet.pet_no, mainPet.pet_no);
                }

                // 5. 获取道具效果（支持两个道具，对应老系统的道具1和道具2）
                bool hasFailureProtection = false;
                string script1 = "";
                string script2 = "";

                // 处理道具1
                if (!string.IsNullOrEmpty(request.UsedItemId1))
                {
                    var propEffect1 = await _calculationService.GetPropEffectAsync(request.UsedItemId1);
                    hasFailureProtection = hasFailureProtection || propEffect1.FailureProtection;
                    script1 = propEffect1.Script ?? "";
                }

                // 处理道具2
                if (!string.IsNullOrEmpty(request.UsedItemId2))
                {
                    var propEffect2 = await _calculationService.GetPropEffectAsync(request.UsedItemId2);
                    hasFailureProtection = hasFailureProtection || propEffect2.FailureProtection;
                    script2 = propEffect2.Script ?? "";
                }

                // 6. 计算成功率和消耗（按照老系统固定逻辑）
                var successRate = await _calculationService.CalculateSuccessRateAsync(request, config);
                const long costGold = 500000; // 老系统固定500,000金币
                var vipBonus = _calculationService.CalculateVipBonus(user);

                // 7. 验证资源（按照老系统逻辑）
                var userGold = user.gold ?? 0;
                if (userGold < costGold)
                {
                    return ApiResult<NirvanaResultDto>.CreateError("金币不足!涅槃一次需要500000金币噢!");
                }

                // 8. 开始事务执行转生
                var result = await _db.Ado.UseTranAsync(async () =>
                {
                    // 扣除金币
                    var currentGold = user.gold ?? 0;
                    var newGold = currentGold - costGold;
                    await _db.Updateable<user>()
                        .SetColumns(u => u.gold == newGold)
                        .SetColumns(u => u.nirvana_cd == DateTime.Now)
                        .Where(u => u.id == request.UserId)
                        .ExecuteCommandAsync();

                    // 扣除使用的道具（支持两个道具，对应老系统逻辑）
                    if (!string.IsNullOrEmpty(request.UsedItemId1))
                    {
                        await ConsumeItemAsync(request.UserId, request.UsedItemId1);
                    }
                    if (!string.IsNullOrEmpty(request.UsedItemId2))
                    {
                        await ConsumeItemAsync(request.UserId, request.UsedItemId2);
                    }

                    // 判断是否成功
                    var random = new Random();
                    var isSuccess = random.NextDouble() <= (double)successRate;

                    NirvanaResultDto nirvanaResult;

                    if (isSuccess)
                    {
                        // 转生成功，传递道具脚本
                        nirvanaResult = await ExecuteSuccessfulNirvanaAsync(request, config, mainPet, subPet, nirvanaPet, user, vipBonus, script1, script2);
                    }
                    else
                    {
                        // 转生失败，应用道具保护效果
                        nirvanaResult = await ExecuteFailedNirvanaAsync(request, mainPet, subPet, nirvanaPet, hasFailureProtection);
                    }

                    // 记录转生日志
                    await RecordNirvanaLogAsync(request, config, mainPet, subPet, nirvanaPet, 
                        successRate, costGold, vipBonus, nirvanaResult);

                    // 记录反作弊信息
                    await _antiCheatService.RecordNirvanaAttemptAsync(request.UserId);

                    nirvanaResult.CostGold = costGold;
                    nirvanaResult.SuccessRate = successRate;
                    nirvanaResult.VipBonus = vipBonus - 1; // 转换为加成百分比

                    return nirvanaResult;
                });

                _logger.LogInformation("转生执行完成 - 用户ID:{UserId}, 结果:{IsSuccess}",
                    request.UserId, result.IsSuccess);

                return ApiResult<NirvanaResultDto>.CreateSuccess(result.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行转生失败 - 用户ID:{UserId}", request.UserId);
                return ApiResult<NirvanaResultDto>.CreateError("转生执行失败");
            }
        }

        /// <summary>
        /// 执行成功的转生
        /// </summary>
        private async Task<NirvanaResultDto> ExecuteSuccessfulNirvanaAsync(
            NirvanaRequestDto request, PetNirvanaConfig config,
            user_pet mainPet, user_pet subPet, user_pet nirvanaPet,
            user user, decimal vipBonus, string script1 = "", string script2 = "")
        {
            // 计算获得成长，传递道具脚本（对应老系统的脚本1和脚本2）
            var growthGain = await _calculationService.CalculateGrowthGainAsync(mainPet, subPet, 0, user, script1, script2);

            // 获取1级对应的经验值
            var level1Exp = await _levelService.GetRequiredExpAsync(1, "pet");

            // 创建新宠物
            var newPet = new user_pet
            {
                user_id = request.UserId,
                pet_no = config.ResultPetNo,
                name = $"转生{GetPetName(config.ResultPetNo)}",
                growth = (decimal)growthGain,
                atk = CalculateNewAttribute(mainPet.atk, subPet.atk, config.MainGrowthInherit, config.SubGrowthInherit),
                def = CalculateNewAttribute(mainPet.def, subPet.def, config.MainGrowthInherit, config.SubGrowthInherit),
                hp = CalculateNewAttribute(mainPet.hp, subPet.hp, config.MainGrowthInherit, config.SubGrowthInherit),
                mp = CalculateNewAttribute(mainPet.mp, subPet.mp, config.MainGrowthInherit, config.SubGrowthInherit),
                spd = CalculateNewAttribute(mainPet.spd, subPet.spd, config.MainGrowthInherit, config.SubGrowthInherit),
                exp = level1Exp,
                status = "牧场",
                is_main = false,
                create_time = DateTime.Now,
                nirvana_count = (mainPet.nirvana_count ?? 0) + 1,
                parent_main_pet_id = mainPet.id,
                parent_sub_pet_id = subPet.id,
                original_pet_no = mainPet.original_pet_no ?? mainPet.pet_no
            };

            // 插入新宠物
            var insertedPet = await _db.Insertable(newPet).ExecuteReturnEntityAsync();

            // 删除原宠物
            await _db.Deleteable<user_pet>().Where(p => p.id == mainPet.id).ExecuteCommandAsync();
            await _db.Deleteable<user_pet>().Where(p => p.id == subPet.id).ExecuteCommandAsync();
            await _db.Deleteable<user_pet>().Where(p => p.id == nirvanaPet.id).ExecuteCommandAsync();

            return new NirvanaResultDto
            {
                IsSuccess = true,
                Message = "转生成功！",
                ResultGrowth = growthGain,
                ResultPetNo = config.ResultPetNo,
                ResultPet = insertedPet,
                NirvanaType = request.NirvanaType.ToString()
            };
        }

        /// <summary>
        /// 执行失败的转生
        /// </summary>
        private async Task<NirvanaResultDto> ExecuteFailedNirvanaAsync(
            NirvanaRequestDto request, user_pet mainPet, user_pet subPet, user_pet nirvanaPet, bool hasFailureProtection = false)
        {
            string message;

            if (hasFailureProtection)
            {
                // 有失败保护，只删除涅槃兽，保留主宠和副宠
                await _db.Deleteable<user_pet>().Where(p => p.id == nirvanaPet.id).ExecuteCommandAsync();
                message = "转生失败，但由于护宠道具保护，副宠得以保留";
            }
            else
            {
                // 无保护，删除副宠和涅槃兽，保留主宠
                await _db.Deleteable<user_pet>().Where(p => p.id == subPet.id).ExecuteCommandAsync();
                await _db.Deleteable<user_pet>().Where(p => p.id == nirvanaPet.id).ExecuteCommandAsync();
                message = "转生失败，副宠和涅槃兽已消失";
            }

            return new NirvanaResultDto
            {
                IsSuccess = false,
                Message = message,
                ResultGrowth = null,
                ResultPetNo = null,
                ResultPet = mainPet,
                NirvanaType = request.NirvanaType.ToString()
            };
        }

        /// <summary>
        /// 记录转生日志
        /// </summary>
        private async Task RecordNirvanaLogAsync(
            NirvanaRequestDto request, PetNirvanaConfig config,
            user_pet mainPet, user_pet subPet, user_pet nirvanaPet,
            decimal successRate, long costGold, decimal vipBonus, NirvanaResultDto result)
        {
            var log = new PetNirvanaLog
            {
                UserId = request.UserId,
                MainPetId = request.MainPetId,
                SubPetId = request.SubPetId,
                NirvanaPetId = request.NirvanaPetId,
                MainPetNo = mainPet.pet_no,
                SubPetNo = subPet.pet_no,
                NirvanaPetNo = nirvanaPet.pet_no,
                MainGrowth = Convert.ToDecimal(mainPet.growth),
                SubGrowth = Convert.ToDecimal(subPet.growth),
                MainLevel = await _levelService.CalculateLevelAsync(mainPet.exp ?? 0, "pet"),
                SubLevel = await _levelService.CalculateLevelAsync(subPet.exp ?? 0, "pet"),
                ResultPetNo = result.ResultPetNo,
                ResultGrowth = result.ResultGrowth,
                SuccessRate = successRate * 100, // 转换为百分比
                UsedItemId = request.UsedItemId,
                CostGold = costGold,
                IsSuccess = result.IsSuccess ? 1 : 0,
                VipBonus = (vipBonus - 1) * 100, // 转换为加成百分比
                NirvanaType = request.NirvanaType.ToString(),
                CreateTime = DateTime.Now
            };

            await _db.Insertable(log).ExecuteCommandAsync();
        }

        /// <summary>
        /// 计算新属性值
        /// </summary>
        private long CalculateNewAttribute(long? mainAttr, long? subAttr, decimal mainInherit, decimal subInherit)
        {
            try
            {
                var mainValue = mainAttr ?? 0;
                var subValue = subAttr ?? 0;

                var newValue = (long)(mainValue * mainInherit + subValue * subInherit);
                return Math.Max(1, newValue);
            }
            catch
            {
                return 1;
            }
        }



        /// <summary>
        /// 执行变脸
        /// </summary>
        public async Task<ApiResult<FaceChangeResultDto>> ExecuteFaceChangeAsync(FaceChangeRequestDto request)
        {
            // 变脸功能将在下一个文件中实现
            return ApiResult<FaceChangeResultDto>.CreateError("变脸功能暂未实现");
        }

        /// <summary>
        /// 获取转生配置列表
        /// </summary>
        public async Task<ApiResult<List<PetNirvanaConfigDto>>> GetNirvanaConfigsAsync()
        {
            try
            {
                var configs = await _configService.GetActiveConfigsAsync();
                var configDtos = configs.Select(c => new PetNirvanaConfigDto
                {
                    Id = c.Id,
                    MainPetNo = c.MainPetNo,
                    SubPetNo = c.SubPetNo,
                    NirvanaPetNo = c.NirvanaPetNo,
                    ResultPetNo = c.ResultPetNo,
                    RequiredLevel = c.RequiredLevel,
                    BaseSuccessRate = c.BaseSuccessRate,
                    CostGold = c.CostGold,
                    MainGrowthInherit = c.MainGrowthInherit,
                    SubGrowthInherit = c.SubGrowthInherit,
                    SpecialRule = c.SpecialRule,
                    IsActive = c.IsActive == 1
                }).ToList();

                return ApiResult<List<PetNirvanaConfigDto>>.CreateSuccess(configDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取转生配置列表失败");
                return ApiResult<List<PetNirvanaConfigDto>>.CreateError("获取转生配置失败");
            }
        }

        /// <summary>
        /// 预览转生结果
        /// </summary>
        public async Task<ApiResult<NirvanaPreviewDto>> PreviewNirvanaAsync(NirvanaPreviewRequestDto request)
        {
            try
            {
                var preview = new NirvanaPreviewDto();

                // 验证条件（支持两个道具）
                var nirvanaRequest = new NirvanaRequestDto
                {
                    UserId = request.UserId,
                    MainPetId = request.MainPetId,
                    SubPetId = request.SubPetId,
                    NirvanaPetId = request.NirvanaPetId,
                    UsedItemId1 = request.UsedItemId1,
                    UsedItemId2 = request.UsedItemId2
                };

                var (isValid, errors) = await _calculationService.ValidateNirvanaConditionsAsync(nirvanaRequest);
                preview.CanNirvana = isValid;
                preview.ValidationErrors = errors;

                if (isValid)
                {
                    // 获取相关数据
                    var user = await _db.Queryable<user>().FirstAsync(u => u.id == request.UserId);
                    var mainPet = await _db.Queryable<user_pet>().FirstAsync(p => p.id == request.MainPetId);
                    var subPet = await _db.Queryable<user_pet>().FirstAsync(p => p.id == request.SubPetId);
                    var nirvanaPet = await _db.Queryable<user_pet>().FirstAsync(p => p.id == request.NirvanaPetId);

                    var config = await _configService.FindMatchingConfigAsync(mainPet.pet_no, subPet.pet_no, nirvanaPet.pet_no);
                    
                    if (config != null)
                    {
                        preview.EstimatedSuccessRate = await _calculationService.CalculateSuccessRateAsync(nirvanaRequest, config);
                        preview.EstimatedCostGold = _calculationService.CalculateCostGold(config, user);
                        preview.VipBonus = _calculationService.CalculateVipBonus(user) - 1;

                        // 计算成长范围
                        var minGrowth = await _calculationService.CalculateGrowthGainAsync(mainPet, subPet, -0.1m, user);
                        var maxGrowth = await _calculationService.CalculateGrowthGainAsync(mainPet, subPet, 0.1m, user);
                        preview.EstimatedGrowthRange = $"{minGrowth:F2} - {maxGrowth:F2}";

                        preview.ConfigInfo = new PetNirvanaConfigDto
                        {
                            Id = config.Id,
                            MainPetNo = config.MainPetNo,
                            SubPetNo = config.SubPetNo,
                            NirvanaPetNo = config.NirvanaPetNo,
                            ResultPetNo = config.ResultPetNo,
                            RequiredLevel = config.RequiredLevel,
                            BaseSuccessRate = config.BaseSuccessRate,
                            CostGold = config.CostGold,
                            IsActive = config.IsActive == 1
                        };
                    }
                }

                return ApiResult<NirvanaPreviewDto>.CreateSuccess(preview);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "预览转生结果失败");
                return ApiResult<NirvanaPreviewDto>.CreateError("预览转生结果失败");
            }
        }

        /// <summary>
        /// 获取用户转生记录
        /// </summary>
        public async Task<ApiResult<PagedResult<NirvanaRecordDto>>> GetUserRecordsAsync(int userId, int page = 1, int size = 20)
        {
            try
            {
                var totalCount = await _db.Queryable<PetNirvanaLog>()
                    .Where(l => l.UserId == userId)
                    .CountAsync();

                var records = await _db.Queryable<PetNirvanaLog>()
                    .Where(l => l.UserId == userId)
                    .OrderBy(l => l.CreateTime, OrderByType.Desc)
                    .ToPageListAsync(page, size);

                var recordDtos = records.Select(r => new NirvanaRecordDto
                {
                    Id = r.Id,
                    UserId = r.UserId,
                    MainPetId = r.MainPetId,
                    SubPetId = r.SubPetId,
                    NirvanaPetId = r.NirvanaPetId,
                    IsSuccess = r.IsSuccess == 1,
                    ResultGrowth = r.ResultGrowth,
                    CostGold = r.CostGold,
                    VipBonus = r.VipBonus,
                    NirvanaType = r.NirvanaType,
                    CreateTime = r.CreateTime
                }).ToList();

                var pagedResult = new PagedResult<NirvanaRecordDto>
                {
                    Items = recordDtos,
                    TotalCount = totalCount,
                    PageIndex = page,
                    PageSize = size
                };

                return ApiResult<PagedResult<NirvanaRecordDto>>.CreateSuccess(pagedResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户转生记录失败");
                return ApiResult<PagedResult<NirvanaRecordDto>>.CreateError("获取转生记录失败");
            }
        }

        /// <summary>
        /// 获取转生统计信息
        /// </summary>
        public async Task<ApiResult<NirvanaStatisticsDto>> GetStatisticsAsync(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var query = _db.Queryable<PetNirvanaLog>();

                if (startDate.HasValue)
                    query = query.Where(l => l.CreateTime >= startDate.Value);
                
                if (endDate.HasValue)
                    query = query.Where(l => l.CreateTime <= endDate.Value);

                var logs = await query.ToListAsync();

                var statistics = new NirvanaStatisticsDto
                {
                    TotalAttempts = logs.Count,
                    SuccessCount = logs.Count(l => l.IsSuccess == 1),
                    TotalCostGold = logs.Sum(l => l.CostGold),
                    FaceChangeCount = logs.Count(l => l.NirvanaType == "FACE_CHANGE"),
                    TimeRange = $"{startDate:yyyy-MM-dd} 至 {endDate:yyyy-MM-dd}"
                };

                if (statistics.TotalAttempts > 0)
                {
                    statistics.SuccessRate = (decimal)statistics.SuccessCount / statistics.TotalAttempts;
                    
                    var successfulLogs = logs.Where(l => l.IsSuccess == 1 && l.ResultGrowth.HasValue).ToList();
                    if (successfulLogs.Any())
                    {
                        statistics.AverageGrowthGained = successfulLogs.Average(l => l.ResultGrowth.Value);
                    }
                }

                return ApiResult<NirvanaStatisticsDto>.CreateSuccess(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取转生统计信息失败");
                return ApiResult<NirvanaStatisticsDto>.CreateError("获取统计信息失败");
            }
        }

        /// <summary>
        /// 消耗道具
        /// </summary>
        private async Task ConsumeItemAsync(int userId, string itemId)
        {
            try
            {
                var userItem = await _db.Queryable<user_item>()
                    .FirstAsync(i => i.user_id == userId && i.item_id == itemId && i.item_count > 0);

                if (userItem != null)
                {
                    if (userItem.item_count > 1)
                    {
                        // 减少数量
                        await _db.Updateable<user_item>()
                            .SetColumns(i => i.item_count == userItem.item_count - 1)
                            .Where(i => i.item_seq == userItem.item_seq)
                            .ExecuteCommandAsync();
                    }
                    else
                    {
                        // 删除道具
                        await _db.Deleteable<user_item>()
                            .Where(i => i.item_seq == userItem.item_seq)
                            .ExecuteCommandAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "消耗道具失败 - 用户ID:{UserId}, 道具ID:{ItemId}", userId, itemId);
            }
        }

        /// <summary>
        /// 获取宠物名称
        /// </summary>
        private string GetPetName(int petNo)
        {
            try
            {
                var petConfig = _db.Queryable<pet_config>().First(p => p.pet_no == petNo);
                return petConfig?.name ?? $"宠物{petNo}";
            }
            catch
            {
                return $"宠物{petNo}";
            }
        }

        /// <summary>
        /// 获取用户的涅槃兽列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>涅槃兽列表</returns>
        public async Task<List<object>> GetUserNirvanaBeastsAsync(int userId)
        {
            try
            {
                _logger.LogInformation("开始获取用户涅槃兽列表 - 用户ID:{UserId}", userId);

                // 查询用户所有的涅槃兽（pet_no = 103），关联pet_config获取宠物名称
                var nirvanaBeasts = await _db.Queryable<user_pet>()
                    .LeftJoin<pet_config>((up, pc) => up.pet_no == pc.pet_no)
                    .Where((up, pc) => up.user_id == userId && up.pet_no == 103)
                    .Select((up, pc) => new
                    {
                        宠物序号 = up.id,
                        宠物名字 = up.name ?? pc.name, // 优先使用用户自定义名称，否则使用配置表中的默认名称
                        等级 = up.level,
                        形象 = up.pet_no, // 涅槃兽形象固定为103
                        状态 = up.status,
                        成长 = up.growth,
                        五行 = pc.attribute ?? "无"
                    })
                    .ToListAsync();

                _logger.LogInformation("查询到{Count}只涅槃兽 - 用户ID:{UserId}", nirvanaBeasts.Count, userId);

                // 转换为object列表以匹配接口返回类型
                var result = nirvanaBeasts.Cast<object>().ToList();

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户涅槃兽列表失败 - 用户ID:{UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// 创建默认转生配置（基于老系统的硬编码逻辑）
        /// </summary>
        private async Task<PetNirvanaConfig> CreateDefaultNirvanaConfigAsync(int mainPetNo, int subPetNo, int nirvanaPetNo, int resultPetNo)
        {
            try
            {
                _logger.LogInformation("开始创建默认转生配置 - 主宠:{MainPet}, 副宠:{SubPet}, 涅槃兽:{NirvanaPet}",
                    mainPetNo, subPetNo, nirvanaPetNo);

                // 基于老系统的硬编码参数创建默认配置
                var defaultConfig = new PetNirvanaConfig
                {
                    MainPetNo = mainPetNo,
                    SubPetNo = subPetNo,
                    NirvanaPetNo = nirvanaPetNo,
                    ResultPetNo = resultPetNo, // 转生后保持主宠形象

                    // 老系统硬编码的基础参数
                    RequiredLevel = 60,           // 老系统固定60级要求
                    BaseSuccessRate = 30.00m,     // 老系统基础30%成功率
                    CostGold = 500000,            // 老系统固定500,000金币

                    // 老系统的成长继承比例
                    MainGrowthInherit = 0.2500m,  // 主宠成长继承25%
                    SubGrowthInherit = 0.0500m,   // 副宠成长继承5%

                    // 特殊规则（JSON格式，暂时为空）
                    SpecialRule = "{}",

                    // 系统字段
                    IsActive = 1,                 // 激活状态
                    CreateTime = DateTime.Now     // 创建时间
                };

                // 保存到数据库
                var savedConfig = await _configService.CreateConfigAsync(defaultConfig);

                if (savedConfig != null)
                {
                    _logger.LogInformation("默认转生配置创建成功 - ID:{ConfigId}", savedConfig.Id);
                    return savedConfig;
                }
                else
                {
                    _logger.LogWarning("默认转生配置创建失败 - 可能已存在相同配置");

                    // 尝试再次查询，可能是并发创建导致的
                    var existingConfig = await _configService.FindMatchingConfigAsync(mainPetNo, subPetNo, nirvanaPetNo);
                    if (existingConfig != null)
                    {
                        _logger.LogInformation("找到已存在的转生配置 - ID:{ConfigId}", existingConfig.Id);
                        return existingConfig;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建默认转生配置失败 - 主宠:{MainPet}, 副宠:{SubPet}, 涅槃兽:{NirvanaPet}",
                    mainPetNo, subPetNo, nirvanaPetNo);
                return null;
            }
        }
    }
}
