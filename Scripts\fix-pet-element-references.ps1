# PowerShell脚本：批量修复pet.element引用
# 将所有使用pet.element的地方改为通过pet_config表获取attribute

Write-Host "开始批量修复pet.element引用..." -ForegroundColor Green

# 定义需要修复的文件列表
$filesToFix = @(
    "Controllers\PetSynthesisTestController.cs",
    "Services\EquipmentService.cs", 
    "Services\PetManagementService.cs",
    "Services\PropScript\SimplePropScriptEngine.cs"
)

# 定义替换规则
$replacements = @{
    # 直接使用pet.element的情况
    "pet\.element" = "GetPetAttribute(pet.pet_no)"
    "userPet\.element" = "GetPetAttribute(userPet.pet_no)"
    "mainPet\.element" = "GetPetAttribute(mainPet.pet_no)"
    "subPet\.element" = "GetPetAttribute(subPet.pet_no)"
    "currentPet\.element" = "GetPetAttribute(currentPet.pet_no)"
    
    # 在条件判断中的使用
    "x\.element" = "GetPetAttribute(x.pet_no)"
    "p\.element" = "GetPetAttribute(p.pet_no)"
    
    # 在LINQ查询中的使用
    "\.Where\(.*x\.element.*\)" = ".Where(x => fiveElements.Contains(GetPetAttribute(x.pet_no)))"
}

foreach ($file in $filesToFix) {
    $fullPath = Join-Path $PSScriptRoot "..\$file"
    
    if (Test-Path $fullPath) {
        Write-Host "正在修复文件: $file" -ForegroundColor Yellow
        
        try {
            $content = Get-Content $fullPath -Raw -Encoding UTF8
            $originalContent = $content
            
            # 应用替换规则
            foreach ($pattern in $replacements.Keys) {
                $replacement = $replacements[$pattern]
                $content = $content -replace $pattern, $replacement
            }
            
            # 如果内容有变化，则写回文件
            if ($content -ne $originalContent) {
                Set-Content $fullPath $content -Encoding UTF8
                Write-Host "  ✅ 文件已修复" -ForegroundColor Green
            } else {
                Write-Host "  ℹ️ 文件无需修复" -ForegroundColor Cyan
            }
        }
        catch {
            Write-Host "  ❌ 修复失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "  ⚠️ 文件不存在: $file" -ForegroundColor Magenta
    }
}

Write-Host "`n批量修复完成！" -ForegroundColor Green
Write-Host "请注意：" -ForegroundColor Yellow
Write-Host "1. 需要在每个修复的文件中添加GetPetAttribute辅助方法" -ForegroundColor Yellow
Write-Host "2. 需要确保每个服务都能访问pet_config表" -ForegroundColor Yellow
Write-Host "3. 建议运行编译检查确保修复正确" -ForegroundColor Yellow
