/**
 * 牧场页面 API 适配器
 * 为 Pasture.html 页面提供与后台 API 的对接功能
 */

const API_BASE_URL = 'http://localhost:5000/api';

// 获取当前用户ID的辅助函数
function getCurrentUserId() {
    try {
        // 优先从认证管理器获取
        if (window.authManager && typeof window.authManager.getCurrentUserId === 'function') {
            const userId = window.authManager.getCurrentUserId();
            if (userId && userId > 0) {
                return userId;
            }
        }
        
        // 从localStorage获取
        const currentUser = localStorage.getItem('currentUser');
        if (currentUser) {
            const user = JSON.parse(currentUser);
            return user.userId || user.id || 1;
        }
        
        // 默认测试用户ID
        return 1;
    } catch (error) {
        console.error('获取用户ID失败:', error);
        return 1;
    }
}

// 创建 window.external 对象的模拟实现
if (typeof window.external === 'undefined') {
    window.external = {};
}

// 牧场 API 适配器对象
Object.assign(window.external, {
    
    /**
     * 检查页面是否可以加载数据
     * @returns {string} "true" 表示可以加载
     */
    check() {
        console.log('🔍 检查页面加载状态...');
        return "true";
    },

    /**
     * 获取牧场容量信息
     * @returns {number} 牧场最大容量
     */
    async fhfvnsd() {
        try {
            const userId = getCurrentUserId();
            console.log('🏠 获取牧场容量信息 - 用户ID:', userId);
            
            const response = await fetch(`${API_BASE_URL}/PetManagement/capacity/${userId}`);
            const result = await response.json();
            
            if (result.success) {
                console.log('✅ 获取牧场容量成功:', result.data);
                return result.data.maxCapacity || 80;
            } else {
                console.error('❌ 获取牧场容量失败:', result.message);
                return 80; // 默认容量
            }
        } catch (error) {
            console.error('💥 获取牧场容量异常:', error);
            return 80; // 默认容量
        }
    },

    /**
     * 获取牧场宠物列表数据
     * @returns {string} JSON格式的宠物列表
     */
    async getPastureData() {
        try {
            const userId = getCurrentUserId();
            console.log('🐾 获取牧场宠物列表 - 用户ID:', userId);
            
            const response = await fetch(`${API_BASE_URL}/PetManagement/pasture/${userId}?status=牧场&pageSize=100`);
            const result = await response.json();
            
            if (result.success && result.pets) {
                console.log('✅ 获取牧场宠物成功，数量:', result.pets.length);
                
                // 转换为页面所需的格式
                const convertedPets = result.pets.map(pet => ({
                    宠物序号: pet.id,
                    宠物名字: pet.name,
                    等级: pet.level,
                    五行: pet.element || '无',
                    境界: pet.realm || '无',
                    生命: pet.hp,
                    魔法: pet.mp,
                    最大生命: pet.hp,
                    最大魔法: pet.mp,
                    成长: pet.growth,
                    形象: pet.petNo || 264,
                    状态: "1", // 牧场状态
                    当前经验: pet.exp || 0,
                    是否主宠: false // 牧场中的宠物不是主宠
                }));
                
                return JSON.stringify(convertedPets);
            } else {
                console.error('❌ 获取牧场宠物失败:', result.message);
                return JSON.stringify([]);
            }
        } catch (error) {
            console.error('💥 获取牧场宠物异常:', error);
            return JSON.stringify([]);
        }
    },

    /**
     * 存放宠物到牧场（寄养）
     * @param {number} petId 宠物ID
     * @returns {boolean} 操作是否成功
     */
    async jiyang(petId) {
        try {
            const userId = getCurrentUserId();
            console.log('📦 存放宠物到牧场 - 用户ID:', userId, '宠物ID:', petId);
            
            const response = await fetch(`${API_BASE_URL}/PetManagement/store`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    userId: userId,
                    petId: parseInt(petId)
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                console.log('✅ 存放宠物成功');
                // 刷新页面数据
                await this.refreshPastureData();
                await this.refreshCarryPetsData();
                return true;
            } else {
                console.error('❌ 存放宠物失败:', result.message);
                alert(result.message || '存放失败');
                return false;
            }
        } catch (error) {
            console.error('💥 存放宠物异常:', error);
            alert('网络错误，请重试');
            return false;
        }
    },

    /**
     * 携带宠物（从牧场取出）
     * @param {number} petId 宠物ID
     * @returns {boolean} 操作是否成功
     */
    async xiedai(petId) {
        try {
            const userId = getCurrentUserId();
            console.log('🎒 携带宠物 - 用户ID:', userId, '宠物ID:', petId);
            
            const response = await fetch(`${API_BASE_URL}/PetManagement/carry`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    userId: userId,
                    petId: parseInt(petId)
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                console.log('✅ 携带宠物成功');
                // 刷新页面数据
                await this.refreshPastureData();
                await this.refreshCarryPetsData();
                return true;
            } else {
                console.error('❌ 携带宠物失败:', result.message);
                alert(result.message || '携带失败');
                return false;
            }
        } catch (error) {
            console.error('💥 携带宠物异常:', error);
            alert('网络错误，请重试');
            return false;
        }
    },

    /**
     * 设置主战宠物
     * @param {number} petId 宠物ID
     * @returns {boolean} 操作是否成功
     */
    async getPetInfo(petId) {
        try {
            const userId = getCurrentUserId();
            console.log('⭐ 设置主战宠物 - 用户ID:', userId, '宠物ID:', petId);
            
            const response = await fetch(`${API_BASE_URL}/PetManagement/setMain`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    userId: userId,
                    petId: parseInt(petId)
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                console.log('✅ 设置主战宠物成功');
                // 刷新携带宠物数据以更新主宠状态
                await this.refreshCarryPetsData();
                return true;
            } else {
                console.error('❌ 设置主战宠物失败:', result.message);
                alert(result.message || '设置主宠失败');
                return false;
            }
        } catch (error) {
            console.error('💥 设置主战宠物异常:', error);
            alert('网络错误，请重试');
            return false;
        }
    },

    /**
     * 丢弃宠物
     * @param {number} petId 宠物ID
     * @param {number} confirmFlag 确认标志
     * @returns {boolean} 操作是否成功
     */
    async deletePet(petId, confirmFlag) {
        try {
            const userId = getCurrentUserId();
            console.log('🗑️ 丢弃宠物 - 用户ID:', userId, '宠物ID:', petId);
            
            const response = await fetch(`${API_BASE_URL}/PetManagement/discard`, {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    userId: userId,
                    petId: parseInt(petId)
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                console.log('✅ 丢弃宠物成功');
                // 刷新页面数据
                await this.refreshPastureData();
                await this.refreshCarryPetsData();
                return true;
            } else {
                console.error('❌ 丢弃宠物失败:', result.message);
                alert(result.message || '丢弃失败');
                return false;
            }
        } catch (error) {
            console.error('💥 丢弃宠物异常:', error);
            alert('网络错误，请重试');
            return false;
        }
    },

    /**
     * 获取携带宠物列表数据（用于updatePet函数）
     * @returns {string} JSON格式的携带宠物列表
     */
    async getCarryPetsData() {
        try {
            const userId = getCurrentUserId();
            console.log('🎒 获取携带宠物列表 - 用户ID:', userId);

            const response = await fetch(`${API_BASE_URL}/Player/GetCarryPets?userId=${userId}`);
            const result = await response.json();

            if (result.success && result.pets) {
                console.log('✅ 获取携带宠物成功，数量:', result.pets.length);

                // 转换为页面所需的格式
                const convertedPets = result.pets.map(pet => ({
                    宠物序号: pet.id,
                    宠物名字: pet.name,
                    等级: pet.level,
                    五行: pet.element || '无',
                    境界: pet.realm || '无',
                    生命: pet.hp,
                    魔法: pet.mp,
                    最大生命: pet.hp,
                    最大魔法: pet.mp,
                    成长: pet.growth,
                    形象: pet.petNo || 264,
                    状态: pet.isMain ? "0" : "1", // 0表示主战宠物，1表示普通携带
                    当前经验: pet.exp || 0,
                    是否主宠: pet.isMain
                }));

                return JSON.stringify(convertedPets);
            } else {
                console.error('❌ 获取携带宠物失败:', result.message);
                return JSON.stringify([]);
            }
        } catch (error) {
            console.error('💥 获取携带宠物异常:', error);
            return JSON.stringify([]);
        }
    },

    /**
     * 刷新牧场数据
     */
    async refreshPastureData() {
        try {
            console.log('🔄 刷新牧场数据...');
            const newData = await this.getPastureData();

            // 更新页面数据
            if (typeof window.updatePetList === 'function') {
                window.updatePetList(newData);
            }

            // 更新全局变量
            if (typeof window.JSONS !== 'undefined') {
                window.JSONS = JSON.parse(newData);
            }

            console.log('✅ 牧场数据刷新完成');
        } catch (error) {
            console.error('💥 刷新牧场数据失败:', error);
        }
    },

    /**
     * 刷新携带宠物数据
     */
    async refreshCarryPetsData() {
        try {
            console.log('🔄 刷新携带宠物数据...');
            const carryData = await this.getCarryPetsData();

            // 更新携带宠物显示
            if (typeof window.updatePet === 'function') {
                window.updatePet(carryData);
            }

            console.log('✅ 携带宠物数据刷新完成');
        } catch (error) {
            console.error('💥 刷新携带宠物数据失败:', error);
        }
    }
});

// 页面初始化时自动加载数据
$(document).ready(async function() {
    console.log('🚀 牧场页面初始化...');

    try {
        // 检查是否可以加载数据
        if (window.external.check() === "true") {
            console.log('📊 开始加载牧场数据...');

            // 获取牧场数据
            const pastureData = await window.external.getPastureData();

            // 获取携带宠物数据
            const carryData = await window.external.getCarryPetsData();

            // 更新页面
            if (typeof window.updatePetList === 'function') {
                window.updatePetList(pastureData);
                window.JSONS = JSON.parse(pastureData);
                console.log('✅ 牧场数据加载完成');
            } else {
                console.warn('⚠️ updatePetList 函数未找到，延迟加载...');
                // 延迟重试
                setTimeout(async () => {
                    if (typeof window.updatePetList === 'function') {
                        window.updatePetList(pastureData);
                        window.JSONS = JSON.parse(pastureData);
                        console.log('✅ 牧场数据延迟加载完成');
                    }
                }, 1000);
            }

            // 更新携带宠物显示
            if (typeof window.updatePet === 'function') {
                window.updatePet(carryData);
                console.log('✅ 携带宠物数据加载完成');
            } else {
                console.warn('⚠️ updatePet 函数未找到，延迟加载...');
                // 延迟重试
                setTimeout(async () => {
                    if (typeof window.updatePet === 'function') {
                        window.updatePet(carryData);
                        console.log('✅ 携带宠物数据延迟加载完成');
                    }
                }, 1000);
            }
        }
    } catch (error) {
        console.error('💥 牧场页面初始化失败:', error);
    }
});

console.log('🎮 牧场 API 适配器已加载');
