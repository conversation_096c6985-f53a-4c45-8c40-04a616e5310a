---
type: "manual"
---

任务执行完成后记得关闭打开的进程

### 🔄 工作流程
**分析 → 实施 → 验证** - 先分析再动手，避免盲目开发

### 🛠️ 工具使用
- **页面分析**: `view` 查看HTML/JS，搜索 `window.external`
- **后端分析**: `codebase-retrieval` 查找Controller/API
- **测试验证**: 创建测试页面记录结果

### ⚡ 技术标准
- **API适配**: 统一在 `game-api-adapter.js` 处理
- **数据转换**: 标准JSON → 页面期望格式
- **Controller**: 优先复用现有Controller

### 🚨 **关键原则 - 避免过度工程化**
- ✅ **最小化改动** - 只修复真正问题，不重写正常代码
- ✅ **保持原架构** - 尊重现有HTML/JS结构
- ✅ **渐进式改进** - 先修复关键问题再优化
- ✅ **兼容性优先** - 确保100%兼容原版

### 🔍 问题诊断
1. **先查简单问题**: 路径错误、依赖缺失、语法错误
2. **再查复杂问题**: API调用、数据库、业务逻辑
3. **避免假设**: 不要假设问题复杂
4. **对比分析**: 对比工作版本vs问题版本

### 📁 文档管理
- 中文命名，统一存放在 `WebApplication_HM/文档/`

### 💡 核心教训
**BattleMap案例**: 原本只需修复jQuery路径，却错误地重写了整个页面逻辑 - 典型的过度工程化错误！

**记住**: 🎯 **简单问题简单解决，不要把简单问题复杂化！**
