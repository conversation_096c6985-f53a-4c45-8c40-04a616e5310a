namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 整理背包结果DTO
    /// </summary>
    public class SortBagResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 整理前物品数量
        /// </summary>
        public int BeforeCount { get; set; }

        /// <summary>
        /// 整理后物品数量
        /// </summary>
        public int AfterCount { get; set; }

        /// <summary>
        /// 合并的物品数量
        /// </summary>
        public int MergedCount { get; set; }

        /// <summary>
        /// 整理详情
        /// </summary>
        public string Details { get; set; }
    }
} 