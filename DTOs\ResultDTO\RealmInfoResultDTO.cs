namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 境界信息结果DTO
    /// </summary>
    public class RealmInfoResultDTO
    {
        /// <summary>
        /// 境界等级
        /// </summary>
        public int RealmLevel { get; set; }

        /// <summary>
        /// 境界名称
        /// </summary>
        public string RealmName { get; set; } = "";

        /// <summary>
        /// 属性加成倍率
        /// </summary>
        public double AttributeBonusRate { get; set; }

        /// <summary>
        /// 升级成功率
        /// </summary>
        public double UpgradeSuccessRate { get; set; }

        /// <summary>
        /// 升级消耗金币
        /// </summary>
        public long UpgradeCostGold { get; set; }

        /// <summary>
        /// 升级所需道具
        /// </summary>
        public string? UpgradeItemRequired { get; set; }

        /// <summary>
        /// 境界描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 是否可以升级
        /// </summary>
        public bool CanUpgrade { get; set; }

        /// <summary>
        /// 下一境界信息
        /// </summary>
        public NextRealmInfo? NextRealm { get; set; }
    }

    /// <summary>
    /// 下一境界信息
    /// </summary>
    public class NextRealmInfo
    {
        /// <summary>
        /// 下一境界等级
        /// </summary>
        public int RealmLevel { get; set; }

        /// <summary>
        /// 下一境界名称
        /// </summary>
        public string RealmName { get; set; } = "";

        /// <summary>
        /// 下一境界属性加成倍率
        /// </summary>
        public double AttributeBonusRate { get; set; }
    }

    /// <summary>
    /// 境界列表结果DTO
    /// </summary>
    public class RealmListResultDTO
    {
        /// <summary>
        /// 境界配置列表
        /// </summary>
        public List<RealmConfigInfo> Realms { get; set; } = new();
    }

    /// <summary>
    /// 境界配置信息
    /// </summary>
    public class RealmConfigInfo
    {
        /// <summary>
        /// 境界等级
        /// </summary>
        public int RealmLevel { get; set; }

        /// <summary>
        /// 境界名称
        /// </summary>
        public string RealmName { get; set; } = "";

        /// <summary>
        /// 等级要求
        /// </summary>
        public int LevelRequirement { get; set; }

        /// <summary>
        /// 属性加成倍率
        /// </summary>
        public double AttributeBonusRate { get; set; }

        /// <summary>
        /// 升级成功率
        /// </summary>
        public double UpgradeSuccessRate { get; set; }

        /// <summary>
        /// 升级消耗金币
        /// </summary>
        public long UpgradeCostGold { get; set; }

        /// <summary>
        /// 升级所需道具
        /// </summary>
        public string? UpgradeItemRequired { get; set; }

        /// <summary>
        /// 境界描述
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// 境界历史结果DTO
    /// </summary>
    public class RealmHistoryResultDTO
    {
        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int CurrentPage { get; set; }

        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// 升级记录列表
        /// </summary>
        public List<RealmUpgradeRecord> Records { get; set; } = new();
    }

    /// <summary>
    /// 境界升级记录
    /// </summary>
    public class RealmUpgradeRecord
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 宠物ID
        /// </summary>
        public int PetId { get; set; }

        /// <summary>
        /// 升级前境界等级
        /// </summary>
        public int FromRealmLevel { get; set; }

        /// <summary>
        /// 升级前境界名称
        /// </summary>
        public string FromRealmName { get; set; } = "";

        /// <summary>
        /// 升级后境界等级
        /// </summary>
        public int ToRealmLevel { get; set; }

        /// <summary>
        /// 升级后境界名称
        /// </summary>
        public string ToRealmName { get; set; } = "";

        /// <summary>
        /// 升级类型
        /// </summary>
        public string UpgradeType { get; set; } = "";

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消耗金币
        /// </summary>
        public long CostGold { get; set; }

        /// <summary>
        /// 消耗道具
        /// </summary>
        public string? CostItems { get; set; }

        /// <summary>
        /// 升级时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
    }
}
