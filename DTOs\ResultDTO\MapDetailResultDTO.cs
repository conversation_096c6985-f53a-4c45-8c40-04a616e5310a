namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 地图详情结果DTO
    /// </summary>
    public class MapDetailResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 地图基础信息
        /// </summary>
        public MapInfoDTO? MapInfo { get; set; }

        /// <summary>
        /// 地图详细配置
        /// </summary>
        public MapDetailConfigDTO? DetailConfig { get; set; }

        /// <summary>
        /// 地图怪物列表
        /// </summary>
        public List<MapMonsterDTO> Monsters { get; set; } = new List<MapMonsterDTO>();

        /// <summary>
        /// 地图掉落配置
        /// </summary>
        public List<MapDropDTO> Drops { get; set; } = new List<MapDropDTO>();
    }

    /// <summary>
    /// 地图详细配置DTO
    /// </summary>
    public class MapDetailConfigDTO
    {
        /// <summary>
        /// 限制等级
        /// </summary>
        public int LimitLevel { get; set; }

        /// <summary>
        /// 限制成长
        /// </summary>
        public decimal LimitGrowth { get; set; }

        /// <summary>
        /// 是否需要钥匙
        /// </summary>
        public bool RequireKey { get; set; }

        /// <summary>
        /// 最小金币奖励
        /// </summary>
        public long MinGold { get; set; }

        /// <summary>
        /// 最大金币奖励
        /// </summary>
        public long MaxGold { get; set; }

        /// <summary>
        /// 最小元宝奖励
        /// </summary>
        public int MinYuanbao { get; set; }

        /// <summary>
        /// 最大元宝奖励
        /// </summary>
        public int MaxYuanbao { get; set; }
    }

    /// <summary>
    /// 地图怪物DTO
    /// </summary>
    public class MapMonsterDTO
    {
        /// <summary>
        /// 怪物ID
        /// </summary>
        public int MonsterId { get; set; }

        /// <summary>
        /// 怪物名称
        /// </summary>
        public string MonsterName { get; set; } = string.Empty;

        /// <summary>
        /// 怪物成长
        /// </summary>
        public long Growth { get; set; }

        /// <summary>
        /// 怪物五行属性
        /// </summary>
        public string Element { get; set; } = string.Empty;

        /// <summary>
        /// 最小等级
        /// </summary>
        public int MinLevel { get; set; }

        /// <summary>
        /// 最大等级
        /// </summary>
        public int MaxLevel { get; set; }

        /// <summary>
        /// 当前等级（战斗时随机生成）
        /// </summary>
        public int CurrentLevel { get; set; }

        /// <summary>
        /// 等级范围描述
        /// </summary>
        public string LevelRange => $"{MinLevel}-{MaxLevel}级";

        /// <summary>
        /// 经验奖励
        /// </summary>
        public long ExpReward { get; set; }
    }

    /// <summary>
    /// 地图掉落DTO
    /// </summary>
    public class MapDropDTO
    {
        /// <summary>
        /// 道具ID
        /// </summary>
        public int ItemId { get; set; }

        /// <summary>
        /// 道具名称
        /// </summary>
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 掉落类型
        /// </summary>
        public string DropType { get; set; } = string.Empty;

        /// <summary>
        /// 掉落概率 (0-1之间的小数)
        /// </summary>
        public decimal DropRate { get; set; }

        /// <summary>
        /// 最小数量
        /// </summary>
        public int MinQuantity { get; set; }

        /// <summary>
        /// 最大数量
        /// </summary>
        public int MaxQuantity { get; set; }

        /// <summary>
        /// 数量范围
        /// </summary>
        public string CountRange => $"{MinQuantity}-{MaxQuantity}";
    }

    /// <summary>
    /// 地图战斗信息DTO
    /// </summary>
    public class MapBattleInfoDTO
    {
        /// <summary>
        /// 地图ID
        /// </summary>
        public int MapId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 宠物ID
        /// </summary>
        public int PetId { get; set; }

        /// <summary>
        /// 怪物列表
        /// </summary>
        public List<MapMonsterDTO> Monsters { get; set; } = new List<MapMonsterDTO>();

        /// <summary>
        /// 奖励列表
        /// </summary>
        public List<MapDropDTO> Rewards { get; set; } = new List<MapDropDTO>();

        /// <summary>
        /// 战斗开始时间
        /// </summary>
        public DateTime BattleStartTime { get; set; }
    }

    /// <summary>
    /// 进入地图结果DTO
    /// </summary>
    public class EnterMapResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 战斗信息
        /// </summary>
        public MapBattleInfoDTO? BattleInfo { get; set; }
    }

    /// <summary>
    /// 解锁地图结果DTO
    /// </summary>
    public class UnlockMapResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 解锁的地图信息
        /// </summary>
        public MapInfoDTO? MapInfo { get; set; }
    }

    /// <summary>
    /// 更新地图进度结果DTO
    /// </summary>
    public class UpdateMapProgressResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 新的最佳成绩
        /// </summary>
        public int? NewBestScore { get; set; }

        /// <summary>
        /// 是否创造了新记录
        /// </summary>
        public bool IsNewRecord { get; set; }
    }

    /// <summary>
    /// 用户地图进度DTO
    /// </summary>
    public class UserMapProgressDTO
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 已解锁的地图ID列表
        /// </summary>
        public List<int> UnlockedMaps { get; set; } = new List<int>();

        /// <summary>
        /// 地图最佳成绩字典 (地图ID -> 最佳成绩)
        /// </summary>
        public Dictionary<int, int> MapBestScores { get; set; } = new Dictionary<int, int>();

        /// <summary>
        /// 地图通关次数字典 (地图ID -> 通关次数)
        /// </summary>
        public Dictionary<int, int> MapCompletionCounts { get; set; } = new Dictionary<int, int>();
    }

    /// <summary>
    /// 地图解锁条件检查结果DTO
    /// </summary>
    public class MapUnlockCheckResultDTO
    {
        /// <summary>
        /// 是否可以解锁
        /// </summary>
        public bool CanUnlock { get; set; }

        /// <summary>
        /// 不能解锁的原因
        /// </summary>
        public List<string> Reasons { get; set; } = new List<string>();

        /// <summary>
        /// 当前用户等级
        /// </summary>
        public int CurrentLevel { get; set; }

        /// <summary>
        /// 需要的等级
        /// </summary>
        public int RequiredLevel { get; set; }

        /// <summary>
        /// 当前用户成长
        /// </summary>
        public decimal CurrentGrowth { get; set; }

        /// <summary>
        /// 需要的成长
        /// </summary>
        public decimal RequiredGrowth { get; set; }

        /// <summary>
        /// 缺少的前置地图
        /// </summary>
        public List<int> MissingPrerequisiteMaps { get; set; } = new List<int>();
    }
}