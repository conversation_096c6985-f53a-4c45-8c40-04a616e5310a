using Microsoft.Extensions.Logging;
using SqlSugar;
using WebApplication_HM.Sugar;
using WebApplication_HM.Models;
using WebApplication_HM.DTOs.Common;
using WebApplication_HM.Services;
using WebApplication_HM.Interface;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 装备宝石镶嵌服务
    /// 基于老项目装备宝石/Gemstone.cs中的镶嵌逻辑完整迁移
    /// </summary>
    public class EquipmentGemstoneService
    {
        private readonly ILogger<EquipmentGemstoneService> _logger;
        private readonly DbContext _dbContext;
        private readonly IEquipmentRepository _equipmentRepository;
        private readonly IGemstoneService _gemstoneService;
        private readonly PropService _propService;

        // 二孔宝石类型 (基于老项目)
        private static readonly string[] TWO_HOLE_GEMSTONES = { "混元", "赤炎", "烛龙" };
        
        // 三孔宝石类型 (基于老项目)
        private static readonly string[] THREE_HOLE_GEMSTONES = { "极阳", "圣光", "虚空" };

        public EquipmentGemstoneService(
            ILogger<EquipmentGemstoneService> logger,
            DbContext dbContext,
            IEquipmentRepository equipmentRepository,
            IGemstoneService gemstoneService,
            PropService propService)
        {
            _logger = logger;
            _dbContext = dbContext;
            _equipmentRepository = equipmentRepository;
            _gemstoneService = gemstoneService;
            _propService = propService;
        }

        /// <summary>
        /// 宝石镶嵌主方法
        /// 基于老项目Gemstone.cs中的setGemstone方法完整迁移
        /// </summary>
        /// <param name="userEquipmentId">用户装备ID</param>
        /// <param name="itemId">道具ID</param>
        /// <returns></returns>
        public async Task<ApiResult> SetGemstoneAsync(int userEquipmentId, string itemId)
        {
            try
            {
                var result = await _dbContext.Db.Ado.UseTranAsync(async () =>
                {
                    // 1. 获取装备信息
                    var equipment = await _equipmentRepository.GetEquipmentByIdAsync(userEquipmentId);
                    if (equipment == null)
                        return ApiResult.CreateError("装备不存在");

                    var equipDetail = await _equipmentRepository.GetEquipmentDetailAsync(equipment.equip_id);
                    if (equipDetail == null)
                        return ApiResult.CreateError("装备配置不存在");

                    // 2. 获取道具信息
                    var item = await GetUserItemAsync(equipment.user_id, itemId);
                    if (item == null)
                        return ApiResult.CreateError("道具不存在，无法继续。");

                    // 3. 获取道具脚本
                    var itemScript = await GetItemScriptAsync(itemId);
                    if (string.IsNullOrEmpty(itemScript))
                        return ApiResult.CreateError("道具脚本不存在");

                    // 4. 巫族装备特殊限制检查
                    if (equipDetail.element_limit == "巫" && !itemScript.Contains("返回消耗|"))
                        return ApiResult.CreateError("该类型装备不能镶嵌宝石。");

                    // 5. 根据脚本类型执行不同操作
                    if (itemScript.Contains("清空宝石|"))
                    {
                        return await ClearGemstonesAsync(equipment, item);
                    }
                    else if (itemScript.Contains("宝石开孔|"))
                    {
                        return await OpenGemstoneSlotAsync(equipment, item);
                    }
                    else if (itemScript.Contains("返回消耗|"))
                    {
                        return await ReturnEnhanceMaterialsAsync(equipment, item, itemScript);
                    }
                    else if (itemScript.Contains("宝石镶嵌|"))
                    {
                        return await EmbedGemstoneAsync(equipment, item, itemScript);
                    }
                    else
                    {
                        return ApiResult.CreateError("不支持的道具类型");
                    }
                });

                return result.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "宝石操作失败，装备ID: {EquipmentId}, 道具ID: {ItemId}", userEquipmentId, itemId);
                return ApiResult.CreateError("宝石操作失败");
            }
        }

        /// <summary>
        /// 清空宝石
        /// 基于老项目清空宝石逻辑完整迁移
        /// </summary>
        private async Task<ApiResult> ClearGemstonesAsync(user_equipment equipment, user_item item)
        {
            try
            {
                // 1. 检查装备是否有宝石
                var gemstones = await _equipmentRepository.GetEquipmentGemstonesAsync(equipment.id);
                if (!gemstones.Any())
                {
                    return ApiResult.CreateError("本装备没有宝石属性，无法清空宝石！");
                }

                // 2. 将宝石返还到背包
                foreach (var gemstone in gemstones)
                {
                    var gemstoneConfig = await GetGemstoneConfigAsync(gemstone.gemstone_type_name);
                    if (gemstoneConfig != null)
                    {
                        await AddItemToUserAsync(equipment.user_id, gemstoneConfig.prop_id ?? "", 1);
                    }
                }

                // 3. 清空装备上的宝石
                await _equipmentRepository.ClearAllGemstonesAsync(equipment.id);

                // 4. 消耗清空道具
                await ConsumeItemAsync(equipment.user_id, item.item_id, 1);

                // 5. 记录操作日志
                await LogGemstoneOperationAsync(equipment.user_id, equipment.id, "CLEAR_ALL",
                    "清空宝石", "SUCCESS", "宝石清空成功！对应的宝石已经拆卸到你的背包。");

                return ApiResult.CreateSuccess("宝石清空成功！对应的宝石已经拆卸到你的背包。");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清空宝石失败，装备ID: {EquipmentId}", equipment.id);
                return ApiResult.CreateError("清空宝石失败");
            }
        }

        /// <summary>
        /// 宝石开孔
        /// 基于老项目开孔逻辑完整迁移
        /// </summary>
        private async Task<ApiResult> OpenGemstoneSlotAsync(user_equipment equipment, user_item item)
        {
            try
            {
                // 1. 计算开孔所需道具数量 (基于老项目逻辑)
                int requiredCount = 10;
                if (equipment.slot >= 1) requiredCount = 20;

                // 2. 检查道具数量是否足够
                if (item.item_count < requiredCount)
                {
                    return ApiResult.CreateError($"需要开孔道具：{requiredCount}个，您的数量不足，无法开孔！");
                }

                // 3. 检查槽位限制 (最多3个槽位)
                if (equipment.slot >= 2)
                {
                    return ApiResult.CreateError("镶嵌宝石失败，装备只能开三个槽位！");
                }

                // 4. 增加槽位
                equipment.slot = (equipment.slot ?? 0) + 1;
                equipment.update_time = DateTime.Now;
                await _equipmentRepository.UpdateEquipmentAsync(equipment);

                // 5. 消耗开孔道具
                await ConsumeItemAsync(equipment.user_id, item.item_id, requiredCount);

                // 6. 记录操作日志
                await LogGemstoneOperationAsync(equipment.user_id, equipment.id, "OPEN_SLOT",
                    $"开孔+{equipment.slot}", "SUCCESS", "开槽成功！");

                return ApiResult.CreateSuccess("开槽成功！");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "宝石开孔失败，装备ID: {EquipmentId}", equipment.id);
                return ApiResult.CreateError("宝石开孔失败");
            }
        }

        /// <summary>
        /// 宝石镶嵌
        /// 基于老项目宝石镶嵌逻辑完整迁移
        /// </summary>
        private async Task<ApiResult> EmbedGemstoneAsync(user_equipment equipment, user_item item, string itemScript)
        {
            try
            {
                // 1. 解析宝石信息
                var scriptParts = itemScript.Split('|');
                if (scriptParts.Length < 2)
                    return ApiResult.CreateError("宝石脚本格式错误");

                var gemstoneTypeName = scriptParts[1];
                var gemstoneConfig = await GetGemstoneConfigByNameAsync(gemstoneTypeName);
                if (gemstoneConfig == null)
                    return ApiResult.CreateError("宝石配置不存在");

                // 2. 检查装备类型限制 (基于老项目逻辑)
                var equipDetail = await _equipmentRepository.GetEquipmentDetailAsync(equipment.equip_id);
                var equipType = await _equipmentRepository.GetEquipmentTypeAsync(int.TryParse(equipDetail.equip_type_id, out int typeId) ? typeId : 1);

                // 检查装备是否可以镶嵌该宝石
                var canEmbed = await CheckEquipmentCanEmbedGemstone(equipType, gemstoneConfig);
                if (!canEmbed.Success)
                    return canEmbed;

                // 3. 获取当前装备的宝石列表
                var currentGemstones = await _equipmentRepository.GetEquipmentGemstonesAsync(equipment.id);
                var currentSlotCount = currentGemstones.Count;

                // 4. 检查槽位限制和宝石类型限制
                var slotCheck = CheckGemstoneSlotRestrictions(currentSlotCount, gemstoneConfig, currentGemstones);
                if (!slotCheck.Success)
                {
                    // 尝试替换同类型低等级宝石
                    var replaceResult = await TryReplaceGemstoneAsync(equipment, gemstoneConfig, item, currentGemstones);
                    return replaceResult;
                }

                // 5. 检查金币是否足够 (基于老项目：10亿金币)
                const long EMBED_COST = 1000000000;
                var user = await GetUserAsync(equipment.user_id);
                if (long.Parse(user.money ?? "0") < EMBED_COST)
                {
                    return ApiResult.CreateError($"金币不足{EMBED_COST}，无法镶嵌。");
                }

                // 6. 执行镶嵌
                var newGemstone = new equipment_gemstone
                {
                    user_equipment_id = equipment.id,
                    gemstone_type_name = gemstoneConfig.type_name,
                    position = currentSlotCount + 1,
                    create_time = DateTime.Now
                };

                await _equipmentRepository.AddGemstoneAsync(newGemstone);

                // 7. 消耗道具和金币
                await ConsumeItemAsync(equipment.user_id, item.item_id, 1);
                await ConsumeMoneyAsync(equipment.user_id, EMBED_COST);

                // 8. 记录操作日志
                await LogGemstoneOperationAsync(equipment.user_id, equipment.id, "EMBED",
                    $"镶嵌{gemstoneTypeName}", "SUCCESS", "镶嵌成功！");

                return ApiResult.CreateSuccess("镶嵌成功！");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "宝石镶嵌失败，装备ID: {EquipmentId}", equipment.id);
                return ApiResult.CreateError("宝石镶嵌失败");
            }
        }

        /// <summary>
        /// 返回强化消耗材料 (时间回溯功能)
        /// 基于老项目"返回消耗"逻辑
        /// </summary>
        private async Task<ApiResult> ReturnEnhanceMaterialsAsync(user_equipment equipment, user_item item, string itemScript)
        {
            try
            {
                // 1. 检查装备是否已强化
                if (equipment.strengthen_level <= 0)
                {
                    return ApiResult.CreateError("该装备未强化，无法返还材料！");
                }

                // 2. 计算返还的强化石数量 (基于老项目逻辑)
                int returnedStones = 0;
                for (int i = 1; i <= equipment.strengthen_level; i++)
                {
                    int levelCost = i <= 5
                        ? Convert.ToInt32(Math.Pow(i, 2) * 150)
                        : Convert.ToInt32(Math.Pow(i, 2) * 1050);
                    returnedStones += levelCost;
                }

                // 3. 返还强化石到背包
                const string STRENGTHEN_STONE_ID = "2016092503"; // 强化石道具ID
                await AddItemToUserAsync(equipment.user_id, STRENGTHEN_STONE_ID, returnedStones);

                // 4. 重置装备强化等级
                equipment.strengthen_level = 0;
                equipment.update_time = DateTime.Now;
                await _equipmentRepository.UpdateEquipmentAsync(equipment);

                // 5. 消耗时间之石
                await ConsumeItemAsync(equipment.user_id, item.item_id, 1);

                // 6. 记录操作日志
                await LogGemstoneOperationAsync(equipment.user_id, equipment.id, "RETURN_MATERIALS",
                    $"返还{returnedStones}个强化石", "SUCCESS", $"时间回溯成功！返还了{returnedStones}个强化石。");

                return ApiResult.CreateSuccess($"时间回溯成功！返还了{returnedStones}个强化石。");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "返还强化材料失败，装备ID: {EquipmentId}", equipment.id);
                return ApiResult.CreateError("时间回溯失败");
            }
        }

        #region 辅助方法

        /// <summary>
        /// 获取用户道具
        /// </summary>
        private async Task<user_item?> GetUserItemAsync(int userId, string itemId)
        {
            return await _dbContext.Db.Queryable<user_item>()
                .Where(x => x.user_id == userId && x.item_id == itemId && x.item_count > 0)
                .FirstAsync();
        }

        /// <summary>
        /// 获取道具脚本
        /// </summary>
        private async Task<string?> GetItemScriptAsync(string itemId)
        {
            // 先通过item_no获取配置，然后获取脚本
            if (!int.TryParse(itemId, out int itemNo))
                return null;

            var itemConfig = await _dbContext.Db.Queryable<item_config>()
                .Where(x => x.item_no == itemNo)
                .FirstAsync();

            if (itemConfig == null) return null;

            var itemScript = await _dbContext.Db.Queryable<item_script>()
                .Where(x => x.item_no == itemConfig.item_no)
                .FirstAsync();
            return itemScript?.script;
        }

        /// <summary>
        /// 根据名称获取宝石配置
        /// </summary>
        private async Task<gemstone_config?> GetGemstoneConfigByNameAsync(string gemstoneTypeName)
        {
            return await _dbContext.Db.Queryable<gemstone_config>()
                .Where(x => x.type_name == gemstoneTypeName)
                .FirstAsync();
        }

        /// <summary>
        /// 获取宝石配置
        /// </summary>
        private async Task<gemstone_config?> GetGemstoneConfigAsync(string gemstoneTypeId)
        {
            return await _dbContext.Db.Queryable<gemstone_config>()
                .Where(x => x.type_name == gemstoneTypeId)
                .FirstAsync();
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        private async Task<user> GetUserAsync(int userId)
        {
            return await _dbContext.Db.Queryable<user>()
                .Where(x => x.id == userId)
                .FirstAsync();
        }

        /// <summary>
        /// 检查装备是否可以镶嵌宝石
        /// 基于老项目的装备类型限制逻辑
        /// </summary>
        private async Task<ApiResult> CheckEquipmentCanEmbedGemstone(equipment_type equipType, gemstone_config gemstoneConfig)
        {
            // 1. 检查装备类型限制 (基于老项目逻辑)
            var restrictedTypes = new[] { "卡牌", "法宝", "灵饰", "背饰" };
            if (restrictedTypes.Contains(equipType.type_name))
            {
                // 检查宝石是否有特殊配置允许镶嵌到这些装备上
                if (string.IsNullOrEmpty(gemstoneConfig.equip_types) ||
                    !gemstoneConfig.equip_types.Contains(equipType.type_name))
                {
                    return ApiResult.CreateError("卡牌、法宝、灵饰、背饰无法镶嵌该宝石！");
                }
            }

            // 2. 检查宝石的装备类型限制
            if (!string.IsNullOrEmpty(gemstoneConfig.equip_types) &&
                !gemstoneConfig.equip_types.Contains(equipType.type_name))
            {
                return ApiResult.CreateError($"该宝石只能镶嵌在部位：{gemstoneConfig.equip_types}的装备上面！");
            }

            return ApiResult.CreateSuccess();
        }

        /// <summary>
        /// 检查宝石槽位限制
        /// 基于老项目的槽位限制逻辑
        /// </summary>
        private ApiResult CheckGemstoneSlotRestrictions(int currentSlotCount, gemstone_config gemstoneConfig,
            List<equipment_gemstone> currentGemstones)
        {
            // 基于老项目的宝石类型定义
            var twoHoleGemstones = new[] { "混元", "赤炎", "烛龙" };
            var threeHoleGemstones = new[] { "极阳", "圣光", "虚空" };

            // 检查槽位限制
            if (string.IsNullOrEmpty(gemstoneConfig.positions))
            {
                // 第1槽：只能镶嵌一孔宝石
                if (currentSlotCount == 0 &&
                    (twoHoleGemstones.Any(g => gemstoneConfig.type_name.Contains(g)) ||
                     threeHoleGemstones.Any(g => gemstoneConfig.type_name.Contains(g))))
                {
                    return ApiResult.CreateError("该槽位不能打二三孔专门打的宝石，只能镶嵌一孔宝石！");
                }

                // 第2槽：只能镶嵌二孔宝石
                if (currentSlotCount == 1 &&
                    !twoHoleGemstones.Any(g => gemstoneConfig.type_name.Contains(g)))
                {
                    return ApiResult.CreateError("该槽位只能打二孔宝石！三孔宝石和一孔宝石无法进行镶嵌！");
                }

                // 第3槽：只能镶嵌三孔宝石
                if (currentSlotCount == 2 &&
                    !threeHoleGemstones.Any(g => gemstoneConfig.type_name.Contains(g)))
                {
                    return ApiResult.CreateError("该槽位只能打三孔宝石！二孔宝石和一孔宝石无法进行镶嵌！");
                }
            }
            else
            {
                // 检查宝石配置的位置限制
                var allowedPositions = gemstoneConfig.positions.Split(',').Select(int.Parse).ToArray();
                if (!allowedPositions.Contains(currentSlotCount + 1))
                {
                    return ApiResult.CreateError($"该槽位只能打在{string.Join("、", allowedPositions)}孔！");
                }
            }

            return ApiResult.CreateSuccess();
        }

        /// <summary>
        /// 尝试替换同类型低等级宝石
        /// 基于老项目的ReplaceGemstone逻辑
        /// </summary>
        private async Task<ApiResult> TryReplaceGemstoneAsync(user_equipment equipment, gemstone_config newGemstone,
            user_item item, List<equipment_gemstone> currentGemstones)
        {
            try
            {
                // 查找同类型但等级低于新宝石的宝石
                int replaceIndex = -1;
                int minReplaceableLevel = int.MaxValue;

                for (int i = 0; i < currentGemstones.Count; i++)
                {
                    var existingGemstone = await GetGemstoneConfigAsync(currentGemstones[i].gemstone_type_name);
                    if (existingGemstone == null) continue;

                    // 检查是否同类型且等级更低
                    if (existingGemstone.type_class == newGemstone.type_class &&
                        existingGemstone.level < newGemstone.level &&
                        existingGemstone.level < minReplaceableLevel)
                    {
                        replaceIndex = i;
                        minReplaceableLevel = existingGemstone.level;
                    }
                }

                if (replaceIndex == -1)
                {
                    return ApiResult.CreateError("槽位不足或当前镶嵌宝石等级低于所有已镶嵌同类型宝石，无法继续镶嵌。");
                }

                // 检查金币是否足够
                const long EMBED_COST = 1000000000;
                var user = await GetUserAsync(equipment.user_id);
                if (long.Parse(user.money ?? "0") < EMBED_COST)
                {
                    return ApiResult.CreateError($"金币不足{EMBED_COST}，无法覆盖同类型宝石。");
                }

                // 执行替换
                var targetGemstone = currentGemstones[replaceIndex];
                targetGemstone.gemstone_type_name = newGemstone.type_name;
                targetGemstone.create_time = DateTime.Now;

                await _equipmentRepository.UpdateGemstoneAsync(targetGemstone);

                // 消耗道具和金币
                await ConsumeItemAsync(equipment.user_id, item.item_id, 1);
                await ConsumeMoneyAsync(equipment.user_id, EMBED_COST);

                // 记录操作日志
                await LogGemstoneOperationAsync(equipment.user_id, equipment.id, "REPLACE",
                    $"替换{newGemstone.type_class}", "SUCCESS", $"替换{newGemstone.type_class}成功！");

                return ApiResult.CreateSuccess($"替换{newGemstone.type_class}成功！");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "替换宝石失败，装备ID: {EquipmentId}", equipment.id);
                return ApiResult.CreateError("替换宝石失败");
            }
        }

        /// <summary>
        /// 消耗道具
        /// </summary>
        private async Task ConsumeItemAsync(int userId, string itemId, int quantity)
        {
            var item = await _dbContext.Db.Queryable<user_item>()
                .Where(x => x.user_id == userId && x.item_id == itemId)
                .FirstAsync();

            if (item != null)
            {
                item.item_count -= quantity;
                if (item.item_count <= 0)
                {
                    await _dbContext.Db.Deleteable<user_item>()
                        .Where(x => x.id == item.id)
                        .ExecuteCommandAsync();
                }
                else
                {
                    await _dbContext.Db.Updateable(item).ExecuteCommandAsync();
                }
            }
        }

        /// <summary>
        /// 消耗金币
        /// </summary>
        private async Task ConsumeMoneyAsync(int userId, long amount)
        {
            await _dbContext.Db.Updateable<user>()
                .SetColumns(x => x.money == (long.Parse(x.money) - amount).ToString())
                .Where(x => x.id == userId)
                .ExecuteCommandAsync();
        }

        /// <summary>
        /// 添加道具到用户背包
        /// </summary>
        private async Task AddItemToUserAsync(int userId, string itemId, int quantity)
        {
            var existingItem = await _dbContext.Db.Queryable<user_item>()
                .Where(x => x.user_id == userId && x.item_id == itemId)
                .FirstAsync();

            if (existingItem != null)
            {
                existingItem.item_count += quantity;
                await _dbContext.Db.Updateable(existingItem).ExecuteCommandAsync();
            }
            else
            {
                var newItem = new user_item
                {
                    user_id = userId,
                    item_id = itemId,
                    item_count = quantity,
                    create_time = DateTime.Now
                };
                await _dbContext.Db.Insertable(newItem).ExecuteCommandAsync();
            }
        }

        #endregion

        /// <summary>
        /// 记录宝石操作日志
        /// </summary>
        private async Task LogGemstoneOperationAsync(int userId, int userEquipmentId, string operationType,
            string operationData, string status, string description)
        {
            try
            {
                var log = new equipment_operation_log
                {
                    user_id = userId,
                    user_equipment_id = userEquipmentId,
                    operation_type = operationType,
                    operation_data = operationData,
                    result = status,
                    result_message = description,
                    create_time = DateTime.Now
                };

                await _dbContext.Db.Insertable(log).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "记录宝石操作日志失败");
            }
        }
    }
}
