namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 装备列表结果DTO
    /// </summary>
    public class EquipmentListResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 装备列表
        /// </summary>
        public List<UserEquipmentDTO> Equipments { get; set; } = new List<UserEquipmentDTO>();
    }

    /// <summary>
    /// 用户装备DTO
    /// </summary>
    public class UserEquipmentDTO
    {
        /// <summary>
        /// 装备实例ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 装备ID
        /// </summary>
        public string EquipId { get; set; } = string.Empty;

        /// <summary>
        /// 装备名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 装备图标
        /// </summary>
        public string Icon { get; set; } = string.Empty;

        /// <summary>
        /// 装备类型ID
        /// </summary>
        public int EquipTypeId { get; set; }

        /// <summary>
        /// 强化等级
        /// </summary>
        public int StrengthenLevel { get; set; }

        /// <summary>
        /// 扩展槽位
        /// </summary>
        public int Slot { get; set; }

        /// <summary>
        /// 装备位置
        /// </summary>
        public int? Position { get; set; }

        /// <summary>
        /// 是否已装备
        /// </summary>
        public bool IsEquipped { get; set; }

        /// <summary>
        /// 获得时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }
} 