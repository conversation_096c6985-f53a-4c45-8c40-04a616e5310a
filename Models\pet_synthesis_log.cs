﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///宠物合成记录表
    ///</summary>
    [SugarTable("pet_synthesis_log")]
    public partial class pet_synthesis_log
    {
           public pet_synthesis_log(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:用户ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:主宠物ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int main_pet_id {get;set;}

           /// <summary>
           /// Desc:副宠物ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int sub_pet_id {get;set;}

           /// <summary>
           /// Desc:主宠物编号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int main_pet_no {get;set;}

           /// <summary>
           /// Desc:副宠物编号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int sub_pet_no {get;set;}

           /// <summary>
           /// Desc:主宠成长
           /// Default:
           /// Nullable:False
           /// </summary>           
           public decimal main_growth {get;set;}

           /// <summary>
           /// Desc:副宠成长
           /// Default:
           /// Nullable:False
           /// </summary>           
           public decimal sub_growth {get;set;}

           /// <summary>
           /// Desc:合成结果宠物编号
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? result_pet_no {get;set;}

           /// <summary>
           /// Desc:合成结果成长
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? result_growth {get;set;}

           /// <summary>
           /// Desc:实际成功率
           /// Default:
           /// Nullable:False
           /// </summary>           
           public decimal success_rate {get;set;}

           /// <summary>
           /// Desc:使用的辅助道具ID
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? used_item_id {get;set;}

           /// <summary>
           /// Desc:消耗金币
           /// Default:
           /// Nullable:False
           /// </summary>           
           public long cost_gold {get;set;}

           /// <summary>
           /// Desc:是否成功
           /// Default:true
           /// Nullable:False
           /// </summary>           
           public bool is_success {get;set;}

           /// <summary>
           /// Desc:五行加成(%)
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? element_bonus {get;set;}

           /// <summary>
           /// Desc:VIP加成(%)
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? vip_bonus {get;set;}

           /// <summary>
           /// Desc:合成时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

    }
}
