using SqlSugar;
using WebApplication_HM.Models;
using WebApplication_HM.Models.Define;
using TaskStatus = WebApplication_HM.Models.Define.TaskStatus;

namespace WebApplication_HM.Services.TaskHandlers
{
    /// <summary>
    /// 击杀怪物任务处理器
    /// </summary>
    public class KillMonsterTaskHandler : BaseTaskHandler
    {
        public KillMonsterTaskHandler(ISqlSugarClient db, ILogger<KillMonsterTaskHandler> logger) 
            : base(db, logger)
        {
        }

        public override string SupportedObjectiveType => TaskObjectiveTypes.KILL_MONSTER;

        /// <summary>
        /// 检查击杀怪物进度
        /// </summary>
        public override async Task<int> CheckProgressAsync(int userId, task_objective objective)
        {
            if (!ValidateObjective(objective))
                return 0;

            try
            {
                // 查找用户相关的任务进度
                var userTasks = await _db.Queryable<user_task, user_task_progress>((ut, utp) => new JoinQueryInfos(
                    JoinType.Inner, ut.user_task_id == utp.user_task_id))
                    .Where((ut, utp) => ut.user_id == userId && 
                                      utp.objective_id == objective.objective_id &&
                                      ut.task_status == (byte)TaskStatus.InProgress)
                    .Select((ut, utp) => utp)
                    .FirstAsync();

                if (userTasks != null)
                {
                    LogHandlerAction("检查进度", userId, objective, new { CurrentAmount = userTasks.current_amount });
                    return userTasks.current_amount ?? 0;
                }

                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查击杀怪物进度失败: UserId={UserId}, ObjectiveId={ObjectiveId}", 
                    userId, objective.objective_id);
                return 0;
            }
        }

        /// <summary>
        /// 更新击杀怪物进度
        /// </summary>
        public override async Task<int> UpdateProgressAsync(int userId, task_objective objective, int amount)
        {
            if (!ValidateObjective(objective) || amount <= 0)
                return 0;

            try
            {
                // 查找用户相关的任务和进度
                var taskProgress = await _db.Queryable<user_task, user_task_progress>((ut, utp) => new JoinQueryInfos(
                    JoinType.Inner, ut.user_task_id == utp.user_task_id))
                    .Where((ut, utp) => ut.user_id == userId && 
                                      utp.objective_id == objective.objective_id &&
                                      ut.task_status == (byte)TaskStatus.InProgress)
                    .Select((ut, utp) => new { UserTask = ut, Progress = utp })
                    .FirstAsync();

                if (taskProgress == null)
                {
                    _logger.LogWarning("未找到用户击杀怪物任务进度: UserId={UserId}, ObjectiveId={ObjectiveId}", 
                        userId, objective.objective_id);
                    return 0;
                }

                var progress = taskProgress.Progress;
                
                // 检查是否已完成
                if (progress.is_completed == 1)
                {
                    LogHandlerAction("任务已完成", userId, objective, new { CurrentAmount = progress.current_amount });
                    return progress.current_amount ?? 0;
                }

                // 检查目标怪物ID是否匹配（如果指定了特定怪物）
                if (!string.IsNullOrEmpty(objective.target_id))
                {
                    // 这里需要传入击杀的怪物ID进行匹配验证
                    // 在实际调用时，amount参数可能需要扩展为包含怪物ID的复合参数
                    _logger.LogDebug("击杀怪物任务需要特定怪物ID: {TargetId}", objective.target_id);
                }

                // 更新进度
                var oldAmount = progress.current_amount ?? 0;
                var newAmount = SafeAddProgress(oldAmount, amount, objective.target_amount);
                
                progress.current_amount = newAmount;
                progress.is_completed = (byte)(IsProgressCompleted(newAmount, objective.target_amount) ? 1 : 0);

                var updateResult = await UpdateUserTaskProgressAsync(progress);
                
                if (updateResult)
                {
                    LogHandlerAction("更新进度成功", userId, objective, new { 
                        OldAmount = oldAmount, 
                        NewAmount = newAmount, 
                        AddAmount = amount,
                        IsCompleted = progress.is_completed == 1
                    });
                    
                    return newAmount;
                }
                else
                {
                    _logger.LogError("更新击杀怪物任务进度失败: UserId={UserId}, ObjectiveId={ObjectiveId}", 
                        userId, objective.objective_id);
                    return oldAmount;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新击杀怪物进度失败: UserId={UserId}, ObjectiveId={ObjectiveId}, Amount={Amount}", 
                    userId, objective.objective_id, amount);
                return 0;
            }
        }

        /// <summary>
        /// 处理特定怪物击杀事件
        /// </summary>
        public async Task<int> HandleMonsterKillAsync(int userId, string monsterId, int killCount = 1)
        {
            try
            {
                // 查找所有相关的击杀怪物任务
                var relevantTasks = await _db.Queryable<user_task, task_config, task_objective, user_task_progress>(
                    (ut, tc, to, utp) => new JoinQueryInfos(
                        JoinType.Inner, ut.task_id == tc.task_id,
                        JoinType.Inner, tc.task_id == to.task_id,
                        JoinType.Inner, ut.user_task_id == utp.user_task_id && utp.objective_id == to.objective_id))
                    .Where((ut, tc, to, utp) => ut.user_id == userId &&
                                              ut.task_status == (byte)TaskStatus.InProgress &&
                                              to.objective_type == TaskObjectiveTypes.KILL_MONSTER &&
                                              (string.IsNullOrEmpty(to.target_id) || to.target_id == monsterId) &&
                                              utp.is_completed == 0)
                    .Select((ut, tc, to, utp) => new { UserTask = ut, TaskConfig = tc, Objective = to, Progress = utp })
                    .ToListAsync();

                int totalUpdated = 0;

                foreach (var task in relevantTasks)
                {
                    var updatedAmount = await UpdateProgressAsync(userId, task.Objective, killCount);
                    if (updatedAmount > (task.Progress.current_amount ?? 0))
                    {
                        totalUpdated++;
                    }
                }

                if (totalUpdated > 0)
                {
                    _logger.LogInformation("处理怪物击杀事件: UserId={UserId}, MonsterId={MonsterId}, KillCount={KillCount}, UpdatedTasks={UpdatedTasks}",
                        userId, monsterId, killCount, totalUpdated);
                }

                return totalUpdated;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理怪物击杀事件失败: UserId={UserId}, MonsterId={MonsterId}", userId, monsterId);
                return 0;
            }
        }
    }
}
