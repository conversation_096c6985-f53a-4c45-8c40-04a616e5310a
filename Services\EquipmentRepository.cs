using WebApplication_HM.Interface;
using WebApplication_HM.Models;
using WebApplication_HM.Sugar;
using SqlSugar;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 装备仓储实现
    /// </summary>
    public class EquipmentRepository : IEquipmentRepository
    {
        private readonly DbContext _dbContext;
        private readonly ILogger<EquipmentRepository> _logger;

        public EquipmentRepository(DbContext dbContext, ILogger<EquipmentRepository> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        #region 用户装备操作

        public async Task<List<user_equipment>> GetUserEquipmentsAsync(int userId)
        {
            try
            {
                return await _dbContext.Db.Queryable<user_equipment>()
                    .Where(x => x.user_id == userId)
                    .OrderBy(x => x.create_time, OrderByType.Desc)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户装备失败，用户ID: {UserId}", userId);
                throw;
            }
        }

        public async Task<user_equipment?> GetEquipmentByIdAsync(int userEquipmentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<user_equipment>()
                    .Where(x => x.id == userEquipmentId)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取装备失败，装备ID: {EquipmentId}", userEquipmentId);
                throw;
            }
        }

        public async Task<bool> AddEquipmentAsync(user_equipment equipment)
        {
            try
            {
                var result = await _dbContext.Db.Insertable(equipment).ExecuteCommandAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加装备失败");
                throw;
            }
        }

        public async Task<bool> UpdateEquipmentAsync(user_equipment equipment)
        {
            try
            {
                equipment.update_time = DateTime.Now;
                var result = await _dbContext.Db.Updateable(equipment).ExecuteCommandAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新装备失败，装备ID: {EquipmentId}", equipment.id);
                throw;
            }
        }

        public async Task<bool> DeleteEquipmentAsync(int userEquipmentId)
        {
            try
            {
                var result = await _dbContext.Db.Deleteable<user_equipment>()
                    .Where(x => x.id == userEquipmentId)
                    .ExecuteCommandAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除装备失败，装备ID: {EquipmentId}", userEquipmentId);
                throw;
            }
        }

        #endregion

        #region 装备详情操作

        public async Task<equipment_detail?> GetEquipmentDetailAsync(string equipId)
        {
            try
            {
                return await _dbContext.Db.Queryable<equipment_detail>()
                    .Where(x => x.equip_id == equipId)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取装备详情失败，装备ID: {EquipId}", equipId);
                throw;
            }
        }

        public async Task<List<equipment_detail>> GetEquipmentDetailsByIdsAsync(List<string> equipIds)
        {
            try
            {
                return await _dbContext.Db.Queryable<equipment_detail>()
                    .Where(x => equipIds.Contains(x.equip_id))
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量获取装备详情失败");
                throw;
            }
        }

        #endregion

        #region 宝石操作

        public async Task<List<equipment_gemstone>> GetEquipmentGemstonesAsync(int userEquipmentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<equipment_gemstone>()
                    .Where(x => x.user_equipment_id == userEquipmentId)
                    .OrderBy(x => x.position)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取装备宝石失败，装备ID: {EquipmentId}", userEquipmentId);
                throw;
            }
        }

        public async Task<bool> AddGemstoneAsync(equipment_gemstone gemstone)
        {
            try
            {
                var result = await _dbContext.Db.Insertable(gemstone).ExecuteCommandAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加宝石失败");
                throw;
            }
        }

        public async Task<bool> UpdateGemstoneAsync(equipment_gemstone gemstone)
        {
            try
            {
                var result = await _dbContext.Db.Updateable(gemstone).ExecuteCommandAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新宝石失败");
                throw;
            }
        }

        public async Task<bool> RemoveGemstoneAsync(int userEquipmentId, int position)
        {
            try
            {
                var result = await _dbContext.Db.Deleteable<equipment_gemstone>()
                    .Where(x => x.user_equipment_id == userEquipmentId && x.position == position)
                    .ExecuteCommandAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移除宝石失败，装备ID: {EquipmentId}, 位置: {Position}", userEquipmentId, position);
                throw;
            }
        }

        public async Task<bool> ClearAllGemstonesAsync(int userEquipmentId)
        {
            try
            {
                var result = await _dbContext.Db.Deleteable<equipment_gemstone>()
                    .Where(x => x.user_equipment_id == userEquipmentId)
                    .ExecuteCommandAsync();
                return result >= 0; // 即使没有宝石也算成功
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清空宝石失败，装备ID: {EquipmentId}", userEquipmentId);
                throw;
            }
        }

        public async Task<equipment_type?> GetEquipmentTypeAsync(int typeId)
        {
            try
            {
                return await _dbContext.Db.Queryable<equipment_type>()
                    .Where(x => x.id == typeId)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取装备类型失败，类型ID: {TypeId}", typeId);
                throw;
            }
        }

        #endregion

        #region 宝石配置操作

        public async Task<List<gemstone_config>> GetAllGemstoneConfigsAsync()
        {
            try
            {
                return await _dbContext.Db.Queryable<gemstone_config>()
                    .OrderBy(x => x.order_num)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宝石配置失败");
                throw;
            }
        }

        public async Task<gemstone_config?> GetGemstoneConfigAsync(string typeName)
        {
            try
            {
                return await _dbContext.Db.Queryable<gemstone_config>()
                    .Where(x => x.type_name == typeName)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宝石配置失败，类型: {TypeName}", typeName);
                throw;
            }
        }

        #endregion

        #region 套装操作

        public async Task<List<suit_config>> GetAllSuitConfigsAsync()
        {
            try
            {
                return await _dbContext.Db.Queryable<suit_config>()
                    .OrderBy(x => x.create_time)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取套装配置失败");
                throw;
            }
        }

        public async Task<suit_config?> GetSuitConfigAsync(string suitId)
        {
            try
            {
                return await _dbContext.Db.Queryable<suit_config>()
                    .Where(x => x.suit_id == suitId)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取套装配置失败，套装ID: {SuitId}", suitId);
                throw;
            }
        }

        public async Task<List<suit_attribute>> GetSuitAttributesAsync(string suitId)
        {
            try
            {
                return await _dbContext.Db.Queryable<suit_attribute>()
                    .Where(x => x.suit_id == suitId)
                    .OrderBy(x => x.piece_count)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取套装属性失败，套装ID: {SuitId}", suitId);
                throw;
            }
        }

        #endregion

        #region 操作日志

        public async Task<bool> LogOperationAsync(equipment_operation_log log)
        {
            try
            {
                var result = await _dbContext.Db.Insertable(log).ExecuteCommandAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录操作日志失败");
                throw;
            }
        }

        public async Task<List<equipment_operation_log>> GetOperationLogsAsync(int userId, int? userEquipmentId = null)
        {
            try
            {
                var query = _dbContext.Db.Queryable<equipment_operation_log>()
                    .Where(x => x.user_id == userId);

                if (userEquipmentId.HasValue)
                {
                    query = query.Where(x => x.user_equipment_id == userEquipmentId.Value);
                }

                return await query.OrderBy(x => x.create_time, OrderByType.Desc)
                    .Take(100) // 限制返回最近100条记录
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取操作日志失败，用户ID: {UserId}", userId);
                throw;
            }
        }

        #endregion
    }
}
