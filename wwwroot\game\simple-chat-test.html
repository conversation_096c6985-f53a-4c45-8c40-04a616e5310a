<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>简单聊天测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; font-weight: bold; }
        .status.connected { background: #d4edda; color: #155724; }
        .status.disconnected { background: #f8d7da; color: #721c24; }
        .chat-box { border: 1px solid #ddd; height: 300px; overflow-y: auto; padding: 10px; margin: 10px 0; }
        .input-group { margin: 10px 0; }
        .input-group input, .input-group button { padding: 8px; margin: 5px; }
        .send_ms0 { margin: 5px 0; padding: 5px; border-left: 3px solid #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>简单聊天测试</h1>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="input-group">
            <input type="number" id="playerId" placeholder="玩家ID" value="1001">
            <input type="text" id="playerName" placeholder="玩家名称" value="测试用户">
            <button onclick="connect()">连接并登录</button>
            <button onclick="disconnect()">断开连接</button>
        </div>
        
        <div class="chat-box" id="chatBox">
            <div class="send_ms0">系统：欢迎使用简单聊天测试</div>
        </div>
        
        <div class="input-group">
            <input type="text" id="messageInput" placeholder="输入消息..." style="width: 300px;" onkeydown="if(event.keyCode==13) sendMessage()">
            <button onclick="sendMessage()">发送公聊</button>
        </div>
        
        <div class="input-group">
            <button onclick="sendTestMessage()">发送测试消息</button>
            <button onclick="clearChat()">清空聊天</button>
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        let currentPlayerId = null;
        let currentPlayerName = null;

        function log(message) {
            console.log(message);
            const chatBox = document.getElementById('chatBox');
            const div = document.createElement('div');
            div.className = 'send_ms0';
            div.innerHTML = `<font color="#666">[${new Date().toLocaleTimeString()}] ${message}</font>`;
            chatBox.appendChild(div);
            chatBox.scrollTop = chatBox.scrollHeight;
        }

        function updateStatus(connected) {
            const status = document.getElementById('status');
            isConnected = connected;
            if (connected) {
                status.className = 'status connected';
                status.textContent = '已连接';
            } else {
                status.className = 'status disconnected';
                status.textContent = '未连接';
            }
        }

        function connect() {
            const playerId = parseInt(document.getElementById('playerId').value);
            const playerName = document.getElementById('playerName').value.trim();
            
            if (!playerId || !playerName) {
                alert('请填写玩家ID和名称');
                return;
            }

            currentPlayerId = playerId;
            currentPlayerName = playerName;

            try {
                ws = new WebSocket('ws://localhost:5000/ws/universal');
                
                ws.onopen = function() {
                    log('WebSocket连接已建立');
                    updateStatus(true);
                    
                    // 发送登录消息
                    const loginMsg = {
                        Type: "L",
                        playerId: currentPlayerId,
                        number: currentPlayerName,
                        password: "",
                        Name: currentPlayerName,
                        Content: "login"
                    };
                    
                    ws.send(JSON.stringify(loginMsg));
                    log('发送登录消息: ' + JSON.stringify(loginMsg));
                };
                
                ws.onmessage = function(event) {
                    log('收到消息: ' + event.data);
                    
                    try {
                        const message = JSON.parse(event.data);
                        handleMessage(message);
                    } catch (e) {
                        log('解析消息失败: ' + e.message);
                    }
                };
                
                ws.onclose = function(event) {
                    log('WebSocket连接已关闭: ' + event.code + ' - ' + event.reason);
                    updateStatus(false);
                };
                
                ws.onerror = function(error) {
                    log('WebSocket错误: ' + error);
                    updateStatus(false);
                };
                
            } catch (error) {
                log('连接失败: ' + error.message);
                updateStatus(false);
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
            updateStatus(false);
            log('手动断开连接');
        }

        function handleMessage(message) {
            const chatBox = document.getElementById('chatBox');
            
            switch (message.Type) {
                case 'La': // 登录成功
                    log('登录成功: ' + message.Name);
                    break;
                    
                case 'login_error':
                    log('登录失败: ' + message.Message);
                    break;
                    
                case 'chat':
                    displayChatMessage(message);
                    break;
                    
                case 'private':
                    displayPrivateMessage(message);
                    break;
                    
                default:
                    log('未知消息类型: ' + message.Type);
            }
        }

        function displayChatMessage(message) {
            const chatBox = document.getElementById('chatBox');
            const div = document.createElement('div');
            div.className = 'send_ms0';
            
            // 解析颜色
            const colortext = message.Content.split("&&");
            let color = "#000";
            let content = colortext[0];
            
            if (colortext.length >= 2) {
                const colorName = colortext[1];
                switch(colorName) {
                    case "黑": color = "#000"; break;
                    case "绿": color = "#33cc00"; break;
                    case "粉": color = "#ff3399"; break;
                    case "蓝": color = "#0000ff"; break;
                }
            }
            
            div.innerHTML = `<font color="#ff0000">${message.number}</font>说：<font color="${color}">${content}</font>`;
            chatBox.appendChild(div);
            chatBox.scrollTop = chatBox.scrollHeight;
        }

        function displayPrivateMessage(message) {
            const chatBox = document.getElementById('chatBox');
            const div = document.createElement('div');
            div.className = 'send_ms0';
            div.style.background = 'rgba(40, 167, 69, 0.1)';
            
            const colortext = message.Content.split("&&");
            let content = colortext[0];
            
            div.innerHTML = `<font color="#28a745">[私聊]</font><font color="#ff0000">${message.number}</font>：<font color="#28a745">${content}</font>`;
            chatBox.appendChild(div);
            chatBox.scrollTop = chatBox.scrollHeight;
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const content = input.value.trim();
            
            if (!content) return;
            if (!isConnected || !ws) {
                alert('请先连接WebSocket');
                return;
            }
            
            const message = {
                Type: "chat",
                playerId: currentPlayerId,
                number: currentPlayerName,
                Name: currentPlayerName,
                Content: content + "&&黑",
                TargetUserId: null
            };
            
            ws.send(JSON.stringify(message));
            log('发送聊天消息: ' + JSON.stringify(message));
            input.value = '';
        }

        function sendTestMessage() {
            const testContent = `测试消息 ${new Date().toLocaleTimeString()}`;
            document.getElementById('messageInput').value = testContent;
            sendMessage();
        }

        function clearChat() {
            const chatBox = document.getElementById('chatBox');
            chatBox.innerHTML = '<div class="send_ms0">系统：聊天记录已清空</div>';
        }

        // 页面加载完成后的提示
        window.onload = function() {
            log('页面加载完成，请点击"连接并登录"开始测试');
        };
    </script>
</body>
</html>
