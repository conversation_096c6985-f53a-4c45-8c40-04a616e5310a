using SqlSugar;

namespace WebApplication_HM.Models
{
    /// <summary>
    /// 道具信息类（标准化英文字段，兼容原系统）
    /// </summary>
    public class PropInfo
    {
        public int ItemSeq { get; set; }        // 道具序号
        public string ItemId { get; set; }      // 道具类型ID  
        public long ItemCount { get; set; }     // 道具数量
        public int? ItemPos { get; set; }       // 道具位置
        public string ItemName { get; set; }    // 道具名称
        public string ItemType { get; set; }    // 道具类型
        public string Description { get; set; }  // 道具描述
        public string Script { get; set; }      // 道具脚本
        public string ItemIcon { get; set; }    // 道具图标
        public int ItemPrice { get; set; }      // 道具价格
        
        // 兼容原系统的属性访问器（保持向后兼容）
        public string 道具序号 => ItemSeq.ToString();
        public string 道具类型ID => ItemId;
        public string 道具数量 => ItemCount.ToString();
        public string 道具位置 => ItemPos?.ToString() ?? "1";
        public string 道具名字 => ItemName;
        public string 道具图标 => ItemIcon;
        public string 道具价格 => ItemPrice.ToString();
        
        /// <summary>
        /// 从数据库实体映射到PropInfo
        /// </summary>
        public static PropInfo FromUserItem(user_item userItem, item_config config)
        {
            return new PropInfo
            {
                ItemSeq = userItem.item_seq,
                ItemId = userItem.item_id,
                ItemCount = userItem.item_count,
                ItemPos = userItem.item_pos,
                ItemName = config?.name ?? "未知道具",
                Description = config?.description ?? "",
                ItemIcon = config?.icon ?? "",
                ItemPrice = config?.price ?? 0
            };
        }
        
        /// <summary>
        /// 转换为数据库实体
        /// </summary>
        public user_item ToUserItem(int userId)
        {
            return new user_item
            {
                user_id = userId,
                item_id = ItemId,
                item_count = ItemCount,
                item_pos = ItemPos ?? 1, // 道具位置不能为空，默认为1（背包）
                item_seq = ItemSeq
            };
        }
    }

    /// <summary>
    /// 道具配置类（标准化英文字段）
    /// </summary>
    public class PropConfig
    {
        public int ItemNo { get; set; }          // 道具编号
        public string Name { get; set; }         // 道具名称
        public string Type { get; set; }         // 道具类型
        public string Description { get; set; }  // 道具描述
        public string Script { get; set; }       // 道具脚本
        public int Price { get; set; }           // 道具价格
        public string Quality { get; set; }      // 道具品质
        public string Icon { get; set; }         // 道具图标
        public string UseLimit { get; set; }     // 使用限制
        
        // 兼容原系统的属性访问器
        public string 道具脚本 => Script;
        public string 道具名称 => Name;
        public string 道具描述 => Description;
        public string 道具类型 => Type;
        public int 道具编号 => ItemNo;
        public string 道具序号 => ItemNo.ToString();
        public string 道具说明 => Description;
        
        /// <summary>
        /// 从数据库实体映射到PropConfig
        /// </summary>
        public static PropConfig FromItemConfig(item_config config, item_script script = null)
        {
            return new PropConfig
            {
                ItemNo = config.item_no,
                Name = config.name,
                Type = config.type,
                // 优先使用 item_script 表的 description（道具说明），如果没有则使用 item_config 的 description
                Description = script?.description ?? config.description ?? "",
                Script = script?.script ?? "",
                Price = config.price ?? 0,
                Quality = config.quality,
                Icon = config.icon,
                UseLimit = config.use_limit
            };
        }
    }

    /// <summary>
    /// 道具脚本信息类
    /// </summary>
    public class PropScriptInfo
    {
        public int ItemNo { get; set; }
        public string Script { get; set; }
        public string Description { get; set; }
        
        // 兼容访问器
        public int 道具编号 => ItemNo;
        public string 道具脚本 => Script;
        public string 脚本说明 => Description;
        public string 道具序号 => ItemNo.ToString();
        public string 道具说明 => Description;
        
        public static PropScriptInfo FromItemScript(item_script script)
        {
            return new PropScriptInfo
            {
                ItemNo = script.item_no,
                Script = script.script,
                Description = script.description
            };
        }
    }

    /// <summary>
    /// 添加道具请求模型
    /// </summary>
    public class AddItemRequest
    {
        public int UserId { get; set; }
        public string ItemId { get; set; }
        public long Count { get; set; }
        public int? Position { get; set; }
    }

    /// <summary>
    /// 使用道具请求模型
    /// </summary>
    public class UseItemRequest
    {
        public int UserId { get; set; }
        public int ItemSeq { get; set; }
    }

    /// <summary>
    /// 移动道具位置请求
    /// </summary>
    public class MoveItemRequest
    {
        public int UserId { get; set; }
        public int ItemSeq { get; set; }
        public int NewPosition { get; set; } // 1.背包 2.仓库 3.丢弃
    }

    /// <summary>
    /// 丢弃道具请求
    /// </summary>
    public class DiscardItemRequest
    {
        public int UserId { get; set; }
        public int ItemSeq { get; set; }
        public string Reason { get; set; } = "用户主动丢弃"; // 丢弃原因
    }

    /// <summary>
    /// 道具位置枚举（兼容原系统）
    /// </summary>
    public enum PropLoaction
    {
        背包 = 1,
        仓库 = 2
    }
}
