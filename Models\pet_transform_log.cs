using SqlSugar;
using System;

namespace WebApplication_HM.Models
{
    /// <summary>
    /// 百变宠物记录表
    /// </summary>
    [SugarTable("pet_transform_log")]
    public class pet_transform_log
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int user_id { get; set; }

        /// <summary>
        /// 源宠物ID
        /// </summary>
        public int? source_pet_id { get; set; }

        /// <summary>
        /// 目标宠物编号
        /// </summary>
        public int target_pet_no { get; set; }

        /// <summary>
        /// 结果宠物ID
        /// </summary>
        public int? result_pet_id { get; set; }

        /// <summary>
        /// 变换类型
        /// </summary>
        public string transform_type { get; set; } = string.Empty;

        /// <summary>
        /// 消耗金币
        /// </summary>
        public long cost_gold { get; set; } = 0;

        /// <summary>
        /// 使用的道具ID
        /// </summary>
        public string? used_item_id { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool success { get; set; }

        /// <summary>
        /// 是否为神宠
        /// </summary>
        public bool is_god_pet { get; set; } = false;

        /// <summary>
        /// 是否为神圣宠物
        /// </summary>
        public bool is_holy_pet { get; set; } = false;

        /// <summary>
        /// 备注信息
        /// </summary>
        public string? remark { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime create_time { get; set; } = DateTime.Now;
    }
}
