using SqlSugar;
using WebApplication_HM.Models;
using WebApplication_HM.Interface;

namespace WebApplication_HM.Tools
{
    /// <summary>
    /// 等级数据导入工具
    /// </summary>
    public class LevelDataImporter
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<LevelDataImporter> _logger;

        public LevelDataImporter(ISqlSugarClient db, ILogger<LevelDataImporter> logger)
        {
            _db = db;
            _logger = logger;
        }

        /// <summary>
        /// 从原项目等级列表导入数据
        /// </summary>
        /// <param name="levelExpList">原项目的等级经验列表</param>
        /// <param name="systemName">系统名称</param>
        /// <returns>导入结果</returns>
        public async Task<ImportResult> ImportLevelDataAsync(List<long> levelExpList, string systemName = "pet")
        {
            try
            {
                _logger.LogInformation("开始导入等级数据，系统: {SystemName}, 等级数量: {Count}", systemName, levelExpList.Count);

                // 1. 验证数据
                if (levelExpList == null || levelExpList.Count == 0)
                {
                    return new ImportResult
                    {
                        Success = false,
                        Message = "等级经验列表为空"
                    };
                }

                // 2. 转换数据格式
                var levelConfigs = ConvertToLevelConfigs(levelExpList);

                // 3. 验证数据一致性
                var validationResult = ValidateLevelConfigs(levelConfigs);
                if (!validationResult.Success)
                {
                    return validationResult;
                }

                // 4. 开始事务导入
                _db.Ado.BeginTran();

                try
                {
                    // 清除现有数据（如果存在）
                    await _db.Deleteable<LevelConfig>().ExecuteCommandAsync();

                    // 批量插入新数据
                    await _db.Insertable(levelConfigs).ExecuteCommandAsync();

                    // 更新或插入系统配置
                    await UpsertSystemConfigAsync(systemName, levelConfigs);

                    _db.Ado.CommitTran();

                    _logger.LogInformation("等级数据导入成功，系统: {SystemName}, 导入数量: {Count}", 
                        systemName, levelConfigs.Count);

                    return new ImportResult
                    {
                        Success = true,
                        ImportedCount = levelConfigs.Count,
                        Message = $"成功导入 {levelConfigs.Count} 条等级配置"
                    };
                }
                catch (Exception ex)
                {
                    _db.Ado.RollbackTran();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入等级数据失败，系统: {SystemName}", systemName);
                return new ImportResult
                {
                    Success = false,
                    Message = $"导入失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 从文件导入等级数据
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="systemName">系统名称</param>
        /// <returns>导入结果</returns>
        public async Task<ImportResult> ImportFromFileAsync(string filePath, string systemName = "pet")
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return new ImportResult
                    {
                        Success = false,
                        Message = $"文件不存在: {filePath}"
                    };
                }

                // 读取文件内容
                var lines = await File.ReadAllLinesAsync(filePath);
                var levelExpList = new List<long>();

                foreach (var line in lines)
                {
                    if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#"))
                        continue;

                    if (long.TryParse(line.Trim(), out long exp))
                    {
                        levelExpList.Add(exp);
                    }
                    else
                    {
                        _logger.LogWarning("无法解析经验值: {Line}", line);
                    }
                }

                return await ImportLevelDataAsync(levelExpList, systemName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从文件导入等级数据失败: {FilePath}", filePath);
                return new ImportResult
                {
                    Success = false,
                    Message = $"文件导入失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 导出等级数据到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="systemName">系统名称</param>
        /// <returns>导出结果</returns>
        public async Task<ImportResult> ExportToFileAsync(string filePath, string systemName = "pet")
        {
            try
            {
                var levelConfigs = await _db.Queryable<LevelConfig>()
                    .Where(x => x.is_active == 1)
                    .OrderBy(x => x.level)
                    .ToListAsync();

                if (levelConfigs.Count == 0)
                {
                    return new ImportResult
                    {
                        Success = false,
                        Message = "没有找到等级配置数据"
                    };
                }

                var lines = new List<string>
                {
                    $"# 等级配置导出文件",
                    $"# 系统: {systemName}",
                    $"# 导出时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                    $"# 格式: 等级,累积经验,升级经验",
                    ""
                };

                foreach (var config in levelConfigs)
                {
                    lines.Add($"{config.level},{config.required_exp},{config.upgrade_exp}");
                }

                await File.WriteAllLinesAsync(filePath, lines);

                return new ImportResult
                {
                    Success = true,
                    ImportedCount = levelConfigs.Count,
                    Message = $"成功导出 {levelConfigs.Count} 条等级配置到 {filePath}"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出等级数据失败: {FilePath}", filePath);
                return new ImportResult
                {
                    Success = false,
                    Message = $"导出失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 验证等级配置数据
        /// </summary>
        /// <param name="levelConfigs">等级配置列表</param>
        /// <returns>验证结果</returns>
        public ImportResult ValidateLevelConfigs(List<LevelConfig> levelConfigs)
        {
            try
            {
                var errors = new List<string>();

                // 检查等级连续性
                for (int i = 0; i < levelConfigs.Count; i++)
                {
                    var config = levelConfigs[i];
                    var expectedLevel = i + 1;

                    if (config.level != expectedLevel)
                    {
                        errors.Add($"等级不连续: 期望 {expectedLevel}, 实际 {config.level}");
                    }

                    // 检查经验值合理性
                    if (config.required_exp < 0)
                    {
                        errors.Add($"等级 {config.level} 累积经验为负数: {config.required_exp}");
                    }

                    if (config.upgrade_exp < 0)
                    {
                        errors.Add($"等级 {config.level} 升级经验为负数: {config.upgrade_exp}");
                    }

                    // 检查经验递增性
                    if (i > 0 && config.required_exp <= levelConfigs[i - 1].required_exp)
                    {
                        errors.Add($"等级 {config.level} 累积经验未递增: {config.required_exp}");
                    }

                    // 检查升级经验一致性
                    if (i < levelConfigs.Count - 1)
                    {
                        var nextConfig = levelConfigs[i + 1];
                        var expectedNextExp = config.required_exp + config.upgrade_exp;
                        
                        if (expectedNextExp != nextConfig.required_exp)
                        {
                            errors.Add($"等级 {config.level} 升级经验不一致: " +
                                     $"{config.required_exp} + {config.upgrade_exp} != {nextConfig.required_exp}");
                        }
                    }
                }

                if (errors.Count > 0)
                {
                    return new ImportResult
                    {
                        Success = false,
                        Message = $"数据验证失败:\n{string.Join("\n", errors)}"
                    };
                }

                return new ImportResult
                {
                    Success = true,
                    Message = "数据验证通过"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证等级配置数据失败");
                return new ImportResult
                {
                    Success = false,
                    Message = $"验证失败: {ex.Message}"
                };
            }
        }

        #region 私有方法

        /// <summary>
        /// 转换原项目等级列表为等级配置
        /// </summary>
        private List<LevelConfig> ConvertToLevelConfigs(List<long> levelExpList)
        {
            var configs = new List<LevelConfig>();

            for (int i = 0; i < levelExpList.Count; i++)
            {
                var level = i + 1;
                var requiredExp = levelExpList[i];
                var upgradeExp = (i < levelExpList.Count - 1) ? levelExpList[i + 1] - requiredExp : 0;

                configs.Add(new LevelConfig
                {
                    level = level,
                    required_exp = requiredExp,
                    upgrade_exp = upgradeExp,
                    is_active = 1,
                    created_at = DateTime.Now,
                    updated_at = DateTime.Now
                });
            }

            return configs;
        }

        /// <summary>
        /// 更新或插入系统配置
        /// </summary>
        private async Task UpsertSystemConfigAsync(string systemName, List<LevelConfig> levelConfigs)
        {
            var maxLevel = levelConfigs.Max(x => x.level);
            var maxExp = levelConfigs.Max(x => x.required_exp);

            var existingConfig = await _db.Queryable<ExpSystemConfig>()
                .Where(x => x.system_name == systemName)
                .FirstAsync();

            if (existingConfig != null)
            {
                // 更新现有配置
                await _db.Updateable<ExpSystemConfig>()
                    .SetColumns(x => new ExpSystemConfig
                    {
                        max_level = maxLevel,
                        max_exp = maxExp,
                        exp_formula = "cumulative"
                    })
                    .Where(x => x.system_name == systemName)
                    .ExecuteCommandAsync();
            }
            else
            {
                // 插入新配置
                var newConfig = new ExpSystemConfig
                {
                    system_name = systemName,
                    max_level = maxLevel,
                    max_exp = maxExp,
                    exp_formula = "cumulative",
                    is_active = 1,
                    created_at = DateTime.Now
                };

                await _db.Insertable(newConfig).ExecuteCommandAsync();
            }
        }

        #endregion
    }
}
