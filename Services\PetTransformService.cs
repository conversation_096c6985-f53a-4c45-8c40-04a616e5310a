using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WebApplication_HM.DTOs;
using WebApplication_HM.Models;
using WebApplication_HM.IServices;
using WebApplication_HM.Sugar;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 百变宠物服务实现
    /// </summary>
    public class PetTransformService : IPetTransformService
    {
        private readonly DbContext _dbContext;
        private readonly ISynthesisValidPetsService _validPetsService;
        private readonly ILogger<PetTransformService> _logger;
        private readonly IMemoryCache _cache;
        private readonly Random _random;

        // 缓存键
        private static readonly string TRANSFORMABLE_PETS_CACHE_KEY = "transformable_pets";
        private static readonly string GOD_PETS_CACHE_KEY = "god_pets";
        private static readonly string HOLY_PETS_CACHE_KEY = "holy_pets";
        private static readonly string TRANSFORM_CONFIG_CACHE_KEY = "transform_config_{0}";
        private static readonly TimeSpan CACHE_DURATION = TimeSpan.FromHours(1);

        // 冷却时间记录
        private static readonly Dictionary<string, long> _lastTransformTime = new Dictionary<string, long>();

        public PetTransformService(
            DbContext dbContext,
            ISynthesisValidPetsService validPetsService,
            ILogger<PetTransformService> logger,
            IMemoryCache cache)
        {
            _dbContext = dbContext;
            _validPetsService = validPetsService;
            _logger = logger;
            _cache = cache;
            _random = new Random();
        }

        /// <summary>
        /// 获取可用于百变的宠物列表
        /// </summary>
        public async Task<List<TransformablePetDto>> GetTransformablePetsAsync()
        {
            try
            {
                // 尝试从缓存获取
                if (_cache.TryGetValue(TRANSFORMABLE_PETS_CACHE_KEY, out List<TransformablePetDto> cachedPets))
                {
                    return cachedPets;
                }

                // 获取可百变的宠物ID列表
                var validPetIds = await _validPetsService.GetAllValidPetNosAsync();

                // 查询宠物配置
                var petConfigs = await _dbContext.Db.Queryable<pet_config>()
                    .Where(p => validPetIds.Contains(p.pet_no) && (p.is_active == true || p.is_active == null))
                    .ToListAsync();

                // 转换为DTO
                var transformablePets = petConfigs.Select(p => new TransformablePetDto
                {
                    PetNo = p.pet_no,
                    Name = p.name ?? string.Empty,
                    Element = p.attribute ?? string.Empty, // 使用attribute作为element
                    Attribute = p.attribute ?? string.Empty,
                    IsGodPet = p.attribute == "神",
                    IsHolyPet = p.attribute == "神圣",
                    ImagePath = $"Content/PetPhoto/{p.pet_no}.jpg",
                    RarityLevel = GetRarityLevel(p.attribute ?? string.Empty)
                }).ToList();

                // 缓存结果
                _cache.Set(TRANSFORMABLE_PETS_CACHE_KEY, transformablePets, CACHE_DURATION);

                _logger.LogInformation($"获取到{transformablePets.Count}个可变换宠物");
                return transformablePets;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可变换宠物列表失败");
                return new List<TransformablePetDto>();
            }
        }

        /// <summary>
        /// 获取神宠列表
        /// </summary>
        public async Task<List<TransformablePetDto>> GetGodPetsAsync()
        {
            try
            {
                if (_cache.TryGetValue(GOD_PETS_CACHE_KEY, out List<TransformablePetDto> cachedGodPets))
                {
                    return cachedGodPets;
                }

                var allPets = await GetTransformablePetsAsync();
                var godPets = allPets.Where(p => p.IsGodPet).ToList();

                _cache.Set(GOD_PETS_CACHE_KEY, godPets, CACHE_DURATION);
                return godPets;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取神宠列表失败");
                return new List<TransformablePetDto>();
            }
        }

        /// <summary>
        /// 获取神圣宠物列表
        /// </summary>
        public async Task<List<TransformablePetDto>> GetHolyPetsAsync()
        {
            try
            {
                if (_cache.TryGetValue(HOLY_PETS_CACHE_KEY, out List<TransformablePetDto> cachedHolyPets))
                {
                    return cachedHolyPets;
                }

                var allPets = await GetTransformablePetsAsync();
                var holyPets = allPets.Where(p => p.IsHolyPet).ToList();

                _cache.Set(HOLY_PETS_CACHE_KEY, holyPets, CACHE_DURATION);
                return holyPets;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取神圣宠物列表失败");
                return new List<TransformablePetDto>();
            }
        }

        /// <summary>
        /// 随机生成神宠
        /// </summary>
        public async Task<PetTransformResultDto> RandomGenerateGodPetAsync(RandomGodPetRequestDto request)
        {
            try
            {
                _logger.LogInformation($"用户{request.UserId}请求随机生成神宠");

                // 验证条件
                var transformRequest = new PetTransformRequestDto
                {
                    UserId = request.UserId,
                    SourcePetId = request.SourcePetId ?? 0,
                    TargetPetNo = 0, // 随机选择
                    TransformType = PetTransformType.RandomGod,
                    UsedItemId = request.UsedItemId
                };

                var (isValid, errorMessage) = await ValidateTransformConditionsAsync(transformRequest);
                if (!isValid)
                {
                    return new PetTransformResultDto
                    {
                        Success = false,
                        Message = errorMessage,
                        TransformType = PetTransformType.RandomGod
                    };
                }

                // 获取神宠列表
                var godPets = await GetGodPetsAsync();
                var holyPets = await GetHolyPetsAsync();

                if (!godPets.Any() && !holyPets.Any())
                {
                    return new PetTransformResultDto
                    {
                        Success = false,
                        Message = "没有可用的神宠或神圣宠物",
                        TransformType = PetTransformType.RandomGod
                    };
                }

                // 获取配置
                var config = await GetTransformConfigAsync(PetTransformType.RandomGod);
                if (config == null)
                {
                    return new PetTransformResultDto
                    {
                        Success = false,
                        Message = "百变配置不存在",
                        TransformType = PetTransformType.RandomGod
                    };
                }

                // 计算概率
                TransformablePetDto selectedPet;
                bool isHoly = false;

                // 1/100 概率获得神圣宠物
                if (holyPets.Any() && _random.Next(1, 101) <= config.HolyPetRate)
                {
                    selectedPet = holyPets[_random.Next(holyPets.Count)];
                    isHoly = true;
                    _logger.LogInformation($"用户{request.UserId}获得神圣宠物: {selectedPet.Name}");
                }
                else if (godPets.Any())
                {
                    selectedPet = godPets[_random.Next(godPets.Count)];
                    _logger.LogInformation($"用户{request.UserId}获得神宠: {selectedPet.Name}");
                }
                else
                {
                    return new PetTransformResultDto
                    {
                        Success = false,
                        Message = "没有可用的神宠",
                        TransformType = PetTransformType.RandomGod
                    };
                }

                // 获取目标宠物配置
                var targetPetConfig = await _dbContext.Db.Queryable<pet_config>()
                    .Where(p => p.pet_no == selectedPet.PetNo)
                    .FirstAsync();

                if (targetPetConfig == null)
                {
                    return new PetTransformResultDto
                    {
                        Success = false,
                        Message = "目标宠物配置不存在",
                        TransformType = PetTransformType.RandomGod
                    };
                }

                // 更新请求参数
                transformRequest.TargetPetNo = selectedPet.PetNo;

                // 执行变换
                var result = await ExecuteTransformAsync(transformRequest, targetPetConfig);
                result.IsGodPet = selectedPet.IsGodPet;
                result.IsHolyPet = isHoly;

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"用户{request.UserId}随机生成神宠失败");
                return new PetTransformResultDto
                {
                    Success = false,
                    Message = "随机生成神宠失败",
                    TransformType = PetTransformType.RandomGod
                };
            }
        }

        /// <summary>
        /// 获取稀有度等级
        /// </summary>
        private int GetRarityLevel(string attribute)
        {
            return attribute switch
            {
                "神圣" => 5,
                "神" => 4,
                "稀有" => 3,
                "精英" => 2,
                _ => 1
            };
        }

        /// <summary>
        /// 验证宠物是否可用于百变
        /// </summary>
        public async Task<bool> IsTransformablePetAsync(int petNo)
        {
            return await _validPetsService.IsValidSynthesisPetAsync(petNo);
        }

        /// <summary>
        /// 百变宠物变换
        /// </summary>
        public async Task<PetTransformResultDto> TransformPetAsync(PetTransformRequestDto request)
        {
            try
            {
                _logger.LogInformation($"用户{request.UserId}请求宠物变换: {request.SourcePetId} -> {request.TargetPetNo}");

                // 验证条件
                var (isValid, errorMessage) = await ValidateTransformConditionsAsync(request);
                if (!isValid)
                {
                    return new PetTransformResultDto
                    {
                        Success = false,
                        Message = errorMessage,
                        TransformType = request.TransformType
                    };
                }

                // 获取目标宠物配置
                var targetPetConfig = await _dbContext.Db.Queryable<pet_config>()
                    .Where(p => p.pet_no == request.TargetPetNo)
                    .FirstAsync();

                if (targetPetConfig == null)
                {
                    return new PetTransformResultDto
                    {
                        Success = false,
                        Message = "目标宠物配置不存在",
                        TransformType = request.TransformType
                    };
                }

                // 执行变换
                return await ExecuteTransformAsync(request, targetPetConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"用户{request.UserId}宠物变换失败");
                return new PetTransformResultDto
                {
                    Success = false,
                    Message = "宠物变换失败",
                    TransformType = request.TransformType
                };
            }
        }

        /// <summary>
        /// 获取百变宠物选择界面数据
        /// </summary>
        public async Task<PetTransformSelectionDto> GetTransformSelectionDataAsync(int userId)
        {
            try
            {
                var result = new PetTransformSelectionDto
                {
                    TransformablePets = await GetTransformablePetsAsync(),
                    GodPets = await GetGodPetsAsync(),
                    HolyPets = await GetHolyPetsAsync(),
                    Config = await GetTransformConfigAsync(PetTransformType.RandomGod) ?? new PetTransformConfigDto(),
                    UserStats = await GetUserTransformStatsAsync(userId)
                };

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取用户{userId}的百变选择数据失败");
                return new PetTransformSelectionDto();
            }
        }

        /// <summary>
        /// 获取用户百变宠物统计
        /// </summary>
        public async Task<PetTransformStatsDto> GetUserTransformStatsAsync(int userId)
        {
            try
            {
                var logs = await _dbContext.Db.Queryable<pet_transform_log>()
                    .Where(l => l.user_id == userId)
                    .ToListAsync();

                var stats = new PetTransformStatsDto
                {
                    TotalTransforms = logs.Count(),
                    SuccessCount = logs.Count(l => l.success),
                    FailureCount = logs.Count(l => !l.success),
                    GodPetCount = logs.Count(l => l.success && l.is_god_pet),
                    HolyPetCount = logs.Count(l => l.success && l.is_holy_pet),
                    TotalCostGold = logs.Sum(l => l.cost_gold),
                    LastTransformTime = logs.OrderByDescending(l => l.create_time).FirstOrDefault()?.create_time
                };

                stats.SuccessRate = stats.TotalTransforms > 0
                    ? Math.Round((decimal)stats.SuccessCount / stats.TotalTransforms * 100, 2)
                    : 0;

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取用户{userId}的百变统计失败");
                return new PetTransformStatsDto();
            }
        }

        /// <summary>
        /// 获取百变宠物配置
        /// </summary>
        public async Task<PetTransformConfigDto?> GetTransformConfigAsync(PetTransformType transformType)
        {
            try
            {
                var cacheKey = string.Format(TRANSFORM_CONFIG_CACHE_KEY, transformType.ToString());
                if (_cache.TryGetValue(cacheKey, out PetTransformConfigDto cachedConfig))
                {
                    return cachedConfig;
                }

                var config = await _dbContext.Db.Queryable<pet_transform_config>()
                    .Where(c => c.transform_type == transformType.ToString() && c.is_active)
                    .FirstAsync();

                if (config == null)
                {
                    return null;
                }

                var configDto = new PetTransformConfigDto
                {
                    TransformType = transformType,
                    CostGold = config.cost_gold,
                    RequiredLevel = config.required_level,
                    SuccessRate = config.success_rate,
                    CooldownTime = config.cooldown_time,
                    IsActive = config.is_active,
                    GodPetRate = config.god_pet_rate,
                    HolyPetRate = config.holy_pet_rate
                };

                _cache.Set(cacheKey, configDto, CACHE_DURATION);
                return configDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取变换配置失败: {transformType}");
                return null;
            }
        }

        /// <summary>
        /// 验证变换条件
        /// </summary>
        public async Task<(bool IsValid, string ErrorMessage)> ValidateTransformConditionsAsync(PetTransformRequestDto request)
        {
            try
            {
                // 1. 检查冷却时间
                var (inCooldown, remainingTime) = await CheckCooldownAsync(request.UserId, request.TransformType);
                if (inCooldown)
                {
                    return (false, $"操作冷却中，剩余时间: {remainingTime / 1000}秒");
                }

                // 2. 获取配置
                var config = await GetTransformConfigAsync(request.TransformType);
                if (config == null || !config.IsActive)
                {
                    return (false, "百变功能暂时不可用");
                }

                // 3. 验证源宠物（如果指定了）
                if (request.SourcePetId > 0)
                {
                    var sourcePet = await _dbContext.Db.Queryable<user_pet>()
                        .Where(p => p.id == request.SourcePetId && p.user_id == request.UserId)
                        .FirstAsync();

                    if (sourcePet == null)
                    {
                        return (false, "源宠物不存在或不属于您");
                    }

                    if (sourcePet.level < config.RequiredLevel)
                    {
                        return (false, $"宠物等级不足，需要{config.RequiredLevel}级");
                    }
                }

                // 4. 验证目标宠物（如果指定了）
                if (request.TargetPetNo > 0)
                {
                    if (!await IsTransformablePetAsync(request.TargetPetNo))
                    {
                        return (false, "目标宠物不能用于百变");
                    }
                }

                // 5. 验证用户金币
                var user = await _dbContext.Db.Queryable<user>()
                    .Where(u => u.id == request.UserId)
                    .FirstAsync();

                if (user == null)
                {
                    return (false, "用户不存在");
                }

                if (user.gold < config.CostGold)
                {
                    return (false, $"金币不足，需要{config.CostGold}金币");
                }

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证变换条件失败");
                return (false, "验证失败");
            }
        }

        /// <summary>
        /// 检查用户冷却时间
        /// </summary>
        public async Task<(bool InCooldown, long RemainingTime)> CheckCooldownAsync(int userId, PetTransformType transformType)
        {
            try
            {
                var config = await GetTransformConfigAsync(transformType);
                if (config == null || config.CooldownTime <= 0)
                {
                    return (false, 0);
                }

                var key = $"{userId}_{transformType}";
                if (_lastTransformTime.TryGetValue(key, out long lastTime))
                {
                    var elapsed = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() - lastTime;
                    if (elapsed < config.CooldownTime)
                    {
                        return (true, config.CooldownTime - elapsed);
                    }
                }

                return (false, 0);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查冷却时间失败");
                return (false, 0);
            }
        }

        /// <summary>
        /// 计算神宠概率
        /// </summary>
        public async Task<decimal> CalculateGodPetProbabilityAsync(user_pet sourcePet)
        {
            try
            {
                // 基于原系统逻辑：成长越高，神宠概率越高
                var growth = sourcePet.growth;

                if (growth <= 45)
                {
                    return 0; // 成长太低，无法获得神宠
                }

                if (growth > 185)
                {
                    return 35; // 最高35%概率
                }

                // 线性计算：(成长 - 45) * 0.25
                return (decimal)((double)(growth - 45) * 0.25);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算神宠概率失败");
                return 0;
            }
        }

        /// <summary>
        /// 执行宠物变换
        /// </summary>
        public async Task<PetTransformResultDto> ExecuteTransformAsync(PetTransformRequestDto request, pet_config targetPetConfig)
        {
            try
            {
                _dbContext.Db.Ado.BeginTran();

                try
                {
                    var config = await GetTransformConfigAsync(request.TransformType);
                    if (config == null)
                    {
                        return new PetTransformResultDto
                        {
                            Success = false,
                            Message = "配置不存在",
                            TransformType = request.TransformType
                        };
                    }

                    // 1. 扣除金币
                    await _dbContext.Db.Updateable<user>()
                        .SetColumns(u => u.gold == u.gold - config.CostGold)
                        .Where(u => u.id == request.UserId)
                        .ExecuteCommandAsync();

                    // 2. 创建新宠物
                    var newPet = new user_pet
                    {
                        user_id = request.UserId,
                        pet_no = targetPetConfig.pet_no,
                        name = targetPetConfig.name ?? string.Empty,
                        exp = 1,
                        growth = 1, // 基础成长，根据kddata.sql中growth字段类型为decimal(20,0)
                        hp = 100, // 基础生命值
                        mp = 100, // 基础魔法值
                        atk = 10, // 基础攻击力
                        def = 10, // 基础防御力
                        spd = 10, // 基础速度
                        create_time = DateTime.Now
                    };

                    // 如果有源宠物，继承部分属性
                    if (request.SourcePetId > 0)
                    {
                        var sourcePet = await _dbContext.Db.Queryable<user_pet>()
                            .Where(p => p.id == request.SourcePetId)
                            .FirstAsync();

                        if (sourcePet != null)
                        {
                            // 继承等级和经验的一部分
                            newPet.level = Math.Max(1, (sourcePet.level ?? 1) / 2);
                            newPet.exp = (sourcePet.exp ?? 0) / 2;

                            // 继承成长的一部分
                            newPet.growth = Math.Max(1, (sourcePet.growth ?? 1) * 0.8m);

                            // 删除源宠物
                            await _dbContext.Db.Deleteable<user_pet>()
                                .Where(p => p.id == request.SourcePetId)
                                .ExecuteCommandAsync();
                        }
                    }

                    // 插入新宠物
                    var newPetId = await _dbContext.Db.Insertable(newPet)
                        .ExecuteReturnIdentityAsync();

                    // 3. 记录变换日志
                    var log = new pet_transform_log
                    {
                        user_id = request.UserId,
                        source_pet_id = request.SourcePetId > 0 ? request.SourcePetId : null,
                        target_pet_no = request.TargetPetNo,
                        result_pet_id = newPetId,
                        transform_type = request.TransformType.ToString(),
                        cost_gold = config.CostGold,
                        used_item_id = request.UsedItemId,
                        success = true,
                        is_god_pet = targetPetConfig.attribute == "神",
                        is_holy_pet = targetPetConfig.attribute == "神圣",
                        remark = $"百变获得: {targetPetConfig.name}",
                        create_time = DateTime.Now
                    };

                    await LogTransformAsync(log);

                    // 4. 更新冷却时间
                    var cooldownKey = $"{request.UserId}_{request.TransformType}";
                    _lastTransformTime[cooldownKey] = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

                    _dbContext.Db.Ado.CommitTran();

                    _logger.LogInformation($"用户{request.UserId}百变成功: {targetPetConfig.name}");

                    return new PetTransformResultDto
                    {
                        Success = true,
                        Message = $"百变成功！获得了 {targetPetConfig.name}",
                        ResultPetNo = targetPetConfig.pet_no,
                        ResultPetId = newPetId,
                        ResultPetName = targetPetConfig.name,
                        IsGodPet = targetPetConfig.attribute == "神",
                        IsHolyPet = targetPetConfig.attribute == "神圣",
                        CostGold = config.CostGold,
                        UsedItem = request.UsedItemId,
                        TransformType = request.TransformType
                    };
                }
                catch
                {
                    _dbContext.Db.Ado.RollbackTran();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"执行宠物变换失败: 用户{request.UserId}");
                return new PetTransformResultDto
                {
                    Success = false,
                    Message = "变换失败，请重试",
                    TransformType = request.TransformType
                };
            }
        }

        /// <summary>
        /// 记录变换日志
        /// </summary>
        public async Task<bool> LogTransformAsync(pet_transform_log log)
        {
            try
            {
                await _dbContext.Db.Insertable(log).ExecuteCommandAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录变换日志失败");
                return false;
            }
        }

        /// <summary>
        /// 初始化默认配置
        /// </summary>
        public async Task<bool> InitializeDefaultConfigAsync()
        {
            try
            {
                var existingConfigs = await _dbContext.Db.Queryable<pet_transform_config>().ToListAsync();
                if (existingConfigs.Any())
                {
                    _logger.LogInformation("百变配置已存在，跳过初始化");
                    return true;
                }

                var defaultConfigs = new List<pet_transform_config>
                {
                    new pet_transform_config
                    {
                        transform_type = PetTransformType.RandomGod.ToString(),
                        cost_gold = 100000,
                        required_level = 40,
                        success_rate = 100.00m,
                        cooldown_time = 300000, // 5分钟
                        god_pet_rate = 90.00m,
                        holy_pet_rate = 1.00m,
                        is_active = true
                    },
                    new pet_transform_config
                    {
                        transform_type = PetTransformType.RandomHoly.ToString(),
                        cost_gold = 500000,
                        required_level = 60,
                        success_rate = 100.00m,
                        cooldown_time = 600000, // 10分钟
                        god_pet_rate = 0.00m,
                        holy_pet_rate = 100.00m,
                        is_active = true
                    },
                    new pet_transform_config
                    {
                        transform_type = PetTransformType.Specified.ToString(),
                        cost_gold = 200000,
                        required_level = 50,
                        success_rate = 100.00m,
                        cooldown_time = 180000, // 3分钟
                        god_pet_rate = 0.00m,
                        holy_pet_rate = 0.00m,
                        is_active = true
                    }
                };

                await _dbContext.Db.Insertable(defaultConfigs).ExecuteCommandAsync();
                _logger.LogInformation("百变配置初始化完成");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化百变配置失败");
                return false;
            }
        }
    }
}
