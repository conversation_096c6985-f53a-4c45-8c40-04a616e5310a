using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.Interface;
using WebApplication_HM.DTOs;
using WebApplication_HM.Models;
using System.ComponentModel.DataAnnotations;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 等级管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class LevelController : ControllerBase
    {
        private readonly ILevelService _levelService;
        private readonly ILogger<LevelController> _logger;

        public LevelController(ILevelService levelService, ILogger<LevelController> logger)
        {
            _levelService = levelService;
            _logger = logger;
        }

        /// <summary>
        /// 根据经验计算等级
        /// </summary>
        /// <param name="request">等级计算请求</param>
        /// <returns>等级计算结果</returns>
        [HttpPost]
        public async Task<ActionResult<LevelCalculationResultDTO>> CalculateLevel([FromBody] LevelCalculationRequestDTO request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new LevelCalculationResultDTO
                    {
                        Success = false,
                        Message = "请求参数无效"
                    });
                }

                var result = await _levelService.CalculateLevelDetailAsync(request.Experience, request.SystemName);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算等级失败，经验: {Experience}, 系统: {SystemName}", 
                    request.Experience, request.SystemName);
                
                return BadRequest(new LevelCalculationResultDTO
                {
                    Success = false,
                    Message = "计算等级失败：" + ex.Message
                });
            }
        }

        /// <summary>
        /// 批量计算等级
        /// </summary>
        /// <param name="request">批量等级计算请求</param>
        /// <returns>批量等级计算结果</returns>
        [HttpPost]
        public async Task<ActionResult<BatchLevelCalculationResultDTO>> BatchCalculateLevel([FromBody] BatchLevelCalculationRequestDTO request)
        {
            try
            {
                if (!ModelState.IsValid || request.ExperienceList.Count == 0)
                {
                    return BadRequest(new BatchLevelCalculationResultDTO
                    {
                        Success = false,
                        Message = "请求参数无效或经验值列表为空"
                    });
                }

                // 限制批量计算数量
                if (request.ExperienceList.Count > 1000)
                {
                    return BadRequest(new BatchLevelCalculationResultDTO
                    {
                        Success = false,
                        Message = "批量计算数量不能超过1000个"
                    });
                }

                var results = new List<LevelCalculationResultDTO>();
                
                foreach (var exp in request.ExperienceList)
                {
                    var result = await _levelService.CalculateLevelDetailAsync(exp, request.SystemName);
                    results.Add(result);
                }

                return Ok(new BatchLevelCalculationResultDTO
                {
                    Success = true,
                    Results = results
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量计算等级失败，数量: {Count}, 系统: {SystemName}", 
                    request.ExperienceList.Count, request.SystemName);
                
                return BadRequest(new BatchLevelCalculationResultDTO
                {
                    Success = false,
                    Message = "批量计算等级失败：" + ex.Message
                });
            }
        }

        /// <summary>
        /// 获取等级配置列表
        /// </summary>
        /// <param name="request">等级范围请求</param>
        /// <returns>等级配置列表</returns>
        [HttpGet]
        public async Task<ActionResult<LevelConfigListResultDTO>> GetLevelConfigs([FromQuery] LevelRangeRequestDTO request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new LevelConfigListResultDTO
                    {
                        Success = false,
                        Message = "请求参数无效"
                    });
                }

                if (request.StartLevel > request.EndLevel)
                {
                    return BadRequest(new LevelConfigListResultDTO
                    {
                        Success = false,
                        Message = "起始等级不能大于结束等级"
                    });
                }

                // 限制查询范围
                if (request.EndLevel - request.StartLevel > 500)
                {
                    return BadRequest(new LevelConfigListResultDTO
                    {
                        Success = false,
                        Message = "查询范围不能超过500级"
                    });
                }

                var configs = await _levelService.GetLevelConfigsAsync(
                    request.StartLevel, 
                    request.EndLevel, 
                    request.SystemName ?? "pet");

                var configDTOs = configs.Select(x => new LevelConfigDTO
                {
                    Level = x.level,
                    RequiredExp = x.required_exp,
                    UpgradeExp = x.upgrade_exp,
                    IsActive = x.is_active == 1
                }).ToList();

                return Ok(new LevelConfigListResultDTO
                {
                    Success = true,
                    Configs = configDTOs,
                    TotalCount = configDTOs.Count
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取等级配置失败，范围: {StartLevel}-{EndLevel}, 系统: {SystemName}", 
                    request.StartLevel, request.EndLevel, request.SystemName);
                
                return BadRequest(new LevelConfigListResultDTO
                {
                    Success = false,
                    Message = "获取等级配置失败：" + ex.Message
                });
            }
        }

        /// <summary>
        /// 获取等级进度信息
        /// </summary>
        /// <param name="experience">当前经验值</param>
        /// <param name="systemName">系统名称</param>
        /// <returns>等级进度信息</returns>
        [HttpGet]
        public async Task<ActionResult<LevelProgressDTO>> GetLevelProgress(
            [Required][Range(0, long.MaxValue)] long experience,
            string systemName = "pet")
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest("请求参数无效");
                }

                var progress = await _levelService.GetLevelProgressAsync(experience, systemName);
                return Ok(progress);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取等级进度失败，经验: {Experience}, 系统: {SystemName}", 
                    experience, systemName);
                
                return BadRequest("获取等级进度失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 检查升级情况
        /// </summary>
        /// <param name="currentExp">当前经验</param>
        /// <param name="addExp">增加的经验</param>
        /// <param name="systemName">系统名称</param>
        /// <returns>升级检查结果</returns>
        [HttpGet]
        public async Task<ActionResult<object>> CheckLevelUp(
            [Required][Range(0, long.MaxValue)] long currentExp,
            [Required][Range(0, long.MaxValue)] long addExp,
            string systemName = "pet")
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest("请求参数无效");
                }

                var (canLevelUp, newLevel, levelDiff) = await _levelService.CheckLevelUpAsync(currentExp, addExp, systemName);
                
                return Ok(new
                {
                    CanLevelUp = canLevelUp,
                    OldLevel = await _levelService.CalculateLevelAsync(currentExp, systemName),
                    NewLevel = newLevel,
                    LevelDiff = levelDiff,
                    TotalExp = currentExp + addExp
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查升级失败，当前经验: {CurrentExp}, 增加经验: {AddExp}, 系统: {SystemName}", 
                    currentExp, addExp, systemName);
                
                return BadRequest("检查升级失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 验证经验值
        /// </summary>
        /// <param name="experience">经验值</param>
        /// <param name="systemName">系统名称</param>
        /// <returns>验证结果</returns>
        [HttpGet]
        public async Task<ActionResult<object>> ValidateExp(
            [Required] long experience,
            string systemName = "pet")
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest("请求参数无效");
                }

                var isValid = await _levelService.ValidateExpAsync(experience, systemName);
                var systemConfig = await _levelService.GetSystemConfigAsync(systemName);
                
                return Ok(new
                {
                    IsValid = isValid,
                    Experience = experience,
                    SystemName = systemName,
                    MaxExp = systemConfig?.max_exp ?? 0,
                    MaxLevel = systemConfig?.max_level ?? 0
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证经验值失败，经验: {Experience}, 系统: {SystemName}", 
                    experience, systemName);
                
                return BadRequest("验证经验值失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 获取系统配置
        /// </summary>
        /// <param name="systemName">系统名称</param>
        /// <returns>系统配置</returns>
        [HttpGet]
        public async Task<ActionResult<ExpSystemConfigDTO>> GetSystemConfig(string systemName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(systemName))
                {
                    return BadRequest("系统名称不能为空");
                }

                var config = await _levelService.GetSystemConfigAsync(systemName);
                if (config == null)
                {
                    return NotFound($"未找到系统配置：{systemName}");
                }

                var configDTO = new ExpSystemConfigDTO
                {
                    SystemName = config.system_name,
                    MaxExp = config.max_exp,
                    MaxLevel = config.max_level,
                    ExpFormula = config.exp_formula,
                    IsActive = config.is_active == 1
                };

                return Ok(configDTO);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统配置失败，系统: {SystemName}", systemName);
                return BadRequest("获取系统配置失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 获取所有等级配置（用于调试）
        /// </summary>
        /// <param name="systemName">系统名称</param>
        /// <returns>等级配置列表</returns>
        [HttpGet("configs")]
        public async Task<ActionResult<List<LevelConfig>>> GetLevelConfigs([FromQuery] string systemName = "pet")
        {
            try
            {
                var configs = await _levelService.GetLevelConfigsAsync(1, 200, systemName);
                return Ok(configs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取等级配置失败，系统: {SystemName}", systemName);
                return BadRequest(new { message = "获取等级配置失败：" + ex.Message });
            }
        }

        /// <summary>
        /// 获取系统配置（用于调试）
        /// </summary>
        /// <param name="systemName">系统名称</param>
        /// <returns>系统配置</returns>
        [HttpGet("system-config/{systemName}")]
        public async Task<ActionResult<ExpSystemConfig>> GetSystemConfigForDebug(string systemName)
        {
            try
            {
                var config = await _levelService.GetSystemConfigAsync(systemName);
                if (config == null)
                {
                    return NotFound(new { message = $"未找到系统配置：{systemName}" });
                }
                return Ok(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统配置失败，系统: {SystemName}", systemName);
                return BadRequest(new { message = "获取系统配置失败：" + ex.Message });
            }
        }



        /// <summary>
        /// 清除等级计算缓存（用于调试）
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("clear-cache")]
        public ActionResult ClearLevelCache()
        {
            try
            {
                // 这里需要调用缓存服务的清除方法
                // 由于缓存服务没有直接的清除方法，我们返回提示重启应用
                return Ok(new { message = "请重启应用以清除等级计算缓存，或等待缓存自动过期" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清除缓存失败");
                return BadRequest(new { message = "清除缓存失败：" + ex.Message });
            }
        }
    }
}
