/**
 * 美观的确认对话框组件
 * 提供比原生confirm更好的用户体验
 */

class ConfirmDialog {
    constructor() {
        this.init();
    }

    init() {
        this.createDialogContainer();
    }

    createDialogContainer() {
        const container = document.createElement('div');
        container.id = 'confirm-dialog-container';
        container.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 10001;
            display: none;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(2px);
        `;

        // 确保 document.body 存在
        if (document.body) {
            document.body.appendChild(container);
        } else {
            // 如果 body 还没有加载，等待 DOM 加载完成
            document.addEventListener('DOMContentLoaded', () => {
                document.body.appendChild(container);
            });
        }
        this.container = container;
    }

    show(options = {}) {
        const {
            title = '确认操作',
            message = '您确定要执行此操作吗？',
            confirmText = '确定',
            cancelText = '取消',
            type = 'warning', // warning, danger, info
            onConfirm = () => {},
            onCancel = () => {}
        } = options;

        return new Promise((resolve) => {
            const dialog = this.createDialog({
                title,
                message,
                confirmText,
                cancelText,
                type,
                onConfirm: () => {
                    this.hide();
                    onConfirm();
                    resolve(true);
                },
                onCancel: () => {
                    this.hide();
                    onCancel();
                    resolve(false);
                }
            });

            this.container.innerHTML = '';
            this.container.appendChild(dialog);
            this.container.style.display = 'flex';

            // 添加动画效果
            setTimeout(() => {
                dialog.style.transform = 'scale(1)';
                dialog.style.opacity = '1';
            }, 10);
        });
    }

    createDialog({ title, message, confirmText, cancelText, type, onConfirm, onCancel }) {
        const dialog = document.createElement('div');
        
        const typeColors = {
            warning: { bg: '#fff3cd', border: '#ffeaa7', icon: '⚠️', confirmBg: '#ffc107' },
            danger: { bg: '#f8d7da', border: '#f5c6cb', icon: '🗑️', confirmBg: '#dc3545' },
            info: { bg: '#d1ecf1', border: '#bee5eb', icon: 'ℹ️', confirmBg: '#17a2b8' }
        };
        
        const colors = typeColors[type] || typeColors.warning;
        
        dialog.style.cssText = `
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            max-width: 400px;
            width: 90%;
            transform: scale(0.8);
            opacity: 0;
            transition: all 0.3s ease;
            overflow: hidden;
        `;

        dialog.innerHTML = `
            <div style="
                background: ${colors.bg};
                border-bottom: 1px solid ${colors.border};
                padding: 20px;
                text-align: center;
            ">
                <div style="font-size: 48px; margin-bottom: 10px;">${colors.icon}</div>
                <h3 style="
                    margin: 0 0 10px 0;
                    color: #333;
                    font-size: 18px;
                    font-weight: 600;
                ">${title}</h3>
                <p style="
                    margin: 0;
                    color: #666;
                    font-size: 14px;
                    line-height: 1.5;
                ">${message}</p>
            </div>
            <div style="
                padding: 20px;
                display: flex;
                gap: 12px;
                justify-content: flex-end;
            ">
                <button id="cancel-btn" style="
                    padding: 10px 20px;
                    border: 1px solid #ddd;
                    background: white;
                    color: #666;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: 500;
                    transition: all 0.2s ease;
                    min-width: 80px;
                ">${cancelText}</button>
                <button id="confirm-btn" style="
                    padding: 10px 20px;
                    border: none;
                    background: ${colors.confirmBg};
                    color: white;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: 500;
                    transition: all 0.2s ease;
                    min-width: 80px;
                ">${confirmText}</button>
            </div>
        `;

        // 添加按钮事件
        const cancelBtn = dialog.querySelector('#cancel-btn');
        const confirmBtn = dialog.querySelector('#confirm-btn');

        cancelBtn.onmouseover = () => {
            cancelBtn.style.background = '#f8f9fa';
            cancelBtn.style.borderColor = '#adb5bd';
        };
        cancelBtn.onmouseout = () => {
            cancelBtn.style.background = 'white';
            cancelBtn.style.borderColor = '#ddd';
        };

        confirmBtn.onmouseover = () => {
            confirmBtn.style.opacity = '0.9';
            confirmBtn.style.transform = 'translateY(-1px)';
        };
        confirmBtn.onmouseout = () => {
            confirmBtn.style.opacity = '1';
            confirmBtn.style.transform = 'translateY(0)';
        };

        cancelBtn.onclick = onCancel;
        confirmBtn.onclick = onConfirm;

        // ESC键关闭
        const handleKeydown = (e) => {
            if (e.key === 'Escape') {
                document.removeEventListener('keydown', handleKeydown);
                onCancel();
            }
        };
        document.addEventListener('keydown', handleKeydown);

        // 点击背景关闭
        this.container.onclick = (e) => {
            if (e.target === this.container) {
                onCancel();
            }
        };

        return dialog;
    }

    hide() {
        if (this.container) {
            this.container.style.display = 'none';
        }
    }

    // 便捷方法
    static confirm(message, title = '确认操作') {
        return window.confirmDialog.show({
            title,
            message,
            type: 'warning'
        });
    }

    static confirmDelete(message = '此操作不可逆，您确定要删除吗？', title = '确认删除') {
        return window.confirmDialog.show({
            title,
            message,
            type: 'danger',
            confirmText: '删除',
            cancelText: '取消'
        });
    }

    static confirmUnequip(itemName = '装备', title = '确认卸下') {
        return window.confirmDialog.show({
            title,
            message: `您确定要卸下${itemName}吗？`,
            type: 'info',
            confirmText: '卸下',
            cancelText: '取消'
        });
    }
}

// 创建全局实例
window.confirmDialog = new ConfirmDialog();

console.log('确认对话框组件已初始化');
