-- =====================================================
-- 牧场系统数据迁移和兼容性检查脚本
-- 版本: 1.0
-- 创建时间: 2025-09-01
-- 描述: 确保现有宠物数据与新牧场系统的兼容性
-- =====================================================

-- 1. 检查当前数据状态
-- =====================================================

-- 1.1 检查宠物状态分布
SELECT 
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user_pet), 2) as percentage
FROM user_pet 
GROUP BY status
ORDER BY count DESC;

-- 1.2 检查是否有无效的状态值
SELECT 
    id,
    user_id,
    pet_no,
    status,
    'Invalid Status' as issue
FROM user_pet 
WHERE status NOT IN ('牧场', '携带', '丢弃');

-- 1.3 检查主战宠物设置
SELECT 
    user_id,
    COUNT(*) as main_pet_count,
    GROUP_CONCAT(id) as main_pet_ids
FROM user_pet 
WHERE is_main = 1 AND status != '丢弃'
GROUP BY user_id
HAVING COUNT(*) > 1;

-- 1.4 检查用户是否有主战宠物
SELECT 
    u.id as user_id,
    u.username,
    COUNT(up.id) as total_pets,
    SUM(CASE WHEN up.is_main = 1 THEN 1 ELSE 0 END) as main_pets
FROM user u
LEFT JOIN user_pet up ON u.id = up.user_id AND up.status != '丢弃'
GROUP BY u.id, u.username
HAVING main_pets = 0 AND total_pets > 0;

-- 2. 数据修复和标准化
-- =====================================================

-- 2.1 修复无效的宠物状态（如果有的话）
UPDATE user_pet 
SET status = '牧场' 
WHERE status NOT IN ('牧场', '携带', '丢弃') OR status IS NULL;

-- 2.2 确保每个用户只有一个主战宠物
-- 首先取消所有主战宠物标记
UPDATE user_pet SET is_main = 0;

-- 然后为每个用户设置一个主战宠物（选择ID最小的携带状态宠物，如果没有则选择牧场中的）
UPDATE user_pet up1
SET is_main = 1
WHERE up1.id = (
    SELECT MIN(up2.id)
    FROM user_pet up2
    WHERE up2.user_id = up1.user_id 
    AND up2.status = '携带'
    AND up2.status != '丢弃'
)
AND up1.status != '丢弃';

-- 如果用户没有携带状态的宠物，则从牧场中选择一个
UPDATE user_pet up1
SET is_main = 1
WHERE up1.id = (
    SELECT MIN(up2.id)
    FROM user_pet up2
    WHERE up2.user_id = up1.user_id 
    AND up2.status = '牧场'
    AND up2.user_id NOT IN (
        SELECT DISTINCT user_id 
        FROM user_pet 
        WHERE is_main = 1 AND status != '丢弃'
    )
);

-- 3. 性能优化索引
-- =====================================================

-- 3.1 为牧场系统查询添加索引
CREATE INDEX IF NOT EXISTS idx_user_pet_status_user ON user_pet(status, user_id);
CREATE INDEX IF NOT EXISTS idx_user_pet_user_status ON user_pet(user_id, status);
CREATE INDEX IF NOT EXISTS idx_user_pet_main ON user_pet(is_main, user_id);

-- 3.2 为分页查询优化
CREATE INDEX IF NOT EXISTS idx_user_pet_user_status_id ON user_pet(user_id, status, id);

-- 4. 数据完整性检查
-- =====================================================

-- 4.1 验证修复后的数据
SELECT 
    '主战宠物检查' as check_type,
    user_id,
    COUNT(*) as main_pet_count
FROM user_pet 
WHERE is_main = 1 AND status != '丢弃'
GROUP BY user_id
HAVING COUNT(*) != 1;

-- 4.2 验证状态分布
SELECT 
    '状态分布检查' as check_type,
    status,
    COUNT(*) as count
FROM user_pet 
GROUP BY status;

-- 4.3 验证用户宠物数量
SELECT 
    '用户宠物统计' as check_type,
    user_id,
    COUNT(*) as total_pets,
    SUM(CASE WHEN status = '牧场' THEN 1 ELSE 0 END) as pasture_pets,
    SUM(CASE WHEN status = '携带' THEN 1 ELSE 0 END) as carry_pets,
    SUM(CASE WHEN status = '丢弃' THEN 1 ELSE 0 END) as discarded_pets
FROM user_pet 
GROUP BY user_id
ORDER BY total_pets DESC
LIMIT 10;

-- 5. 创建牧场系统视图（可选）
-- =====================================================

-- 5.1 牧场宠物视图
CREATE OR REPLACE VIEW v_pasture_pets AS
SELECT 
    up.id,
    up.user_id,
    up.pet_no,
    up.custom_name,
    pc.name as pet_name,
    up.element,
    up.level,
    up.exp,
    up.hp,
    up.mp,
    up.max_hp,
    up.max_mp,
    up.atk,
    up.def,
    up.hit,
    up.dodge,
    up.spd,
    up.growth,
    up.realm,
    up.is_main,
    up.status,
    up.create_time,
    pc.attribute as pet_attribute,
    pc.description as pet_description
FROM user_pet up
LEFT JOIN pet_config pc ON up.pet_no = pc.pet_no
WHERE up.status IN ('牧场', '携带');

-- 5.2 用户牧场统计视图
CREATE OR REPLACE VIEW v_user_pasture_stats AS
SELECT 
    user_id,
    COUNT(*) as total_pets,
    SUM(CASE WHEN status = '牧场' THEN 1 ELSE 0 END) as pasture_count,
    SUM(CASE WHEN status = '携带' THEN 1 ELSE 0 END) as carry_count,
    SUM(CASE WHEN is_main = 1 THEN 1 ELSE 0 END) as main_pet_count,
    MAX(level) as max_level,
    AVG(level) as avg_level,
    SUM(exp) as total_exp
FROM user_pet 
WHERE status != '丢弃'
GROUP BY user_id;

-- 6. 迁移完成验证
-- =====================================================

-- 6.1 最终数据验证报告
SELECT 
    '=== 牧场系统迁移完成报告 ===' as report_title,
    NOW() as completion_time;

SELECT 
    '总用户数' as metric,
    COUNT(DISTINCT user_id) as value
FROM user_pet
UNION ALL
SELECT 
    '总宠物数' as metric,
    COUNT(*) as value
FROM user_pet
UNION ALL
SELECT 
    '牧场宠物数' as metric,
    COUNT(*) as value
FROM user_pet WHERE status = '牧场'
UNION ALL
SELECT 
    '携带宠物数' as metric,
    COUNT(*) as value
FROM user_pet WHERE status = '携带'
UNION ALL
SELECT 
    '已丢弃宠物数' as metric,
    COUNT(*) as value
FROM user_pet WHERE status = '丢弃'
UNION ALL
SELECT 
    '主战宠物数' as metric,
    COUNT(*) as value
FROM user_pet WHERE is_main = 1 AND status != '丢弃';

-- 显示迁移成功消息
SELECT 
    '✅ 牧场系统数据迁移完成！' as status,
    '所有数据已标准化并优化索引' as details,
    '新的API系统已准备就绪' as next_step;
