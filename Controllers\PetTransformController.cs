using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.DTOs;
using WebApplication_HM.Services;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 百变宠物控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class PetTransformController : ControllerBase
    {
        private readonly IPetTransformService _transformService;
        private readonly ILogger<PetTransformController> _logger;

        public PetTransformController(
            IPetTransformService transformService,
            ILogger<PetTransformController> logger)
        {
            _transformService = transformService;
            _logger = logger;
        }

        /// <summary>
        /// 获取可变换宠物列表
        /// </summary>
        /// <returns>可变换宠物列表</returns>
        [HttpGet("transformable-pets")]
        public async Task<IActionResult> GetTransformablePets()
        {
            try
            {
                var pets = await _transformService.GetTransformablePetsAsync();
                return Ok(new ApiResponse<List<TransformablePetDto>>
                {
                    Success = true,
                    Data = pets,
                    Message = $"获取到{pets.Count}个可变换宠物"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可变换宠物列表失败");
                return Ok(new ApiResponse<List<TransformablePetDto>>
                {
                    Success = false,
                    Message = "获取可变换宠物列表失败",
                    Data = new List<TransformablePetDto>()
                });
            }
        }

        /// <summary>
        /// 获取神宠列表
        /// </summary>
        /// <returns>神宠列表</returns>
        [HttpGet("god-pets")]
        public async Task<IActionResult> GetGodPets()
        {
            try
            {
                var pets = await _transformService.GetGodPetsAsync();
                return Ok(new ApiResponse<List<TransformablePetDto>>
                {
                    Success = true,
                    Data = pets,
                    Message = $"获取到{pets.Count}个神宠"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取神宠列表失败");
                return Ok(new ApiResponse<List<TransformablePetDto>>
                {
                    Success = false,
                    Message = "获取神宠列表失败",
                    Data = new List<TransformablePetDto>()
                });
            }
        }

        /// <summary>
        /// 获取神圣宠物列表
        /// </summary>
        /// <returns>神圣宠物列表</returns>
        [HttpGet("holy-pets")]
        public async Task<IActionResult> GetHolyPets()
        {
            try
            {
                var pets = await _transformService.GetHolyPetsAsync();
                return Ok(new ApiResponse<List<TransformablePetDto>>
                {
                    Success = true,
                    Data = pets,
                    Message = $"获取到{pets.Count}个神圣宠物"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取神圣宠物列表失败");
                return Ok(new ApiResponse<List<TransformablePetDto>>
                {
                    Success = false,
                    Message = "获取神圣宠物列表失败",
                    Data = new List<TransformablePetDto>()
                });
            }
        }

        /// <summary>
        /// 随机生成神宠
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>变换结果</returns>
        [HttpPost("random-god-pet")]
        public async Task<IActionResult> RandomGenerateGodPet([FromBody] RandomGodPetRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Ok(new ApiResponse<PetTransformResultDto>
                    {
                        Success = false,
                        Message = "请求参数无效",
                        Data = null
                    });
                }

                var result = await _transformService.RandomGenerateGodPetAsync(request);
                return Ok(new ApiResponse<PetTransformResultDto>
                {
                    Success = result.Success,
                    Data = result,
                    Message = result.Message
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"用户{request.UserId}随机生成神宠失败");
                return Ok(new ApiResponse<PetTransformResultDto>
                {
                    Success = false,
                    Message = "随机生成神宠失败",
                    Data = null
                });
            }
        }

        /// <summary>
        /// 宠物变换
        /// </summary>
        /// <param name="request">变换请求</param>
        /// <returns>变换结果</returns>
        [HttpPost("transform")]
        public async Task<IActionResult> TransformPet([FromBody] PetTransformRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Ok(new ApiResponse<PetTransformResultDto>
                    {
                        Success = false,
                        Message = "请求参数无效",
                        Data = null
                    });
                }

                var result = await _transformService.TransformPetAsync(request);
                return Ok(new ApiResponse<PetTransformResultDto>
                {
                    Success = result.Success,
                    Data = result,
                    Message = result.Message
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"用户{request.UserId}宠物变换失败");
                return Ok(new ApiResponse<PetTransformResultDto>
                {
                    Success = false,
                    Message = "宠物变换失败",
                    Data = null
                });
            }
        }

        /// <summary>
        /// 获取百变选择界面数据
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>选择界面数据</returns>
        [HttpGet("selection-data/{userId}")]
        public async Task<IActionResult> GetTransformSelectionData(int userId)
        {
            try
            {
                var data = await _transformService.GetTransformSelectionDataAsync(userId);
                return Ok(new ApiResponse<PetTransformSelectionDto>
                {
                    Success = true,
                    Data = data,
                    Message = "获取百变选择数据成功"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取用户{userId}的百变选择数据失败");
                return Ok(new ApiResponse<PetTransformSelectionDto>
                {
                    Success = false,
                    Message = "获取百变选择数据失败",
                    Data = new PetTransformSelectionDto()
                });
            }
        }

        /// <summary>
        /// 获取用户百变统计
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>统计数据</returns>
        [HttpGet("stats/{userId}")]
        public async Task<IActionResult> GetUserTransformStats(int userId)
        {
            try
            {
                var stats = await _transformService.GetUserTransformStatsAsync(userId);
                return Ok(new ApiResponse<PetTransformStatsDto>
                {
                    Success = true,
                    Data = stats,
                    Message = "获取百变统计成功"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取用户{userId}的百变统计失败");
                return Ok(new ApiResponse<PetTransformStatsDto>
                {
                    Success = false,
                    Message = "获取百变统计失败",
                    Data = new PetTransformStatsDto()
                });
            }
        }

        /// <summary>
        /// 验证宠物是否可变换
        /// </summary>
        /// <param name="petNo">宠物编号</param>
        /// <returns>验证结果</returns>
        [HttpGet("validate/{petNo}")]
        public async Task<IActionResult> ValidateTransformablePet(int petNo)
        {
            try
            {
                var isValid = await _transformService.IsTransformablePetAsync(petNo);
                return Ok(new ApiResponse<bool>
                {
                    Success = true,
                    Data = isValid,
                    Message = isValid ? "宠物可以用于百变" : "宠物不能用于百变"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"验证宠物{petNo}是否可变换失败");
                return Ok(new ApiResponse<bool>
                {
                    Success = false,
                    Data = false,
                    Message = "验证失败"
                });
            }
        }

        /// <summary>
        /// 获取变换配置
        /// </summary>
        /// <param name="transformType">变换类型</param>
        /// <returns>配置信息</returns>
        [HttpGet("config/{transformType}")]
        public async Task<IActionResult> GetTransformConfig(PetTransformType transformType)
        {
            try
            {
                var config = await _transformService.GetTransformConfigAsync(transformType);
                return Ok(new ApiResponse<PetTransformConfigDto>
                {
                    Success = config != null,
                    Data = config,
                    Message = config != null ? "获取配置成功" : "配置不存在"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取变换配置失败: {transformType}");
                return Ok(new ApiResponse<PetTransformConfigDto>
                {
                    Success = false,
                    Message = "获取配置失败",
                    Data = null
                });
            }
        }

        /// <summary>
        /// 检查冷却时间
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="transformType">变换类型</param>
        /// <returns>冷却状态</returns>
        [HttpGet("cooldown/{userId}/{transformType}")]
        public async Task<IActionResult> CheckCooldown(int userId, PetTransformType transformType)
        {
            try
            {
                var (inCooldown, remainingTime) = await _transformService.CheckCooldownAsync(userId, transformType);
                return Ok(new ApiResponse<object>
                {
                    Success = true,
                    Data = new { InCooldown = inCooldown, RemainingTime = remainingTime },
                    Message = inCooldown ? $"冷却中，剩余{remainingTime / 1000}秒" : "可以操作"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"检查用户{userId}的冷却时间失败");
                return Ok(new ApiResponse<object>
                {
                    Success = false,
                    Message = "检查冷却时间失败",
                    Data = null
                });
            }
        }

        /// <summary>
        /// 初始化默认配置
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("initialize")]
        public async Task<IActionResult> InitializeDefaultConfig()
        {
            try
            {
                var success = await _transformService.InitializeDefaultConfigAsync();
                return Ok(new ApiResponse<object>
                {
                    Success = success,
                    Message = success ? "初始化配置成功" : "初始化配置失败",
                    Data = null
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化百变配置失败");
                return Ok(new ApiResponse<object>
                {
                    Success = false,
                    Message = "初始化配置失败",
                    Data = null
                });
            }
        }
    }
}
