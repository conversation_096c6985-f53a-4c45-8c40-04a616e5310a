using System.Reflection;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.DependencyInjection;
using System.Net.WebSockets;
using WebApplication_HM.DTOs.RequestDTO;
using WebApplication_HM.DTOs.ResultDTO;
using System.Collections.Concurrent;
using WebApplication_HM.Interface;
using WebApplication_HM.Models.Define;

namespace WebApplication_HM.Services.SocketInfo
{
    public class UniversalWebSocketHandler
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly RealTimeNotificationService _realTimeService;
        // 全局连接管理（简单实现，生产建议用单独管理器）
        private static readonly ConcurrentDictionary<string, WebSocket> _connections = new();

        public UniversalWebSocketHandler(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _realTimeService = new RealTimeNotificationService();
        }

        /// <summary>
        /// 处理WebSocket消息，支持通用反射和聊天(chat/private)功能
        /// </summary>
        public async Task HandleAsync(WebSocket socket, string message)
        {  
            // 记录连接（简单实现，实际可用唯一标识）
             string connId = socket.GetHashCode().ToString();
            _connections[connId] = socket;

            using (var scope = _serviceProvider.CreateScope())
            {
                object result = "";
                var scopedProvider = scope.ServiceProvider;
                var response = new WebSocketResponseDTO();
                try
                {
                    // 用户认证消息优先处理
                    var authMsg = TryParseUserAuth(message);
                    if (authMsg != null)
                    {
                        await HandleUserAuth(socket, authMsg, connId);
                        return;
                    }

                    // 聊天消息处理
                    var chatMsg = TryParse<ChatMessageRtDTO>(message);
                    if (chatMsg != null)
                    {
                        await HandleChatMessage(socket, chatMsg);
                        return;
                    }

                    // 其余类型走原有通用反射
                    var request = JsonSerializer.Deserialize<WebSocketRequestDTO>(message);
                    if (request == null)
                        throw new Exception("请求格式错误");

                    var serviceType = Type.GetType($"WebApplication_HM.Services.{request.Service}Service");
                    if (serviceType == null)
                        throw new Exception($"服务 {request.Service}Service 不存在");

                    var service = scopedProvider.GetService(serviceType);
                    if (service == null)
                        throw new Exception($"服务 {request.Service}Service 未注册");

                    var method = serviceType.GetMethod(request.Method);
                    if (method == null)
                        throw new Exception($"方法 {request.Method} 不存在");

                    var parameters = method.GetParameters();
                    object[] invokeParams = new object[parameters.Length];
                    for (int i = 0; i < parameters.Length; i++)
                    {
                        if (request.Parameters != null && i < request.Parameters.Length && request.Parameters[i] != null)
                        {
                            invokeParams[i] = JsonSerializer.Deserialize(request.Parameters[i].ToString(), parameters[i].ParameterType);
                        }
                        else
                        {
                            invokeParams[i] = parameters[i].HasDefaultValue ? parameters[i].DefaultValue : null;
                        }
                    }

                    if (typeof(Task).IsAssignableFrom(method.ReturnType))
                    {
                        dynamic task = method.Invoke(service, invokeParams);
                        await task;
                        result = task.GetAwaiter().GetResult();
                    }
                    else
                    {
                        result = method.Invoke(service, invokeParams);
                    }
                }
                catch (Exception ex)
                {
                    response.Success = false;
                    response.Message = ex.Message;
                    response.Data = null;
                    result = response;
                }

                var respStr = JsonSerializer.Serialize(result);
                await socket.SendAsync(Encoding.UTF8.GetBytes(respStr), WebSocketMessageType.Text, true, CancellationToken.None);
            }
        }

        /// <summary>
        /// 群发消息到所有连接
        /// </summary>
        private async Task SendMessageToAllAsync(string msg)
        {
            foreach (var ws in _connections.Values)
            {
                if (ws.State == WebSocketState.Open)
                {
                    await ws.SendAsync(Encoding.UTF8.GetBytes(msg), WebSocketMessageType.Text, true, CancellationToken.None);
                }
            }
        }

        /// <summary>
        /// 发送消息到指定用户（TargetUserId为连接Id或自定义标识）
        /// </summary>
        private async Task SendMessageToUserAsync(string targetUserId, string msg)
        {
            if (_connections.TryGetValue(targetUserId, out var ws) && ws.State == WebSocketState.Open)
            {
                await ws.SendAsync(Encoding.UTF8.GetBytes(msg), WebSocketMessageType.Text, true, CancellationToken.None);
            }
        }

        /// <summary>
        /// 处理聊天消息
        /// </summary>
        /// <param name="socket">WebSocket连接</param>
        /// <param name="chatMsg">聊天消息</param>
        private async Task HandleChatMessage(WebSocket socket, ChatMessageRtDTO chatMsg)
        {
            try
            {
                string connId = socket.GetHashCode().ToString();
                
                if (chatMsg.Type == MessageTypeEnum.Login)
                {
                    // 处理登录消息
                    await HandleLogin(socket, chatMsg);
                }
                else if (chatMsg.Type == "chat")
                {
                    // 群发聊天
                    await _realTimeService.BroadcastToAll(JsonSerializer.Serialize(chatMsg));
                }
                else if (chatMsg.Type == "private" && !string.IsNullOrEmpty(chatMsg.TargetUserId))
                {
                    // 私聊消息
                    if (int.TryParse(chatMsg.TargetUserId, out int targetPlayerId))
                    {
                        await _realTimeService.SendToPlayer(targetPlayerId, JsonSerializer.Serialize(chatMsg));
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理聊天消息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理登录消息
        /// </summary>
        /// <param name="socket">WebSocket连接</param>
        /// <param name="loginMsg">登录消息</param>
        private async Task HandleLogin(WebSocket socket, ChatMessageRtDTO loginMsg)
        {
            try
            {
                // 简化登录处理 - 直接允许登录用于聊天测试
                // 在生产环境中应该进行适当的验证

                bool loginSuccess = false;
                string errorMessage = "";

                // 基本验证：检查必要字段
                if (!string.IsNullOrEmpty(loginMsg.number) && loginMsg.playerId > 0)
                {
                    try
                    {
                        using (var scope = _serviceProvider.CreateScope())
                        {
                            var playerService = scope.ServiceProvider.GetService<IPlayerService>();
                            if (playerService != null && !string.IsNullOrEmpty(loginMsg.password))
                            {
                                // 尝试正式登录验证
                                var loginRequest = new LoginRequestDTO
                                {
                                    Number = loginMsg.number,
                                    Password = loginMsg.password
                                };

                                var loginResult = playerService.Login(loginRequest);
                                loginSuccess = loginResult.Success;
                                if (!loginSuccess)
                                {
                                    errorMessage = loginResult.Message ?? "登录验证失败";
                                }
                            }
                            else
                            {
                                // 如果没有密码或服务不可用，允许测试登录
                                loginSuccess = true;
                                errorMessage = "测试模式登录";
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // 如果验证服务出错，允许测试登录
                        loginSuccess = true;
                        errorMessage = $"验证服务异常，允许测试登录: {ex.Message}";
                        Console.WriteLine($"登录验证异常: {ex.Message}");
                    }
                }
                else
                {
                    errorMessage = "用户名或玩家ID不能为空";
                }

                if (loginSuccess)
                {
                    // 登录成功，添加到实时通知服务
                    _realTimeService.AddPlayerConnection(loginMsg.playerId, socket, loginMsg.number);

                    // 发送登录成功响应
                    var response = new ChatMessageRtDTO
                    {
                        Type = MessageTypeEnum.LoginSuccess,
                        playerId = loginMsg.playerId,
                        Name = loginMsg.number,
                        Content = "登录成功"
                    };

                    var responseJson = JsonSerializer.Serialize(response);
                    await socket.SendAsync(Encoding.UTF8.GetBytes(responseJson), WebSocketMessageType.Text, true, CancellationToken.None);

                    Console.WriteLine($"用户登录成功: {loginMsg.number} (ID: {loginMsg.playerId})");
                }
                else
                {
                    // 登录失败
                    var response = new
                    {
                        Type = "login_error",
                        Message = errorMessage
                    };

                    var responseJson = JsonSerializer.Serialize(response);
                    await socket.SendAsync(Encoding.UTF8.GetBytes(responseJson), WebSocketMessageType.Text, true, CancellationToken.None);

                    Console.WriteLine($"用户登录失败: {loginMsg.number} - {errorMessage}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理登录失败: {ex.Message}");

                // 发送错误响应
                try
                {
                    var errorResponse = new
                    {
                        Type = "login_error",
                        Message = "登录处理异常: " + ex.Message
                    };
                    var errorJson = JsonSerializer.Serialize(errorResponse);
                    await socket.SendAsync(Encoding.UTF8.GetBytes(errorJson), WebSocketMessageType.Text, true, CancellationToken.None);
                }
                catch
                {
                    // 忽略发送错误响应时的异常
                }
            }
        }

        /// <summary>
        /// 处理连接断开
        /// </summary>
        /// <param name="socket">WebSocket连接</param>
        public void HandleDisconnection(WebSocket socket)
        {
            string connId = socket.GetHashCode().ToString();
            var playerId = _realTimeService.GetPlayerIdByConnection(connId);
            
            if (playerId.HasValue)
            {
                _realTimeService.RemovePlayerConnection(playerId.Value);
            }

            _connections.TryRemove(connId, out _);
        }

        /// <summary>
        /// 发送消息到指定WebSocket连接
        /// </summary>
        private async Task SendToSocket(WebSocket socket, string message)
        {
            if (socket.State == WebSocketState.Open)
            {
                var bytes = Encoding.UTF8.GetBytes(message);
                await socket.SendAsync(bytes, WebSocketMessageType.Text, true, CancellationToken.None);
            }
        }

        /// <summary>
        /// 尝试解析用户认证消息
        /// </summary>
        private object TryParseUserAuth(string message)
        {
            try
            {
                var jsonDoc = JsonDocument.Parse(message);
                if (jsonDoc.RootElement.TryGetProperty("type", out var typeElement) &&
                    typeElement.GetString() == "user_auth")
                {
                    return JsonSerializer.Deserialize<object>(message);
                }
            }
            catch
            {
                // 忽略解析错误
            }
            return null;
        }

        /// <summary>
        /// 处理用户认证
        /// </summary>
        private async Task HandleUserAuth(WebSocket socket, object authMessage, string connectionId)
        {
            try
            {
                var jsonDoc = JsonDocument.Parse(JsonSerializer.Serialize(authMessage));
                if (jsonDoc.RootElement.TryGetProperty("data", out var dataElement))
                {
                    var userId = dataElement.TryGetProperty("userId", out var userIdElement) ? userIdElement.GetInt32() : 0;
                    var userName = dataElement.TryGetProperty("userName", out var userNameElement) ? userNameElement.GetString() : "";

                    if (userId > 0 && !string.IsNullOrEmpty(userName))
                    {
                        // 存储用户认证信息到连接管理器
                        _connections[connectionId] = socket;

                        // 发送认证成功响应
                        var response = new
                        {
                            type = "auth_response",
                            success = true,
                            message = "用户认证成功",
                            data = new
                            {
                                userId = userId,
                                userName = userName,
                                timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                            }
                        };

                        await SendToSocket(socket, JsonSerializer.Serialize(response));

                        Console.WriteLine($"✅ 用户认证成功: {userName} (ID: {userId}) - 连接: {connectionId}");
                    }
                    else
                    {
                        // 认证失败
                        var errorResponse = new
                        {
                            type = "auth_response",
                            success = false,
                            message = "用户认证失败：无效的用户信息"
                        };

                        await SendToSocket(socket, JsonSerializer.Serialize(errorResponse));
                        Console.WriteLine($"❌ 用户认证失败: 无效的用户信息 - 连接: {connectionId}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 处理用户认证时发生错误: {ex.Message}");

                var errorResponse = new
                {
                    type = "auth_response",
                    success = false,
                    message = "用户认证失败：服务器错误"
                };

                await SendToSocket(socket, JsonSerializer.Serialize(errorResponse));
            }
        }

        /// <summary>
        /// 尝试反序列化为指定类型，失败返回null
        /// </summary>
        private T? TryParse<T>(string json) where T : class
        {
            try { return JsonSerializer.Deserialize<T>(json); } catch { return null; }
        }
    }
}