using System.ComponentModel.DataAnnotations;

namespace WebApplication_HM.DTOs
{
    /// <summary>
    /// 等级计算请求DTO
    /// </summary>
    public class LevelCalculationRequestDTO
    {
        /// <summary>
        /// 当前经验值
        /// </summary>
        [Required(ErrorMessage = "经验值不能为空")]
        [Range(0, long.MaxValue, ErrorMessage = "经验值必须大于等于0")]
        public long Experience { get; set; }
        
        /// <summary>
        /// 系统名称（可选，默认为宠物系统）
        /// </summary>
        public string SystemName { get; set; } = "pet";
    }

    /// <summary>
    /// 等级计算结果DTO
    /// </summary>
    public class LevelCalculationResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 当前等级
        /// </summary>
        public int CurrentLevel { get; set; }
        
        /// <summary>
        /// 当前经验
        /// </summary>
        public long CurrentExp { get; set; }
        
        /// <summary>
        /// 该等级所需累积经验
        /// </summary>
        public long RequiredExp { get; set; }
        
        /// <summary>
        /// 升级到下一级所需经验
        /// </summary>
        public long UpgradeExp { get; set; }
        
        /// <summary>
        /// 距离下一级还需要的经验
        /// </summary>
        public long ExpToNextLevel { get; set; }
        
        /// <summary>
        /// 当前等级进度百分比
        /// </summary>
        public decimal Progress { get; set; }
        
        /// <summary>
        /// 是否已达最高等级
        /// </summary>
        public bool IsMaxLevel { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string? Message { get; set; }
    }

    /// <summary>
    /// 等级配置DTO
    /// </summary>
    public class LevelConfigDTO
    {
        /// <summary>
        /// 等级
        /// </summary>
        public int Level { get; set; }
        
        /// <summary>
        /// 该等级所需累积经验
        /// </summary>
        public long RequiredExp { get; set; }
        
        /// <summary>
        /// 升级到下一级所需经验
        /// </summary>
        public long UpgradeExp { get; set; }
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// 等级配置列表结果DTO
    /// </summary>
    public class LevelConfigListResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 等级配置列表
        /// </summary>
        public List<LevelConfigDTO> Configs { get; set; } = new();
        
        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string? Message { get; set; }
    }

    /// <summary>
    /// 批量等级计算请求DTO
    /// </summary>
    public class BatchLevelCalculationRequestDTO
    {
        /// <summary>
        /// 经验值列表
        /// </summary>
        [Required(ErrorMessage = "经验值列表不能为空")]
        public List<long> ExperienceList { get; set; } = new();
        
        /// <summary>
        /// 系统名称（可选，默认为宠物系统）
        /// </summary>
        public string SystemName { get; set; } = "pet";
    }

    /// <summary>
    /// 批量等级计算结果DTO
    /// </summary>
    public class BatchLevelCalculationResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 等级计算结果列表
        /// </summary>
        public List<LevelCalculationResultDTO> Results { get; set; } = new();
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string? Message { get; set; }
    }

    /// <summary>
    /// 经验系统配置DTO
    /// </summary>
    public class ExpSystemConfigDTO
    {
        /// <summary>
        /// 系统名称
        /// </summary>
        public string SystemName { get; set; } = string.Empty;
        
        /// <summary>
        /// 经验上限
        /// </summary>
        public long MaxExp { get; set; }
        
        /// <summary>
        /// 等级上限
        /// </summary>
        public int MaxLevel { get; set; }
        
        /// <summary>
        /// 经验计算公式
        /// </summary>
        public string? ExpFormula { get; set; }
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// 等级范围查询请求DTO
    /// </summary>
    public class LevelRangeRequestDTO
    {
        /// <summary>
        /// 起始等级
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "起始等级必须大于0")]
        public int StartLevel { get; set; } = 1;
        
        /// <summary>
        /// 结束等级
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "结束等级必须大于0")]
        public int EndLevel { get; set; } = 100;
        
        /// <summary>
        /// 系统名称（可选）
        /// </summary>
        public string? SystemName { get; set; }
    }

    /// <summary>
    /// 等级进度信息DTO
    /// </summary>
    public class LevelProgressDTO
    {
        /// <summary>
        /// 当前等级
        /// </summary>
        public int CurrentLevel { get; set; }
        
        /// <summary>
        /// 当前经验
        /// </summary>
        public long CurrentExp { get; set; }
        
        /// <summary>
        /// 当前等级起始经验
        /// </summary>
        public long LevelStartExp { get; set; }
        
        /// <summary>
        /// 下一等级起始经验
        /// </summary>
        public long NextLevelStartExp { get; set; }
        
        /// <summary>
        /// 当前等级内的经验
        /// </summary>
        public long ExpInCurrentLevel { get; set; }
        
        /// <summary>
        /// 当前等级总经验需求
        /// </summary>
        public long TotalExpInLevel { get; set; }
        
        /// <summary>
        /// 进度百分比
        /// </summary>
        public decimal ProgressPercentage { get; set; }
        
        /// <summary>
        /// 是否已达最高等级
        /// </summary>
        public bool IsMaxLevel { get; set; }
    }
}
