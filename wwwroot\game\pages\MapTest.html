<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>地图系统测试</title>
    <script src="Content/Javascript/jquery-1.8.3.min.js"></script>
    <script src="/game/js/game-api-adapter.js"></script>
    
    <style type="text/css">
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        
        .button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .button:hover {
            background: #45a049;
        }
        
        .button:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        
        .result {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success {
            border-color: #4CAF50;
            background: #e8f5e8;
        }
        
        .error {
            border-color: #f44336;
            background: #ffeaea;
        }
        
        .input-group {
            margin: 10px 0;
        }
        
        .input-group label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        
        .input-group input {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            width: 200px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🗺️ 地图系统测试页面</h1>
        
        <!-- 基础信息 -->
        <div class="section">
            <h3>📋 基础信息</h3>
            <div class="input-group">
                <label>用户ID:</label>
                <input type="number" id="userId" value="1" />
            </div>
            <div class="input-group">
                <label>地图ID:</label>
                <input type="number" id="mapId" value="1" />
            </div>
            <div class="input-group">
                <label>宠物ID:</label>
                <input type="number" id="petId" value="1" />
            </div>
        </div>
        
        <!-- API测试 -->
        <div class="section">
            <h3>🔧 API测试</h3>
            <button class="button" onclick="testGetMapList()">获取地图列表</button>
            <button class="button" onclick="testGetMapDetail()">获取地图详情</button>
            <button class="button" onclick="testGetMapMonsters()">获取地图怪物</button>
            <button class="button" onclick="testGetMapDrops()">获取地图掉落</button>
            <button class="button" onclick="testEnterMap()">进入地图</button>
            <button class="button" onclick="testUnlockMap()">解锁地图</button>
            <div id="apiResult" class="result"></div>
        </div>
        
        <!-- 页面测试 -->
        <div class="section">
            <h3>📄 页面测试</h3>
            <button class="button" onclick="openBattleMap()">打开战斗地图</button>
            <button class="button" onclick="openMapInfo()">打开地图详情</button>
            <button class="button" onclick="openMapTemplate()">打开统一模板</button>
            <div id="pageResult" class="result"></div>
        </div>
        
        <!-- 系统状态 -->
        <div class="section">
            <h3>📊 系统状态</h3>
            <button class="button" onclick="checkSystemStatus()">检查系统状态</button>
            <div id="statusResult" class="result"></div>
        </div>
    </div>

    <script type="text/javascript">
        // 获取输入值
        function getInputValues() {
            return {
                userId: parseInt(document.getElementById('userId').value) || 1,
                mapId: parseInt(document.getElementById('mapId').value) || 1,
                petId: parseInt(document.getElementById('petId').value) || 1
            };
        }
        
        // 显示结果
        function showResult(elementId, result, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.textContent = typeof result === 'object' ? JSON.stringify(result, null, 2) : result;
        }
        
        // 测试获取地图列表
        async function testGetMapList() {
            try {
                const { userId } = getInputValues();
                showResult('apiResult', '正在获取地图列表...');
                
                const result = await gameAPI.getMapList(userId);
                showResult('apiResult', result, result.success);
            } catch (error) {
                showResult('apiResult', `错误: ${error.message}`, false);
            }
        }
        
        // 测试获取地图详情
        async function testGetMapDetail() {
            try {
                const { userId, mapId } = getInputValues();
                showResult('apiResult', '正在获取地图详情...');
                
                const result = await gameAPI.getMapDetail(mapId, userId);
                showResult('apiResult', result, result.success);
            } catch (error) {
                showResult('apiResult', `错误: ${error.message}`, false);
            }
        }
        
        // 测试获取地图怪物
        async function testGetMapMonsters() {
            try {
                const { mapId } = getInputValues();
                showResult('apiResult', '正在获取地图怪物...');
                
                const result = await gameAPI.getMapMonsters(mapId);
                showResult('apiResult', result, result.success);
            } catch (error) {
                showResult('apiResult', `错误: ${error.message}`, false);
            }
        }
        
        // 测试获取地图掉落
        async function testGetMapDrops() {
            try {
                const { mapId } = getInputValues();
                showResult('apiResult', '正在获取地图掉落...');
                
                const result = await gameAPI.getMapDrops(mapId);
                showResult('apiResult', result, result.success);
            } catch (error) {
                showResult('apiResult', `错误: ${error.message}`, false);
            }
        }
        
        // 测试进入地图
        async function testEnterMap() {
            try {
                const { userId, mapId, petId } = getInputValues();
                showResult('apiResult', '正在进入地图...');
                
                const result = await gameAPI.enterMap(mapId, userId, petId);
                showResult('apiResult', result, result.success);
            } catch (error) {
                showResult('apiResult', `错误: ${error.message}`, false);
            }
        }
        
        // 测试解锁地图
        async function testUnlockMap() {
            try {
                const { userId, mapId } = getInputValues();
                showResult('apiResult', '正在解锁地图...');
                
                const result = await gameAPI.unlockMap(mapId, userId, 'level');
                showResult('apiResult', result, result.success);
            } catch (error) {
                showResult('apiResult', `错误: ${error.message}`, false);
            }
        }
        
        // 打开战斗地图页面
        function openBattleMap() {
            try {
                const url = '/game/pages/BattleMap.html';
                window.open(url, '_blank');
                showResult('pageResult', '已打开战斗地图页面: ' + url);
            } catch (error) {
                showResult('pageResult', `错误: ${error.message}`, false);
            }
        }
        
        // 打开地图详情页面
        function openMapInfo() {
            try {
                const { mapId } = getInputValues();
                const url = `/game/pages/MapInfo/t${mapId}.html`;
                window.open(url, '_blank');
                showResult('pageResult', `已打开地图${mapId}详情页面: ${url}`);
            } catch (error) {
                showResult('pageResult', `错误: ${error.message}`, false);
            }
        }
        
        // 打开统一模板
        function openMapTemplate() {
            try {
                const url = '/game/pages/MapInfo/template.html';
                window.open(url, '_blank');
                showResult('pageResult', '已打开统一模板页面: ' + url);
            } catch (error) {
                showResult('pageResult', `错误: ${error.message}`, false);
            }
        }
        
        // 检查系统状态
        function checkSystemStatus() {
            const status = {
                gameAPI: typeof gameAPI !== 'undefined',
                jQuery: typeof $ !== 'undefined',
                currentTime: new Date().toLocaleString(),
                userAgent: navigator.userAgent,
                apiMethods: typeof gameAPI !== 'undefined' ? Object.keys(gameAPI) : []
            };
            
            showResult('statusResult', status);
        }
        
        // 页面加载完成后检查状态
        $(document).ready(function() {
            checkSystemStatus();
        });
        
        // 模拟父窗口的一些函数
        window.gameAPI = window.gameAPI || {};
        window.gameAPI.getCurrentUserId = window.gameAPI.getCurrentUserId || function() {
            return parseInt(document.getElementById('userId').value) || 1;
        };
        
        window.showBox = window.showBox || function(message) {
            alert(message);
        };
    </script>
</body>
</html>
