-- 百变宠物系统数据库表创建脚本
-- 注意: pet_transform_log 表已在 kddata.sql 中存在，此处不重复创建

-- 检查 pet_transform_log 表是否存在
SELECT 'pet_transform_log 表已存在，跳过创建' as message
WHERE EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_schema = DATABASE()
    AND table_name = 'pet_transform_log'
);

-- ----------------------------
-- Table structure for pet_transform_config
-- ----------------------------
DROP TABLE IF EXISTS `pet_transform_config`;
CREATE TABLE `pet_transform_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `transform_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '变换类型',
  `cost_gold` bigint(20) NULL DEFAULT 0 COMMENT '消耗金币',
  `required_level` int(11) NULL DEFAULT 1 COMMENT '所需等级',
  `success_rate` decimal(5,2) NULL DEFAULT 100.00 COMMENT '成功率（百分比）',
  `cooldown_time` bigint(20) NULL DEFAULT 0 COMMENT '冷却时间（毫秒）',
  `god_pet_rate` decimal(5,2) NULL DEFAULT 10.00 COMMENT '神宠概率（百分比）',
  `holy_pet_rate` decimal(5,2) NULL DEFAULT 1.00 COMMENT '神圣宠物概率（百分比）',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_transform_type`(`transform_type` ASC) USING BTREE,
  INDEX `idx_is_active`(`is_active` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_unicode_ci COMMENT = '百变宠物配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of pet_transform_config
-- ----------------------------
INSERT INTO `pet_transform_config` (`transform_type`, `cost_gold`, `required_level`, `success_rate`, `cooldown_time`, `god_pet_rate`, `holy_pet_rate`, `is_active`) VALUES
('RandomGod', 100000, 40, 100.00, 300000, 90.00, 1.00, 1),
('RandomHoly', 500000, 60, 100.00, 600000, 0.00, 100.00, 1),
('Specified', 200000, 50, 100.00, 180000, 0.00, 0.00, 1),
('RandomNormal', 50000, 30, 100.00, 120000, 5.00, 0.10, 1);

-- ----------------------------
-- 创建视图：百变宠物统计
-- ----------------------------
DROP VIEW IF EXISTS `v_pet_transform_stats`;
CREATE VIEW `v_pet_transform_stats` AS
SELECT 
    user_id,
    COUNT(*) as total_transforms,
    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failure_count,
    SUM(CASE WHEN success = 1 AND is_god_pet = 1 THEN 1 ELSE 0 END) as god_pet_count,
    SUM(CASE WHEN success = 1 AND is_holy_pet = 1 THEN 1 ELSE 0 END) as holy_pet_count,
    SUM(cost_gold) as total_cost_gold,
    ROUND(
        CASE 
            WHEN COUNT(*) > 0 THEN (SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))
            ELSE 0 
        END, 2
    ) as success_rate,
    MAX(create_time) as last_transform_time
FROM pet_transform_log 
GROUP BY user_id;

-- ----------------------------
-- 创建存储过程：清理过期日志
-- ----------------------------
DELIMITER $$
DROP PROCEDURE IF EXISTS `sp_cleanup_transform_logs`$$
CREATE PROCEDURE `sp_cleanup_transform_logs`(IN days_to_keep INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE affected_rows INT DEFAULT 0;
    
    -- 删除指定天数之前的日志记录
    DELETE FROM pet_transform_log 
    WHERE create_time < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    -- 获取影响的行数
    SET affected_rows = ROW_COUNT();
    
    -- 输出清理结果
    SELECT CONCAT('清理了 ', affected_rows, ' 条过期的百变记录') as result;
END$$
DELIMITER ;

-- ----------------------------
-- 创建触发器：更新配置时间
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_pet_transform_config_update`;
CREATE TRIGGER `tr_pet_transform_config_update`
BEFORE UPDATE ON `pet_transform_config`
FOR EACH ROW
BEGIN
    SET NEW.update_time = CURRENT_TIMESTAMP;
END;

-- ----------------------------
-- 创建索引优化查询性能
-- ----------------------------

-- 注意: pet_transform_log 表的索引已在 kddata.sql 中创建，此处仅创建额外索引
-- 检查并创建 pet_transform_log 表的额外索引（如果不存在）
SET @sql = IF((SELECT COUNT(*) FROM information_schema.statistics
               WHERE table_schema = DATABASE()
               AND table_name = 'pet_transform_log'
               AND index_name = 'idx_user_transform_type') = 0,
              'CREATE INDEX `idx_user_transform_type` ON `pet_transform_log` (`user_id`, `transform_type`)',
              'SELECT "索引 idx_user_transform_type 已存在" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM information_schema.statistics
               WHERE table_schema = DATABASE()
               AND table_name = 'pet_transform_log'
               AND index_name = 'idx_user_success_time') = 0,
              'CREATE INDEX `idx_user_success_time` ON `pet_transform_log` (`user_id`, `success`, `create_time`)',
              'SELECT "索引 idx_user_success_time 已存在" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM information_schema.statistics
               WHERE table_schema = DATABASE()
               AND table_name = 'pet_transform_log'
               AND index_name = 'idx_result_pet') = 0,
              'CREATE INDEX `idx_result_pet` ON `pet_transform_log` (`result_pet_id`)',
              'SELECT "索引 idx_result_pet 已存在" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 配置表的索引
CREATE INDEX `idx_config_active_type` ON `pet_transform_config` (`is_active`, `transform_type`);

-- ----------------------------
-- 插入测试数据（可选）
-- ----------------------------

-- 注意: pet_transform_log 表已存在，如需插入测试数据请手动执行
-- 测试用户的百变记录（示例SQL，请根据需要手动执行）
/*
INSERT INTO `pet_transform_log`
(`user_id`, `source_pet_id`, `target_pet_no`, `result_pet_id`, `transform_type`, `cost_gold`, `success`, `is_god_pet`, `is_holy_pet`, `remark`)
VALUES
(1, 1, 79, 101, 'RandomGod', 100000, 1, 1, 0, '测试随机神宠'),
(1, 2, 83, 102, 'Specified', 200000, 1, 0, 0, '测试指定变换'),
(2, 3, 84, 103, 'RandomGod', 100000, 1, 1, 1, '测试获得神圣宠物');
*/

-- ----------------------------
-- 数据完整性检查查询
-- ----------------------------

-- 检查表是否创建成功
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('pet_transform_log', 'pet_transform_config');

-- 检查配置数据
SELECT * FROM pet_transform_config WHERE is_active = 1;

-- 检查视图是否创建成功
SELECT COUNT(*) as view_count 
FROM information_schema.VIEWS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'v_pet_transform_stats';

-- 检查存储过程是否创建成功
SELECT ROUTINE_NAME, ROUTINE_TYPE 
FROM information_schema.ROUTINES 
WHERE ROUTINE_SCHEMA = DATABASE() 
AND ROUTINE_NAME = 'sp_cleanup_transform_logs';

-- 检查触发器是否创建成功
SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE 
FROM information_schema.TRIGGERS 
WHERE TRIGGER_SCHEMA = DATABASE() 
AND TRIGGER_NAME = 'tr_pet_transform_config_update';

-- ----------------------------
-- 性能优化建议
-- ----------------------------

-- 1. 定期清理过期日志（建议保留3个月的数据）
-- CALL sp_cleanup_transform_logs(90);

-- 2. 分析表以优化查询性能
-- ANALYZE TABLE pet_transform_log;
-- ANALYZE TABLE pet_transform_config;

-- 3. 监控慢查询
-- 可以通过以下查询检查是否有慢查询：
-- SELECT * FROM mysql.slow_log WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 DAY);

-- ----------------------------
-- 使用示例
-- ----------------------------

-- 获取用户的百变统计
-- SELECT * FROM v_pet_transform_stats WHERE user_id = 1;

-- 获取最近的百变记录
-- SELECT * FROM pet_transform_log WHERE user_id = 1 ORDER BY create_time DESC LIMIT 10;

-- 获取神宠获得记录
-- SELECT * FROM pet_transform_log WHERE is_god_pet = 1 ORDER BY create_time DESC;

-- 获取当前活跃的配置
-- SELECT * FROM pet_transform_config WHERE is_active = 1;

-- ----------------------------
-- 备份建议
-- ----------------------------

-- 1. 定期备份百变记录表
-- mysqldump -u username -p database_name pet_transform_log > pet_transform_log_backup.sql

-- 2. 备份配置表
-- mysqldump -u username -p database_name pet_transform_config > pet_transform_config_backup.sql

-- 3. 完整备份（包括结构和数据）
-- mysqldump -u username -p database_name > full_backup.sql

COMMIT;
