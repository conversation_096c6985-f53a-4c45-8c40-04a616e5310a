using System.ComponentModel.DataAnnotations;

namespace WebApplication_HM.DTOs.PetManagement
{
    /// <summary>
    /// 牧场宠物列表结果DTO
    /// </summary>
    public class PastureePetListResultDTO
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public List<PasturePetDTO> Pets { get; set; } = new List<PasturePetDTO>();
        public int CurrentCount { get; set; }
        public int MaxCapacity { get; set; }
    }

    /// <summary>
    /// 牧场宠物信息DTO
    /// </summary>
    public class PasturePetDTO
    {
        public int Id { get; set; }
        public int PetNo { get; set; }
        public string Name { get; set; } = string.Empty;
        public string CustomName { get; set; } = string.Empty;
        public string Element { get; set; } = string.Empty;
        public int Level { get; set; }
        public long Exp { get; set; }
        public long Hp { get; set; }
        public long Mp { get; set; }
        public long MaxHp { get; set; }
        public long MaxMp { get; set; }
        public long Atk { get; set; }
        public long Def { get; set; }
        public long Hit { get; set; }
        public long Dodge { get; set; }
        public long Spd { get; set; }
        public decimal Growth { get; set; }
        public string Realm { get; set; } = string.Empty;
        public bool IsMain { get; set; }
        public string Status { get; set; } = string.Empty;
        public string ImagePath { get; set; } = string.Empty;
        public DateTime CreateTime { get; set; }
        
        // 扩展属性
        public decimal Deepen { get; set; }  // 加深伤害
        public decimal Offset { get; set; }  // 抵消伤害
        public decimal Vamp { get; set; }    // 吸血比例
        public decimal VampMp { get; set; }  // 吸魔比例
        public string TalismanState { get; set; } = string.Empty; // 法宝状态
        public int EvolveCount { get; set; }     // 进化次数
        public int SynthesisCount { get; set; }  // 合成次数
        public int NirvanaCount { get; set; }    // 转生次数
    }

    /// <summary>
    /// 宠物操作请求基类
    /// </summary>
    public class PetOperationRequestDTO
    {
        [Required(ErrorMessage = "用户ID不能为空")]
        public int UserId { get; set; }

        [Required(ErrorMessage = "宠物ID不能为空")]
        public int PetId { get; set; }
    }

    /// <summary>
    /// 携带宠物请求DTO
    /// </summary>
    public class CarryPetRequestDTO : PetOperationRequestDTO
    {
        // 继承基类属性即可
    }

    /// <summary>
    /// 存放宠物请求DTO
    /// </summary>
    public class StorePetRequestDTO : PetOperationRequestDTO
    {
        // 继承基类属性即可
    }

    /// <summary>
    /// 设置主宠请求DTO
    /// </summary>
    public class SetMainPetRequestDTO : PetOperationRequestDTO
    {
        // 继承基类属性即可
    }

    /// <summary>
    /// 丢弃宠物请求DTO
    /// </summary>
    public class DiscardPetRequestDTO : PetOperationRequestDTO
    {
        [Required(ErrorMessage = "确认密码不能为空")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// 宠物操作结果DTO
    /// </summary>
    public class PetOperationResultDTO
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public object? Data { get; set; }
    }

    /// <summary>
    /// 牧场容量信息DTO
    /// </summary>
    public class PastureCapacityDTO
    {
        public int CurrentCount { get; set; }
        public int MaxCapacity { get; set; }
        public int AvailableSlots { get; set; }
        public bool IsFull { get; set; }
    }

    /// <summary>
    /// 宠物详细信息DTO
    /// </summary>
    public class PetDetailInfoDTO
    {
        public int Id { get; set; }
        public int PetNo { get; set; }
        public string Name { get; set; } = string.Empty;
        public string CustomName { get; set; } = string.Empty;
        public string Element { get; set; } = string.Empty;
        public int Level { get; set; }
        public long Exp { get; set; }
        public long NextLevelExp { get; set; }
        public decimal ExpProgress { get; set; }
        
        // 基础属性
        public long Hp { get; set; }
        public long Mp { get; set; }
        public long MaxHp { get; set; }
        public long MaxMp { get; set; }
        public long Atk { get; set; }
        public long Def { get; set; }
        public long Hit { get; set; }
        public long Dodge { get; set; }
        public long Spd { get; set; }
        
        // 成长和境界
        public decimal Growth { get; set; }
        public string Realm { get; set; } = string.Empty;
        
        // 特殊属性
        public decimal Deepen { get; set; }
        public decimal Offset { get; set; }
        public decimal Vamp { get; set; }
        public decimal VampMp { get; set; }
        
        // 状态信息
        public bool IsMain { get; set; }
        public string Status { get; set; } = string.Empty;
        public string TalismanState { get; set; } = string.Empty;
        
        // 进化信息
        public int EvolveCount { get; set; }
        public int SynthesisCount { get; set; }
        public int NirvanaCount { get; set; }
        public DateTime? LastEvolutionTime { get; set; }
        public DateTime? LastSynthesisTime { get; set; }
        public DateTime? LastNirvanaTime { get; set; }
        
        // 图片路径
        public string ImagePath { get; set; } = string.Empty;
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 宠物列表查询请求DTO
    /// </summary>
    public class PasturePetListRequestDTO
    {
        [Required(ErrorMessage = "用户ID不能为空")]
        public int UserId { get; set; }

        /// <summary>
        /// 宠物状态筛选 (牧场/携带/全部)
        /// </summary>
        public string Status { get; set; } = "牧场";

        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; } = 50;

        /// <summary>
        /// 排序字段
        /// </summary>
        public string SortBy { get; set; } = "pet_no";

        /// <summary>
        /// 排序方向 (asc/desc)
        /// </summary>
        public string SortDirection { get; set; } = "asc";
    }

    /// <summary>
    /// 宠物重命名请求DTO
    /// </summary>
    public class RenamePetRequestDTO : PetOperationRequestDTO
    {
        [Required(ErrorMessage = "新名称不能为空")]
        [StringLength(20, ErrorMessage = "宠物名称不能超过20个字符")]
        public string NewName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 批量操作宠物请求DTO
    /// </summary>
    public class BatchPetOperationRequestDTO
    {
        [Required(ErrorMessage = "用户ID不能为空")]
        public int UserId { get; set; }

        [Required(ErrorMessage = "宠物ID列表不能为空")]
        public List<int> PetIds { get; set; } = new List<int>();

        [Required(ErrorMessage = "操作类型不能为空")]
        public string Operation { get; set; } = string.Empty; // carry, store, discard
    }

    /// <summary>
    /// 宠物统计信息DTO
    /// </summary>
    public class PetStatisticsDTO
    {
        public int TotalPets { get; set; }
        public int PasturePets { get; set; }
        public int CarryPets { get; set; }
        public int DiscardedPets { get; set; }
        public Dictionary<string, int> ElementDistribution { get; set; } = new Dictionary<string, int>();
        public Dictionary<int, int> LevelDistribution { get; set; } = new Dictionary<int, int>();
        public decimal AverageLevel { get; set; }
        public decimal AverageGrowth { get; set; }
    }
}
