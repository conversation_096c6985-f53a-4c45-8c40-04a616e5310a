using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.DTOs;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 宠物合成API控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class PetSynthesisController : ControllerBase
    {
        private readonly IPetSynthesisService _synthesisService;
        private readonly ILogger<PetSynthesisController> _logger;

        public PetSynthesisController(
            IPetSynthesisService synthesisService,
            ILogger<PetSynthesisController> logger)
        {
            _synthesisService = synthesisService;
            _logger = logger;
        }

        /// <summary>
        /// 执行宠物合成
        /// </summary>
        /// <param name="request">合成请求</param>
        /// <returns>合成结果</returns>
        [HttpPost("synthesize")]
        public async Task<IActionResult> SynthesizePet([FromBody] SynthesisRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ApiResponse<object>
                    {
                        Success = false,
                        Message = "请求参数无效",
                        Data = ModelState
                    });
                }

                var userId = GetCurrentUserId();
                if (userId <= 0)
                {
                    return Unauthorized(new ApiResponse<object>
                    {
                        Success = false,
                        Message = "用户未登录"
                    });
                }

                _logger.LogInformation($"用户 {userId} 请求合成宠物: 主宠ID={request.MainPetId}, 副宠ID={request.VicePetId}");

                var result = await _synthesisService.SynthesizePetAsync(userId, request);

                if (result.Success)
                {
                    _logger.LogInformation($"用户 {userId} 合成成功: 成长增加={result.GrowthIncrease}, 新宠物={result.AfterPetNo}");
                    
                    return Ok(new ApiResponse<SynthesisResultDto>
                    {
                        Success = true,
                        Message = "合成成功！",
                        Data = result
                    });
                }
                else
                {
                    _logger.LogWarning($"用户 {userId} 合成失败: {result.Message}");
                    
                    return BadRequest(new ApiResponse<SynthesisResultDto>
                    {
                        Success = false,
                        Message = result.Message,
                        Data = result
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "合成宠物异常");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = "系统错误，请稍后重试"
                });
            }
        }

        /// <summary>
        /// 获取合成配置信息
        /// </summary>
        /// <param name="mainPetId">主宠ID</param>
        /// <param name="vicePetId">副宠ID</param>
        /// <returns>合成配置</returns>
        [HttpGet("{mainPetId}/{vicePetId}/config")]
        public async Task<IActionResult> GetSynthesisConfig(int mainPetId, int vicePetId)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId <= 0)
                {
                    return Unauthorized(new ApiResponse<object>
                    {
                        Success = false,
                        Message = "用户未登录"
                    });
                }

                var config = await _synthesisService.GetSynthesisConfigAsync(userId, mainPetId, vicePetId);

                if (config == null)
                {
                    return NotFound(new ApiResponse<object>
                    {
                        Success = false,
                        Message = "获取合成配置失败"
                    });
                }

                return Ok(new ApiResponse<SynthesisConfigDto>
                {
                    Success = true,
                    Message = "获取合成配置成功",
                    Data = config
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取合成配置异常");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = "系统错误，请稍后重试"
                });
            }
        }

        /// <summary>
        /// 获取合成历史
        /// </summary>
        /// <param name="petId">宠物ID（可选）</param>
        /// <returns>合成历史列表</returns>
        [HttpGet("history")]
        public async Task<IActionResult> GetSynthesisHistory([FromQuery] int? petId = null)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId <= 0)
                {
                    return Unauthorized(new ApiResponse<object>
                    {
                        Success = false,
                        Message = "用户未登录"
                    });
                }

                var history = await _synthesisService.GetSynthesisHistoryAsync(userId, petId);

                return Ok(new ApiResponse<List<SynthesisHistoryDto>>
                {
                    Success = true,
                    Message = "获取合成历史成功",
                    Data = history
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取合成历史异常");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = "系统错误，请稍后重试"
                });
            }
        }

        /// <summary>
        /// 获取合成统计
        /// </summary>
        /// <returns>合成统计</returns>
        [HttpGet("statistics")]
        public async Task<IActionResult> GetSynthesisStatistics()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId <= 0)
                {
                    return Unauthorized(new ApiResponse<object>
                    {
                        Success = false,
                        Message = "用户未登录"
                    });
                }

                var statistics = await _synthesisService.GetSynthesisStatisticsAsync(userId);

                return Ok(new ApiResponse<SynthesisStatisticsDto>
                {
                    Success = true,
                    Message = "获取合成统计成功",
                    Data = statistics
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取合成统计异常");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = "系统错误，请稍后重试"
                });
            }
        }

        /// <summary>
        /// 验证合成条件
        /// </summary>
        /// <param name="mainPetId">主宠ID</param>
        /// <param name="vicePetId">副宠ID</param>
        /// <returns>验证结果</returns>
        [HttpGet("{mainPetId}/{vicePetId}/validate")]
        public async Task<IActionResult> ValidateSynthesisConditions(int mainPetId, int vicePetId)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId <= 0)
                {
                    return Unauthorized(new ApiResponse<object>
                    {
                        Success = false,
                        Message = "用户未登录"
                    });
                }

                var (isValid, errorMessage) = await _synthesisService.ValidateSynthesisConditionsAsync(userId, mainPetId, vicePetId);

                return Ok(new ApiResponse<object>
                {
                    Success = isValid,
                    Message = isValid ? "验证通过" : errorMessage,
                    Data = new { IsValid = isValid, ErrorMessage = errorMessage }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证合成条件异常");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = "系统错误，请稍后重试"
                });
            }
        }

        /// <summary>
        /// 获取神宠合成概率
        /// </summary>
        /// <param name="currentGrowth">当前成长</param>
        /// <returns>神宠概率</returns>
        [HttpGet("god-pet-probability/{currentGrowth}")]
        public async Task<IActionResult> GetGodPetProbability(decimal currentGrowth)
        {
            try
            {
                var probability = await _synthesisService.CalculateGodPetProbabilityAsync(currentGrowth);

                return Ok(new ApiResponse<object>
                {
                    Success = true,
                    Message = "获取神宠概率成功",
                    Data = new { CurrentGrowth = currentGrowth, GodPetProbability = probability }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取神宠概率异常");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = "系统错误，请稍后重试"
                });
            }
        }

        /// <summary>
        /// 获取VIP加成信息
        /// </summary>
        /// <returns>VIP加成信息</returns>
        [HttpGet("vip-bonus")]
        public async Task<IActionResult> GetVipBonus()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId <= 0)
                {
                    return Unauthorized(new ApiResponse<object>
                    {
                        Success = false,
                        Message = "用户未登录"
                    });
                }

                var vipBonus = await _synthesisService.GetVipBonusAsync(userId);

                return Ok(new ApiResponse<VipBonusDto>
                {
                    Success = true,
                    Message = "获取VIP加成信息成功",
                    Data = vipBonus
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取VIP加成信息异常");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = "系统错误，请稍后重试"
                });
            }
        }

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        /// <returns>用户ID</returns>
        private int GetCurrentUserId()
        {
            try
            {
                // 1. 优先从中间件设置的上下文获取用户ID
                if (HttpContext.Items.TryGetValue("UserId", out var contextUserId) &&
                    contextUserId is int userId && userId > 0)
                {
                    return userId;
                }

                // 2. 从Session中获取用户ID
                var sessionUserId = GetUserIdFromSession();
                if (sessionUserId > 0)
                {
                    return sessionUserId;
                }

                // 3. 从请求头中获取用户ID（用于API调用）
                var headerUserId = GetUserIdFromHeaders();
                if (headerUserId > 0)
                {
                    return headerUserId;
                }

                // 4. 从查询参数中获取用户ID（用于测试）
                var queryUserId = GetUserIdFromQuery();
                if (queryUserId > 0)
                {
                    return queryUserId;
                }

                // 5. 开发环境下的默认测试用户
                if (HttpContext.RequestServices.GetService<IWebHostEnvironment>()?.IsDevelopment() == true)
                {
                    _logger.LogWarning("开发环境：使用默认测试用户ID 1001");
                    return 1001;
                }

                // 6. 生产环境下返回0表示未认证
                _logger.LogWarning("未找到有效的用户身份信息");
                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户ID时发生错误");
                return 0;
            }
        }



        /// <summary>
        /// 从Session中获取用户ID
        /// </summary>
        /// <returns>用户ID</returns>
        private int GetUserIdFromSession()
        {
            try
            {
                if (HttpContext.Session.TryGetValue("UserId", out var userIdBytes))
                {
                    return BitConverter.ToInt32(userIdBytes, 0);
                }

                // 尝试从Session中获取用户登录信息
                var userLoginJson = HttpContext.Session.GetString("UserLogin");
                if (!string.IsNullOrEmpty(userLoginJson))
                {
                    var userLogin = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(userLoginJson);
                    if (userLogin.TryGetValue("userId", out var userIdObj) &&
                        int.TryParse(userIdObj.ToString(), out var userId))
                    {
                        return userId;
                    }
                }

                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从Session获取用户ID失败");
                return 0;
            }
        }

        /// <summary>
        /// 从请求头中获取用户ID
        /// </summary>
        /// <returns>用户ID</returns>
        private int GetUserIdFromHeaders()
        {
            try
            {
                // 从自定义请求头获取用户ID
                var userIdHeader = HttpContext.Request.Headers["X-User-Id"].FirstOrDefault();
                if (!string.IsNullOrEmpty(userIdHeader) && int.TryParse(userIdHeader, out var userId))
                {
                    return userId;
                }

                // 从游戏客户端传递的用户ID头获取
                var gameUserIdHeader = HttpContext.Request.Headers["X-Game-User-Id"].FirstOrDefault();
                if (!string.IsNullOrEmpty(gameUserIdHeader) && int.TryParse(gameUserIdHeader, out var gameUserId))
                {
                    return gameUserId;
                }

                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从请求头获取用户ID失败");
                return 0;
            }
        }

        /// <summary>
        /// 从查询参数中获取用户ID（主要用于测试）
        /// </summary>
        /// <returns>用户ID</returns>
        private int GetUserIdFromQuery()
        {
            try
            {
                var userIdQuery = HttpContext.Request.Query["userId"].FirstOrDefault();
                if (!string.IsNullOrEmpty(userIdQuery) && int.TryParse(userIdQuery, out var userId))
                {
                    return userId;
                }

                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从查询参数获取用户ID失败");
                return 0;
            }
        }
    }
}
