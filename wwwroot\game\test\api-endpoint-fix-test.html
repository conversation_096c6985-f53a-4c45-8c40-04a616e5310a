<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>API端点修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-section h3 { margin-top: 0; color: #333; }
        .test-button { background: #4CAF50; color: white; border: none; padding: 8px 16px; margin: 5px; border-radius: 4px; cursor: pointer; }
        .test-button:hover { background: #45a049; }
        .test-button.error { background: #f44336; }
        .test-button.warning { background: #ff9800; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .endpoint-info { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API端点修复测试</h1>
        
        <div class="endpoint-info">
            <h3>❌ 原始错误请求</h3>
            <code>POST http://localhost:5078/UserPet/Create</code>
            <p><strong>问题</strong>: 端口错误(5078→5000) + 路由不存在(UserPet→PetManagement)</p>
        </div>

        <!-- 服务器连接测试 -->
        <div class="test-section">
            <h3>🔗 服务器连接测试</h3>
            <button class="test-button" onclick="testServerConnection()">测试正确端口连接</button>
            <button class="test-button error" onclick="testWrongPort()">测试错误端口(5078)</button>
            <div id="connectionResults"></div>
        </div>

        <!-- 正确的API端点测试 -->
        <div class="test-section">
            <h3>✅ 正确的宠物管理API测试</h3>
            <button class="test-button" onclick="testStorePet()">存放宠物 (store)</button>
            <button class="test-button" onclick="testCarryPet()">携带宠物 (carry)</button>
            <button class="test-button" onclick="testGetPets()">获取宠物列表</button>
            <button class="test-button" onclick="testSetMainPet()">设置主宠</button>
            <div id="correctApiResults"></div>
        </div>

        <!-- 路由对比测试 -->
        <div class="test-section">
            <h3>🔄 路由对比测试</h3>
            <button class="test-button warning" onclick="testWrongRoute()">测试错误路由 (/UserPet/Create)</button>
            <button class="test-button" onclick="testCorrectRoutes()">测试正确路由</button>
            <div id="routeResults"></div>
        </div>

        <!-- 修复建议 -->
        <div class="test-section">
            <h3>💡 修复建议</h3>
            <div id="fixSuggestions">
                <div class="endpoint-info">
                    <h4>✅ 推荐的替代方案</h4>
                    <ul>
                        <li><strong>存放宠物</strong>: <code>POST /api/PetManagement/store</code></li>
                        <li><strong>携带宠物</strong>: <code>POST /api/PetManagement/carry</code></li>
                        <li><strong>获取宠物</strong>: <code>GET /api/Player/GetCarryPets?userId=1</code></li>
                        <li><strong>设置主宠</strong>: <code>POST /api/PetManagement/setMain</code></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        const CORRECT_BASE_URL = 'http://localhost:5000/api';
        const WRONG_BASE_URL = 'http://localhost:5078/api';
        const TEST_USER_ID = 1;

        function addResult(sectionId, message, type = 'info', data = null) {
            const section = document.getElementById(sectionId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            
            const time = new Date().toLocaleTimeString();
            let content = `[${time}] ${message}`;
            
            if (data) {
                content += '\n' + JSON.stringify(data, null, 2);
            }
            
            div.textContent = content;
            section.appendChild(div);
            section.scrollTop = section.scrollHeight;
        }

        // 测试服务器连接
        async function testServerConnection() {
            try {
                addResult('connectionResults', '🔄 测试正确端口连接 (localhost:5000)...', 'info');
                
                const response = await fetch(`${CORRECT_BASE_URL.replace('/api', '')}/health`);
                
                if (response.ok) {
                    const result = await response.text();
                    addResult('connectionResults', '✅ 正确端口连接成功', 'success', { port: 5000, status: result });
                } else {
                    addResult('connectionResults', '❌ 正确端口连接失败', 'error', { status: response.status });
                }
            } catch (error) {
                addResult('connectionResults', `💥 正确端口连接异常: ${error.message}`, 'error');
            }
        }

        // 测试错误端口
        async function testWrongPort() {
            try {
                addResult('connectionResults', '🔄 测试错误端口连接 (localhost:5078)...', 'info');
                
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒超时
                
                const response = await fetch(`${WRONG_BASE_URL.replace('/api', '')}/health`, {
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                addResult('connectionResults', '⚠️ 错误端口意外连接成功', 'warning');
            } catch (error) {
                if (error.name === 'AbortError') {
                    addResult('connectionResults', '❌ 错误端口连接超时 (预期结果)', 'error', { port: 5078, error: '连接超时' });
                } else {
                    addResult('connectionResults', `❌ 错误端口连接失败 (预期结果): ${error.message}`, 'error', { port: 5078 });
                }
            }
        }

        // 测试存放宠物API
        async function testStorePet() {
            try {
                addResult('correctApiResults', '🔄 测试存放宠物API...', 'info');
                
                const response = await fetch(`${CORRECT_BASE_URL}/PetManagement/store`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        userId: TEST_USER_ID,
                        petId: 999 // 使用不存在的宠物ID进行测试
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    addResult('correctApiResults', '✅ 存放宠物API可达', 'success', result);
                } else {
                    addResult('correctApiResults', '✅ 存放宠物API可达 (业务逻辑错误是正常的)', 'success', result);
                }
            } catch (error) {
                addResult('correctApiResults', `💥 存放宠物API异常: ${error.message}`, 'error');
            }
        }

        // 测试携带宠物API
        async function testCarryPet() {
            try {
                addResult('correctApiResults', '🔄 测试携带宠物API...', 'info');
                
                const response = await fetch(`${CORRECT_BASE_URL}/PetManagement/carry`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        userId: TEST_USER_ID,
                        petId: 999
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    addResult('correctApiResults', '✅ 携带宠物API可达', 'success', result);
                } else {
                    addResult('correctApiResults', '✅ 携带宠物API可达 (业务逻辑错误是正常的)', 'success', result);
                }
            } catch (error) {
                addResult('correctApiResults', `💥 携带宠物API异常: ${error.message}`, 'error');
            }
        }

        // 测试获取宠物列表API
        async function testGetPets() {
            try {
                addResult('correctApiResults', '🔄 测试获取宠物列表API...', 'info');
                
                const response = await fetch(`${CORRECT_BASE_URL}/Player/GetCarryPets?userId=${TEST_USER_ID}`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('correctApiResults', `✅ 获取宠物列表成功，携带宠物数量: ${result.pets.length}`, 'success', {
                        petCount: result.pets.length,
                        pets: result.pets.slice(0, 2) // 只显示前2个
                    });
                } else {
                    addResult('correctApiResults', '✅ 获取宠物列表API可达', 'success', result);
                }
            } catch (error) {
                addResult('correctApiResults', `💥 获取宠物列表API异常: ${error.message}`, 'error');
            }
        }

        // 测试设置主宠API
        async function testSetMainPet() {
            try {
                addResult('correctApiResults', '🔄 测试设置主宠API...', 'info');
                
                const response = await fetch(`${CORRECT_BASE_URL}/PetManagement/setMain`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        userId: TEST_USER_ID,
                        petId: 999
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    addResult('correctApiResults', '✅ 设置主宠API可达', 'success', result);
                } else {
                    addResult('correctApiResults', '✅ 设置主宠API可达 (业务逻辑错误是正常的)', 'success', result);
                }
            } catch (error) {
                addResult('correctApiResults', `💥 设置主宠API异常: ${error.message}`, 'error');
            }
        }

        // 测试错误路由
        async function testWrongRoute() {
            try {
                addResult('routeResults', '🔄 测试错误路由 (/UserPet/Create)...', 'info');
                
                const response = await fetch(`${CORRECT_BASE_URL}/UserPet/Create`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ userId: TEST_USER_ID })
                });
                
                addResult('routeResults', `❌ 错误路由返回状态: ${response.status} ${response.statusText}`, 'error', {
                    status: response.status,
                    statusText: response.statusText,
                    url: response.url
                });
            } catch (error) {
                addResult('routeResults', `❌ 错误路由请求失败: ${error.message}`, 'error');
            }
        }

        // 测试正确路由
        async function testCorrectRoutes() {
            try {
                addResult('routeResults', '🔄 测试正确路由...', 'info');
                
                const routes = [
                    { name: 'PetManagement/store', url: `${CORRECT_BASE_URL}/PetManagement/store`, method: 'POST' },
                    { name: 'PetManagement/carry', url: `${CORRECT_BASE_URL}/PetManagement/carry`, method: 'POST' },
                    { name: 'Player/GetCarryPets', url: `${CORRECT_BASE_URL}/Player/GetCarryPets?userId=${TEST_USER_ID}`, method: 'GET' },
                    { name: 'PetManagement/setMain', url: `${CORRECT_BASE_URL}/PetManagement/setMain`, method: 'POST' }
                ];
                
                let successCount = 0;
                
                for (const route of routes) {
                    try {
                        const options = {
                            method: route.method,
                            headers: { 'Content-Type': 'application/json' }
                        };
                        
                        if (route.method === 'POST') {
                            options.body = JSON.stringify({ userId: TEST_USER_ID, petId: 999 });
                        }
                        
                        const response = await fetch(route.url, options);
                        
                        if (response.status !== 404) {
                            successCount++;
                            addResult('routeResults', `✅ ${route.name} 路由可达 (${response.status})`, 'success');
                        } else {
                            addResult('routeResults', `❌ ${route.name} 路由不存在 (404)`, 'error');
                        }
                    } catch (error) {
                        addResult('routeResults', `💥 ${route.name} 路由测试异常: ${error.message}`, 'error');
                    }
                }
                
                addResult('routeResults', `📊 路由测试完成: ${successCount}/${routes.length} 个路由可达`, 'info');
            } catch (error) {
                addResult('routeResults', `💥 正确路由测试异常: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                testServerConnection();
            }, 1000);
        });
    </script>
</body>
</html>
