namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 地图列表结果DTO
    /// </summary>
    public class MapListResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 地图列表
        /// </summary>
        public List<MapInfoDTO> Maps { get; set; } = new List<MapInfoDTO>();
    }

    /// <summary>
    /// 地图信息DTO
    /// </summary>
    public class MapInfoDTO
    {
        /// <summary>
        /// 地图ID
        /// </summary>
        public int MapId { get; set; }

        /// <summary>
        /// 地图名称
        /// </summary>
        public string MapName { get; set; } = string.Empty;

        /// <summary>
        /// 地图描述
        /// </summary>
        public string MapDesc { get; set; } = string.Empty;

        /// <summary>
        /// 地图类型
        /// </summary>
        public int MapType { get; set; }

        /// <summary>
        /// 图集名称
        /// </summary>
        public string AtlastName { get; set; } = string.Empty;

        /// <summary>
        /// 地图背景图片
        /// </summary>
        public string Background { get; set; } = string.Empty;

        /// <summary>
        /// 地图图标
        /// </summary>
        public string Icon { get; set; } = string.Empty;

        /// <summary>
        /// 是否解锁（根据用户等级等条件判断）
        /// </summary>
        public bool IsUnlocked { get; set; }

        /// <summary>
        /// 推荐等级
        /// </summary>
        public int RecommendLevel { get; set; }

        /// <summary>
        /// 解锁条件描述
        /// </summary>
        public string UnlockCondition { get; set; } = string.Empty;
    }
}