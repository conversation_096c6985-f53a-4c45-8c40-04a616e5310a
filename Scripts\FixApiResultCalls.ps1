# PowerShell脚本：批量修复ApiResult调用
# 将ApiResult.Success和ApiResult.Error替换为ApiResult.CreateSuccess和ApiResult.CreateError

$files = @(
    "Services/EquipmentService.cs",
    "Controllers/EquipmentController.cs", 
    "Controllers/EquipmentTestController.cs",
    "Controllers/EquipmentAttributeController.cs"
)

foreach ($file in $files) {
    $filePath = Join-Path $PSScriptRoot "..\$file"
    if (Test-Path $filePath) {
        Write-Host "Processing $file..."
        
        $content = Get-Content $filePath -Raw
        
        # 替换ApiResult.Success为ApiResult.CreateSuccess
        $content = $content -replace 'ApiResult\.Success\(', 'ApiResult.CreateSuccess('
        
        # 替换ApiResult.Error为ApiResult.CreateError
        $content = $content -replace 'ApiResult\.Error\(', 'ApiResult.CreateError('
        
        # 替换ApiResult<T>.Success为ApiResult<T>.CreateSuccess
        $content = $content -replace 'ApiResult<([^>]+)>\.Success\(', 'ApiResult<$1>.CreateSuccess('
        
        # 替换ApiResult<T>.Error为ApiResult<T>.CreateError
        $content = $content -replace 'ApiResult<([^>]+)>\.Error\(', 'ApiResult<$1>.CreateError('
        
        Set-Content $filePath $content -NoNewline
        Write-Host "Completed $file"
    } else {
        Write-Host "File not found: $file"
    }
}

Write-Host "All files processed!"
