using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WebApplication_HM.Models;
using WebApplication_HM.Sugar;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 合成有效宠物验证服务实现
    /// </summary>
    public class SynthesisValidPetsService : ISynthesisValidPetsService
    {
        private readonly DbContext _dbContext;
        private readonly IMemoryCache _cache;
        private readonly ILogger<SynthesisValidPetsService> _logger;
        private static readonly string CACHE_KEY = "valid_synthesis_pets";
        private static readonly TimeSpan CACHE_DURATION = TimeSpan.FromHours(1);

        public SynthesisValidPetsService(
            DbContext dbContext,
            IMemoryCache cache,
            ILogger<SynthesisValidPetsService> logger)
        {
            _dbContext = dbContext;
            _cache = cache;
            _logger = logger;
        }

        /// <summary>
        /// 验证宠物是否可以参与合成
        /// </summary>
        public async Task<bool> IsValidSynthesisPetAsync(int petNo)
        {
            try
            {
                var validPets = await GetAllValidPetNosAsync();
                return validPets.Contains(petNo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"验证宠物{petNo}是否可合成失败");
                return false;
            }
        }

        /// <summary>
        /// 获取所有可合成宠物编号
        /// </summary>
        public async Task<List<int>> GetAllValidPetNosAsync()
        {
            try
            {
                // 尝试从缓存获取
                if (_cache.TryGetValue(CACHE_KEY, out List<int> cachedPets))
                {
                    return cachedPets;
                }

                // 从数据库查询
                var validPets = await _dbContext.Db.Queryable<synthesis_valid_pets>()
                    .Where(v => v.is_active == true)
                    .Select(v => v.pet_no)
                    .ToListAsync();

                // 缓存结果
                _cache.Set(CACHE_KEY, validPets, CACHE_DURATION);
                
                _logger.LogInformation($"从数据库加载了{validPets.Count}个可合成宠物ID");
                return validPets;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可合成宠物列表失败");
                return new List<int>();
            }
        }

        /// <summary>
        /// 刷新缓存
        /// </summary>
        public async Task RefreshValidPetsAsync()
        {
            try
            {
                _cache.Remove(CACHE_KEY);
                await GetAllValidPetNosAsync();
                _logger.LogInformation("可合成宠物缓存已刷新");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新可合成宠物缓存失败");
            }
        }

        /// <summary>
        /// 验证宠物是否可以参与合成（直接查询数据库）
        /// </summary>
        public async Task<bool> ValidatePetForSynthesisAsync(int petNo)
        {
            try
            {
                return await _dbContext.Db.Queryable<synthesis_valid_pets>()
                    .Where(v => v.pet_no == petNo && v.is_active == true)
                    .AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"直接验证宠物{petNo}是否可合成失败");
                return false;
            }
        }

        /// <summary>
        /// 批量验证宠物是否可以参与合成
        /// </summary>
        public async Task<Dictionary<int, bool>> ValidateMultiplePetsAsync(List<int> petNos)
        {
            var result = new Dictionary<int, bool>();
            
            try
            {
                if (petNos == null || !petNos.Any())
                {
                    return result;
                }

                var validPets = await GetAllValidPetNosAsync();
                
                foreach (var petNo in petNos)
                {
                    result[petNo] = validPets.Contains(petNo);
                }

                _logger.LogDebug($"批量验证{petNos.Count}个宠物，有效数量：{result.Values.Count(v => v)}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量验证宠物失败");
                
                // 发生异常时，将所有宠物标记为无效
                foreach (var petNo in petNos)
                {
                    result[petNo] = false;
                }
            }

            return result;
        }

        /// <summary>
        /// 获取可合成宠物数量
        /// </summary>
        public async Task<int> GetValidPetsCountAsync()
        {
            try
            {
                var validPets = await GetAllValidPetNosAsync();
                return validPets.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可合成宠物数量失败");
                return 0;
            }
        }

        /// <summary>
        /// 确保synthesis_valid_pets表存在
        /// </summary>
        private async Task EnsureTableExistsAsync()
        {
            try
            {
                var tableExists = _dbContext.Db.DbMaintenance.IsAnyTable("synthesis_valid_pets", false);
                
                if (!tableExists)
                {
                    _logger.LogWarning("synthesis_valid_pets表不存在，正在创建...");
                    
                    // 创建表
                    _dbContext.Db.CodeFirst.InitTables<synthesis_valid_pets>();
                    
                    _logger.LogInformation("synthesis_valid_pets表创建成功");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查/创建synthesis_valid_pets表失败");
                throw;
            }
        }

        /// <summary>
        /// 初始化默认数据（如果表为空）
        /// </summary>
        public async Task InitializeDefaultDataAsync()
        {
            try
            {
                await EnsureTableExistsAsync();
                
                var count = await _dbContext.Db.Queryable<synthesis_valid_pets>().CountAsync();
                if (count == 0)
                {
                    _logger.LogInformation("synthesis_valid_pets表为空，正在初始化默认数据...");
                    
                    // 这里可以插入从synthetsis.md解密得到的150个宠物ID
                    var defaultPetIds = GetDefaultValidPetIds();
                    var validPets = defaultPetIds.Select(id => new synthesis_valid_pets
                    {
                        pet_no = id,
                        is_active = true,
                        create_time = DateTime.Now
                    }).ToList();

                    await _dbContext.Db.Insertable(validPets).ExecuteCommandAsync();
                    
                    _logger.LogInformation($"初始化了{validPets.Count}个默认可合成宠物ID");
                    
                    // 刷新缓存
                    await RefreshValidPetsAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化默认数据失败");
            }
        }

        /// <summary>
        /// 获取默认的可合成宠物ID列表（基于synthetsis.md解密结果）
        /// </summary>
        private List<int> GetDefaultValidPetIds()
        {
            return new List<int>
            {
                79, 83, 84, 98, 99, 100, 103, 104, 109, 110, 117, 119, 120, 128, 130, 131,
                136, 140, 143, 144, 145, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175,
                176, 177, 178, 179, 180, 184, 185, 193, 194, 195, 196, 197, 204, 205, 206,
                207, 208, 209, 210, 211, 212, 216, 217, 218, 219, 220, 221, 222, 223, 224,
                225, 226, 227, 228, 229, 250, 251, 252, 255, 257, 261, 262, 269, 271, 272,
                273, 276, 277, 278, 280, 283, 287, 288, 289, 290, 291, 294, 295, 296, 297,
                298, 299, 300, 301, 302, 306, 308, 309, 311, 312, 313, 314, 322, 354, 355,
                356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 368, 369, 370, 383,
                384, 385, 386, 388, 389, 390, 392, 393, 394, 396, 397, 399, 400, 401, 402,
                403, 404, 405, 406, 407, 408, 409, 410, 411, 412
            };
        }
    }
}
