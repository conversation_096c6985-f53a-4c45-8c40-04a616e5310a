using WebApplication_HM.DTOs.ResultDTO;
using WebApplication_HM.DTOs;
using WebApplication_HM.Models;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 原项目战斗算法适配器
    /// 完整迁移WindowsFormsApplication7的战斗计算逻辑
    /// 增强版：考虑成长值、等级等因素
    /// </summary>
    public static class OriginalBattleAdapter
    {
        private static readonly Random _random = new Random();

        /// <summary>
        /// 计算玩家伤害 (基于原项目Fight.cs第617-653行)
        /// 增强版：考虑成长值差异和随机波动
        /// </summary>
        /// <param name="playerAttr">玩家属性</param>
        /// <param name="monsterAttr">怪物属性</param>
        /// <param name="skillMultiplier">技能倍数</param>
        /// <param name="talismanState">护符状态</param>
        /// <returns>伤害值</returns>
        public static long CalculatePlayerDamage(
            AttributeResultDTO playerAttr,
            AttributeResultDTO monsterAttr,
            double skillMultiplier = 1.0,
            string talismanState = "")
        {
            long damage = 0;
            double outputBonus = Convert.ToDouble(playerAttr.Hit) - Convert.ToDouble(monsterAttr.Dodge);
            
            // 处理护符状态 - 会心一击
            string tempAttack = playerAttr.Atk.ToString();
            if (talismanState.Contains("会心一击"))
            {
                string[] cfg = talismanState.Split('|');
                if (_random.Next(0, 100) < Convert.ToInt16(cfg[1]))
                {
                    tempAttack = Convert.ToInt64(Convert.ToInt64(tempAttack) * (1 + Convert.ToDouble(cfg[2]))).ToString();
                }
            }
            
            if (outputBonus > 0)
            {
                // 正输出加成计算
                outputBonus = outputBonus / Convert.ToDouble(playerAttr.Hit);
                outputBonus = outputBonus / 2;
                outputBonus = Math.Pow(outputBonus, 2);
                damage = (long)(Convert.ToInt64(tempAttack) * skillMultiplier * (1 + outputBonus));
                
                // 反作弊验证
                long verification = damage;
                long restored = (long)(verification / skillMultiplier / (1 + outputBonus));
                if (Math.Abs(Convert.ToDouble(tempAttack) - restored) >= 10)
                {
                    damage = long.MaxValue; // 标记为异常
                }
                else
                {
                    // 随机伤害波动 (-5% 到 +10%) - 基于原项目Fight.cs第631行
                    damage = (long)(damage * (1.0 + _random.Next(-5000, 10001) / 100000.0));
                }
            }
            else
            {
                // 负输出加成计算
                damage = (long)((Convert.ToInt64(tempAttack) - Convert.ToInt64(monsterAttr.Def)) *
                         (1 - (Convert.ToInt64(monsterAttr.Dodge) - Convert.ToInt64(playerAttr.Hit)) / Convert.ToDouble(monsterAttr.Dodge)));
                
                // 反作弊验证
                long verification = damage;
                long restored = (long)(verification / (1 - (Convert.ToInt64(monsterAttr.Dodge) - Convert.ToInt64(playerAttr.Hit)) / Convert.ToDouble(monsterAttr.Dodge)) +
                              Convert.ToInt64(monsterAttr.Def));
                if (Math.Abs(Convert.ToDouble(tempAttack) - restored) >= 10)
                {
                    // 检查成长差距
                    if (Convert.ToInt64(monsterAttr.Growth ?? "0") - Convert.ToInt64(playerAttr.Growth ?? "0") >= 5000)
                    {
                        damage = 1;
                    }
                    else
                    {
                        damage = long.MaxValue;
                    }
                }
            }
            
            return damage;
        }
        
        /// <summary>
        /// 计算怪物伤害 (基于原项目Fight.cs第785-811行)
        /// 增强版：考虑护符状态和反作弊验证
        /// </summary>
        /// <param name="monsterAttr">怪物属性</param>
        /// <param name="playerAttr">玩家属性</param>
        /// <param name="talismanState">护符状态</param>
        /// <returns>伤害值</returns>
        public static long CalculateMonsterDamage(
            AttributeResultDTO monsterAttr,
            AttributeResultDTO playerAttr,
            string talismanState = "")
        {
            long damage = 0;
            double outputBonus = Convert.ToDouble(monsterAttr.Hit) - Convert.ToDouble(playerAttr.Dodge);
            
            // 处理护符状态 - 伤害抵御
            string tempAttack = monsterAttr.Atk.ToString();
            if (talismanState.Contains("伤害抵御"))
            {
                string[] cfg = talismanState.Split('|');
                if (_random.Next(0, 100) < Convert.ToInt16(cfg[1]))
                {
                    tempAttack = Convert.ToInt64(Convert.ToInt64(tempAttack) * (1 - Convert.ToDouble(cfg[2]))).ToString();
                }
            }
            
            if (outputBonus > 0)
            {
                // 正输出加成计算 (怪物使用1.5次方)
                outputBonus = outputBonus / Convert.ToDouble(monsterAttr.Hit);
                outputBonus = outputBonus / 2;
                outputBonus = Math.Pow(outputBonus, 1.5);
                damage = (long)(Convert.ToInt64(tempAttack) * (1 + outputBonus));
                
                // 反作弊验证
                long verification = damage;
                long restored = (long)(verification / (1 + outputBonus));
                if (Math.Abs(Convert.ToDouble(tempAttack) - restored) >= 10)
                {
                    damage = long.MaxValue;
                }
            }
            else
            {
                // 负输出加成计算
                damage = (long)((Convert.ToInt64(tempAttack) - Convert.ToInt64(playerAttr.Def)) *
                         (1 - (Convert.ToInt64(playerAttr.Dodge) - Convert.ToInt64(monsterAttr.Hit)) / Convert.ToDouble(playerAttr.Dodge)));
                
                // 反作弊验证
                long verification = damage;
                long restored = (long)(verification / (1 - (Convert.ToInt64(playerAttr.Dodge) - Convert.ToInt64(monsterAttr.Hit)) / Convert.ToDouble(playerAttr.Dodge)) +
                              Convert.ToInt64(playerAttr.Def));
                if (Math.Abs(Convert.ToDouble(tempAttack) - restored) >= 10)
                {
                    damage = long.MaxValue;
                }
            }
            
            return damage;
        }
        
        /// <summary>
        /// 计算加深伤害 (基于原项目Fight.cs第658-673行)
        /// </summary>
        /// <param name="baseDamage">基础伤害</param>
        /// <param name="amplifyRate">加深率</param>
        /// <returns>加深伤害值</returns>
        public static long CalculateAmplifiedDamage(long baseDamage, double amplifyRate)
        {
            if (amplifyRate <= 0) return -1;
            
            if (baseDamage != long.MaxValue)
            {
                long amplified = (long)(baseDamage * amplifyRate);
                return amplified < 0 && amplified != -2 ? 1 : amplified;
            }
            else if (amplifyRate > 0)
            {
                return -2; // 异常标记
            }
            
            return -1;
        }
        
        /// <summary>
        /// 计算抵消伤害 (基于原项目Fight.cs第820-842行)
        /// </summary>
        /// <param name="incomingDamage">受到的伤害</param>
        /// <param name="reductionRate">抵消率</param>
        /// <param name="isTT">是否通天塔</param>
        /// <param name="mapId">地图ID</param>
        /// <returns>抵消的伤害值</returns>
        public static long CalculateReducedDamage(long incomingDamage, double reductionRate, bool isTT = false, string mapId = "")
        {
            if (reductionRate <= 0) return -1;
            
            double finalReduction = reductionRate;
            
            // 分段计算公式
            if (reductionRate > 0 && reductionRate <= 0.3)
            {
                finalReduction = reductionRate;
            }
            else if (reductionRate > 0.3 && reductionRate <= 1)
            {
                finalReduction = 2.0 * reductionRate / 7.0 + 0.21428571;
                if (isTT) finalReduction = 2.0 * reductionRate / 7.0 + 0.16428571;
            }
            else
            {
                finalReduction = 0.5;
                if (isTT) finalReduction = 0.5 * 0.9;
            }
            
            // 地图特殊限制
            if (mapId == "23") // 冰岛
            {
                finalReduction *= 0.3;
            }
            
            long reduced = (long)(incomingDamage * finalReduction);
            return Math.Max(0, reduced);
        }
        
        /// <summary>
        /// 计算吸血回复 (基于原项目Fight.cs第714-762行)
        /// </summary>
        /// <param name="damage">造成的伤害</param>
        /// <param name="lifeStealRate">吸血率</param>
        /// <param name="isTT">是否通天塔</param>
        /// <param name="mapId">地图ID</param>
        /// <returns>吸血回复值</returns>
        public static long CalculateLifeSteal(long damage, double lifeStealRate, bool isTT = false, string mapId = "")
        {
            if (lifeStealRate <= 0) return 0;
            
            double finalLifeSteal = lifeStealRate;
            
            // 分段计算公式
            if (lifeStealRate > 0 && lifeStealRate <= 0.4)
            {
                finalLifeSteal = lifeStealRate;
                if (isTT) finalLifeSteal *= 0.50;
            }
            else if (lifeStealRate > 0.4 && lifeStealRate <= 1.6)
            {
                if (isTT)
                {
                    finalLifeSteal *= 0.50;
                }
                else
                {
                    finalLifeSteal = 0.5 * lifeStealRate + 0.2;
                }
            }
            else
            {
                finalLifeSteal = 1.0;
                if (isTT) finalLifeSteal *= 0.50;
            }
            
            // 地图特殊限制
            if (mapId == "23") // 冰岛
            {
                finalLifeSteal *= 0.2;
            }
            else if (mapId == "201901") // 赫拉神殿
            {
                return 0; // 完全禁止吸血
            }
            
            long lifeSteal = (long)(damage * finalLifeSteal);
            
            // 异常检测
            if (lifeSteal <= -10000)
            {
                return -2;
            }
            
            return lifeSteal;
        }
        
        /// <summary>
        /// 计算吸魔回复 (基于原项目Fight.cs第764-772行)
        /// </summary>
        /// <param name="damage">造成的伤害</param>
        /// <param name="manaStealRate">吸魔率</param>
        /// <returns>吸魔回复值</returns>
        public static long CalculateManaSteal(long damage, double manaStealRate)
        {
            if (manaStealRate <= 0) return 0;

            return (long)(damage * manaStealRate);
        }

        /// <summary>
        /// 计算考虑等级和成长的玩家伤害 (增强版)
        /// 基于原项目Fight.cs完整逻辑，考虑等级、成长值、随机波动
        /// </summary>
        /// <param name="playerAttr">玩家属性</param>
        /// <param name="monsterAttr">怪物属性</param>
        /// <param name="playerLevel">玩家等级</param>
        /// <param name="monsterLevel">怪物等级</param>
        /// <param name="skillMultiplier">技能倍数</param>
        /// <param name="talismanState">护符状态</param>
        /// <returns>伤害值</returns>
        public static long CalculateEnhancedPlayerDamage(
            AttributeResultDTO playerAttr,
            AttributeResultDTO monsterAttr,
            int playerLevel,
            int monsterLevel,
            double skillMultiplier = 1.0,
            string talismanState = "")
        {
            // 基础伤害计算
            long baseDamage = CalculatePlayerDamage(playerAttr, monsterAttr, skillMultiplier, talismanState);

            // 等级差异影响 (等级差距过大时的特殊处理)
            int levelDiff = Math.Abs(playerLevel - monsterLevel);
            if (levelDiff > 50) // 等级差距超过50级时
            {
                if (playerLevel < monsterLevel)
                {
                    // 玩家等级过低，伤害减少
                    double levelPenalty = Math.Min(0.8, levelDiff * 0.01); // 最多减少80%
                    baseDamage = (long)(baseDamage * (1 - levelPenalty));
                }
                else
                {
                    // 玩家等级过高，伤害增加
                    double levelBonus = Math.Min(0.5, levelDiff * 0.005); // 最多增加50%
                    baseDamage = (long)(baseDamage * (1 + levelBonus));
                }
            }

            return Math.Max(1, baseDamage); // 确保最小伤害为1
        }

        /// <summary>
        /// 计算考虑等级和成长的怪物伤害 (增强版)
        /// 基于原项目Fight.cs完整逻辑
        /// </summary>
        /// <param name="monsterAttr">怪物属性</param>
        /// <param name="playerAttr">玩家属性</param>
        /// <param name="monsterLevel">怪物等级</param>
        /// <param name="playerLevel">玩家等级</param>
        /// <param name="talismanState">护符状态</param>
        /// <returns>伤害值</returns>
        public static long CalculateEnhancedMonsterDamage(
            AttributeResultDTO monsterAttr,
            AttributeResultDTO playerAttr,
            int monsterLevel,
            int playerLevel,
            string talismanState = "")
        {
            // 基础伤害计算
            long baseDamage = CalculateMonsterDamage(monsterAttr, playerAttr, talismanState);

            // 等级差异影响
            int levelDiff = Math.Abs(monsterLevel - playerLevel);
            if (levelDiff > 50)
            {
                if (monsterLevel > playerLevel)
                {
                    // 怪物等级过高，伤害增加
                    double levelBonus = Math.Min(0.6, levelDiff * 0.008); // 最多增加60%
                    baseDamage = (long)(baseDamage * (1 + levelBonus));
                }
                else
                {
                    // 怪物等级过低，伤害减少
                    double levelPenalty = Math.Min(0.7, levelDiff * 0.01); // 最多减少70%
                    baseDamage = (long)(baseDamage * (1 - levelPenalty));
                }
            }

            return Math.Max(1, baseDamage);
        }

        /// <summary>
        /// 检查成长值差异的反作弊机制 (基于原项目Fight.cs第644行)
        /// </summary>
        /// <param name="playerGrowth">玩家成长值</param>
        /// <param name="monsterGrowth">怪物成长值</param>
        /// <param name="calculatedDamage">计算出的伤害</param>
        /// <returns>调整后的伤害值</returns>
        public static long ApplyGrowthAntiCheat(double playerGrowth, double monsterGrowth, long calculatedDamage)
        {
            // 基于原项目Fight.cs第644行的成长差异检查
            if (monsterGrowth - playerGrowth >= 5000)
            {
                return 1; // 成长差距过大，伤害降为1
            }

            return calculatedDamage;
        }

        #region 技能系统集成 (基于原项目Fight.cs技能逻辑)

        /// <summary>
        /// 计算技能伤害倍数 (基于原项目Fight.cs第577行)
        /// </summary>
        /// <param name="petSkills">宠物技能列表</param>
        /// <param name="activeSkillId">使用的主动技能ID</param>
        /// <returns>技能伤害倍数</returns>
        public static double CalculateSkillMultiplier(List<PetSkillDetailDTO> petSkills, string? activeSkillId)
        {
            if (string.IsNullOrEmpty(activeSkillId) || petSkills == null || !petSkills.Any())
            {
                return 1.0; // 默认倍数
            }

            var activeSkill = petSkills.FirstOrDefault(ps => ps.SkillId == activeSkillId && ps.SkillConfig?.SkillType == "ACTIVE");
            if (activeSkill?.SkillConfig == null)
            {
                return 1.0;
            }

            // 基于原项目Fight.cs第577行的计算逻辑
            // 技能加成 = 1 + Convert.ToDouble(技能配置.技能百分比) + Convert.ToInt32(技能.技能等级) * NumEncrypt.零点零二()
            return 1.0 + (double)activeSkill.SkillConfig.SkillPercent + (activeSkill.SkillLevel * 0.02);
        }

        /// <summary>
        /// 计算被动技能效果 (基于原项目PetInfo.cs第186行)
        /// </summary>
        /// <param name="petSkills">宠物技能列表</param>
        /// <returns>被动效果字典</returns>
        public static Dictionary<string, double> CalculatePassiveEffects(List<PetSkillDetailDTO> petSkills)
        {
            var effects = new Dictionary<string, double>();

            if (petSkills == null || !petSkills.Any())
            {
                return effects;
            }

            foreach (var skill in petSkills.Where(ps => ps.SkillConfig?.SkillType == "PASSIVE" && ps.SkillConfig.IsBuff))
            {
                if (skill.SkillConfig == null || string.IsNullOrEmpty(skill.SkillConfig.EffectType) || skill.SkillConfig.EffectType == "null")
                    continue;

                var effectType = skill.SkillConfig.EffectType;

                // 基于原项目PetInfo.cs第186行的计算逻辑
                // skval = skval + (Convert.ToInt32(技.技能等级) * 0.005);
                var effectValue = skill.CalculateEffectValue();

                if (effects.ContainsKey(effectType))
                {
                    effects[effectType] += effectValue;
                }
                else
                {
                    effects[effectType] = effectValue;
                }
            }

            return effects;
        }

        /// <summary>
        /// 计算技能魔法消耗 (基于原项目Fight.cs第569行)
        /// </summary>
        /// <param name="petSkills">宠物技能列表</param>
        /// <param name="activeSkillId">使用的主动技能ID</param>
        /// <returns>魔法消耗</returns>
        public static int CalculateSkillManaCost(List<PetSkillDetailDTO> petSkills, string? activeSkillId)
        {
            if (string.IsNullOrEmpty(activeSkillId) || petSkills == null || !petSkills.Any())
            {
                return 0;
            }

            var activeSkill = petSkills.FirstOrDefault(ps => ps.SkillId == activeSkillId && ps.SkillConfig?.SkillType == "ACTIVE");
            if (activeSkill?.SkillConfig == null)
            {
                return 0;
            }

            // 基于原项目Fight.cs第569行的计算逻辑
            // long 耗蓝量 = (Convert.ToInt32(技能.技能等级) + 1) * Convert.ToInt64(技能.信息.耗蓝量);
            return activeSkill.CalculateManaCost();
        }

        /// <summary>
        /// 检查是否有足够魔法使用技能 (基于原项目Fight.cs第570行)
        /// </summary>
        /// <param name="currentMana">当前魔法值</param>
        /// <param name="manaCost">技能魔法消耗</param>
        /// <returns>是否有足够魔法</returns>
        public static bool HasSufficientMana(int currentMana, int manaCost)
        {
            return currentMana >= manaCost;
        }

        /// <summary>
        /// 应用被动技能效果到属性 (基于原项目逻辑)
        /// </summary>
        /// <param name="baseAttributes">基础属性</param>
        /// <param name="passiveEffects">被动技能效果</param>
        /// <returns>增强后的属性</returns>
        public static AttributeResultDTO ApplyPassiveEffectsToAttributes(AttributeResultDTO baseAttributes, Dictionary<string, double> passiveEffects)
        {
            var enhancedAttributes = new AttributeResultDTO
            {
                Atk = baseAttributes.Atk,
                Def = baseAttributes.Def,
                Hit = baseAttributes.Hit,
                Dodge = baseAttributes.Dodge,
                Spd = baseAttributes.Spd,
                Hp = baseAttributes.Hp,
                Mp = baseAttributes.Mp,
                Growth = baseAttributes.Growth
            };

            // 应用被动技能效果
            foreach (var effect in passiveEffects)
            {
                var effectType = effect.Key;
                var effectValue = effect.Value;

                switch (effectType)
                {
                    case "攻击":
                        enhancedAttributes.Atk = (int)(enhancedAttributes.Atk * (1 + effectValue));
                        break;
                    case "防御":
                        enhancedAttributes.Def = (int)(enhancedAttributes.Def * (1 + effectValue));
                        break;
                    case "命中":
                        enhancedAttributes.Hit = (int)(enhancedAttributes.Hit * (1 + effectValue));
                        break;
                    case "闪避":
                        enhancedAttributes.Dodge = (int)(enhancedAttributes.Dodge * (1 + effectValue));
                        break;
                    case "速度":
                        enhancedAttributes.Spd = (int)(enhancedAttributes.Spd * (1 + effectValue));
                        break;
                    case "生命":
                        enhancedAttributes.Hp = (int)(enhancedAttributes.Hp * (1 + effectValue));
                        break;
                    case "魔法":
                        enhancedAttributes.Mp = (int)(enhancedAttributes.Mp * (1 + effectValue));
                        break;
                }
            }

            return enhancedAttributes;
        }

        /// <summary>
        /// 计算完整的技能增强伤害 (集成版本)
        /// </summary>
        /// <param name="playerAttr">玩家基础属性</param>
        /// <param name="monsterAttr">怪物属性</param>
        /// <param name="playerLevel">玩家等级</param>
        /// <param name="monsterLevel">怪物等级</param>
        /// <param name="petSkills">宠物技能列表</param>
        /// <param name="activeSkillId">使用的主动技能ID</param>
        /// <param name="currentMana">当前魔法值</param>
        /// <param name="talismanState">护符状态</param>
        /// <returns>技能增强后的伤害值和魔法消耗</returns>
        public static (long damage, int manaCost, bool hasEnoughMana) CalculateSkillEnhancedDamage(
            AttributeResultDTO playerAttr,
            AttributeResultDTO monsterAttr,
            int playerLevel,
            int monsterLevel,
            List<PetSkillDetailDTO> petSkills,
            string? activeSkillId = null,
            int currentMana = 0,
            string talismanState = "")
        {
            // 1. 计算被动技能效果
            var passiveEffects = CalculatePassiveEffects(petSkills);

            // 2. 应用被动技能效果到属性
            var enhancedPlayerAttr = ApplyPassiveEffectsToAttributes(playerAttr, passiveEffects);

            // 3. 计算主动技能倍数
            var skillMultiplier = CalculateSkillMultiplier(petSkills, activeSkillId);

            // 4. 计算魔法消耗
            var manaCost = CalculateSkillManaCost(petSkills, activeSkillId);

            // 5. 检查魔法是否足够
            var hasEnoughMana = HasSufficientMana(currentMana, manaCost);

            // 6. 如果魔法不足，使用普通攻击
            if (!string.IsNullOrEmpty(activeSkillId) && !hasEnoughMana)
            {
                skillMultiplier = 1.0;
                manaCost = 0;
            }

            // 7. 计算最终伤害
            var damage = CalculateEnhancedPlayerDamage(
                enhancedPlayerAttr, monsterAttr, playerLevel, monsterLevel, skillMultiplier, talismanState);

            return (damage, manaCost, hasEnoughMana);
        }

        #endregion
    }
}
