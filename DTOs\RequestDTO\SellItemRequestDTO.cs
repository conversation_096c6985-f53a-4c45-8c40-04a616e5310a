using System.ComponentModel.DataAnnotations;

namespace WebApplication_HM.DTOs.RequestDTO
{
    /// <summary>
    /// 出售道具请求DTO
    /// </summary>
    public class SellItemRequestDTO
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public int UserId { get; set; }

        /// <summary>
        /// 道具ID
        /// </summary>
        [Required(ErrorMessage = "道具ID不能为空")]
        public string ItemId { get; set; }

        /// <summary>
        /// 出售数量
        /// </summary>
        [Required(ErrorMessage = "出售数量不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "出售数量必须大于0")]
        public int SellCount { get; set; }
    }
}