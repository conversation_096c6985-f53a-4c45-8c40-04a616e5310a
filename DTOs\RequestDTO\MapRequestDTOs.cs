namespace WebApplication_HM.DTOs.RequestDTO
{
    /// <summary>
    /// 进入地图请求DTO
    /// </summary>
    public class EnterMapRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 宠物ID
        /// </summary>
        public int PetId { get; set; }
    }

    /// <summary>
    /// 解锁地图请求DTO
    /// </summary>
    public class UnlockMapRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 解锁方式 (level=等级解锁, key=钥匙解锁, quest=任务解锁)
        /// </summary>
        public string UnlockMethod { get; set; } = "level";
    }

    /// <summary>
    /// 更新地图进度请求DTO
    /// </summary>
    public class UpdateMapProgressRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 得分
        /// </summary>
        public int Score { get; set; }

        /// <summary>
        /// 完成时间（秒）
        /// </summary>
        public int CompletionTimeSeconds { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public TimeSpan CompletionTime => TimeSpan.FromSeconds(CompletionTimeSeconds);
    }
}
