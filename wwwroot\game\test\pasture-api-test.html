<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>牧场 API 测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-section h3 { margin-top: 0; color: #333; }
        .test-button { background: #4CAF50; color: white; border: none; padding: 8px 16px; margin: 5px; border-radius: 4px; cursor: pointer; }
        .test-button:hover { background: #45a049; }
        .test-button:disabled { background: #ccc; cursor: not-allowed; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .json-display { background: #f8f9fa; border: 1px solid #e9ecef; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏠 牧场 API 测试</h1>
        
        <!-- 基础API测试 -->
        <div class="test-section">
            <h3>🔗 基础API测试</h3>
            <button class="test-button" onclick="testGetPasturePets()">获取牧场宠物</button>
            <button class="test-button" onclick="testGetCapacity()">获取牧场容量</button>
            <button class="test-button" onclick="testCarryPets()">获取携带宠物</button>
            <div id="basicResults"></div>
        </div>

        <!-- 宠物操作测试 -->
        <div class="test-section">
            <h3>🐾 宠物操作测试</h3>
            <button class="test-button" onclick="testStorePet()">存放宠物</button>
            <button class="test-button" onclick="testCarryPet()">携带宠物</button>
            <button class="test-button" onclick="testSetMainPet()">设置主宠</button>
            <div id="operationResults"></div>
        </div>

        <!-- API适配器测试 -->
        <div class="test-section">
            <h3>🔧 API适配器测试</h3>
            <button class="test-button" onclick="testApiAdapter()">测试适配器</button>
            <button class="test-button" onclick="testExternalFunctions()">测试external函数</button>
            <div id="adapterResults"></div>
        </div>

        <!-- 综合测试 -->
        <div class="test-section">
            <h3>🎯 综合测试</h3>
            <button class="test-button" onclick="runAllTests()">运行所有测试</button>
            <button class="test-button" onclick="clearResults()">清空结果</button>
            <div id="comprehensiveResults"></div>
        </div>
    </div>

    <script src="../js/cache-manager.js"></script>
    <script src="../js/loading-manager.js"></script>
    <script src="../js/auth-manager.js"></script>
    <script src="../js/pasture-api-adapter.js"></script>

    <script>
        const API_BASE_URL = 'http://localhost:5000/api';
        const TEST_USER_ID = 1;
        let testPetId = null;

        function addResult(sectionId, message, type = 'info', data = null) {
            const section = document.getElementById(sectionId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            
            const time = new Date().toLocaleTimeString();
            let content = `[${time}] ${message}`;
            
            if (data) {
                content += '\n' + JSON.stringify(data, null, 2);
            }
            
            div.textContent = content;
            section.appendChild(div);
            section.scrollTop = section.scrollHeight;
        }

        async function testGetPasturePets() {
            try {
                addResult('basicResults', '🔄 测试获取牧场宠物...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/PetManagement/pasture/${TEST_USER_ID}?status=牧场&pageSize=100`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('basicResults', `✅ 获取牧场宠物成功，数量: ${result.pets.length}`, 'success', {
                        petCount: result.pets.length,
                        currentCount: result.currentCount,
                        maxCapacity: result.maxCapacity,
                        pets: result.pets.slice(0, 3) // 只显示前3个
                    });
                    
                    // 保存第一个宠物ID用于后续测试
                    if (result.pets.length > 0) {
                        testPetId = result.pets[0].id;
                    }
                } else {
                    addResult('basicResults', '❌ 获取牧场宠物失败', 'error', result);
                }
            } catch (error) {
                addResult('basicResults', `💥 获取牧场宠物异常: ${error.message}`, 'error');
            }
        }

        async function testGetCapacity() {
            try {
                addResult('basicResults', '🔄 测试获取牧场容量...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/PetManagement/capacity/${TEST_USER_ID}`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('basicResults', '✅ 获取牧场容量成功', 'success', result.data);
                } else {
                    addResult('basicResults', '❌ 获取牧场容量失败', 'error', result);
                }
            } catch (error) {
                addResult('basicResults', `💥 获取牧场容量异常: ${error.message}`, 'error');
            }
        }

        async function testCarryPets() {
            try {
                addResult('basicResults', '🔄 测试获取携带宠物...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/PetManagement/pasture/${TEST_USER_ID}?status=携带&pageSize=100`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('basicResults', `✅ 获取携带宠物成功，数量: ${result.pets.length}`, 'success', {
                        petCount: result.pets.length,
                        pets: result.pets.slice(0, 3) // 只显示前3个
                    });
                } else {
                    addResult('basicResults', '❌ 获取携带宠物失败', 'error', result);
                }
            } catch (error) {
                addResult('basicResults', `💥 获取携带宠物异常: ${error.message}`, 'error');
            }
        }

        async function testStorePet() {
            addResult('operationResults', '⚠️ 存放宠物测试需要有携带状态的宠物，此处仅测试接口可达性', 'warning');
        }

        async function testCarryPet() {
            if (!testPetId) {
                addResult('operationResults', '⚠️ 请先运行获取牧场宠物测试以获取测试宠物ID', 'warning');
                return;
            }
            addResult('operationResults', `⚠️ 携带宠物测试 (宠物ID: ${testPetId}) 可能影响游戏数据，此处仅测试接口可达性`, 'warning');
        }

        async function testSetMainPet() {
            addResult('operationResults', '⚠️ 设置主宠测试可能影响游戏数据，此处仅测试接口可达性', 'warning');
        }

        async function testApiAdapter() {
            try {
                addResult('adapterResults', '🔄 测试API适配器...', 'info');
                
                // 测试 window.external 是否存在
                if (typeof window.external !== 'undefined') {
                    addResult('adapterResults', '✅ window.external 对象存在', 'success');
                    
                    // 测试各个函数
                    const functions = ['check', 'fhfvnsd', 'getPastureData', 'jiyang', 'xiedai', 'getPetInfo', 'deletePet'];
                    const results = {};
                    
                    functions.forEach(func => {
                        results[func] = typeof window.external[func] === 'function';
                    });
                    
                    addResult('adapterResults', '📋 API适配器函数检查', 'info', results);
                } else {
                    addResult('adapterResults', '❌ window.external 对象不存在', 'error');
                }
            } catch (error) {
                addResult('adapterResults', `💥 测试API适配器异常: ${error.message}`, 'error');
            }
        }

        async function testExternalFunctions() {
            try {
                addResult('adapterResults', '🔄 测试external函数调用...', 'info');
                
                if (typeof window.external !== 'undefined') {
                    // 测试 check 函数
                    const checkResult = window.external.check();
                    addResult('adapterResults', `✅ check() 返回: ${checkResult}`, 'success');
                    
                    // 测试 fhfvnsd 函数（获取容量）
                    try {
                        const capacity = await window.external.fhfvnsd();
                        addResult('adapterResults', `✅ fhfvnsd() 返回容量: ${capacity}`, 'success');
                    } catch (error) {
                        addResult('adapterResults', `❌ fhfvnsd() 调用失败: ${error.message}`, 'error');
                    }
                    
                    // 测试 getPastureData 函数
                    try {
                        const data = await window.external.getPastureData();
                        const pets = JSON.parse(data);
                        addResult('adapterResults', `✅ getPastureData() 返回 ${pets.length} 只宠物`, 'success');
                    } catch (error) {
                        addResult('adapterResults', `❌ getPastureData() 调用失败: ${error.message}`, 'error');
                    }
                } else {
                    addResult('adapterResults', '❌ window.external 对象不存在', 'error');
                }
            } catch (error) {
                addResult('adapterResults', `💥 测试external函数异常: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            addResult('comprehensiveResults', '🚀 开始运行所有测试...', 'info');
            
            // 基础API测试
            await testGetPasturePets();
            await testGetCapacity();
            await testCarryPets();
            
            // API适配器测试
            await testApiAdapter();
            await testExternalFunctions();
            
            addResult('comprehensiveResults', '🎉 所有测试完成！', 'success');
        }

        function clearResults() {
            const sections = ['basicResults', 'operationResults', 'adapterResults', 'comprehensiveResults'];
            sections.forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                testGetPasturePets();
            }, 1000);
        });
    </script>
</body>
</html>
