namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 境界修炼进度DTO
    /// </summary>
    public class RealmProgressDTO
    {
        /// <summary>
        /// 消息类型
        /// </summary>
        public string Type { get; set; } = "realm_progress";

        /// <summary>
        /// 玩家ID
        /// </summary>
        public int PlayerId { get; set; }

        /// <summary>
        /// 宠物ID
        /// </summary>
        public int PetId { get; set; }

        /// <summary>
        /// 宠物名称
        /// </summary>
        public string PetName { get; set; }

        /// <summary>
        /// 当前境界
        /// </summary>
        public string CurrentRealm { get; set; }

        /// <summary>
        /// 当前境界经验
        /// </summary>
        public int CurrentExp { get; set; }

        /// <summary>
        /// 突破所需经验
        /// </summary>
        public int RequiredExp { get; set; }

        /// <summary>
        /// 修炼进度百分比
        /// </summary>
        public double ProgressPercent { get; set; }

        /// <summary>
        /// 是否接近突破
        /// </summary>
        public bool NearBreakthrough { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.Now;
    }
} 