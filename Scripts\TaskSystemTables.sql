-- 任务系统数据库表创建脚本
-- 创建时间: 2025-07-24
-- 说明: 从WindowsFormsApplication7迁移到WebApplication_HM的任务系统表结构

-- 1. 任务配置表
CREATE TABLE IF NOT EXISTS task_config (
    task_id VARCHAR(50) PRIMARY KEY COMMENT '任务ID',
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    task_description TEXT COMMENT '任务描述',
    task_type TINYINT DEFAULT 0 COMMENT '任务类型(0=普通,1=循环,2=活动)',
    is_repeatable TINYINT DEFAULT 0 COMMENT '是否可重复(0=否,1=是)',
    prerequisite_task VARCHAR(50) COMMENT '前置任务ID',
    required_pet VARCHAR(50) COMMENT '指定宠物ID',
    reward_config TEXT COMMENT '奖励配置(JSON格式)',
    is_network_task TINYINT DEFAULT 0 COMMENT '是否网络任务',
    is_active TINYINT DEFAULT 1 COMMENT '是否激活',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_task_type (task_type),
    INDEX idx_prerequisite (prerequisite_task),
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务配置表';

-- 2. 任务目标表
CREATE TABLE IF NOT EXISTS task_objective (
    objective_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '目标ID',
    task_id VARCHAR(50) NOT NULL COMMENT '任务ID',
    objective_type VARCHAR(50) NOT NULL COMMENT '目标类型(KILL_MONSTER,COLLECT_ITEM,REACH_LEVEL等)',
    target_id VARCHAR(50) COMMENT '目标ID(怪物ID/道具ID等)',
    target_amount INT NOT NULL DEFAULT 1 COMMENT '目标数量',
    objective_order INT DEFAULT 0 COMMENT '目标顺序',
    objective_description VARCHAR(500) COMMENT '目标描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (task_id) REFERENCES task_config(task_id) ON DELETE CASCADE,
    INDEX idx_task_id (task_id),
    INDEX idx_objective_type (objective_type),
    INDEX idx_target_id (target_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务目标表';

-- 3. 用户任务表
CREATE TABLE IF NOT EXISTS user_task (
    user_task_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户任务ID',
    user_id INT NOT NULL COMMENT '用户ID',
    task_id VARCHAR(50) NOT NULL COMMENT '任务ID',
    task_status TINYINT DEFAULT 1 COMMENT '任务状态(0=已完成,1=进行中,2=已放弃)',
    accepted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '接取时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    abandoned_at TIMESTAMP NULL COMMENT '放弃时间',
    completion_count INT DEFAULT 0 COMMENT '完成次数(用于可重复任务)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_user_task (user_id, task_id),
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES task_config(task_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_task_status (task_status),
    INDEX idx_accepted_at (accepted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户任务表';

-- 4. 用户任务进度表
CREATE TABLE IF NOT EXISTS user_task_progress (
    progress_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '进度ID',
    user_task_id INT NOT NULL COMMENT '用户任务ID',
    objective_id INT NOT NULL COMMENT '目标ID',
    current_amount INT DEFAULT 0 COMMENT '当前进度',
    is_completed TINYINT DEFAULT 0 COMMENT '是否完成',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_progress (user_task_id, objective_id),
    FOREIGN KEY (user_task_id) REFERENCES user_task(user_task_id) ON DELETE CASCADE,
    FOREIGN KEY (objective_id) REFERENCES task_objective(objective_id) ON DELETE CASCADE,
    INDEX idx_user_task_id (user_task_id),
    INDEX idx_is_completed (is_completed)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户任务进度表';

-- 5. 任务奖励记录表
CREATE TABLE IF NOT EXISTS task_reward_log (
    log_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    user_id INT NOT NULL COMMENT '用户ID',
    task_id VARCHAR(50) NOT NULL COMMENT '任务ID',
    reward_type VARCHAR(50) NOT NULL COMMENT '奖励类型(ITEM,CURRENCY,EQUIPMENT等)',
    reward_id VARCHAR(50) COMMENT '奖励物品ID',
    reward_amount INT NOT NULL DEFAULT 1 COMMENT '奖励数量',
    reward_description VARCHAR(200) COMMENT '奖励描述',
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发放时间',
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES task_config(task_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_task_id (task_id),
    INDEX idx_reward_type (reward_type),
    INDEX idx_granted_at (granted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务奖励记录表';

-- 6. 任务类型配置表(可选，用于管理任务类型)
CREATE TABLE IF NOT EXISTS task_type_config (
    type_id VARCHAR(50) PRIMARY KEY COMMENT '类型ID',
    type_name VARCHAR(100) NOT NULL COMMENT '类型名称',
    type_description VARCHAR(500) COMMENT '类型描述',
    handler_class VARCHAR(200) COMMENT '处理器类名',
    is_active TINYINT DEFAULT 1 COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务类型配置表';

-- 插入基础任务类型数据
INSERT INTO task_type_config (type_id, type_name, type_description, handler_class) VALUES
('KILL_MONSTER', '击杀怪物', '击杀指定数量的怪物', 'KillMonsterTaskHandler'),
('COLLECT_ITEM', '收集道具', '收集指定数量的道具', 'CollectItemTaskHandler'),
('REACH_LEVEL', '达到等级', '宠物达到指定等级', 'ReachLevelTaskHandler'),
('CURRENCY', '货币任务', '收集指定数量的货币', 'CurrencyTaskHandler'),
('PET_GROWTH', '宠物成长', '宠物成长度相关任务', 'PetGrowthTaskHandler'),
('COLLECT_EQUIPMENT', '收集装备', '收集指定装备', 'CollectEquipmentTaskHandler'),
('VIP_LEVEL', 'VIP等级', 'VIP等级相关任务', 'VipLevelTaskHandler'),
('TIME_LIMIT', '时间限制', '在指定时间内完成', 'TimeLimitTaskHandler'),
('DUNGEON', '副本任务', '地狱之门/通天塔等', 'DungeonTaskHandler'),
('CARD', '卡牌任务', '激活卡牌相关', 'CardTaskHandler'),
('ONE_CLICK', '一键完成', '使用道具一键完成', 'OneClickTaskHandler'),
('SPECIAL', '特殊任务', '其他特殊类型任务', 'SpecialTaskHandler')
ON DUPLICATE KEY UPDATE 
    type_name = VALUES(type_name),
    type_description = VALUES(type_description),
    handler_class = VALUES(handler_class),
    updated_at = CURRENT_TIMESTAMP;

-- 创建视图：用户任务详情视图
CREATE OR REPLACE VIEW v_user_task_detail AS
SELECT 
    ut.user_task_id,
    ut.user_id,
    ut.task_id,
    tc.task_name,
    tc.task_description,
    tc.task_type,
    tc.is_repeatable,
    tc.required_pet,
    ut.task_status,
    ut.accepted_at,
    ut.completed_at,
    ut.completion_count,
    -- 计算任务完成进度
    CASE 
        WHEN COUNT(utp.progress_id) = 0 THEN 0
        ELSE ROUND(SUM(CASE WHEN utp.is_completed = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(utp.progress_id), 2)
    END AS completion_percentage,
    -- 是否可以完成
    CASE 
        WHEN ut.task_status = 1 AND COUNT(utp.progress_id) > 0 AND SUM(CASE WHEN utp.is_completed = 1 THEN 1 ELSE 0 END) = COUNT(utp.progress_id) THEN 1
        ELSE 0
    END AS can_complete
FROM user_task ut
JOIN task_config tc ON ut.task_id = tc.task_id
LEFT JOIN user_task_progress utp ON ut.user_task_id = utp.user_task_id
GROUP BY ut.user_task_id, ut.user_id, ut.task_id, tc.task_name, tc.task_description, 
         tc.task_type, tc.is_repeatable, tc.required_pet, ut.task_status, 
         ut.accepted_at, ut.completed_at, ut.completion_count;

-- 创建存储过程：检查任务前置条件
DELIMITER //
CREATE PROCEDURE CheckTaskPrerequisites(
    IN p_user_id INT,
    IN p_task_id VARCHAR(50),
    OUT p_can_accept TINYINT,
    OUT p_message VARCHAR(500)
)
BEGIN
    DECLARE v_prerequisite_task VARCHAR(50);
    DECLARE v_prerequisite_completed INT DEFAULT 0;
    DECLARE v_already_accepted INT DEFAULT 0;
    DECLARE v_task_active INT DEFAULT 0;
    
    -- 初始化返回值
    SET p_can_accept = 0;
    SET p_message = '';
    
    -- 检查任务是否存在且激活
    SELECT is_active, prerequisite_task 
    INTO v_task_active, v_prerequisite_task
    FROM task_config 
    WHERE task_id = p_task_id;
    
    IF v_task_active IS NULL THEN
        SET p_message = '任务不存在';
        LEAVE CheckTaskPrerequisites;
    END IF;
    
    IF v_task_active = 0 THEN
        SET p_message = '任务未激活';
        LEAVE CheckTaskPrerequisites;
    END IF;
    
    -- 检查是否已经接取
    SELECT COUNT(*) INTO v_already_accepted
    FROM user_task 
    WHERE user_id = p_user_id AND task_id = p_task_id AND task_status = 1;
    
    IF v_already_accepted > 0 THEN
        SET p_message = '任务已接取';
        LEAVE CheckTaskPrerequisites;
    END IF;
    
    -- 检查前置任务
    IF v_prerequisite_task IS NOT NULL AND v_prerequisite_task != '' THEN
        SELECT COUNT(*) INTO v_prerequisite_completed
        FROM user_task 
        WHERE user_id = p_user_id 
          AND task_id = v_prerequisite_task 
          AND task_status = 0;
        
        IF v_prerequisite_completed = 0 THEN
            SET p_message = '前置任务未完成';
            LEAVE CheckTaskPrerequisites;
        END IF;
    END IF;
    
    -- 所有检查通过
    SET p_can_accept = 1;
    SET p_message = '可以接取任务';
    
END //
DELIMITER ;

-- 创建触发器：自动创建任务进度记录
DELIMITER //
CREATE TRIGGER tr_user_task_after_insert
AFTER INSERT ON user_task
FOR EACH ROW
BEGIN
    -- 为新接取的任务创建进度记录
    INSERT INTO user_task_progress (user_task_id, objective_id, current_amount, is_completed)
    SELECT NEW.user_task_id, to.objective_id, 0, 0
    FROM task_objective to
    WHERE to.task_id = NEW.task_id;
END //
DELIMITER ;

-- 创建索引优化查询性能
CREATE INDEX idx_user_task_status_user ON user_task(user_id, task_status);
CREATE INDEX idx_task_config_type_active ON task_config(task_type, is_active);
CREATE INDEX idx_task_objective_type_target ON task_objective(objective_type, target_id);
CREATE INDEX idx_reward_log_user_time ON task_reward_log(user_id, granted_at);

-- 添加注释
ALTER TABLE task_config COMMENT = '任务配置表 - 存储所有任务的基本信息和配置';
ALTER TABLE task_objective COMMENT = '任务目标表 - 存储每个任务的具体目标条件';
ALTER TABLE user_task COMMENT = '用户任务表 - 存储用户接取的任务状态';
ALTER TABLE user_task_progress COMMENT = '用户任务进度表 - 存储用户任务的完成进度';
ALTER TABLE task_reward_log COMMENT = '任务奖励记录表 - 记录任务奖励发放历史';
ALTER TABLE task_type_config COMMENT = '任务类型配置表 - 定义支持的任务类型';
