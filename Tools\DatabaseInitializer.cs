using System.Text;
using SqlSugar;

namespace WebApplication_HM.Tools
{
    public class DatabaseInitializer
    {
        private readonly ISqlSugarClient _db;

        public DatabaseInitializer(string connectionString)
        {
            _db = new SqlSugarClient(new ConnectionConfig()
            {
                ConnectionString = connectionString,
                DbType = SqlSugar.DbType.MySql,
                IsAutoCloseConnection = true
            });
        }

        public async Task InitializeMapDataAsync()
        {
            try
            {
                Console.WriteLine("开始初始化地图数据...");

                // 创建地图配置表
                await CreateMapConfigTableAsync();

                // 插入测试地图数据
                await InsertTestMapDataAsync();

                Console.WriteLine("地图数据初始化完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"初始化地图数据失败: {ex.Message}");
                throw;
            }
        }

        private async Task CreateMapConfigTableAsync()
        {
            try
            {
                // 创建地图配置表
                var createMapConfigSql = @"
                    CREATE TABLE IF NOT EXISTS `map_config` (
                      `map_id` int NOT NULL,
                      `map_name` varchar(100) NOT NULL,
                      `map_desc` text,
                      `background` varchar(200),
                      `ico` varchar(200),
                      `type` int DEFAULT 1,
                      PRIMARY KEY (`map_id`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

                await _db.Ado.ExecuteCommandAsync(createMapConfigSql);

                // 创建地图详情表
                var createMapDetailSql = @"
                    CREATE TABLE IF NOT EXISTS `map_detail` (
                      `map_id` int NOT NULL,
                      `limit_level` int DEFAULT 1,
                      `unlock_condition` varchar(200),
                      PRIMARY KEY (`map_id`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

                await _db.Ado.ExecuteCommandAsync(createMapDetailSql);

                Console.WriteLine("地图表创建成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建地图表失败: {ex.Message}");
                throw;
            }
        }

        private async Task InsertTestMapDataAsync()
        {
            try
            {
                // 插入地图配置数据
                var mapConfigs = new[]
                {
                    new { map_id = 1, map_name = "新手训练营", map_desc = "适合新手练级的安全区域", background = "map1.jpg", ico = "1.png", type = 1 },
                    new { map_id = 2, map_name = "彩虹森林", map_desc = "美丽的森林，有各种可爱的小动物", background = "map2.jpg", ico = "2.png", type = 1 },
                    new { map_id = 3, map_name = "火焰山谷", map_desc = "炎热的山谷，火系宠物的聚集地", background = "map3.jpg", ico = "3.png", type = 1 }
                };

                foreach (var config in mapConfigs)
                {
                    var insertSql = $@"
                        INSERT INTO map_config (map_id, map_name, map_desc, background, ico, type)
                        VALUES ({config.map_id}, '{config.map_name}', '{config.map_desc}', '{config.background}', '{config.ico}', {config.type})
                        ON DUPLICATE KEY UPDATE
                        map_name = VALUES(map_name),
                        map_desc = VALUES(map_desc)";

                    await _db.Ado.ExecuteCommandAsync(insertSql);
                }

                // 插入地图详情数据
                var mapDetails = new[]
                {
                    new { map_id = 1, limit_level = 1, unlock_condition = "无条件" },
                    new { map_id = 2, limit_level = 5, unlock_condition = "等级达到5级" },
                    new { map_id = 3, limit_level = 10, unlock_condition = "等级达到10级" }
                };

                foreach (var detail in mapDetails)
                {
                    var insertSql = $@"
                        INSERT INTO map_detail (map_id, limit_level, unlock_condition)
                        VALUES ({detail.map_id}, {detail.limit_level}, '{detail.unlock_condition}')
                        ON DUPLICATE KEY UPDATE
                        limit_level = VALUES(limit_level),
                        unlock_condition = VALUES(unlock_condition)";

                    await _db.Ado.ExecuteCommandAsync(insertSql);
                }

                Console.WriteLine("测试地图数据插入成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"插入测试地图数据失败: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> CheckMapDataExistsAsync()
        {
            try
            {
                // 先检查表是否存在
                var tableExists = await _db.Ado.GetIntAsync(@"
                    SELECT COUNT(*)
                    FROM information_schema.tables
                    WHERE table_schema = DATABASE()
                    AND table_name = 'map_config'");

                if (tableExists == 0)
                {
                    Console.WriteLine("map_config表不存在");
                    return false;
                }

                var count = await _db.Ado.GetIntAsync("SELECT COUNT(*) FROM map_config");
                Console.WriteLine($"map_config表中有 {count} 条记录");
                return count > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查地图数据失败: {ex.Message}");
                return false;
            }
        }
    }
}
