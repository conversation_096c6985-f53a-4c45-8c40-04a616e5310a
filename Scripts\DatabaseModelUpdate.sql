-- 数据库模型更新脚本
-- 为现有表添加缺失的字段

-- 1. 更新user_equipment表，添加装备系统需要的字段
ALTER TABLE user_equipment ADD COLUMN element TEXT; -- 五行属性
ALTER TABLE user_equipment ADD COLUMN pet_id INTEGER; -- 关联宠物ID
ALTER TABLE user_equipment ADD COLUMN gemstone_slots INTEGER DEFAULT 1; -- 宝石槽位数量
ALTER TABLE user_equipment ADD COLUMN suit_id TEXT; -- 套装ID
ALTER TABLE user_equipment ADD COLUMN lssx TEXT; -- 历史属性
ALTER TABLE user_equipment ADD COLUMN special_effect TEXT; -- 特殊效果

-- 2. 更新equipment_detail表，添加装备详情字段
ALTER TABLE equipment_detail ADD COLUMN equip_name TEXT; -- 装备名称
ALTER TABLE equipment_detail ADD COLUMN main_attr_value TEXT; -- 主属性值
ALTER TABLE equipment_detail ADD COLUMN sub_attr TEXT; -- 副属性
ALTER TABLE equipment_detail ADD COLUMN sub_attr_value TEXT; -- 副属性值

-- 3. 更新user表，添加金币字段
ALTER TABLE user ADD COLUMN money TEXT DEFAULT '0'; -- 金币数量

-- 4. 更新user_item表，添加创建时间字段
ALTER TABLE user_item ADD COLUMN create_time DATETIME DEFAULT CURRENT_TIMESTAMP; -- 创建时间

-- 5. 创建装备操作日志表
CREATE TABLE IF NOT EXISTS equipment_operation_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    equipment_id INTEGER NOT NULL,
    operation_type TEXT, -- 操作类型（STRENGTHEN/EMBED_GEM/TRANSFORM_ELEMENT等）
    operation_desc TEXT, -- 操作描述
    result TEXT, -- 操作结果（SUCCESS/FAILED）
    message TEXT, -- 结果消息
    money_cost INTEGER DEFAULT 0, -- 金币消耗
    item_costs TEXT, -- 道具消耗（JSON格式）
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

-- 6. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_user_equipment_pet_id ON user_equipment(pet_id);
CREATE INDEX IF NOT EXISTS idx_user_equipment_element ON user_equipment(element);
CREATE INDEX IF NOT EXISTS idx_user_equipment_suit_id ON user_equipment(suit_id);
CREATE INDEX IF NOT EXISTS idx_equipment_operation_log_user_id ON equipment_operation_log(user_id);
CREATE INDEX IF NOT EXISTS idx_equipment_operation_log_equipment_id ON equipment_operation_log(equipment_id);

-- 验证表结构
SELECT '=== 验证user_equipment表结构 ===' as info;
PRAGMA table_info(user_equipment);

SELECT '=== 验证equipment_detail表结构 ===' as info;
PRAGMA table_info(equipment_detail);

SELECT '=== 验证user表结构 ===' as info;
PRAGMA table_info(user);

SELECT '=== 验证user_item表结构 ===' as info;
PRAGMA table_info(user_item);

SELECT '=== 验证equipment_operation_log表结构 ===' as info;
PRAGMA table_info(equipment_operation_log);
