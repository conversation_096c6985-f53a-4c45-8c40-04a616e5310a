# PowerShell脚本：删除Battle.html中的所有console.log语句

$filePath = "wwwroot/game/pages/Battle.html"

# 读取文件内容
$content = Get-Content $filePath -Encoding UTF8

# 删除console.log行
$newContent = @()
foreach ($line in $content) {
    # 如果行不是纯console.log语句，则保留
    if ($line -notmatch '^\s*console\.log\([^)]*\);\s*$') {
        $newContent += $line
    }
}

# 写回文件
$newContent | Set-Content $filePath -Encoding UTF8

Write-Host "已删除所有独立的console.log语句"
