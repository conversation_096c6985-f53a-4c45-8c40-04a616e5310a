namespace WebApplication_HM.DTOs.RequestDTO
{
    /// <summary>
    /// 系统公告请求DTO
    /// </summary>
    public class SystemNoticeRequestDTO
    {
        /// <summary>
        /// 公告标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 公告内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 公告类型 (maintenance/update/event/warning/info)
        /// </summary>
        public string NoticeType { get; set; } = "info";

        /// <summary>
        /// 优先级 (high/medium/low)
        /// </summary>
        public string Priority { get; set; } = "medium";

        /// <summary>
        /// 是否弹窗显示
        /// </summary>
        public bool ShowPopup { get; set; } = false;

        /// <summary>
        /// 发送者（管理员名称）
        /// </summary>
        public string Sender { get; set; } = "系统";

        /// <summary>
        /// 目标玩家ID列表（空表示全服）
        /// </summary>
        public List<int> TargetPlayerIds { get; set; } = new List<int>();

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpireTime { get; set; }
    }
} 