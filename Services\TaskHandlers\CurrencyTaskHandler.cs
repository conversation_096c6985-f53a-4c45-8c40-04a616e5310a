using SqlSugar;
using WebApplication_HM.Models;
using WebApplication_HM.Models.Define;
using TaskStatus = WebApplication_HM.Models.Define.TaskStatus;

namespace WebApplication_HM.Services.TaskHandlers
{
    /// <summary>
    /// 货币任务处理器
    /// </summary>
    public class CurrencyTaskHandler : BaseTaskHandler
    {
        public CurrencyTaskHandler(ISqlSugarClient db, ILogger<CurrencyTaskHandler> logger) 
            : base(db, logger)
        {
        }

        public override string SupportedObjectiveType => TaskObjectiveTypes.CURRENCY;

        /// <summary>
        /// 检查货币任务进度
        /// </summary>
        public override async Task<int> CheckProgressAsync(int userId, task_objective objective)
        {
            if (!ValidateObjective(objective))
                return 0;

            try
            {
                // 获取用户信息
                var user = await _db.Queryable<user>()
                    .Where(u => u.id == userId)
                    .FirstAsync();

                if (user == null)
                {
                    _logger.LogWarning("用户不存在: UserId={UserId}", userId);
                    return 0;
                }

                int currentAmount = 0;

                // 根据目标ID确定检查哪种货币
                switch (objective.target_id?.ToLower())
                {
                    case "gold":
                    case "金币":
                    case null: // 默认检查金币
                        currentAmount = (int)Math.Min(user.gold ?? 0, int.MaxValue);
                        break;

                    case "diamond":
                    case "钻石":
                    case "yuanbao":
                    case "元宝":
                        currentAmount = user.yuanbao ?? 0;
                        break;

                    case "experience":
                    case "经验":
                        // 暂时使用金币作为经验的替代，因为user表中没有经验字段
                        currentAmount = (int)Math.Min(user.gold ?? 0, int.MaxValue);
                        break;

                    case "honor":
                    case "荣誉":
                        // 暂时使用VIP等级作为荣誉的替代，因为user表中没有荣誉字段
                        currentAmount = user.vip_level ?? 0;
                        break;

                    default:
                        _logger.LogWarning("不支持的货币类型: {CurrencyType}", objective.target_id);
                        return 0;
                }

                LogHandlerAction("检查货币数量", userId, objective, new { 
                    CurrencyType = objective.target_id, 
                    CurrentAmount = currentAmount 
                });
                
                // 返回实际拥有的数量，但不超过任务要求的数量
                return Math.Min(currentAmount, objective.target_amount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查货币任务进度失败: UserId={UserId}, ObjectiveId={ObjectiveId}", 
                    userId, objective.objective_id);
                return 0;
            }
        }

        /// <summary>
        /// 更新货币任务进度
        /// </summary>
        public override async Task<int> UpdateProgressAsync(int userId, task_objective objective, int amount)
        {
            if (!ValidateObjective(objective))
                return 0;

            try
            {
                // 货币任务的进度更新通常是检查当前货币数量
                // 而不是累加，因为货币会被消费
                var currentCurrencyAmount = await CheckProgressAsync(userId, objective);

                // 查找用户相关的任务进度
                var taskProgress = await _db.Queryable<user_task, user_task_progress>((ut, utp) => new JoinQueryInfos(
                    JoinType.Inner, ut.user_task_id == utp.user_task_id))
                    .Where((ut, utp) => ut.user_id == userId && 
                                      utp.objective_id == objective.objective_id &&
                                      ut.task_status == (byte)TaskStatus.InProgress)
                    .Select((ut, utp) => new { UserTask = ut, Progress = utp })
                    .FirstAsync();

                if (taskProgress == null)
                {
                    _logger.LogWarning("未找到用户货币任务进度: UserId={UserId}, ObjectiveId={ObjectiveId}", 
                        userId, objective.objective_id);
                    return 0;
                }

                var progress = taskProgress.Progress;
                var oldAmount = progress.current_amount ?? 0;

                // 更新进度为当前实际拥有的货币数量
                progress.current_amount = currentCurrencyAmount;
                progress.is_completed = (byte)(IsProgressCompleted(currentCurrencyAmount, objective.target_amount) ? 1 : 0);

                var updateResult = await UpdateUserTaskProgressAsync(progress);
                
                if (updateResult)
                {
                    LogHandlerAction("更新进度成功", userId, objective, new { 
                        OldAmount = oldAmount, 
                        NewAmount = currentCurrencyAmount,
                        CurrencyType = objective.target_id,
                        IsCompleted = progress.is_completed == 1
                    });
                    
                    return currentCurrencyAmount;
                }
                else
                {
                    _logger.LogError("更新货币任务进度失败: UserId={UserId}, ObjectiveId={ObjectiveId}", 
                        userId, objective.objective_id);
                    return oldAmount;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新货币任务进度失败: UserId={UserId}, ObjectiveId={ObjectiveId}", 
                    userId, objective.objective_id);
                return 0;
            }
        }

        /// <summary>
        /// 处理货币变化事件
        /// </summary>
        public async Task<int> HandleCurrencyChangeAsync(int userId, string currencyType, long newAmount)
        {
            try
            {
                // 查找所有相关的货币任务
                var relevantTasks = await _db.Queryable<user_task, task_config, task_objective, user_task_progress>(
                    (ut, tc, to, utp) => new JoinQueryInfos(
                        JoinType.Inner, ut.task_id == tc.task_id,
                        JoinType.Inner, tc.task_id == to.task_id,
                        JoinType.Inner, ut.user_task_id == utp.user_task_id && utp.objective_id == to.objective_id))
                    .Where((ut, tc, to, utp) => ut.user_id == userId &&
                                              ut.task_status == (byte)TaskStatus.InProgress &&
                                              to.objective_type == TaskObjectiveTypes.CURRENCY &&
                                              (to.target_id == currencyType || 
                                               (string.IsNullOrEmpty(to.target_id) && currencyType == "gold")) &&
                                              utp.is_completed == 0)
                    .Select((ut, tc, to, utp) => new { UserTask = ut, TaskConfig = tc, Objective = to, Progress = utp })
                    .ToListAsync();

                int totalUpdated = 0;

                foreach (var task in relevantTasks)
                {
                    var updatedAmount = await UpdateProgressAsync(userId, task.Objective, 0); // 传入0表示刷新检查
                    if (updatedAmount != (task.Progress.current_amount ?? 0))
                    {
                        totalUpdated++;
                    }
                }

                if (totalUpdated > 0)
                {
                    _logger.LogInformation("处理货币变化事件: UserId={UserId}, CurrencyType={CurrencyType}, NewAmount={NewAmount}, UpdatedTasks={UpdatedTasks}",
                        userId, currencyType, newAmount, totalUpdated);
                }

                return totalUpdated;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理货币变化事件失败: UserId={UserId}, CurrencyType={CurrencyType}", userId, currencyType);
                return 0;
            }
        }

        /// <summary>
        /// 批量检查所有货币任务的进度
        /// </summary>
        public async Task<int> RefreshAllCurrencyTasksAsync(int userId)
        {
            try
            {
                // 查找用户所有进行中的货币任务
                var currencyTasks = await _db.Queryable<user_task, task_config, task_objective, user_task_progress>(
                    (ut, tc, to, utp) => new JoinQueryInfos(
                        JoinType.Inner, ut.task_id == tc.task_id,
                        JoinType.Inner, tc.task_id == to.task_id,
                        JoinType.Inner, ut.user_task_id == utp.user_task_id && utp.objective_id == to.objective_id))
                    .Where((ut, tc, to, utp) => ut.user_id == userId &&
                                              ut.task_status == (byte)TaskStatus.InProgress &&
                                              to.objective_type == TaskObjectiveTypes.CURRENCY &&
                                              utp.is_completed == 0)
                    .Select((ut, tc, to, utp) => new { UserTask = ut, TaskConfig = tc, Objective = to, Progress = utp })
                    .ToListAsync();

                int totalUpdated = 0;

                foreach (var task in currencyTasks)
                {
                    var updatedAmount = await UpdateProgressAsync(userId, task.Objective, 0); // 传入0表示刷新检查
                    if (updatedAmount != (task.Progress.current_amount ?? 0))
                    {
                        totalUpdated++;
                    }
                }

                if (totalUpdated > 0)
                {
                    _logger.LogInformation("刷新货币任务进度: UserId={UserId}, UpdatedTasks={UpdatedTasks}",
                        userId, totalUpdated);
                }

                return totalUpdated;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新货币任务进度失败: UserId={UserId}", userId);
                return 0;
            }
        }
    }
}
