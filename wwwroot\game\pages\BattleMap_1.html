<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<script language="javascript" src="../javascript/prototype.js"></script>
    <script src="Content/Javascript/jquery-1.8.3.min.js"></script>
<link href="../css/iframe.css" rel="stylesheet" type="text/css">
<style type="text/css">
<!--
body {
	margin-left: 20px;
	margin-top: 20px;
	
	margin-right: 0;
	margin-bottom: 0;
	font-size:12px;
}
#Layer1 {
	position:absolute;
	left:413px;
	top:75px;
	width:27px;
	height:26px;
	z-index:1;
}
-->
.mapBtn{
	background-size: 75px 24px !important;
}
</style>
<!--[if IE 6]>
<script type="text/javascript">try { document.execCommand('BackgroundImageCache', false, true); } catch(e) {}
</script>
<![endif]-->
</head>
<!--background-image:url(../images/ui/map/map5.gif)-->
<body style="margin:0;" oncontextmenu="event.returnValue=false" onkeydown="KeyDown()">
<div style="width:786px;height:321px;margin-left:5px;margin-top:5px; background-image:url(img/map5.jpg); background-repeat:no-repeat;background-size:787px 317px" oncontextmenu="event.returnValue=false"></div>



<div onclick="openMode('9010')" id="hhxw" style="position:absolute;cursor:pointer;left:199px;top:32px;border:1;" width=20 height=20 title="彩虹小屋" >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>

<div style="position:absolute; left:363px; top:161px; border:0; cursor:pointer; width: 79px;" title="圣域宝地" onClick="openMode('102')">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>

<div style="position:absolute;left:509px;top:184px;border:0;cursor:pointer;" title="危机之路(8)" onClick="openMode('20')">
<div class="mapBtn" style="width:75px; height:24px;background:url(img/121.png); _background:none; _filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled='true', sizingMethod='image', src='img/03.png');
"></div></div>

	
<div style="position:absolute;left:263px;top:226px;border:0;cursor:pointer;" title="魔指石印(4)" onClick="openMode('16')">
<div class="mapBtn" style="width:75px; height:24px;background:url(img/109.png); _background:none; _filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled='true', sizingMethod='image', src='img/03.png');
"></div></div>

	
<div style="position:absolute;left:465px;top:17px;border:0;cursor:pointer;" title="魔之路(7)" onClick="openMode('19')">
<div class="mapBtn" style="width:75px; height:24px;background:url(img/118.png); _background:none; _filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled='true', sizingMethod='image', src='img/03.png');
"></div></div>

<div style="position:absolute;left:419px;top:82px;border:0;cursor:pointer;" title="魔兽之谷(6)" onClick="openMode('18')">
<div class="mapBtn" style="width:75px; height:24px;background:url(img/115.png); _background:none; _filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled='true', sizingMethod='image', src='img/03.png');
"></div></div>

<div style="position:absolute; left:45px; top:59px; border:0; cursor:pointer; width: 34px; height: 33px;" title="魔龙窟(3)" onClick="openMode('15')">
<div class="mapBtn" style="width:75px; height:24px;background:url(img/106.png); _background:none; _filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled='true', sizingMethod='image', src='img/03.png');
"></div></div>


<div style="position:absolute;left:130px;top:124px;border:0;cursor:pointer;" title="石林(1)" onClick="openMode('13')">
<div class="mapBtn" style="width:75px; height:24px;background:url(img/100.png); _background:none; _filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled='true', sizingMethod='image', src='img/03.png');
"></div></div>

	
<div style="position:absolute;left:208px;top:157px;border:0;cursor:pointer;" title="平原(2)" onClick="openMode('14')">
<div class="mapBtn" style="width:75px; height:24px;background:url(img/103.png); _background:none; _filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled='true', sizingMethod='image', src='img/03.png');
"></div></div>
		

<div style="position:absolute;left:98px;top:211px;border:0;cursor:pointer;" title="沙漠(5)" onClick="openMode('17')">
<div class="mapBtn" style="width:75px; height:24px;background:url(img/112.png); _background:none; _filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled='true', sizingMethod='image', src='img/03.png');
"></div></div>

<div id="sy" style="position:absolute;cursor:pointer;left:170px;top:280px;border:1;" width="20" height="20" title="魔兽古堡遗迹" onClick="openMode('22')">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>

<div id="ml" style="position:absolute;cursor:pointer;left:441px;top:249px;border:1;" width="20" height="20" title="魔龙王的巢穴" onClick="openMode('9012')";fbMap(124);" ;="">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>

<div onclick="openMode('9011')" id="hhxw" style="position:absolute;cursor:pointer;left:239px;top:99px;border:1;" width=20 height=20 title="楼兰古城的遗迹" >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>

	
<div style="position:absolute;left:20px;top:150px; border:0; cursor:pointer; width: 36px; height:19px" align="center" title="切换地图" onClick="window.parent.openUrl('BattleMap.html')">
<img src="img/left.gif" alt="切换地图"></div>

<div style="position:absolute;left: 621px;top: 283px;border:0;cursor:pointer;" title="时空宝藏" onclick="openMode('23')">
<div style="width:75px;height:24px;/* background:url(img/100.png); */_background:none;_filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled='true', sizingMethod='image', src='img/03.png');"></div></div>


<div style="position:absolute; left:390px; top:291px; border:0; cursor:pointer; width: 76px;" title="恶魔之谷" onClick="openMode('21')";window.parent.$('gw').src='./function/Team_Mod.php?n=125';">
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>

<div style="position:absolute;left: 524px;top: 115px;border:0;cursor:pointer;width: 76px;height: 26px;" title="魔龙秘境" onclick="openMode('201901')" ;window.parent.$('gw').src="./function/Team_Mod.php?n=125" ;"="">
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
<div style="position:absolute;left:641px;top:80px;border:0;cursor:pointer; width:27px; height:77px" title="通天塔" onclick="window.parent.StartBattle('通天塔')"></div>




<script language="javascript" type="text/javascript">

	// Add by DuHao 2009-5-13
	/*function copyWord(words)
	{
		window.parent.$('baike_input').value=words;
	}*/

	function goMap(n,t)
	{
		//if(n==0) 
		//{
		//if(confirm('你还没有打开该地图，要使用钥匙打开吗？')==true)
		//{
		//openMap(t);
		//}
		//else return;
		//}
		//else
		//{
		var opt = {
			method: 'get',
			onSuccess: function(e) {
				//window.parent.Alert(t.responseText);
				var v = e.responseText;
				if(isNaN(v))
				{
					if(!confirm(v))
					{
						return false;
					}
					var opt = {
						method: 'get',
						onSuccess: function(f) {
							//window.parent.Alert(t.responseText);
							var v = parseInt(f.responseText);
							if(v == 11)
							{
								window.parent.Alert("进入地图成功！");
								czl(t);
								//window.parent.$('gw').src='./function/Team_Mod.php?n='+t;
							}
						},
						on404: function(f) {
						},
						onFailure: function(f) {
						},
						asynchronous:true        
					};
					//window.status = '../function/mapGate.php?type=1&n='+n;return false;
					var ajax=new Ajax.Request('../function/mapGate.php?type=2&n='+t, opt);
				}
				else if(v == 10)
				{
					czl(t);
					//window.parent.$('gw').src='./function/Team_Mod.php?n='+t;
				}
				else if(v == 1)
				{
					window.parent.Alert("地图数据错误");
				}
				else if(v == 2)
				{
					window.parent.Alert("你的当前等级不够");
				}
				else if(v == 3)
				{
					window.parent.Alert("你没有相关道具");
				}
				else if(v == 12)
				{
					if(n==0) 
					{
						if(confirm('你还没有打开该地图，要使用钥匙打开吗？')==true)
						{
							openMap(t);
						}
						else return;
					}
					else
					{
						window.parent.$('gw').src='./function/Team_Mod.php?n='+t;
					}
				}
			},
			on404: function(e) {
			},
			onFailure: function(e) {
			},
			asynchronous:true        
		};
		//window.status = '../function/mapGate.php?type=1&n='+t;return false;
		var ajax=new Ajax.Request('../function/mapGate.php?type=1&n='+t, opt);
		/*if(!confirm(""))
		{
			return false;
		}
		window.parent.$('gw').src='./function/Team_Mod.php?n='+n;*/
		//}
	}
	function openMode(i){
		window.location.href = "MapInfo/t"+i+".html";
	}
	//副本地图判断
	function fbMap(id)
	{
		if(id != "")
		{
			window.parent.$('gw').src = './function/fb_Mod.php?mapid='+id;
		}
	}
</script>
</html>
