// 伤害显示组件
const { computed } = Vue

export default {
    name: 'DamageDisplay',
    props: {
        damageInfo: {
            type: Object,
            default: () => ({})
        },
        visible: {
            type: Boolean,
            default: false
        }
    },
    setup(props) {
        // 计算属性
        const damageText = computed(() => {
            if (!props.damageInfo) return ''
            if (!props.damageInfo.isHit) return 'MISS'
            if (props.damageInfo.damage <= 0) return '0'
            return `-${props.damageInfo.damage}`
        })
        
        const damageClasses = computed(() => {
            if (!props.damageInfo) return {}
            return {
                'critical': props.damageInfo.isCritical,
                'miss': !props.damageInfo.isHit,
                'normal': props.damageInfo.isHit && !props.damageInfo.isCritical
            }
        })
        
        const displayStyle = computed(() => ({
            left: '50%',
            top: '30%',
            transform: 'translateX(-50%)'
        }))
        
        const skillName = computed(() => {
            return props.damageInfo?.skillName || '普通攻击'
        })
        
        const hasAmplifiedDamage = computed(() => {
            return props.damageInfo?.amplifiedDamage > 0
        })
        
        const hasReducedDamage = computed(() => {
            return props.damageInfo?.reducedDamage > 0
        })
        
        const hasLifeSteal = computed(() => {
            return props.damageInfo?.lifeSteal > 0
        })
        
        const hasManaSteal = computed(() => {
            return props.damageInfo?.manaSteal > 0
        })
        
        return {
            damageText,
            damageClasses,
            displayStyle,
            skillName,
            hasAmplifiedDamage,
            hasReducedDamage,
            hasLifeSteal,
            hasManaSteal
        }
    },
    template: `
        <transition name="damage-fade">
            <div v-if="visible && damageInfo" class="damage-display" :style="displayStyle">
                <div class="skill-name">{{ skillName }}</div>
                <div class="damage-value" :class="damageClasses">
                    {{ damageText }}
                </div>
                <div v-if="hasAmplifiedDamage" class="effect-text amplified">
                    加深伤害 {{ damageInfo.amplifiedDamage }}
                </div>
                <div v-if="hasReducedDamage" class="effect-text reduced">
                    抵消伤害 {{ damageInfo.reducedDamage }}
                </div>
                <div v-if="hasLifeSteal" class="effect-text life-steal">
                    吸取生命 {{ damageInfo.lifeSteal }}
                </div>
                <div v-if="hasManaSteal" class="effect-text mana-steal">
                    吸取魔法 {{ damageInfo.manaSteal }}
                </div>
            </div>
        </transition>
    `
}
