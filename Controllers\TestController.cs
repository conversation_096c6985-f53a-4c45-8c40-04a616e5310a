using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.Sugar;
using WebApplication_HM.Models;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 测试控制器 - 用于调试数据库连接和数据
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class TestController : ControllerBase
    {
        private readonly DbContext _dbContext;
        private readonly ILogger<TestController> _logger;

        public TestController(DbContext dbContext, ILogger<TestController> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        [HttpGet("connection")]
        public ActionResult TestConnection()
        {
            try
            {
                var result = _dbContext.Db.Queryable<user>().Count();
                return Ok(new { message = "数据库连接成功", userCount = result });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "数据库连接失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 查看用户道具原始数据
        /// </summary>
        [HttpGet("user-items/{userId}")]
        public ActionResult GetUserItemsRaw(int userId)
        {
            try
            {
                var userItems = _dbContext.Db.Queryable<user_item>()
                    .Where(x => x.user_id == userId)
                    .ToList();

                return Ok(new { 
                    message = "查询成功", 
                    count = userItems.Count,
                    items = userItems 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "查询失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 查看道具配置数据
        /// </summary>
        [HttpGet("item-configs")]
        public ActionResult GetItemConfigs()
        {
            try
            {
                var configs = _dbContext.Db.Queryable<item_config>()
                    .Take(10)
                    .ToList();

                return Ok(new { 
                    message = "查询成功", 
                    count = configs.Count,
                    configs = configs 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "查询失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 测试联表查询
        /// </summary>
        [HttpGet("join-test/{userId}")]
        public ActionResult TestJoinQuery(int userId)
        {
            try
            {
                var result = _dbContext.Db.Queryable<user_item, item_config>((ui, ic) => new SqlSugar.JoinQueryInfos(
                    SqlSugar.JoinType.Left, ui.item_id == ic.item_no.ToString()))
                    .Where((ui, ic) => ui.user_id == userId)
                    .Select((ui, ic) => new { 
                        UserItem = ui,
                        Config = ic
                    })
                    .ToList();

                return Ok(new { 
                    message = "联表查询成功", 
                    count = result.Count,
                    data = result 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "联表查询失败", error = ex.Message });
            }
        }
    }
}
