<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>公告牌</title>
    <script src="./jquery-1.8.3.min.js"></script>
</head>
<style>
    body,
    div,
    dl,
    dt,
    dd,
    ul,
    ol,
    li,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    pre,
    form,
    fieldset,
    input,
    textarea,
    p,
    blockquote,
    th,
    td,
    em,
    img {
        padding: 0;
        margin: 0;
        outline: none;
    }

    #main {
        display: flex;
        padding: 0;
        margin: 0;
    }

    .nav li {
        display: inline-block;
        list-style: none;
        cursor: pointer;
        height: 29px;
        width: 89px;
        text-align: center;
        line-height: 34px;
        font-size: 12PX;
        font-weight: bold;
        color: #C6872A;
        padding: 0;
        margin: 0;
        float: left;
    }

    .nav {
        display: flex;
    }

    .bg1 {
        background: url(Content/Img/pai/ggp_01.jpg) no-repeat;
        width: 138px;
        height: 319px;
        display: block;
        float: left;
    }

    .bg2 {
        background: url(Content/Img/pai/cangku02.jpg);
        background-repeat: no-repeat;
        float: left;
        width: 650px;
        height: 319px;
    }
</style>

<body>
    <div id="main">
        <!-- 人物 -->
        <div class="bg1">
            <!-- <img src="./Content/Img/pai/ggp_01.jpg" alt=""> -->
            <img src="./Content/Img/pai/cangku04.jpg" onclick="parent.Load(2)" style="position: absolute;left: 94px;top: 281px;cursor: pointer;"
                alt="">
        </div>
        <!-- 右侧 -->
        <div class="bg2">
            <!-- 上边 -->
            <div>
                <ul class="nav" onselectstart="return false;">
                    <li class="nav_1" onclick="setTab('nav_',1)">贡献排行榜</li>
                    <li class="nav_2" onclick="setTab('nav_',2)">活动排行榜</li>
                    <li class="nav_3" onclick="setTab('nav_',3)">活动排行榜</li>
                    <li class="nav_4" onclick="setTab('nav_',4)">活动排行榜</li>
                    <li class="nav_5" onclick="setTab('nav_',5)">活动排行榜</li>
                    <li class="nav_6" onclick="setTab('nav_',6)">活动排行榜</li>
                </ul>
            </div>
            <!-- 下边 -->
            <div class="content_box">
                <!-- 这里要一个顶部，顶部下面要两个div，左边和右边 -->
                <div class="content_top">

                </div>
                <div>
                    <div class="content_left">
                        <table></table>
                    </div>
                    <div class="content_right">
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        $(function () {
            setTabImg();
            $(".nav_1").css("background", "url(Content/Img/pai/ggp_04.jpg) no-repeat -0px -29px");
        })
        function setTabImg() {
            for (var i = 0; i < $(".nav li").length; i++) {
                $(".nav li").eq(i).css("background", "url(Content/Img/pai/ggp_04.jpg) no-repeat -" + 89 * i + "px 0px")
            }
        }
        function setTab(name, cur) {
            setTabImg();
            $("." + name + cur).css("background", "url(Content/Img/pai/ggp_04.jpg) no-repeat -" + 89 * (cur - 1) + "px -29px");

        }
    </script>
</body>

</html>