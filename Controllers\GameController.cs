using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.Interface;
using WebApplication_HM.DTOs.ResultDTO;
using WebApplication_HM.DTOs.RequestDTO;
using WebApplication_HM.Models;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 游戏页面API控制器
    /// 为游戏前端页面提供API支持
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class GameController : ControllerBase
    {
        private readonly IPlayerService _playerService;
        private readonly ISkillService _skillService;
        private readonly IEquipmentService _equipmentService;
        private readonly ILogger<GameController> _logger;

        public GameController(
            IPlayerService playerService,
            ISkillService skillService,
            IEquipmentService equipmentService,
            ILogger<GameController> logger)
        {
            _playerService = playerService;
            _skillService = skillService;
            _equipmentService = equipmentService;
            _logger = logger;
        }

        /// <summary>
        /// 获取游戏主页数据
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>游戏主页数据</returns>
        [HttpGet("home/{userId}")]
        public ActionResult<object> GetGameHomeData(int userId)
        {
            try
            {
                // 获取玩家基础信息
                var playerInfo = _playerService.GetPlayerInfo(new PlayerInfoRequestDTO { UserId = userId });

                // 获取主战宠物
                var mainPet = _playerService.GetUserPets(new PetListRequestDTO
                {
                    UserId = userId,
                    OnlyMainPet = true
                });

                var result = new
                {
                    player = playerInfo.PlayerInfo,
                    mainPet = mainPet.Pets?.FirstOrDefault(),
                    timestamp = DateTime.Now
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取游戏主页数据失败，用户ID: {UserId}", userId);
                return StatusCode(500, new { message = "获取游戏数据失败" });
            }
        }

        /// <summary>
        /// 游戏页面路由处理
        /// </summary>
        /// <param name="page">页面名称</param>
        /// <returns>页面路径</returns>
        [HttpGet("page/{page}")]
        public ActionResult GetGamePage(string page)
        {
            try
            {
                var validPages = new[]
                {
                    "index", "battle", "map", "petinfo", "playerinfo",
                    "pasture", "malls", "depot", "propmalls", "equip"
                };

                if (!validPages.Contains(page.ToLower()))
                {
                    return NotFound(new { message = "页面不存在" });
                }

                var pagePath = $"/game/pages/{page}.html";
                return Ok(new { path = pagePath });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取游戏页面失败，页面: {Page}", page);
                return StatusCode(500, new { message = "获取页面失败" });
            }
        }

        /// <summary>
        /// 获取宠物信息页面数据（基于现有服务）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>宠物信息页面所需的完整数据</returns>
        [HttpGet("pet-info/{userId}")]
        public async Task<ActionResult<object>> GetPetInfoPageData(int userId)
        {
            try
            {
                // 1. 获取玩家信息（使用现有服务）
                var playerInfo = _playerService.GetPlayerInfo(new PlayerInfoRequestDTO { UserId = userId });

                // 2. 获取宠物列表（使用现有服务）
                var petList = _playerService.GetUserPets(new PetListRequestDTO { UserId = userId });

                // 3. 获取主宠物详细信息（使用现有服务）
                var mainPetResult = await _playerService.GetMainPet(userId);

                if (!mainPetResult.Success || mainPetResult.Pets?.FirstOrDefault() == null)
                {
                    return BadRequest("未找到主战宠物");
                }

                var mainPetInfo = mainPetResult.Pets.First();

                // 4. 装备数据通过独立接口获取，不在此处加载以提高性能
                // 前端可调用 GET /Equipment/pet/{petNo}/user/{userId} 获取装备数据

                // 5. 为每个宠物获取技能列表
                var petSkillsMap = new Dictionary<int, List<WebApplication_HM.DTOs.PetSkillDetailDTO>>();
                if (petList.Pets != null && petList.Pets.Any())
                {
                    foreach (var pet in petList.Pets)
                    {
                        try
                        {
                            var petSkills = await _skillService.GetPetSkillsAsync(pet.Id);
                            petSkillsMap[pet.Id] = petSkills;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "获取宠物技能失败，PetId: {PetId}", pet.Id);
                            petSkillsMap[pet.Id] = new List<WebApplication_HM.DTOs.PetSkillDetailDTO>();
                        }
                    }
                }

                // 6. 获取主宠物技能（保持向后兼容）
                var mainPetSkills = petSkillsMap.ContainsKey(mainPetInfo.Id)
                    ? petSkillsMap[mainPetInfo.Id]
                    : await _skillService.GetPetSkillsAsync(mainPetInfo.Id);

                // 7. 获取宠物详细信息（包含属性）
                var petDetail = await _playerService.GetPetDetail(new PetDetailRequestDTO
                {
                    UserId = userId,
                    PetId = mainPetInfo.PetNo
                });

                return Ok(new
                {
                    success = true,
                    message = "获取成功",
                    data = new
                    {
                        player = playerInfo.PlayerInfo,
                        pets = petList.Pets,
                        mainPet = mainPetInfo,
                        // equipment 字段已移除，使用独立接口 GET /Equipment/pet/{petNo}/user/{userId}
                        skills = mainPetSkills, // 主宠物技能（保持向后兼容）
                        petSkills = petSkillsMap, // 所有宠物的技能映射
                        attributes = petDetail.Attributes
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物信息页面数据失败，UserId: {UserId}", userId);
                return StatusCode(500, new { success = false, message = "获取数据失败" });
            }
        }

        /// <summary>
        /// 获取单个宠物详细信息
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>宠物详细信息</returns>
        [HttpGet("pet/{petId}")]
        public async Task<ActionResult<object>> GetPetDetail(int petId)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId <= 0)
                {
                    return Unauthorized(new { success = false, message = "用户未登录" });
                }

                // 获取宠物基本信息
                var petList = _playerService.GetUserPets(new PetListRequestDTO { UserId = userId });
                var pet = petList.Pets?.FirstOrDefault(p => p.Id == petId);

                if (pet == null)
                {
                    return NotFound(new { success = false, message = "宠物不存在" });
                }

                // 获取宠物详细属性
                var petDetail = await _playerService.GetPetDetail(new PetDetailRequestDTO
                {
                    UserId = userId,
                    PetId = pet.PetNo
                });

                return Ok(new
                {
                    success = true,
                    message = "获取成功",
                    data = new
                    {
                        id = pet.Id,
                        name = pet.Name,
                        level = pet.Level,
                        element = pet.Element,
                        realm = pet.Realm,
                        hp = pet.Hp,
                        mp = pet.Mp,
                        growth = pet.Growth,
                        petNo = pet.PetNo,
                        isMain = pet.IsMain,
                        status = pet.Status,
                        attributes = petDetail.Attributes
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物详细信息失败，PetId: {PetId}", petId);
                return StatusCode(500, new { success = false, message = "获取宠物信息失败" });
            }
        }

        /// <summary>
        /// 切换主战宠物
        /// </summary>
        /// <param name="request">切换宠物请求</param>
        /// <returns>切换结果</returns>
        [HttpPost("switch-pet")]
        public async Task<ActionResult<object>> SwitchPet([FromBody] SwitchPetRequest request)
        {
            try
            {
                if (request.UserId <= 0 || request.PetId <= 0)
                {
                    return BadRequest("用户ID和宠物ID不能为空");
                }

                // 使用现有的SetMainPet服务
                var result = _playerService.SetMainPet(new SetMainPetRequestDTO
                {
                    UserId = request.UserId,
                    PetId = request.PetId
                });

                if (result.Success)
                {
                    // 获取新的主宠物信息
                    var newMainPetResult = await _playerService.GetMainPet(request.UserId);
                    if (newMainPetResult.Success && newMainPetResult.Pets?.FirstOrDefault() != null)
                    {
                        var newMainPetInfo = newMainPetResult.Pets.First();
                        // 装备数据通过独立接口获取，提高切换性能
                        // 前端可调用 GET /Equipment/pet/{petNo}/user/{userId} 获取装备数据

                        return Ok(new
                        {
                            success = true,
                            message = "切换主战宠物成功",
                            data = new
                            {
                                petInfo = newMainPetInfo
                                // equipment 字段已移除，使用独立接口获取
                            }
                        });
                    }
                    else
                    {
                        return BadRequest(new { success = false, message = "获取新主战宠物信息失败" });
                    }
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换主战宠物失败，UserId: {UserId}, PetId: {PetId}", request.UserId, request.PetId);
                return StatusCode(500, new { success = false, message = "切换宠物失败" });
            }
        }

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        /// <returns>用户ID</returns>
        private int GetCurrentUserId()
        {
            try
            {
                // 1. 优先从中间件设置的上下文获取用户ID
                if (HttpContext.Items.TryGetValue("UserId", out var contextUserId) &&
                    contextUserId is int userId && userId > 0)
                {
                    return userId;
                }

                // 2. 从Session中获取用户ID
                var sessionUserId = GetUserIdFromSession();
                if (sessionUserId > 0)
                {
                    return sessionUserId;
                }

                // 3. 从请求头中获取用户ID（用于API调用）
                var headerUserId = GetUserIdFromHeaders();
                if (headerUserId > 0)
                {
                    return headerUserId;
                }

                // 4. 从查询参数中获取用户ID（用于测试）
                var queryUserId = GetUserIdFromQuery();
                if (queryUserId > 0)
                {
                    return queryUserId;
                }

                // 5. 开发环境下的默认测试用户
                if (HttpContext.RequestServices.GetService<IWebHostEnvironment>()?.IsDevelopment() == true)
                {
                    _logger.LogWarning("开发环境：使用默认测试用户ID 1");
                    return 1;
                }

                // 6. 生产环境下返回0表示未认证
                _logger.LogWarning("未找到有效的用户身份信息");
                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户ID时发生错误");
                return 0;
            }
        }

        /// <summary>
        /// 从Session中获取用户ID
        /// </summary>
        /// <returns>用户ID</returns>
        private int GetUserIdFromSession()
        {
            try
            {
                var userIdString = HttpContext.Session.GetString("UserId");
                if (!string.IsNullOrEmpty(userIdString) && int.TryParse(userIdString, out var userId))
                {
                    return userId;
                }
                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从Session获取用户ID失败");
                return 0;
            }
        }

        /// <summary>
        /// 从请求头中获取用户ID
        /// </summary>
        /// <returns>用户ID</returns>
        private int GetUserIdFromHeaders()
        {
            try
            {
                // 从自定义请求头获取用户ID
                var userIdHeader = HttpContext.Request.Headers["X-User-Id"].FirstOrDefault();
                if (!string.IsNullOrEmpty(userIdHeader) && int.TryParse(userIdHeader, out var userId))
                {
                    return userId;
                }

                // 从游戏客户端传递的用户ID头获取
                var gameUserIdHeader = HttpContext.Request.Headers["X-Game-User-Id"].FirstOrDefault();
                if (!string.IsNullOrEmpty(gameUserIdHeader) && int.TryParse(gameUserIdHeader, out var gameUserId))
                {
                    return gameUserId;
                }

                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从请求头获取用户ID失败");
                return 0;
            }
        }

        /// <summary>
        /// 从查询参数中获取用户ID
        /// </summary>
        /// <returns>用户ID</returns>
        private int GetUserIdFromQuery()
        {
            try
            {
                var userIdQuery = HttpContext.Request.Query["userId"].FirstOrDefault();
                if (!string.IsNullOrEmpty(userIdQuery) && int.TryParse(userIdQuery, out var userId))
                {
                    return userId;
                }
                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从查询参数获取用户ID失败");
                return 0;
            }
        }
    }

    /// <summary>
    /// 切换宠物请求DTO
    /// </summary>
    public class SwitchPetRequest
    {
        public int UserId { get; set; }
        public int PetId { get; set; }
    }
}
