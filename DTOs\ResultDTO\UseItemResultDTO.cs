namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 使用道具结果DTO
    /// </summary>
    public class UseItemResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 道具ID
        /// </summary>
        public string ItemId { get; set; }

        /// <summary>
        /// 使用数量
        /// </summary>
        public int UsedCount { get; set; }

        /// <summary>
        /// 剩余数量
        /// </summary>
        public long RemainingCount { get; set; }

        /// <summary>
        /// 使用效果描述
        /// </summary>
        public string EffectDescription { get; set; }

        /// <summary>
        /// 获得的经验
        /// </summary>
        public int GainedExp { get; set; }

        /// <summary>
        /// 获得的金币
        /// </summary>
        public int GainedGold { get; set; }

        /// <summary>
        /// 获得的元宝
        /// </summary>
        public int GainedYuanbao { get; set; }

        /// <summary>
        /// 恢复的生命值
        /// </summary>
        public int RestoredHp { get; set; }

        /// <summary>
        /// 恢复的法力值
        /// </summary>
        public int RestoredMp { get; set; }
    }
} 