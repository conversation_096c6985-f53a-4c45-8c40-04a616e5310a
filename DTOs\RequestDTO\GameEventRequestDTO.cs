namespace WebApplication_HM.DTOs.RequestDTO
{
    /// <summary>
    /// 游戏事件请求DTO
    /// </summary>
    public class GameEventRequestDTO
    {
        /// <summary>
        /// 事件类型
        /// </summary>
        public string EventType { get; set; }

        /// <summary>
        /// 玩家ID
        /// </summary>
        public int PlayerId { get; set; }

        /// <summary>
        /// 玩家名称
        /// </summary>
        public string PlayerName { get; set; }

        /// <summary>
        /// 事件描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 是否广播
        /// </summary>
        public bool Broadcast { get; set; }
    }
} 