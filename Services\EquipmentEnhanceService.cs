using Microsoft.Extensions.Logging;
using SqlSugar;
using WebApplication_HM.Sugar;
using WebApplication_HM.Models;
using WebApplication_HM.DTOs.Common;
using WebApplication_HM.Services;
using WebApplication_HM.Interface;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 装备强化服务
    /// 基于老项目EquipmentProcess.cs中的强化逻辑完整迁移
    /// </summary>
    public class EquipmentEnhanceService
    {
        private readonly ILogger<EquipmentEnhanceService> _logger;
        private readonly DbContext _dbContext;
        private readonly IEquipmentRepository _equipmentRepository;
        private readonly PropService _propService;
        private readonly PlayerService _playerService;
        
        // 强化CD限制 (毫秒)
        private const int ENHANCE_CD_MS = 200;
        
        // 强化石道具ID (基于老项目)
        private const string ENHANCE_STONE_ITEM_ID = "2017060302";
        
        // 基础强化金币消耗 (20万)
        private const long BASE_ENHANCE_COST = 200000;
        
        // 最大强化等级
        private const int MAX_ENHANCE_LEVEL = 20;
        
        // 不可强化的装备类型
        private static readonly string[] NON_ENHANCEABLE_TYPES = 
        {
            "卡牌右", "卡牌左", "灵饰", "法宝", "背饰"
        };
        
        // 强化CD记录 (用户ID -> 最后强化时间)
        private static readonly Dictionary<int, DateTime> _enhanceCdRecord = new();

        public EquipmentEnhanceService(
            ILogger<EquipmentEnhanceService> logger,
            DbContext dbContext,
            IEquipmentRepository equipmentRepository,
            PropService propService,
            PlayerService playerService)
        {
            _logger = logger;
            _dbContext = dbContext;
            _equipmentRepository = equipmentRepository;
            _propService = propService;
            _playerService = playerService;
        }

        /// <summary>
        /// 装备强化 (简化版本，基础框架)
        /// 基于老项目EquipmentProcess.cs中的强化方法完整迁移
        /// </summary>
        /// <param name="userEquipmentId">用户装备ID</param>
        /// <returns></returns>
        public async Task<ApiResult> EnhanceEquipmentAsync(int userEquipmentId)
        {
            try
            {
                // 1. 获取装备信息
                var equipment = await _equipmentRepository.GetEquipmentByIdAsync(userEquipmentId);
                if (equipment == null)
                    return ApiResult.CreateError("装备不存在");

                // 2. 检查强化CD
                var cdCheckResult = CheckEnhanceCd(equipment.user_id);
                if (!cdCheckResult.Success)
                    return cdCheckResult;

                // 3. 检查装备强化等级
                if (equipment.strengthen_level == null)
                    equipment.strengthen_level = 0;

                if (equipment.strengthen_level >= MAX_ENHANCE_LEVEL)
                    return ApiResult.CreateError("该装备已强化到最高等级,无法再强化!");

                // 4. 获取装备详细信息
                var equipDetail = await _equipmentRepository.GetEquipmentDetailAsync(equipment.equip_id);
                if (equipDetail == null)
                    return ApiResult.CreateError("装备配置不存在");

                // 5. 获取装备类型名称并检查是否可强化 - 使用正确的三表关联
                var equipTypeInfo = await _dbContext.Db.Queryable<equipment>()
                    .LeftJoin<equipment_type>((e, et) => e.equip_type_id == et.equip_type_id)
                    .Where((e, et) => e.equip_id == equipment.equip_id)
                    .Select((e, et) => et.type_name)
                    .FirstAsync();
                var typeName = equipTypeInfo ?? "未知类型";

                if (NON_ENHANCEABLE_TYPES.Contains(typeName))
                    return ApiResult.CreateError("此部位装备无法被强化!");

                // 6. 计算强化材料消耗
                var materialCost = CalculateEnhanceMaterialCost(equipment.strengthen_level.Value, equipDetail.element_limit == "巫");
                var goldCost = BASE_ENHANCE_COST * (equipment.strengthen_level.Value + 1);

                // TODO: 7-10. 实现材料和金币检查及消耗逻辑

                // 11. 强化成功，提升等级
                equipment.strengthen_level++;
                equipment.update_time = DateTime.Now;

                var success = await _equipmentRepository.UpdateEquipmentAsync(equipment);
                if (!success)
                    return ApiResult.CreateError("强化失败，请重试");

                // 12. 记录强化CD
                RecordEnhanceCd(equipment.user_id);

                // 13. 记录操作日志
                await LogEnhanceOperationAsync(equipment.user_id, userEquipmentId, equipment.strengthen_level.Value,
                    materialCost, goldCost, "SUCCESS", "强化成功");

                // 14. 发送强化成功公告
                var equipName = await GetEquipmentNameAsync(Convert.ToInt32(equipment.equip_id));
                var announcement = $"恭喜您的{equipName}强化到了{equipment.strengthen_level}级,属性再上一个台阶!";

                return ApiResult.CreateSuccess("强化成功!", new {
                    enhanceLevel = equipment.strengthen_level,
                    materialCost = materialCost,
                    goldCost = goldCost,
                    announcement = announcement
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "装备强化失败，装备ID: {EquipmentId}", userEquipmentId);
                return ApiResult.CreateError("装备强化失败");
            }
        }

        /// <summary>
        /// 检查强化CD
        /// 基于老项目的CD限制逻辑
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns></returns>
        private ApiResult CheckEnhanceCd(int userId)
        {
            if (_enhanceCdRecord.ContainsKey(userId))
            {
                var lastEnhanceTime = _enhanceCdRecord[userId];
                var timeDiff = (DateTime.Now - lastEnhanceTime).TotalMilliseconds;
                
                if (timeDiff < ENHANCE_CD_MS)
                {
                    return ApiResult.CreateError("强化CD未到!");
                }
            }
            
            return ApiResult.CreateSuccess();
        }

        /// <summary>
        /// 记录强化CD
        /// </summary>
        /// <param name="userId">用户ID</param>
        private void RecordEnhanceCd(int userId)
        {
            _enhanceCdRecord[userId] = DateTime.Now;
        }

        /// <summary>
        /// 计算强化材料消耗
        /// 基于老项目的强化石消耗公式
        /// </summary>
        /// <param name="currentLevel">当前强化等级</param>
        /// <param name="isWuEquipment">是否为巫族装备</param>
        /// <returns>需要消耗的强化石数量</returns>
        private int CalculateEnhanceMaterialCost(int currentLevel, bool isWuEquipment)
        {
            int stoneCost = 0;
            
            if (currentLevel >= 0 && currentLevel <= 4)
            {
                stoneCost = Convert.ToInt32(Math.Pow(currentLevel + 1, 2) * 150);
            }
            else if (currentLevel >= 5 && currentLevel <= 19)
            {
                stoneCost = Convert.ToInt32(Math.Pow(currentLevel + 1, 2) * 1050);
            }
            
            // 巫族装备材料消耗翻倍
            if (isWuEquipment)
            {
                stoneCost *= 2;
            }
            
            return stoneCost;
        }

        /// <summary>
        /// 获取装备名称
        /// </summary>
        /// <param name="equipId">装备ID</param>
        /// <returns></returns>
        private async Task<string> GetEquipmentNameAsync(int equipId)
        {
            try
            {
                // 从equipment表获取装备名称，而不是equipment_detail表
                var equipment = await _dbContext.Db.Queryable<equipment>()
                    .Where(x => x.equip_id == equipId.ToString())
                    .FirstAsync();
                return equipment?.name ?? "未知装备";
            }
            catch
            {
                return "未知装备";
            }
        }

        /// <summary>
        /// 记录强化操作日志
        /// </summary>
        private async Task LogEnhanceOperationAsync(int userId, int userEquipmentId, int enhanceLevel,
            int materialCost, long goldCost, string status, string description)
        {
            try
            {
                var log = new equipment_operation_log
                {
                    user_id = userId,
                    user_equipment_id = userEquipmentId,
                    operation_type = "ENHANCE",
                    operation_data = $"强化到{enhanceLevel}级,消耗强化石{materialCost}个,金币{goldCost}",
                    result = status,
                    result_message = description,
                    cost_money = goldCost,
                    create_time = DateTime.Now
                };

                await _dbContext.Db.Insertable(log).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "记录强化日志失败");
            }
        }
    }
}
