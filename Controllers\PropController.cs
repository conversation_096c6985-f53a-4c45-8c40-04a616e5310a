using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.Models;
using WebApplication_HM.Services;
using WebApplication_HM.Interface;
using WebApplication_HM.Services.PropScript;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 道具管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class PropController : ControllerBase
    {
        private readonly PropService _propService;
        private readonly IPropRepository _propRepository;
        private readonly ILogger<PropController> _logger;

        public PropController(PropService propService, IPropRepository propRepository, ILogger<PropController> logger)
        {
            _propService = propService;
            _propRepository = propRepository;
            _logger = logger;
        }

        /// <summary>
        /// 获取用户所有道具
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>道具列表</returns>
        [HttpGet("user/{userId}")]
        public async Task<ActionResult<List<PropInfo>>> GetUserItems(int userId)
        {
            try
            {
                var items = await _propService.GetPAPAsync(userId);
                return Ok(items);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户道具失败，用户ID: {UserId}", userId);
                return StatusCode(500, "获取道具列表失败");
            }
        }

        /// <summary>
        /// 按道具ID获取用户道具
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="itemId">道具ID</param>
        /// <returns>道具信息</returns>
        [HttpGet("user/{userId}/item/{itemId}")]
        public async Task<ActionResult<PropInfo>> GetUserItemById(int userId, string itemId)
        {
            try
            {
                var item = await _propService.GetAP_IDAsync(itemId, userId);
                if (item == null)
                {
                    return NotFound("道具不存在");
                }
                return Ok(item);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户道具失败，用户ID: {UserId}, 道具ID: {ItemId}", userId, itemId);
                return StatusCode(500, "获取道具信息失败");
            }
        }

        /// <summary>
        /// 按序号获取道具
        /// </summary>
        /// <param name="itemSeq">道具序号</param>
        /// <returns>道具信息</returns>
        [HttpGet("seq/{itemSeq}")]
        public async Task<ActionResult<PropInfo>> GetItemBySeq(string itemSeq)
        {
            try
            {
                var item = await _propService.GetAP_XHAsync(itemSeq);
                if (item == null)
                {
                    return NotFound("道具不存在");
                }
                return Ok(item);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "按序号获取道具失败，序号: {ItemSeq}", itemSeq);
                return StatusCode(500, "获取道具信息失败");
            }
        }

        /// <summary>
        /// 获取用户指定位置的道具
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="position">位置（1=背包，2=仓库）</param>
        /// <returns>道具列表</returns>
        [HttpGet("user/{userId}/position/{position}")]
        public async Task<ActionResult<List<PropInfo>>> GetUserItemsByPosition(int userId, int position)
        {
            try
            {
                if (position != 1 && position != 2)
                {
                    return BadRequest("位置参数无效，1=背包，2=仓库");
                }

                var location = (PropLoaction)position;
                var items = await _propService.GetALPPAsync(userId, location);
                return Ok(items);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户指定位置道具失败，用户ID: {UserId}, 位置: {Position}", userId, position);
                return StatusCode(500, "获取道具列表失败");
            }
        }



        /// <summary>
        /// 添加道具到用户背包
        /// </summary>
        /// <param name="request">添加道具请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("add")]
        public async Task<ActionResult> AddUserItem([FromBody] AddItemRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.ItemId) || request.Count <= 0)
                {
                    return BadRequest("道具ID和数量不能为空或小于等于0");
                }

                var prop = new PropInfo
                {
                    ItemId = request.ItemId,
                    ItemCount = request.Count,
                    ItemPos = request.Position ?? 1
                };

                var success = await _propService.AddPlayerPropAsync(prop, request.UserId);
                if (success)
                {
                    return Ok(new { message = "道具添加成功" });
                }
                else
                {
                    return BadRequest("道具添加失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加道具失败，请求: {@Request}", request);
                return StatusCode(500, "添加道具失败");
            }
        }

        /// <summary>
        /// 使用道具 - 支持完整的道具脚本系统
        /// </summary>
        /// <param name="request">使用道具请求</param>
        /// <returns>操作结果</returns>
        /// <summary>
        /// 丢弃道具（真正删除并记录到丢弃表）
        /// </summary>
        [HttpPost("discard")]
        public async Task<ActionResult> DiscardItem([FromBody] DiscardItemRequest request)
        {
            try
            {
                var result = await _propService.DiscardItemAsync(request.UserId, request.ItemSeq, request.Reason);

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message,
                        data = result.Data
                    });
                }
                else
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = result.Message
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "丢弃道具失败，请求: {@Request}", request);
                return StatusCode(500, new { success = false, message = "丢弃道具失败" });
            }
        }

        /// <summary>
        /// 获取用户丢弃记录
        /// </summary>
        [HttpGet("discard-logs/{userId}")]
        public async Task<ActionResult> GetDiscardLogs(int userId, [FromQuery] int page = 1, [FromQuery] int pageSize = 20)
        {
            try
            {
                var logs = await _propService.GetDiscardLogsAsync(userId, page, pageSize);
                return Ok(logs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取丢弃记录失败，用户ID: {UserId}", userId);
                return StatusCode(500, new { success = false, message = "获取丢弃记录失败" });
            }
        }

        /// <summary>
        /// 找回丢弃的道具
        /// </summary>
        [HttpPost("recover/{logId}")]
        public async Task<ActionResult> RecoverDiscardedItem(int logId, [FromBody] int userId)
        {
            try
            {
                var result = await _propService.RecoverDiscardedItemAsync(logId, userId);

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message,
                        data = result.Data
                    });
                }
                else
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = result.Message
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "找回道具失败，记录ID: {LogId}, 用户ID: {UserId}", logId, userId);
                return StatusCode(500, new { success = false, message = "找回道具失败" });
            }
        }

        /// <summary>
        /// 移动道具位置（放入仓库等）
        /// </summary>
        [HttpPost("move")]
        public async Task<ActionResult> MoveItem([FromBody] MoveItemRequest request)
        {
            try
            {
                var success = await _propService.MoveItemPositionAsync(request.UserId, request.ItemSeq, request.NewPosition);

                if (success)
                {
                    string positionName = request.NewPosition switch
                    {
                        1 => "背包",
                        2 => "仓库",
                        3 => "丢弃",
                        _ => "未知位置"
                    };

                    return Ok(new
                    {
                        success = true,
                        message = $"道具已移动到{positionName}",
                        newPosition = request.NewPosition
                    });
                }
                else
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "移动道具失败"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动道具失败，请求: {@Request}", request);
                return StatusCode(500, new { success = false, message = "移动道具失败" });
            }
        }

        [HttpPost("use")]
        public async Task<ActionResult> UseItem([FromBody] UseItemRequest request)
        {
            try
            {
                // 1. 获取道具信息
                var item = await _propRepository.GetItemBySeqAsync(request.ItemSeq);
                if (item == null)
                {
                    return NotFound(new { success = false, message = "道具不存在" });
                }

                // 2. 执行道具脚本逻辑
                var scriptRequest = new PropScriptExecuteRequest
                {
                    UserId = request.UserId,
                    ItemId = item.ItemId,
                    UseCount = 1
                };

                var scriptResult = _propService.UseItemWithScript(scriptRequest);

                // 3. 返回结果
                if (scriptResult.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = scriptResult.Message,
                        data = scriptResult.ExtraData,
                        effectDescription = scriptResult.Message
                    });
                }
                else
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = scriptResult.Message
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "使用道具失败，请求: {@Request}", request);
                return StatusCode(500, new { success = false, message = "使用道具失败" });
            }
        }

        /// <summary>
        /// 删除道具
        /// </summary>
        /// <param name="itemSeq">道具序号</param>
        /// <returns>操作结果</returns>
        [HttpDelete("{itemSeq}")]
        public async Task<ActionResult> DeleteItem(int itemSeq)
        {
            try
            {
                var item = await _propRepository.GetItemBySeqAsync(itemSeq);
                if (item == null)
                {
                    return NotFound("道具不存在");
                }

                var success = await _propService.DeletePPAsync(item);
                if (success)
                {
                    return Ok(new { message = "道具删除成功" });
                }
                else
                {
                    return BadRequest("道具删除失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除道具失败，序号: {ItemSeq}", itemSeq);
                return StatusCode(500, "删除道具失败");
            }
        }

        /// <summary>
        /// 获取道具配置信息
        /// </summary>
        /// <param name="itemId">道具ID</param>
        /// <returns>道具配置</returns>
        [HttpGet("config/{itemId}")]
        public async Task<ActionResult<PropConfig>> GetItemConfig(string itemId)
        {
            try
            {
                var config = await _propService.GetPropConfigAsync(itemId);
                if (config == null)
                {
                    return NotFound("道具配置不存在");
                }
                return Ok(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取道具配置失败，道具ID: {ItemId}", itemId);
                return StatusCode(500, "获取道具配置失败");
            }
        }

        /// <summary>
        /// 获取合成材料道具列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>合成材料道具列表</returns>
        [HttpGet("synthesis-materials")]
        public async Task<ActionResult> GetSynthesisMaterials([FromQuery] int userId)
        {
            try
            {
                if (userId <= 0)
                {
                    return BadRequest(new { success = false, message = "用户ID无效" });
                }

                // 获取用户所有道具
                var userItems = await _propService.GetPAPAsync(userId);

                // 根据原项目分析，合成材料主要通过道具图标分类
                // 常见的合成材料图标: "491", "492", "493", "494", "495", "496", "497", "498", "499", "500"
                // 以及一些特殊的合成道具图标
                var synthesisMaterialIcons = new[] {
                    "491", "492", "493", "494", "495", "496", "497", "498", "499", "500",
                    "501", "502", "503", "504", "505", "506", "507", "508", "509", "510",
                    "511", "512", "513", "514", "515", "516", "517", "518", "519", "520"
                };

                // 筛选合成材料道具
                var synthesisMaterials = userItems.Where(item =>
                    synthesisMaterialIcons.Contains(item.ItemIcon) &&
                    item.ItemCount > 0 &&
                    item.ItemPos == 1 // 只取背包中的道具，不包括仓库
                ).Select(item => new
                {
                    道具序号 = item.ItemId,
                    道具名字 = item.ItemName,
                    道具类型ID = item.ItemId,
                    道具数量 = item.ItemCount,
                    道具图标 = item.ItemIcon,
                    道具价格 = item.ItemPrice,
                    道具说明 = item.Description
                }).ToList();

                return Ok(new {
                    success = true,
                    message = "获取合成材料成功",
                    data = synthesisMaterials
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取合成材料失败，用户ID: {UserId}", userId);
                return StatusCode(500, new { success = false, message = "获取合成材料失败" });
            }
        }

        /// <summary>
        /// 获取涅槃道具列表
        /// 根据老系统分析，涅槃道具主要是图标ID为"26"(涅槃兽类)和"23"(涅槃材料)的道具
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>涅槃道具列表</returns>
        [HttpGet("nirvana-materials")]
        public async Task<ActionResult> GetNirvanaMaterials([FromQuery] int userId)
        {
            try
            {
                if (userId <= 0)
                {
                    return BadRequest(new { success = false, message = "用户ID无效" });
                }

                _logger.LogInformation("开始获取涅槃道具列表，用户ID: {UserId}", userId);

                // 获取用户所有道具
                var userItems = await _propService.GetPAPAsync(userId);

                // 根据老系统分析，涅槃道具分类：
                // 图标ID "26": 涅槃兽类道具（如涅槃兽c123，用于转移副宠属性）
                // 图标ID "23": 涅槃材料道具（如涅槃石、护宠仙石等，用于提升成功率）
                var nirvanaMaterialIcons = new[] { "26", "23" };

                // 筛选涅槃道具
                var nirvanaMaterials = userItems.Where(item =>
                    nirvanaMaterialIcons.Contains(item.ItemIcon) &&
                    item.ItemCount > 0 &&
                    item.ItemPos == 1 // 只取背包中的道具，不包括仓库（对应老系统的读仓库=false）
                ).Select(item => new
                {
                    道具序号 = item.ItemSeq,        // 道具实例序号
                    道具名字 = item.ItemName,       // 道具名称
                    道具类型ID = item.ItemId,       // 道具类型ID
                    道具数量 = item.ItemCount,      // 道具数量
                    道具图标 = item.ItemIcon,       // 道具图标（用于分类）
                    道具价格 = item.ItemPrice,      // 道具价格
                    道具说明 = item.Description,    // 道具说明
                    道具位置 = item.ItemPos,        // 道具位置（1:背包 2:仓库）
                    道具类型 = item.ItemIcon == "26" ? "涅槃兽" : "涅槃材料" // 道具类型标识
                }).OrderBy(item => item.道具图标).ThenBy(item => item.道具名字).ToList();

                _logger.LogInformation("获取到{Count}个涅槃道具，用户ID: {UserId}", nirvanaMaterials.Count, userId);

                return Ok(new {
                    success = true,
                    message = "获取涅槃道具成功",
                    data = nirvanaMaterials,
                    count = nirvanaMaterials.Count
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取涅槃道具失败，用户ID: {UserId}", userId);
                return StatusCode(500, new { success = false, message = "获取涅槃道具失败" });
            }
        }

        /// <summary>
        /// 获取道具脚本信息
        /// </summary>
        /// <param name="itemId">道具ID</param>
        /// <returns>道具脚本</returns>
        [HttpGet("script/{itemId}")]
        public async Task<ActionResult<PropScriptInfo>> GetItemScript(string itemId)
        {
            try
            {
                var script = await _propService.GetPropScriptAsync(itemId);
                if (script == null)
                {
                    return NotFound("道具脚本不存在");
                }
                return Ok(script);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取道具脚本失败，道具ID: {ItemId}", itemId);
                return StatusCode(500, "获取道具脚本失败");
            }
        }

        /// <summary>
        /// 道具放入背包
        /// </summary>
        /// <param name="itemSeq">道具序号</param>
        /// <param name="userId">用户ID</param>
        /// <returns>操作结果</returns>
        [HttpPut("{itemSeq}/move-to-backpack")]
        public async Task<ActionResult> MoveToBackpack(string itemSeq, [FromQuery] int userId)
        {
            try
            {
                var success = await _propService.PAPTPAsync(itemSeq, userId);
                if (success)
                {
                    return Ok(new { message = "道具已放入背包" });
                }
                else
                {
                    return BadRequest("操作失败，可能是背包已满");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "道具放入背包失败，序号: {ItemSeq}, 用户ID: {UserId}", itemSeq, userId);
                return StatusCode(500, "操作失败");
            }
        }

        /// <summary>
        /// 检查用户是否拥有指定道具
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="itemId">道具ID</param>
        /// <returns>是否拥有</returns>
        [HttpGet("user/{userId}/has/{itemId}")]
        public async Task<ActionResult<bool>> HasItem(int userId, string itemId)
        {
            try
            {
                var hasItem = await _propService.OwnOrNot_PropIDAsync(itemId, userId);
                return Ok(hasItem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查道具拥有状态失败，用户ID: {UserId}, 道具ID: {ItemId}", userId, itemId);
                return StatusCode(500, "检查失败");
            }
        }
    }
}
