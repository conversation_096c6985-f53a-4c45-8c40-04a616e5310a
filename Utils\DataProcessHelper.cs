using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace WebApplication_HM.Utils
{
    /// <summary>
    /// 数据处理工具类
    /// </summary>
    public static class DataProcessHelper
    {
        #region 随机数生成
        private static readonly Random GameRandom;
        private static readonly object RandomLock = new object();

        /// <summary>
        /// 静态构造函数，初始化随机数生成器
        /// </summary>
        static DataProcessHelper()
        {
            GameRandom = new Random(Environment.TickCount);
        }

        /// <summary>
        /// 获取0-1之间的随机数，越大的数随机到的概率越小
        /// </summary>
        /// <returns>随机数值</returns>
        public static double GetRandom()
        {
            lock (RandomLock)
            {
                double randomValue = GameRandom.NextDouble(); // 生成一个0到1之间的随机数
                return 1 - randomValue; // 使用倒数处理
            }
        }
        #endregion

        #region MD5哈希
        /// <summary>
        /// 计算字符串的MD5哈希值
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>MD5哈希值的十六进制字符串</returns>
        public static string MD5Hash(string input)
        {
            using var md5 = MD5.Create();
            byte[] inputBytes = Encoding.UTF8.GetBytes(input);
            byte[] hashBytes = md5.ComputeHash(inputBytes);
            return Convert.ToHexString(hashBytes).ToLower();
        }
        #endregion

        #region 时间戳
        /// <summary>
        /// 获取当前时间戳，精确到秒
        /// </summary>
        /// <returns>Unix时间戳（秒）</returns>
        public static long GetTimestamp()
        {
            return DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        }

        /// <summary>
        /// 获取当前时间戳，精确到毫秒
        /// </summary>
        /// <returns>Unix时间戳（毫秒）</returns>
        public static long GetTimestampMillis()
        {
            return DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        }

        /// <summary>
        /// 将时间戳转换为DateTime
        /// </summary>
        /// <param name="timestamp">Unix时间戳（秒）</param>
        /// <returns>DateTime对象</returns>
        public static DateTime TimestampToDateTime(long timestamp)
        {
            return DateTimeOffset.FromUnixTimeSeconds(timestamp).LocalDateTime;
        }

        /// <summary>
        /// 将DateTime转换为时间戳
        /// </summary>
        /// <param name="dateTime">DateTime对象</param>
        /// <returns>Unix时间戳（秒）</returns>
        public static long DateTimeToTimestamp(DateTime dateTime)
        {
            return new DateTimeOffset(dateTime).ToUnixTimeSeconds();
        }
        #endregion

        #region JSON处理
        /// <summary>
        /// 将对象序列化为JSON字符串
        /// </summary>
        /// <param name="obj">要序列化的对象</param>
        /// <returns>JSON字符串，如果序列化失败则返回空字符串</returns>
        public static string JsonToStr(object obj)
        {
            try
            {
                return JsonSerializer.Serialize(obj, new JsonSerializerOptions
                {
                    WriteIndented = false,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 将JSON字符串反序列化为对象
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="json">JSON字符串</param>
        /// <returns>反序列化后的对象，如果失败则返回类型默认值</returns>
        public static T StrToJson<T>(string json)
        {
            try
            {
                return JsonSerializer.Deserialize<T>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true // 使用大小写不敏感的属性名匹配
                });
            }
            catch
            {
                return default;
            }
        }

        /// <summary>
        /// 将对象序列化为格式化的JSON字符串
        /// </summary>
        /// <param name="obj">要序列化的对象</param>
        /// <returns>格式化的JSON字符串，如果序列化失败则返回空字符串</returns>
        public static string JsonToStrFormatted(object obj)
        {
            try
            {
                return JsonSerializer.Serialize(obj, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
            }
            catch
            {
                return string.Empty;
            }
        }
        #endregion
    }
} 