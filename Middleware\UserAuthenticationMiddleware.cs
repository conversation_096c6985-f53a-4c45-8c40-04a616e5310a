using Microsoft.AspNetCore.Http;
using System.Text.Json;

namespace WebApplication_HM.Middleware
{
    /// <summary>
    /// 用户认证中间件
    /// 处理用户身份验证和Session管理
    /// </summary>
    public class UserAuthenticationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<UserAuthenticationMiddleware> _logger;

        public UserAuthenticationMiddleware(RequestDelegate next, ILogger<UserAuthenticationMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // 处理用户认证
            await ProcessUserAuthentication(context);

            // 继续处理请求
            await _next(context);
        }

        /// <summary>
        /// 处理用户认证逻辑
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        private async Task ProcessUserAuthentication(HttpContext context)
        {
            try
            {
                // 跳过静态文件和非API请求
                if (ShouldSkipAuthentication(context))
                {
                    return;
                }

                // 从多个来源获取用户ID
                var userId = GetUserIdFromRequest(context);
                
                if (userId > 0)
                {
                    // 验证用户ID的有效性
                    if (await ValidateUserId(userId))
                    {
                        // 设置用户ID到Session（如果还没有设置）
                        if (context.Session.GetInt32("UserId") != userId)
                        {
                            context.Session.SetInt32("UserId", userId);
                            _logger.LogInformation($"用户 {userId} 身份验证成功，已设置Session");
                        }

                        // 设置用户ID到请求上下文，供Controller使用
                        context.Items["UserId"] = userId;
                    }
                    else
                    {
                        _logger.LogWarning($"用户ID {userId} 验证失败");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "用户认证处理过程中发生错误");
            }
        }

        /// <summary>
        /// 判断是否应该跳过认证
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <returns>是否跳过</returns>
        private bool ShouldSkipAuthentication(HttpContext context)
        {
            var path = context.Request.Path.Value?.ToLower() ?? "";

            // 跳过静态文件
            if (path.Contains(".") && (path.EndsWith(".js") || path.EndsWith(".css") || 
                path.EndsWith(".html") || path.EndsWith(".png") || path.EndsWith(".jpg") || 
                path.EndsWith(".gif") || path.EndsWith(".ico")))
            {
                return true;
            }

            // 跳过登录和注册接口
            if (path.Contains("/api/player/login") || path.Contains("/api/player/register"))
            {
                return true;
            }

            // 跳过健康检查和Swagger
            if (path.Contains("/health") || path.Contains("/swagger"))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 从请求中获取用户ID
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <returns>用户ID</returns>
        private int GetUserIdFromRequest(HttpContext context)
        {
            // 1. 从Session获取
            var sessionUserId = context.Session.GetInt32("UserId");
            if (sessionUserId.HasValue && sessionUserId.Value > 0)
            {
                return sessionUserId.Value;
            }

            // 2. 从请求头获取
            var userIdHeader = context.Request.Headers["X-User-Id"].FirstOrDefault();
            if (!string.IsNullOrEmpty(userIdHeader) && int.TryParse(userIdHeader, out var headerUserId))
            {
                return headerUserId;
            }

            var gameUserIdHeader = context.Request.Headers["X-Game-User-Id"].FirstOrDefault();
            if (!string.IsNullOrEmpty(gameUserIdHeader) && int.TryParse(gameUserIdHeader, out var gameUserId))
            {
                return gameUserId;
            }

            // 3. 从查询参数获取（主要用于测试）
            var userIdQuery = context.Request.Query["userId"].FirstOrDefault();
            if (!string.IsNullOrEmpty(userIdQuery) && int.TryParse(userIdQuery, out var queryUserId))
            {
                return queryUserId;
            }

            // 4. 从JWT Token获取（如果实现了JWT）
            // TODO: 实现JWT Token解析

            return 0;
        }

        /// <summary>
        /// 验证用户ID的有效性
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否有效</returns>
        private async Task<bool> ValidateUserId(int userId)
        {
            try
            {
                // 这里可以添加数据库查询来验证用户是否存在
                // 为了性能考虑，可以添加缓存
                
                // 简单验证：用户ID必须大于0
                if (userId <= 0)
                {
                    return false;
                }

                // TODO: 添加数据库验证逻辑
                // var userService = context.RequestServices.GetService<IUserService>();
                // return await userService.UserExistsAsync(userId);

                // 暂时返回true，表示所有正整数用户ID都有效
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"验证用户ID {userId} 时发生错误");
                return false;
            }
        }
    }

    /// <summary>
    /// 用户认证中间件扩展方法
    /// </summary>
    public static class UserAuthenticationMiddlewareExtensions
    {
        /// <summary>
        /// 添加用户认证中间件
        /// </summary>
        /// <param name="builder">应用程序构建器</param>
        /// <returns>应用程序构建器</returns>
        public static IApplicationBuilder UseUserAuthentication(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<UserAuthenticationMiddleware>();
        }
    }
}
