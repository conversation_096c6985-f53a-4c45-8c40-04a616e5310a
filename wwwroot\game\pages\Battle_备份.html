﻿<!DOCTYPE html
	PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<title>无标题文档</title>
	<script src="./Content/Javascript/petList.js?123"></script>
	<script src="/game/js/game-api-adapter.js"></script>
	<script src="/game/js/battle-api-adapter.js"></script>
	<style type="text/css">
		body {
			/* ReSharper disable once InvalidValue */
			filter: gray;
			margin: 0;
			font-size: 12px;
			margin-left: 8px;
			margin-top: 8px;
		}

		.yingbi {
			POSITION: ABSOLUTE;
			width: 1px;

			height: 1px;

		}

		#info {
			position: absolute;
			left: 8px;
			top: 8px;
			border: 0;
			z-index: 98
		}

		.怪物名字 {
			white-space: nowrap
		}

		#pname {
			padding-top: 3px;
			text-align: center;
			position: absolute;
			z-index: 101;
			left: 8px;
			top: 2px;
			border: 0;
			height: 19px;
			width: 147px;
			background: url(img/zd01.png);
			background-size: 147px 19px;
		}

		#ico {
			position: absolute;
			z-index: 101;
			left: 5px;
			top: 21px;
			border: 0;
			height: 47px;
			width: 90px;
			background: url(img/zd02.png);
			background-size: 90px 47px;

		}

		.c {
			opacity: 0.4;
			filter: progid:DXImageTransform.Microsoft.Alpha(style=0, opacity=40, finishOpacity=100);
		}

		#info_page {
			position: absolute;
			z-index: 999;
			left: 0;
			top: 0;
			border: 0;
			height: 48px;
			width: 160px;
		}

		#hp {
			position: absolute;
			z-index: 100;
			left: 70px;
			top: 24px;
			border: 0;
			height: 16px;
			width: 160px;
		}

		table {
			display: table;
			border-collapse: separate;
			border-spacing: 0;
			border-color: grey;
		}

		td,
		th {
			display: table-cell;
			vertical-align: inherit;
		}

		#mp {
			position: absolute;
			z-index: 100;
			left: 70px;
			top: 41px;
			border: 0;
			height: 16px;
			width: 160px;
		}

		#exp {
			position: absolute;
			z-index: 100;
			left: 70px;
			top: 59px;
			border: 0;
			height: 11px;
			width: 160px;
		}

		#hp_show {
			position: absolute;
			left: 106px;
			top: 27px;
			color: rgb(0, 0, 0);
			z-index: 100000;
			font-size: 0.8em;
		}

		#mp_show {
			position: absolute;
			left: 106px;
			top: 42px;
			color: rgb(0, 0, 0);
			z-index: 100000;
			font-size: 0.8em;
		}

		#exp_show {
			position: absolute;
			left: 106px;
			top: 59px;
			color: rgb(0, 0, 0);
			z-index: 100000;
			font-size: 0.8em;
			padding-left: 2px;
		}

		#petname_label {
			position: absolute;
			left: 2px;
			top: 81px;
			font-size: 12px;
			text-align: center;
			color: rgb(3, 147, 213);
			z-index: 1000;
			width: 100px;
		}

		#InfoIco {
			width: 31px;
			height: 37px;
			z-index: 4;
			top: 0;
			left: 0;
			position: absolute;
			background: url(img/dr02.gif);
		}

		#cw {
			position: absolute;
			left: 10px;
			top: 120px;
		}

		.cw {
			height: 311px;
			width: 788px;
			z-index: 10000;
			POSITION: ABSOLUTE
		}

		#gw {
			position: absolute;
			left: 530px;
			top: 120px;
			width: 250px;
			height: 180px;
			opacity: 100;
		}

		#gwInfo {
			position: absolute;
			left: 620px;
			top: 49px;
			color: rgb(51, 136, 0);
			font-size: 0.8em;
			z-index: 3;
		}

		#gwName {
			width: 114px;
			height: 11px;
			z-index: 5;
			top: 0;
			left: 15px;
			position: absolute;
			padding-left: 6px;
			padding-top: 2px
		}

		#hpIco {
			position: absolute;
			left: 620px;
			top: 49px;
			color: rgb(51, 136, 0);
			font-size: 0.8em;
			z-index: 1;
		}

		#gwimg {
			opacity: 0.7;
			filter: progid:DXImageTransform.Microsoft.Alpha(style=0, opacity=70, finishOpacity=100)
		}

		#hpInfo {
			width: 96px;
			height: 11px;
			position: absolute;
			left: 636px;
			top: 65px;
			z-index: 3;
			padding-left: 13px;
		}

		#gwhpvalue {
			width: 96px;
			height: 11px;
			padding-left: 18px;
			left: 6px;
			position: absolute;
			color: #412804;
			font-size: 10px;
			top: 0;
		}

		#gwhpico {
			width: 96px;
			height: 11px;
			overflow: hidden;
			background-image: url(img/dr03.gif);
			background-position: 100% 0;
			background-repeat: no-repeat;
		}

		#ghp {
			width: auto;
			height: 11px;
			background-image: url(img/dr04.gif);
			background-repeat: repeat-x;
		}

		#timev {
			width: 81px;
			height: 52px;
			background-image: url(img/db.gif);
			position: absolute;
			left: 361px;
			top: 10px;
			padding-top: 25px;
			text-align: center;
			font-weight: bold;
			color: #6A2F06;
			font-size: 25px;
			font-family: 黑体
		}

		#gj {
			position: absolute;
			left: 8px;
			top: 251px;
			background-image: url(img/zdzsk.png);
			background-size: 778px 71px;
			width: 778px;
			height: 71px;
		}

		#ico2 {
			position: absolute;
			width: 37px;
			height: 34px;
			left: 259px;
			top: 7px;
			cursor: pointer;
		}

		#ico3 {
			position: absolute;
			width: 37px;
			height: 34px;
			left: 299px;
			top: 10px;
			cursor: pointer;
		}

		#ico4 {
			position: absolute;
			width: 37px;
			height: 34px;
			left: 340px;
			z-index: 99999999;
			top: 10px;
			cursor: pointer;
		}

		#ico5 {
			position: absolute;
			width: 37px;
			height: 34px;
			left: 384px;
			top: 10px;
			cursor: pointer;
		}

		#ico6 {
			position: absolute;
			width: 37px;
			height: 34px;
			left: 428px;
			top: 10px;
			cursor: pointer;
		}

		#ico7 {
			position: absolute;
			width: 37px;
			height: 34px;
			left: 472px;
			top: 10px;
			cursor: pointer;
		}

		#ico8 {
			position: absolute;
			width: 37px;
			height: 34px;
			left: 513px;
			top: 10px;
			cursor: pointer;
		}

		#state {
			position: absolute;
			left: 620px;
			top: 150px;
			font-weight: bold;
			width: 250px;
			height: auto;
			font-family: 华文新魏;
			font-size: 16px;
			color: yellow;
			z-index: 1000;
		}

		#sh {
			color: #F00;
		}

		.window {
			position: absolute;
			left: 270px;
			height: 160px;
			top: 87px;
			color: #e1e1e1;
			line-height: 1.7;
			width: 246px;
			padding: 10px;
			z-index: 10000;
			overflow-y: auto;
			background-color: #025477;
			opacity: 0.68;
		}

		#gj div {
			cursor: pointer;
		}

		.hc {
			position: absolute;
			top: 0px;
			left: 0px;
			z-index: 99999;
		}
	</style>
</head>

<body>
	<div id="main" width="778px" height="311px" style="width: 778px; height: 311px; position: absolute;">
		<div id="pname"><span class="玩家名字"></span></div>
		<div id="ico">
			<div class="头像"
				style="position: absolute; z-index: 101; left: 23px; top: 1px; border: 0px; height: 36px; width: 36px;">
			</div>
		</div>
		<div id="info_page"></div>
		<div class="hc">
			<img />
		</div>
		<div id="info_page1"></div>
		<div id="hp">
			<table border="0" cellspacing="0" cellpadding="0">
				<tbody>
					<tr>
						<td>
							<img src="img/xthong01.gif" width="156" border="0px" height="16" id="php"
								style="width: 155px;" />
						</td>
						<td width="4">
							<img src="img/xthong00.gif" width="4" border="0" height="16" />
						</td>
					</tr>
				</tbody>
			</table>
		</div>
		<div id="hp_show"><span id="cwdqhp" class="生命">61541</span>/<span id="cwzdhp" class="最大生命">61541</span></div>
		<div id="mp">
			<table border="0" cellspacing="0" cellpadding="0">
				<tbody>
					<tr>
						<td>
							<img src="img/xtlan01.gif" width="155" border="0" height="16" id="pmp"
								style="width: 155px;" />
						</td>
						<td width="5">
							<img src="img/xtlan00.gif" width="5" border="0" height="16" />
						</td>
					</tr>
				</tbody>
			</table>
		</div>
		<div id="mp_show"><span class="魔法"></span>/<span class="最大魔法"></span></div>
		<div id="exp">
			<table border="0" cellspacing="0" cellpadding="0">
				<tbody>
					<tr>
						<td>
							<img src="img/xthuang01.gif" width="156" border="0" height="11" id="pexp"
								style="width: 155px;" />
						</td>
						<td width="4">
							<img src="img/xthuang00.gif" width="4" border="0" height="11" />
						</td>
					</tr>
				</tbody>
			</table>
		</div>
		<div id="exp_show"><span class="当前经验"></span>/<span class="升级经验"></span></div>
		<div id="petname"
			style="position: absolute; z-index: 100; left: 4px; top: 66px; border: 0px; height: 48px; width: 99px; background: url(img/zd03.png);background-size: 99px 48px;">
		</div>
		<div id="petname_label">
			<span class="宠物名字"></span>
			<br />
			<font color="#097603"><span class="宠物等级"></span>级</font>
		</div>
	</div>
	<div id="gwdiv"></div>
	<div id="gwInfo">
		<div id="InfoIco">
			<div style="z-index: 4; top: 12px; left: 15px; position: absolute">
				<font color="#2A9E49" size="2.5"><b><span class="怪物五行"></span></b></font>
			</div>
		</div>
		<div id="gwName">&nbsp;&nbsp;<span class="怪物名字"></span>&nbsp;LV：<span class="怪物等级"></span></div>
	</div>
	<div id="hpIco">
		<div style="width: 114px; height: 28px; top: 0px; left: 13px; position: absolute">
			<img src="img/dr01.gif" id="gwimg">
		</div>
	</div>
	<div id="hpInfo">
		<div id="gwhpvalue"><span id="gwValue" class="怪物生命"></span>/<span id="gwMaxHp" class="怪物最大生命"></span></div>
		<div id="gwhpico">
			<div id="ghp"></div>
		</div>
	</div>
	<div id="timev">KO</div>
	<div id="gj" style="z-index: 99999; display: block; display: none">
		<div onclick="zidonzhandou(0)"
			style="position: absolute; width: 37px; height: 34px; left: 218px; top: 7px; cursor: pointer;" id="ico1">
			&nbsp;&nbsp;<br>
			&nbsp;&nbsp;
		</div>
		<div onclick="$('#jntool3').toggle();$('#jntool').hide();$('#jntool2').hide();"
			style="position: absolute; width: 37px; height: 34px; left: 259px; top: 7px; cursor: pointer;" id="ico2">
			&nbsp;&nbsp;<br>
			&nbsp;&nbsp;
		</div>
		<div id="ico3" style="position: absolute; width: 37px; height: 34px; left: 299px; top: 10px; cursor: pointer;"
			onclick="gongji(null,'普通攻击')">
			&nbsp;&nbsp;<br>
			&nbsp;&nbsp;
		</div>

		<!-- 调试按钮 -->
		<div style="position: absolute; left: 10px; top: 10px; background: #fff; padding: 5px; border: 1px solid #ccc; z-index: 9999;">
			<button onclick="testBattleAPI()" style="margin: 2px;">测试战斗API</button>
			<button onclick="showDebugInfo()" style="margin: 2px;">显示调试信息</button>
		</div>
		<div id="ico4" style="position: absolute; width: 37px; height: 34px; left: 340px; top: 10px; cursor: pointer;"
			onclick="$('#jntool2').toggle();$('#jntool').hide();$('#jntool3').hide();">
			&nbsp;&nbsp;<br>
			&nbsp;&nbsp;
		</div>
		<div id="ico5" style="position: absolute; width: 37px; height: 34px; left: 384px; top: 10px; cursor: pointer;">
			&nbsp;&nbsp;<br>
			&nbsp;&nbsp;
		</div>
		<div id="ico6" onclick="$('#jntool').toggle();$('#jntool2').hide();$('#jntool3').hide();"
			style="position: absolute; width: 37px; height: 34px; left: 428px; top: 10px; cursor: pointer;">
			&nbsp;&nbsp;<br>
			&nbsp;&nbsp;
		</div>
		<div id="ico7" style="position: absolute; width: 37px; height: 34px; left: 472px; top: 10px; cursor: pointer;">
			&nbsp;&nbsp;<br>
			&nbsp;&nbsp;
		</div>
		<div id="ico8" onclick="window.location.href='BattleMap.html'"
			style="position: absolute; width: 41px; height: 45px; left: 518px; top: 10px; cursor: pointer; cursor: pointer;">
			&nbsp;&nbsp;<br>
			&nbsp;&nbsp;
		</div>
	</div>
	<div id="state"
		style="position: absolute; left: 620px; top: 150px; font-weight: bold; width: 250px; height: auto; font-family: 华文新魏; font-size: 16px; color: yellow; z-index: 1000; display: none">
		<span id="jn"></span><span style="color: #F00;" id="sh"></span>
		<br />
		<div style="color: #F3F;" id="js"></div>
		<div style="color: #9FF;" id="dixiao"></div>
		<div style="color: #6F0;" id="xixue"></div>
		<div style="color: #F0F;" id="ximo"></div>
	</div>
	<div id="returnPage" style="display: none">
		<div style="position: absolute; left: 270px; height: 160px; top: 87px; color: #e1e1e1; line-height: 1.7; width: 246px; padding: 10px; z-index:99999999; overflow-y: auto; background-color: #025477; opacity: 0.68; filter: alpha(opacity=68);"
			class="window"></div>
		<div
			style="position: absolute; left: 270px; height: 160px; top: 87px; color: #e1e1e1; line-height: 1.7; width: 246px; padding: 10px; z-index:99999999; overflow-y: auto;">
			<div class="结果"></div>
			<div class="奖励">
				获得经验：<span class="获得经验"></span><br>
				获得金币：<span class="获得金币"></span> 个 获得元宝：<span class="获得元宝"></span> 个<br>
				捕获宠物：0<br>
				获得物品：<span class="获得物品"></span><br>
				<br>
			</div>
			<span style="cursor: pointer;" onclick="window.parent.updateBattle(map)"><b>继续探险</b></span> <span
				onclick="window.parent.Load(2);" style="cursor: pointer;"><b>返回村庄</b></span><br>
		</div>
	</div>
	<img style="position:absolute;left:999999px" id="hccc" />
	<div id="jntool" class="捕捉面板"
		style="z-index:99999999;display: none; position: absolute; left: 432px; top: 110px; height: 136px; border: 0px; color: rgb(122, 147, 3); font-size: 12px; padding: 3px;">
		<table style="min-width: 274px;" border="0" cellspacing="0" cellpadding="0">
			<tbody>
				<tr>
					<td width="17">
						<img src="img/bk01.gif" width="17" height="123">
					</td>
					<td background="img/bk04.gif">
						<table width="100%" border="0" cellspacing="0" cellpadding="0">
							<tbody>
								<tr></tr>
								<tr class="捕捉球"></tr>
							</tbody>
						</table>
					</td>
					<td width="31">
						<table width="100%" border="0" cellspacing="0" cellpadding="0">
							<tbody>
								<tr>
									<td width="31">
										<img src="img/bk02.gif" width="31" height="31" onclick="$('#jntool').hide();"
											style="cursor: pointer;">
									</td>
								</tr>
								<tr>
									<td>
										<img src="img/bk03.gif" width="31" height="92">
									</td>
								</tr>
							</tbody>
						</table>
					</td>
				</tr>
			</tbody>
		</table>
		<table width="100" border="0" align="left" cellpadding="0" cellspacing="0" style="margin-left: 10px">
			<tbody>
				<tr>
					<td>
						<img src="img/jiantou.gif" width="17" height="16">
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div id="jntool2" class="技能面板"
		style="display: none; position: absolute; left: 343px; top: 110px; height: 136px; border: 0px; color: rgb(122, 147, 3); font-size: 12px; padding: 3px;z-index:99999999">
		<table style="min-width: 274px;" border="0" cellspacing="0" cellpadding="0">
			<tbody>
				<tr>
					<td width="17">
						<img src="img/bk01.gif" width="17" height="123">
					</td>
					<td background="img/bk04.gif">
						<table width="100%" border="0" cellspacing="0" cellpadding="0">
							<tbody>
								<tr></tr>
								<tr class="技能列表"></tr>
							</tbody>
						</table>
					</td>
					<td width="31">
						<table width="100%" border="0" cellspacing="0" cellpadding="0">
							<tbody>
								<tr>
									<td width="31">
										<img src="img/bk02.gif" width="31" height="31" onclick="$('#jntool2').hide()"
											style="cursor: pointer;">
									</td>
								</tr>
								<tr>
									<td>
										<img src="img/bk03.gif" width="31" height="92">
									</td>
								</tr>
							</tbody>
						</table>
					</td>
				</tr>
			</tbody>
		</table>
		<table width="100" border="0" align="left" cellpadding="0" cellspacing="0" style="margin-left: 10px">
			<tbody>
				<tr>
					<td>
						<img src="img/jiantou.gif" width="17" height="16">
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div id="jntool3" class="设置面板"
		style="z-index:99999999;display: none; position: absolute; left: 262px; top: 110px; height: 136px; border: 0px; color: rgb(122, 147, 3); font-size: 12px; padding: 3px;">
		<table style="min-width: 274px;" border="0" cellspacing="0" cellpadding="0">
			<tbody>
				<tr>
					<td width="17">
						<img src="img/bk01.gif" width="17" height="123">
					</td>
					<td background="img/bk04.gif">
						<table width="100%" border="0" cellspacing="0" cellpadding="0">
							<tbody>
								<tr></tr>
								<tr class="技能列表"></tr>
							</tbody>
						</table>
					</td>
					<td width="31">
						<table width="100%" border="0" cellspacing="0" cellpadding="0">
							<tbody>
								<tr>
									<td width="31">
										<img src="img/bk02.gif" width="31" height="31" onclick="$('#jntool2').hide()"
											style="cursor: pointer;">
									</td>
								</tr>
								<tr>
									<td>
										<img src="img/bk03.gif" width="31" height="92">
									</td>
								</tr>
							</tbody>
						</table>
					</td>
				</tr>
			</tbody>
		</table>
		<table width="100" border="0" align="left" cellpadding="0" cellspacing="0" style="margin-left: 10px">
			<tbody>
				<tr>
					<td>
						<img src="img/jiantou.gif" width="17" height="16">
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<script type="text/javascript" src="jquery-1.8.3.min.js"></script>
	<script type="text/javascript">
		var ico = "112";
		var gico = "73";
		var map = getMapIdFromUrl(); // 从URL参数获取地图ID
		var zidong = 0;
		var or = true;
		var gj = false;
		//处理键盘事件 禁止后退键（Backspace）密码或单行、多行文本框除外  
		function banBackSpace(e) {
			var ev = e || window.event;//获取event对象     
			var obj = ev.target || ev.srcElement;//获取事件源     

			var t = obj.type || obj.getAttribute('type');//获取事件源类型    

			//获取作为判断条件的事件类型  
			var vReadOnly = obj.getAttribute('readonly');
			var vEnabled = obj.getAttribute('enabled');
			//处理null值情况  
			vReadOnly = (vReadOnly == null) ? false : vReadOnly;
			vEnabled = (vEnabled == null) ? true : vEnabled;

			//当敲Backspace键时，事件源类型为密码或单行、多行文本的，  
			//并且wonly属性为true或enabled属性为false的，则退格键失效  
			var flag1 = (ev.keyCode == 8 && (t == "password" || t == "text" || t == "textarea")
				&& (vReadOnly == true || vEnabled != true)) ? true : false;

			//当敲Backspace键时，事件源类型非密码或单行、多行文本的，则退格键失效  
			var flag2 = (ev.keyCode == 8 && t != "password" && t != "text" && t != "textarea")
				? true : false;

			//判断  
			if (flag2) {
				return false;
			}
			if (flag1) {
				return false;
			}
		}

		//禁止后退键 作用于Firefox、Opera  
		document.onkeypress = banBackSpace;
		//禁止后退键  作用于IE、Chrome  
		document.onkeydown = banBackSpace;
		function setBJ(i) {

			$("#main").css("background-image", "url(Content/Img/Battle/" + i + ")");
		}
		setTimeout(function () {
			$("#gj").show();
		}, 1055);
		setTimeout(function () {

			if (map == 0) {
				window.parent.zidong1();
			}
		}, 1000);

		function zidonzhandou(i) {
			$("#gj").hide();
			or = false;
			zidong = 1;
			gongji(window.parent.jid, window.parent.jname);

		}
		function showInt(i) {
			var num = i;
			if (i > 1000000000000000000) {
				i = toDecimal(i / 1000000000000000000);
				num = i + "百京";
			}
			else if (i > 10000000000000000) {
				i = toDecimal(i / 10000000000000000);
				num = i + "京";
			}
			else if (i >= 100000000000000) {
				i = toDecimal(i / 100000000000000);
				num = i + "百兆";
			}
			else if (i >= 1000000000000) {
				i = toDecimal(i / 1000000000000);
				num = i + "兆";
			}
			else if (i >= 10000000000) {
				i = toDecimal(i / 10000000000);
				num = i + "百亿";
			} else if (i >= 100000000) {
				i = toDecimal(i / 100000000);
				num = i + "亿";
			}
			return num;
		}
		function toDecimal(x) {
			var f = parseFloat(x);
			if (isNaN(f)) {
				return;
			}
			f = Math.round(x * 100) / 100;
			return f;
		}
		function SetMap(m, z) {
			map = m;
			zidong = z;
			if (z == -1) {
				window.parent.Alert("自动战斗剩余次数不足!");

			}
		}
		var t = 10;

		// 从URL参数获取地图ID和宠物ID
		function getMapIdFromUrl() {
			const urlParams = new URLSearchParams(window.location.search);
			const mapId = urlParams.get('mapId');
			if (mapId && !isNaN(mapId)) {
				console.log('[Battle] 从URL获取地图ID:', mapId);
				return parseInt(mapId);
			}

			// 默认地图ID
			console.warn('[Battle] URL中没有地图ID参数，使用默认值100');
			return 100;
		}

		function getPetIdFromUrl() {
			const urlParams = new URLSearchParams(window.location.search);
			const petId = urlParams.get('petId');
			if (petId && !isNaN(petId)) {
				console.log('[Battle] 从URL获取宠物ID:', petId);
				return parseInt(petId);
			}

			// 尝试从parent获取
			if (typeof parent !== 'undefined' && parent.selectedPetId) {
				console.log('[Battle] 从parent获取宠物ID:', parent.selectedPetId);
				return parent.selectedPetId;
			}

			// 默认宠物ID
			console.warn('[Battle] 没有找到宠物ID，使用默认值1');
			return 1;
		}

		// 初始化战斗API系统
		function initBattleAPISystem() {
			console.log('[Battle] 初始化战斗API系统');

			// 检查API适配器是否加载
			if (typeof BattleAPI === 'undefined') {
				console.error('[Battle] BattleAPI未加载，请检查battle-api-adapter.js');
				// 可以选择回退到原有模式
				enableLegacyMode();
				return;
			}

			// 检查是否启用兼容模式
			const useCompatibilityMode = false; // 可以通过配置控制

			if (useCompatibilityMode) {
				enableLegacyMode();
				console.log('[Battle] 使用兼容模式：保持原有函数调用方式');
			} else {
				console.log('[Battle] 使用新模式：直接API调用');
			}

			// 验证必要的全局变量
			if (typeof map === 'undefined') {
				console.warn('[Battle] 地图ID未定义，使用默认值');
				map = 1;
			}

			// 设置错误处理
			window.addEventListener('unhandledrejection', function(event) {
				console.error('[Battle] 未处理的Promise错误:', event.reason);
				ErrorHandler.handleAPIError(event.reason, '战斗系统');
			});

			console.log('[Battle] 战斗API系统初始化完成');

			// 添加调试信息
			const currentPetId = getPetIdFromUrl();
			console.log('[Battle] 当前配置:', {
				mapId: map,
				userId: BattleAPI.getCurrentUserId(),
				selectedPetId: currentPetId,
				urlParams: window.location.search,
				parentData: {
					selectedPetId: window.parent?.selectedPetId,
					currentMapId: window.parent?.currentMapId,
					selectedPet: window.parent?.selectedPet
				}
			});
		}

		// 启用兼容模式（可选）
		function enableLegacyMode() {
			console.log('[Battle] 启用兼容模式');

			// 创建兼容性适配器
			if (typeof window.external === 'undefined' || !window.external.fazhao) {
				window.external = window.external || {};

				// 模拟window.external.fazhao
				window.external.fazhao = async function(skillId) {
					try {
						const selectedPetId = getPetIdFromUrl();
						const battleResult = await BattleAPI.executeBattle(map, selectedPetId, skillId);

						if (!battleResult.success) {
							throw new Error(battleResult.message);
						}

						// 转换为旧格式JSON字符串
						return JSON.stringify(battleResult.legacy);

					} catch (error) {
						console.error('[Battle] 兼容模式战斗失败:', error);
						// 返回失败格式
						return JSON.stringify({
							"战斗是否结束": "1",
							"advance": "0",
							"输出": 0,
							"己方剩余HP": 0,
							"对方剩余HP": 1000,
							"剩余魔法": 0,
							"获得经验": 0,
							"获得金币": 0,
							"获得元宝": 0,
							"获得道具": ""
						});
					}
				};

				// 模拟window.external.buzhuo
				window.external.buzhuo = async function(monsterId) {
					try {
						const result = await BattleAPI.capturePet(map, monsterId);
						return result.success ? "01" : "02"; // 01=成功, 02=失败
					} catch (error) {
						console.error('[Battle] 兼容模式捕捉失败:', error);
						return "02"; // 失败
					}
				};

				console.log('[Battle] 兼容模式：window.external已替换为API调用');
			}
		}

		// 调试函数
		function testBattleAPI() {
			console.log('[Debug] 开始测试战斗API');
			gongji(null, '测试攻击');
		}

		function showDebugInfo() {
			const info = {
				mapId: map,
				userId: BattleAPI.getCurrentUserId(),
				selectedPetId: getPetIdFromUrl(),
				battleAPILoaded: typeof BattleAPI !== 'undefined',
				gj: gj,
				zidong: zidong,
				urlParams: window.location.search,
				parentData: {
					selectedPetId: window.parent?.selectedPetId,
					currentMapId: window.parent?.currentMapId,
					selectedPet: window.parent?.selectedPet
				}
			};
			console.log('[Debug] 当前状态:', info);
			alert('调试信息已输出到控制台，请查看Console');
		}

		$(function () {
			// 初始化战斗API系统
			initBattleAPISystem();

			// 初始化地图战斗数据
			initBattleWithMapData();

			setTimeout(function () {

				if (zidong == 1 && or) {
					gongji(window.parent.jid, window.parent.jname);
				}
			}, 2000);
			setInterval(function () {
				if (t <= 0) {
					$("#timev").html("PK");
					return;
				}


				t = t - 1;
				$("#timev").html(t);
				if (t == 0) {
					gongji(window.parent.jid, window.parent.jname);
				}
			}, 1000);

		});

		// 备份原有buzhuo函数
		function buzhuo_original(id) {
			t = -1;
			zidong = 0;
			var str = window.external.buzhuo(id);
			window.parent.Alert(str);
			if (str.indexOf("01") != -1) {
				t = -1;
				$(".结果").html("捕捉成功,宠物已经放入了您的牧场!!");
				$(".获得金币").html("0");
				$(".获得元宝").html("0");
				$(".获得经验").html("0");
				$(".获得物品").html("无");
				$("#returnPage").show();
				$("#gj").hide();
				$(".捕捉面板").hide();
				$(".技能面板").hide();
				$(".奖励").show();
			} else if (str.indexOf("02") != -1) {
				t = -1;
				$(".结果").html("捕捉失败!!");
				$(".获得金币").html("0");
				$(".获得元宝").html("0");
				$(".获得经验").html("0");
				$(".获得物品").html("无");
				$("#returnPage").show();
				$("#gj").hide();
				$(".捕捉面板").hide();
				$(".技能面板").hide();
				$(".奖励").show();
			}
		}

		// 新的异步捕捉函数
		async function buzhuo(monsterId) {
			try {
				console.log(`[Battle] 尝试捕捉宠物: ${monsterId}`);

				// 停止自动战斗
				t = -1;
				zidong = 0;

				// 隐藏捕捉面板，显示等待状态
				$(".捕捉面板").hide();
				$(".结果").html("正在捕捉宠物...");

				// 调用捕捉API
				const result = await BattleAPI.capturePet(map, monsterId);

				if (result.success) {
					// 捕捉成功
					$(".结果").html("捕捉成功,宠物已经放入了您的牧场!!");
					$(".获得金币").html("0");
					$(".获得元宝").html("0");
					$(".获得经验").html("0");
					$(".获得物品").html("无");

					if (window.parent && window.parent.Alert) {
						window.parent.Alert("捕捉成功！宠物已放入牧场！");
					}
				} else {
					// 捕捉失败
					$(".结果").html("捕捉失败!!");
					$(".获得金币").html("0");
					$(".获得元宝").html("0");
					$(".获得经验").html("0");
					$(".获得物品").html("无");

					if (window.parent && window.parent.Alert) {
						window.parent.Alert(result.message || "捕捉失败");
					}
				}

				// 显示结果面板
				$("#returnPage").show();
				$("#gj").hide();
				$(".捕捉面板").hide();
				$(".技能面板").hide();
				$(".奖励").show();

			} catch (error) {
				console.error('[Battle] 捕捉宠物失败:', error);

				// 错误处理
				$(".结果").html("捕捉失败!!");
				$(".获得金币").html("0");
				$(".获得元宝").html("0");
				$(".获得经验").html("0");
				$(".获得物品").html("无");
				$("#returnPage").show();
				$("#gj").hide();
				$(".捕捉面板").hide();
				$(".技能面板").hide();
				$(".奖励").show();

				ErrorHandler.handleAPIError(error, "捕捉宠物");
			}
		}
		function readBuzhuo(json) {
			var j = $.parseJSON(json);
			var html = "<td height=\"28px\">" +
				"<table  style=\"cursor:pointer;\"  style=\"cursor:pointer\"  border=\"0\" cellspacing=\"0\" cellpadding=\"0\">" +
				"<tbody>" +
				"<tr class=\"buzhuoDIV\">" +
				"<td style=\"display:none\" width=\"10\" class=\"wpid\">{ID}</td>" +
				"<td width=\"10\"><img src=\"img/bk05.gif\" width=\"3\" height=\"22\"></td>" +
				"<td align=\"center\" background=\"img/bk.jpg\"><span>{Name}</span></td>" +
				"<td width=\"4\"><img src=\"img/bk06.gif\" width=\"4\" height=\"22\"></td>" +
				"</tr>" +
				"</tbody>" +
				"</table>" +
				"</td>";

			for (var i = 0; i < j.length; i++) {

				var ht = html.replace("{ID}", j[i].道具序号);

				ht = ht.replace("{Name}", j[i].道具名字);

				$(".捕捉球").html($(".捕捉球").html() + ht);

			}
			$(".buzhuoDIV").click(function () {
				var id = $(this).find(".wpid").html();

				buzhuo(id);

			});

		}
		var 地狱之门颜色 = ["#CCFF33", "#BBFF00", "#99DD00", "#88AA00", "#668800", "#CCFF99", "#BBFF66", "#99FF33", "#77FF00", "#66DD00", "#55AA00", "#227700", "#99FF99", "#66FF66", "#33FF33", "#00FF00", "#00DD00", "#00AA00", "#008800"];

		var 调用宠物素材 = false;
		var ys = 0;
		function readGuaiWu(json) {
			debug("读取怪物信息完毕。");
			var j = $.parseJSON(json);
			gico = j.形象;
			if (j.宠物素材) {
				调用宠物素材 = true;
				$("#gwdiv").html("<img src=\"Content/PetPhoto/z" + j.形象 + ".gif\" id=\"gw\">");
			} else {
				$("#gwdiv").html("<img src=\"Content/gpc/z" + j.形象 + ".gif\" id=\"gw\">");
			}

			if (j.是否水平翻转) {
				// ReSharper disable once InvalidValue
				$("#gw").css("filter", "FlipH Glow");
			}
			if (j.宠物名字.indexOf("魔化的") != -1) {
				$("#gw").css("filter", "XRay glow(color=#CCFF33,strength=5)");

				setInterval(function () {
					if (ys >= 地狱之门颜色.length) {
						ys = 0;
					}

					$("#gw").css("filter", "XRay glow(color=" + 地狱之门颜色[ys] + ",strength=5)");
					ys++;
				}, 100);
			}
			$(".怪物生命").html(showInt(j.生命));
			$(".怪物最大生命").html(showInt(j.最大生命));
			$(".怪物名字").html(j.宠物名字);
			$(".怪物等级").html(j.等级);
			$(".怪物五行").html(j.五行);
			gwMaxHp = j.最大生命;
			if (zidong == 1) window.parent.checkFightZD();
		}
		function debug(i) {
			return;
			//window.parent.recvMsg("sm|" + new Date().toLocaleString() + "  " + i);

		}
		var Init = 0;
		var stime = 100;
		function showPNG(t) {
			if (t == "g") t = "s";

			sI = 1;
			zI = 1;
			if (Init != 0) clearInterval(Init);
			if (t == "s") {

				Init = setInterval("play" + t + "()", stime);
			} else {
				Init = setInterval("play" + t + "()", 100);
			}

			if (形象 == 516) {

			}
		}
		var zI = 1;
		function playz() {

			zI++;
			if (zI > zCount) {
				$(".cw").addClass("yingbi").removeClass("cw");
				zI = 1;
			} else {
				$(".cw").addClass("yingbi").removeClass("cw");
			}



			$(".z" + zI).removeClass("yingbi").addClass("cw");

		}
		var sI = 1;

		function plays() {

			sI++;

			if (sI > sCount) {
				$(".cw").addClass("yingbi").removeClass("cw");
				zI = 1;
				$(".z1").removeClass("yingbi").addClass("cw");

			} else {
				$(".cw").addClass("yingbi").removeClass("cw");
			}



			$(".s" + sI).removeClass("yingbi").addClass("cw");

		}
		var 形象;
		var wuxing = "";
		var zCount = 10;
		var sCount = 31;
		var png_ = true;
		function showFlashPet(t) {
			var petl = PngPet.split('|');
			for (var i = 0; i < petl.length; i++) {
				var p = petl[i].split(',');
				if (形象 == p[0]) {
					zCount = p[1];
					sCount = p[2];
					showPNG(t);
					return;
				}
			}

			png_ = true;
			var left = "0";
			var height = "315";
			var top = "0";
			var f = "z-index:999;position:absolute";

			if (t == "g") { f = "z-index:99999;position:absolute" }
			if (形象 < 514) { left = "-250"; height = "200"; top = "100" }

			var fhtml = "<div style='margin-top:" + top + "px;margin-left:" + left + "px;" + f + "'><object classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000' id='sw' width='988' height='" + height + "' codebase='http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab'>" +
				"<param name='movie' value='Content/FlashPet/" + t + 形象 + ".swf'>" +
				"<param name='allowScriptAccess' value='always' />  " +
				"<param name='allowFullScreen' value='false' />  " +
				"<param name='quality' value='high' />  " +
				"<param name='wmode' value='transparent' />" +
				"<embed src='Content/FlashPet/" + t + 形象 + ".swf' quality='high' width='988' height='" + height + "' name='sw' align='middle' play='true' loop='false' allowScriptAccess='always' type='application/x-shockwave-flash' wmode='transparent' pluginspage='http://www.macromedia.com/go/getflashplayer'></object></div>";

			$("#info_page1").html(fhtml);
		}
		function start_Flash() {

		}
		function readPet(j) {
			if (zidong == 1) window.parent.checkFightZD();
			debug("读取宠物信息完毕。");
			var json = $.parseJSON(j);
			if (json.指定形象 != null && json.指定形象 != "") json.形象 = json.指定形象;
			形象 = json.形象;
			$(".生命").html(showInt(json.生命));

			$(".魔法").html(showInt(json.魔法));
			//<img src="Content/PetPhoto/z112.gif" id="cw" />
			if ((json.五行 == "聖" || json.五行 == "佛" || json.五行 == "萌" || json.五行 == "灵" || json.五行 == "次元" || json.五行 == "巫") && (形象 >= 503 && 形象 != 1000)) {
				wuxing = json.五行;
				showFlashPet("z");
				if (png_) {
						for (var i = 0; i <= zCount; i++) {
							$("#info_page").prepend("<img class='yingbi z" + i + "' src='Content/PetPhoto/z" + 形象 + "_" + i + ".png'>");

						}
						for (var i = 0; i <= sCount; i++) {
							$("#info_page").prepend("<img class='yingbi s" + i + "' src='Content/PetPhoto/s" + 形象 + "_" + i + ".png'>");

						}
					}


			}
			else if (json.形象 >= 1000) {
				//
				$("#info_page").html("<img style=\"width: 200px;height: 200px;margin-top: -30px;margin-left: 20px;\" src=\"Content/PetPhoto/z" + json.形象 + ".gif\" id=\"cw\">");
			} else {
				$("#info_page").html("<img src=\"Content/PetPhoto/z" + json.形象 + ".gif\" id=\"cw\">");
			}
			if (json.五行 != "聖" && json.五行 != "佛" && json.五行 != "萌" && json.五行 != "灵" && json.五行 != "次元" && json.五行 != "巫" || (json.形象 < 503 && json.形象 != 1000)) {
				$(".头像").html("<img style=\" height:36px; width:36px;\" src=\"Content/PetPhoto/t" + json.形象 + ".gif\" id=\"tx\">");
				
			} else {
				$(".头像").html("<img style=\" height:36px; width:36px;\" src=\"Content/PetPhoto/t" + json.形象 + ".png\" id=\"tx\">");
			}
			ico = json.形象;
			$(".最大生命").html(showInt(json.最大生命));
			$(".最大魔法").html(showInt(json.最大魔法));
			$(".宠物等级").html(json.等级);
			$(".当前经验").html(window.external.getExp_(json.当前经验, json.等级));
			$(".宠物名字").html(json.宠物名字);

			var html = "<td style='float:left' height=\"28px\" >" +
				"<table  style=\"cursor:pointer;\"  style=\"cursor:pointer\"  border=\"0\" cellspacing=\"0\" cellpadding=\"0\">" +
				"<tbody>" +
				"<tr class=\"jinengDIV\">" +
				"<td style=\"display:none\" width=\"10\" class=\"wpid\">{ID}</td>" +
				"<td style=\"display:none\" width=\"10\" class=\"wpnm\">{Name}</td>" +
				"<td style=\"display:none\" width=\"10\" class=\"wpmp\">{MP}</td>" +
				"<td width=\"10\"><img src=\"img/bk05.gif\" width=\"3\" height=\"22\"></td>" +
				"<td align=\"center\" background=\"img/bk.jpg\"><span>{Name}</span></td>" +
				"<td width=\"4\"><img src=\"img/bk06.gif\" width=\"4\" height=\"22\"></td>" +
				"</tr>" +
				"</tbody>" +
				"</table>" +
				"</td>";


			var 技能 = json.技能显示.split(",");

			var shtml;
			for (var i = 0; i < 技能.length; i++) {
				var 技能组 = 技能[i].split("|");
				if (技能组.length >= 2) {
					shtml = html.replace("{ID}", 技能组[2]);
					shtml = shtml.replace("{Name}", 技能组[0]);
					shtml = shtml.replace("{Name}", 技能组[0]);
					shtml = shtml.replace("{MP}", 技能组[3]);
					if (技能组[4] != "true") {
						$(".技能列表").html($(".技能列表").html() + shtml);
					}
				}

			}
			shtml = html.replace("{ID}", null);
			shtml = shtml.replace("{Name}", "普通攻击");
			shtml = shtml.replace("{Name}", "普通攻击");
			shtml = shtml.replace("{MP}", "0");
			$(".技能列表").html($(".技能列表").html() + shtml);
			$(".技能面板 .jinengDIV").click(function () {
				var id = $(this).find(".wpid").html();
				var xmp = $(this).find(".wpmp").html();
				$(".技能面板").hide();

				if (parseInt(cwMp) < parseInt(xmp)) {
					window.parent.Alert("主人我魔法值不够啦~~QWQ");
					return;
				}
				gongji(id, $(this).find(".wpnm").html());

			});
			$(".设置面板 .jinengDIV").click(function () {
				var id = $(this).find(".wpid").html();
				var name = $(this).find(".wpnm").html();
				$(".设置面板").hide();
				window.parent.setJN(id, name);
				window.parent.Alert("设置自动技能成功.");

			});
			cwMaxEXP = window.external.getExp(json.等级);
			cwHp = json.生命;
			cwMaxHp = json.最大生命;
			cwMp = json.魔法;
			cwMaxMp = json.最大魔法;
			cwEXP = window.external.getExp_(json.当前经验, json.等级);
			$(".升级经验").html(cwMaxEXP);
			updateUI();
		}
		var cwEXP;
		var cwMaxEXP;
		function updateUI() {


			var returnWidth = parseInt(cwHp) / parseInt(cwMaxHp);


			if (returnWidth <= 0) {
				returnWidth = "1px";
			}
			$("#php").width(155 * returnWidth);

			returnWidth = parseInt(cwMp) / parseInt(cwMaxMp);


			if (returnWidth <= 0) {
				returnWidth = "1px";
			}
			$("#pmp").width(155 * returnWidth);

			returnWidth = parseInt(cwEXP) / parseInt(cwMaxEXP);
			if (returnWidth <= 0) {
				returnWidth = "1px";
			}

			//pexp
			$("#pexp").width(155 * returnWidth);

		}
		var gwsh = "";
		var gwMaxHp;
		var gwHp = "";
		var jsons;
		var cwHp = "";
		var cwMaxHp = "";
		var cwMp = "";
		var cwMaxMp = "";
		var jiasheng = 0;
		var dixiao = 0;
		var ximo = 0;
		var xixue = 0;
		var jn = "普通攻击";
		// 备份原有gongji函数
		function gongji_original(id, jnname) {
			if (gj) {
				return;
			}
			if (zidong == 1) window.parent.checkFightZD();
			var json = window.external.fazhao(id);
			var j = $.parseJSON(json);

			if (j.战斗是否结束 == "10") {
				setTimeout(function () {
					if (zidong == 1) {
						window.parent.zidong(map);
					}
				}, 100);
				window.parent.Alert("战斗太快啦，重新点击一下吧！")
				return;
			}
			if (j.战斗是否结束 == "2") {
				setTimeout(function () {
					if (zidong == 1) {
						window.parent.zidong(map);
					}
				}, 100);
				window.parent.Alert("世界BOSS已被其他人击杀！")
				return;
			}
			if (j.战斗是否结束 == "3") {
				setTimeout(function () {
					if (zidong == 1) {
						window.parent.zidong(map);
					}
				}, 100);
				window.parent.Alert("该存档未登陆论坛账号!请登陆后重新进入地图!")
				return;
			}
			$(".捕捉面板").hide();
			$(".技能面板").hide();
			$("#dixiao").hide();
			$("#gj").hide();

			gj = true;
			window.parent.gjs(gj);
			t = -1;

			jiasheng = (j.加深伤害 == -2) ? "Max" : j.加深伤害;
			dixiao = j.抵消伤害;
			xixue = j.吸血;
			ximo = j.吸魔;
			cwMp = j.剩余魔法;
			gwHp = j.对方剩余HP;
			cwMp = j.剩余魔法;
			jn = jnname;

			if (j.advance == "1") {
				wfgj(j);
				cwHp = j.己方剩余HP;
			} else {
				cwHp = j.己方剩余HP;
				fj(j);
			}
		}

		// 新的异步战斗函数
		async function gongji(id, jnname) {
			console.log(`[Battle] 发起攻击: 技能${id}(${jnname})`);

			if (gj) {
				console.log('[Battle] 战斗进行中，忽略重复攻击');
				return;
			}

			try {
				// 设置战斗状态
				gj = true;
				if (window.parent && window.parent.gjs) {
					window.parent.gjs(true);
				}

				// 自动战斗检查
				if (zidong == 1 && window.parent && window.parent.checkFightZD) {
					window.parent.checkFightZD();
				}

				// 隐藏所有面板
				$(".捕捉面板").hide();
				$(".技能面板").hide();
				$("#dixiao").hide();
				$("#gj").hide();

				// 设置技能名称
				jn = jnname || "普通攻击";

				// 显示战斗状态
				$("#state").css("left", "50px");
				$("#jn").html(jn);
				$("#state").show();

				// 获取选中的宠物ID
				const selectedPetId = getPetIdFromUrl();

				console.log(`[Battle] 战斗参数: 地图${map}, 宠物${selectedPetId}, 技能${id}`);

				// 调用新的战斗API
				const battleResult = await BattleAPI.executeBattle(map, selectedPetId, id);

				if (battleResult.success) {
					// 处理战斗结果
					await processBattleResult(battleResult);
				} else {
					throw new Error(battleResult.message || "战斗失败");
				}

			} catch (error) {
				console.error('[Battle] 战斗执行失败:', error);

				// 错误处理
				ErrorHandler.handleAPIError(error, "战斗");

				// 重置战斗状态
				gj = false;
				if (window.parent && window.parent.gjs) {
					window.parent.gjs(false);
				}
				$("#state").hide();
				$("#gj").show();
			}
		}

		// 战斗结果统一处理
		async function processBattleResult(battleResult) {
			console.log('[Battle] 处理战斗结果:', battleResult);

			// 更新全局变量（兼容现有系统）
			updateGlobalBattleVars(battleResult);

			if (battleResult.isBattleEnd) {
				// 战斗结束，直接显示结果
				await handleBattleEnd(battleResult);
			} else {
				// 战斗继续，播放当前回合动画
				await playCurrentRoundAnimation(battleResult);

				// 动画播放完成后，重置攻击状态
				setTimeout(() => {
					gj = false;
					if (window.parent && window.parent.gjs) {
						window.parent.gjs(false);
					}
					$("#state").hide();
					$("#gj").show();
				}, 2000);
			}
		}

		// 更新全局变量（兼容现有动画系统）
		function updateGlobalBattleVars(battleResult) {
			const currentRound = battleResult.battleRounds[battleResult.currentRound - 1];

			if (currentRound) {
				jiasheng = (currentRound.amplifiedDamage == -2) ? "Max" : (currentRound.amplifiedDamage || 0);
				dixiao = currentRound.reducedDamage || 0;
				xixue = (currentRound.lifeSteal == -2) ? "Max" : (currentRound.lifeSteal || 0);
				ximo = currentRound.manaSteal || 0;
			}

			cwMp = battleResult.playerCurrentMp || 0;
			gwHp = battleResult.monsterCurrentHp || 0;
			cwHp = battleResult.playerCurrentHp || 0;
		}

		// 播放当前回合动画
		async function playCurrentRoundAnimation(battleResult) {
			const currentRound = battleResult.battleRounds[battleResult.currentRound - 1];

			if (!currentRound) {
				console.warn('[Battle] 没有找到当前回合数据');
				return;
			}

			if (currentRound.isPlayerTurn) {
				// 玩家攻击回合
				await playPlayerAttackAnimation(currentRound, battleResult);
			} else {
				// 怪物攻击回合
				await playMonsterAttackAnimation(currentRound, battleResult);
			}
		}

		// 玩家攻击动画
		async function playPlayerAttackAnimation(roundData, battleResult) {
			console.log('[Battle] 播放玩家攻击动画');

			// 显示伤害信息
			$("#jiasheng").html("<span style='color:yellow'>加深伤害 </span>" + showInt(jiasheng));
			$("#dixiao").html("<span style='color:yellow'>抵消伤害 </span>" + showInt(dixiao));
			$("#xixue").html("<span style='color:#6F0'>吸血 </span>" + showInt(xixue));
			$("#ximo").html("<span style='color:#F0F'>吸魔 </span>" + showInt(ximo));

			// 更新怪物HP条
			updateMonsterHP(battleResult.monsterCurrentHp, roundData.playerDamage || 0);

			// 播放攻击动画
			if (调用宠物素材) {
				playz(); // PNG动画
			} else {
				if (typeof thisMovie === 'function' && thisMovie("sw")) {
					thisMovie("sw").inAttack(); // Flash动画
				}
			}

			// 等待动画完成
			await new Promise(resolve => setTimeout(resolve, 1500));
		}

		// 怪物攻击动画
		async function playMonsterAttackAnimation(roundData, battleResult) {
			console.log('[Battle] 播放怪物攻击动画');

			// 更新玩家HP条
			updatePlayerHP(battleResult.playerCurrentHp, roundData.monsterDamage || 0);

			// 播放受击动画
			if (调用宠物素材) {
				plays(); // PNG动画
			} else {
				if (typeof thisMovie === 'function' && thisMovie("sw")) {
					thisMovie("sw").inHurt(); // Flash动画
				}
			}

			// 等待动画完成
			await new Promise(resolve => setTimeout(resolve, 1500));
		}

		// 更新怪物HP条
		function updateMonsterHP(currentHp, damage) {
			const maxHp = parseInt(gwMaxHp) || 1000;
			const hpPercentage = Math.max(0, currentHp / maxHp);
			const newWidth = Math.floor(96 * hpPercentage); // 96px是HP条的最大宽度

			$("#gwhpico").animate({
				width: newWidth + "px"
			}, 1000);

			// 显示伤害数字
			if (damage > 0) {
				showDamageNumber(damage, "monster");
			}
		}

		// 更新玩家HP条
		function updatePlayerHP(currentHp, damage) {
			const maxHp = parseInt(cwMaxHp) || 1000;
			const hpPercentage = Math.max(0, currentHp / maxHp);
			const newWidth = Math.floor(155 * hpPercentage); // 155px是HP条的最大宽度

			$("#php").animate({
				width: newWidth + "px"
			}, 1000);

			// 显示伤害数字
			if (damage > 0) {
				showDamageNumber(damage, "player");
			}
		}

		// 显示伤害数字动画
		function showDamageNumber(damage, target) {
			const damageElement = $(`<div class="damage-number">${damage}</div>`);
			damageElement.css({
				position: 'absolute',
				color: target === 'player' ? '#ff0000' : '#ffff00',
				fontSize: '18px',
				fontWeight: 'bold',
				zIndex: 99999,
				left: target === 'player' ? '200px' : '500px',
				top: '200px'
			});

			$('body').append(damageElement);

			// 动画效果
			damageElement.animate({
				top: '150px',
				opacity: 0
			}, 1500, function() {
				damageElement.remove();
			});
		}

		function wfgj(j) {
			var sh = showInt(j.输出);
			if (j.吸血 == -2) {
				xixue = "Max";
			}
			else {
				if (parseInt(cwHp) + parseInt(xixue) >= parseInt(cwMaxHp)) {
					cwHp = parseInt(cwMaxHp);
					updateUI();
				}
				else {
					cwHp = parseInt(cwHp) + parseInt(xixue);
					updateUI();
				}
			}
			$("#dixiao").hide();
			$(".魔法").html(cwMp);
			updateUI();
			if (jiasheng == "Max" || parseInt(jiasheng) > 0) {
				$("#js").show();
				$("#js").html("<span color='yellow'>加深伤害 </span>" + showInt(jiasheng));
			} else {
				$("#js").hide();
			}

			if (parseInt(ximo) > 0) {

				$("#ximo").show();
				$("#ximo").html("<span color='yellow'>吸取魔法 </span>" + showInt(ximo));
			} else {
				$("#ximo").hide();
			}

			if (xixue == "Max" || parseInt(xixue) > 0) {
				$("#xixue").show();

				$("#xixue").html("<span color='yellow'>吸取生命 </span>" + showInt(xixue));
			} else {
				$("#xixue").hide();
			}
			jsons = j;

			var gwLeft = $("#gw").css("left");
			var cwLeft = $("#cw").css("left");
			$("#jn").html(jn);
			$("#sh").html("&nbsp;-" + sh + "&nbsp;");
			var sLeft = $("#state").css("left");
			var sWidth = $("#state").width();
			var sCount = parseInt(sLeft) + parseInt(sWidth);
			var subLeft = sCount - 778;
			$("#state").css("left", parseInt(sLeft) - parseInt(subLeft));
			$("#state").show();


			var cwWidth = $("#cw").width();

			$("#cw").css("left", parseInt(gwLeft) - 80);
			if (形象 != 1000 && 形象 < 503) {
				//
				$("#info_page").html("<img style=\"width: 250px;height: 180px;margin-top: -30px;margin-left: 20px;\" src=\"Content/PetPhoto/g" + 形象 + ".gif\" id=\"cw\">");
				$("#cw").css("left", parseInt(gwLeft) - 80);


			} else {
				if (wuxing == "聖" || wuxing == "佛" || wuxing == "萌" || wuxing == "灵" || wuxing == "次元" || wuxing == "巫") {
					showFlashPet("g");
				} else {
					$("#cw").attr("src", "Content/PetPhoto/g" + ico + ".gif");
				}
			}
			var returnGwHp = j.对方剩余HP;
			var hpWidth = "1px";
			if (returnGwHp <= 0) {
				returnGwHp = 0;

			} else {
				var aNum = parseInt(gwMaxHp) / 96;
				var subNum = sh / aNum;
				var hWidth = $("#gwhpico").width();
				hpWidth = parseInt(hWidth) - parseInt(subNum) + "px";
			}
			$("#gwhpico").css("width", hpWidth);
			$("#gj").hide();
			$("#gwValue").html(showInt(returnGwHp));

			if (wuxing == "聖" || wuxing == "佛" || wuxing == "萌" || wuxing == "灵" || wuxing == "次元" || wuxing == "巫") {
				showFlashPet("g");
			}
			setTimeout(function () {
				$("#state").hide();

				$("#cw").css("left", "10PX");
				if (形象 >= 1000) {
					$("#cw").width(200);
				}
				if ((wuxing == "聖" || wuxing == "佛" || wuxing == "萌" || wuxing == "灵" || wuxing == "次元" || wuxing == "巫") && (形象 >502 && 形象 != 1000)) {
					showFlashPet("z");
				} else {
					$("#cw").attr("src", "Content/PetPhoto/z" + ico + ".gif");
				}

				$("#jn").html("");
				$("#sh").html("");
				$("#state").hide();


				if (parseInt(gwHp) <= 0 || jsons.advance == "0") {
					jiesuan(jsons);
				} else {
					setTimeout(function () { fj(jsons); }, 800);
				}

			}, 3100);/*
		    */
		}

		function fj(json) {
			
			var sh = showInt(json.受到伤害);
			$("#state").css("left", "50px");
			var cwLeft = 0;
			var cwWidth = $("#cw").width();
			$("#jn").html("普通攻击");

			$("#sh").html("&nbsp;-" + showInt(sh) + "&nbsp;");
			if (parseInt(dixiao) > 0) {
				$("#dixiao").show();
			} else {
				$("#dixiao").hide();
			}
			$("#js").hide();
			$("#xixue").hide();
			$("#ximo").hide();

			$("#dixiao").html("<span color='yellow'>抵消伤害 </span>" + showInt(dixiao));
			var gwInLeft = parseInt(cwLeft) + 100;
			$("#gw").css("left", gwInLeft);
			if (调用宠物素材) {
				$("#gw").attr("src", "Content/PetPhoto/g" + gico + ".gif");

			} else {
				$("#gw").attr("src", "Content/gpc/g" + gico + ".gif");
			}

			$("#state").show();

			var aNum = parseInt(cwMaxHp) / 155;
			var returnWidth = parseInt($("#php").width()) - (sh / aNum);

			if (cwHp < 0) {
				cwHp = 0;
			}
			$("#cwdqhp").html(cwHp);
			if (returnWidth <= 0) {
				returnWidth = "1px";
			}
			$("#php").width(returnWidth);

			setTimeout(function () {
				t = -1;

				$("#gw").css("left", "530px");
				$("#gj").show();
				if (调用宠物素材) {
					$("#gw").attr("src", "Content/PetPhoto/z" + gico + ".gif");


				} else {
					$("#gw").attr("src", "Content/gpc/z" + gico + ".gif");
				}

				$("#jn").html("");
				//这里加入战斗结果判断
				if (json.advance == "1" || json.是否死亡 == "1") {
					jiesuan(json);
				} else {
					wfgj(json);
				}

			}, 3000);
		}

		// 战斗结束处理
		async function handleBattleEnd(battleResult) {
			console.log('[Battle] 战斗结束:', battleResult);

			// 重置战斗状态
			gj = false;
			if (window.parent && window.parent.gjs) {
				window.parent.gjs(false);
			}
			$("#state").hide();
			$("#gj").hide();

			// 隐藏所有面板
			$(".捕捉面板").hide();
			$(".技能面板").hide();

			if (battleResult.isWin) {
				// 胜利处理
				$(".结果").html("战斗胜利！");
				$(".获得金币").html(battleResult.gold || 0);
				$(".获得元宝").html(battleResult.yuanbao || 0);
				$(".获得经验").html(battleResult.exp || 0);
				$(".获得物品").html(battleResult.dropItems?.join(",") || "无");

				// 显示奖励面板
				$(".奖励").show();

				// 捕捉功能暂时禁用
				// TODO: 后续实现捕捉功能

				// 记录战斗日志
				if (typeof debug === 'function') {
					debug(`战斗获胜!获得奖励:${battleResult.dropItems?.join(",") || "无"},获得金币:${battleResult.gold},获得元宝:${battleResult.yuanbao},获得经验:${battleResult.exp}`);
				}

			} else {
				// 失败处理
				$(".结果").html("你的宠物好像有些弱啊！<br><br>赶紧去提升宠物的实力吧！<br><br>噢，对了，氪金可以使你变得强大！<br><br>");
				$(".奖励").hide();
			}

			// 显示返回面板
			$("#returnPage").show();

			// 处理自动战斗逻辑
			if (battleResult.autoStop) {
				if (battleResult.autoStopReason === "key_insufficient") {
					if (window.parent && window.parent.Load) {
						window.parent.Load(2);
					}
				} else if (battleResult.autoStopReason === "monster_too_strong") {
					if (window.parent && window.parent.Load) {
						window.parent.Load(2);
					}
				}
			} else {
				// 继续自动战斗
				setTimeout(function() {
					if (zidong == 1 && window.parent && window.parent.zidong) {
						window.parent.zidong(map);
					}
				}, 3000);
			}

			// 更新地图进度
			if (battleResult.exp && map) {
				const score = parseInt(battleResult.exp) || 0;
				const completionTime = 60; // 可以根据实际战斗时间计算
				if (typeof updateMapProgress === 'function') {
					updateMapProgress(score, completionTime);
				}
			}
		}

		// 显示捕捉选项
		async function showCaptureOptions(captureOptions) {
			console.log('[Battle] 显示捕捉选项:', captureOptions);

			// 构建捕捉面板HTML
			let html = "";
			captureOptions.forEach(option => {
				html += `
					<td height="28px">
						<table style="cursor:pointer;" border="0" cellspacing="0" cellpadding="0">
							<tbody>
								<tr class="buzhuoDIV" onclick="buzhuo(${option.monsterId})">
									<td style="display:none" class="wpid">${option.monsterId}</td>
									<td width="10" class="wpico">
										<img src="Content/PetPhoto/k${option.petNo}.gif" width="25" height="25">
									</td>
									<td width="80" class="wpnm">${option.name}</td>
									<td width="50" class="wplv">Lv.${option.level}</td>
									<td width="60" class="wpsx">${option.element}</td>
								</tr>
							</tbody>
						</table>
					</td>
				`;
			});

			// 更新捕捉面板内容
			if ($(".捕捉列表").length > 0) {
				$(".捕捉列表").html(html);
			}

			// 显示捕捉面板
			$(".捕捉面板").show();
			$(".奖励").hide();
		}

		function jiesuan(json) {
			if (cwHp <= 0) {
				window.parent.gjs(false);
				$(".结果").html("你的宠物好像有些弱啊！<br><br>赶紧去提升宠物的实力吧！<br><br>噢，对了，氪金可以使你变得强大！<br><br>");
				$(".奖励").hide();
				$("#returnPage").show();
				$(".捕捉面板").hide();
				$(".技能面板").hide();
				$("#gj").hide();
				if (json.Auto == 1) {
					window.parent.Load(2);
					//window.parent.recvMsg("怪物太厉害了，自动地狱通天已停止！")
				}

				setTimeout(function () {
					if (zidong == 1) {
						window.parent.zidong(map);
					}
				}, 1000);
			} else if (gwHp <= 0) {
				window.parent.gjs(false);
				$(".结果").html("战斗胜利！");
				$(".获得金币").html(json.获得金币);
				$(".获得元宝").html(json.获得元宝);
				$(".获得经验").html(json.获得经验);
				$(".获得物品").html(json.获得道具);
				$(".捕捉面板").hide();
				$(".技能面板").hide();
				$("#returnPage").show();
				$("#gj").hide();
				$(".奖励").show();
				debug("战斗获胜!获得奖励:" + jsons.获得道具 + ",获得金币:" + jsons.获得金币 + ",获得元宝:" + jsons.获得元宝 + ",获得经验:" + jsons.获得经验);

				if (json.Auto == 2) {
					window.parent.Load(2);
					//window.parent.recvMsg("钥匙不足，自动地狱通天已停止");
				}

				setTimeout(function () {
					if (zidong == 1) {
						window.parent.zidong(map);
					}
				}, 1000);

			} else {
				$("#gj").show();
				t = 10;
				gj = false;
				window.parent.gjs(gj);
				setTimeout(function () {
					if (zidong == 1) {
						gongji(window.parent.jid, window.parent.jname);
					}
				}, 3000);
			}
			$("#sh").html("");
			$("#state").hide();

		}
		function thisMovie(movieName) {

			if (navigator.appName.indexOf("Microsoft") != -1) {
				return window[movieName];
			} else {
				return document.embeds[movieName]; //document[movieName];
			}



		}
		function playState(i) {

			// ReSharper disable once CoercedEqualsUsing
			if (i == "Show") {
				$("#gj").show();
			} else if (i == "inAttack") {
				$("#gj").show();
				// ReSharper disable once CoercedEqualsUsing
			} else if (i == "Attack") {


				if (parseInt(gwHp) <= 0) {
					$("#returnPage").show();
					$("#gj").hide();
				} else {
					//setTimeout(function(){fj()},1500)
					fj();
					thisMovie("sw").inAttack();
				}

			}
		}
		function stopBattle() {
			window.parent.Load(1);
			window.parent.Alert("请不要战斗频繁,如果检测到您正在使用变速齿轮,我们将会做出相应惩罚.");
		}
		var hc_index = 0;
		var hc_id = 0;
		var hc_max = 23;
		var hc_time = 55;
		function playHC() {
			var id = $(".hc").attr("data-id");
			hc_id = id;
			for (var i = 0; i <= hc_max; i++) {
				$("#info_page").prepend("<img class='yingbi hc" + i + "' src='Content/PetPhoto/h_z" + hc_id + "_" + (i + 1) + ".png'>");
			}
			if (id != 0) {
				if (id == 2) {
					$(".hc").css("left", "50px"); $(".hc").css("top", "-20px")
					$(".yingbi").css("left", "50px"); $(".yingbi").css("top", "-20px")

				} if (id == 3) {
					$(".hc").css("left", "50px"); $(".hc").css("top", "-20px")
					$(".yingbi").css("left", "50px"); $(".yingbi").css("top", "-20px")
				}
				setInterval(playHC_AT, hc_time);
			}

		}
		function showHCInfo(json) {
			if (json != "0") {
				$(".hc").show();
				$("#rb_right").show();
				var split = json.split('|');
				$(".hc").attr("data-id", split[0]);
				hc_max = split[1];
				hc_time = split[2];
				playHC();

			}
		}
		function getINT(NUM) {
			if (NUM.indexOf(".") != -1) {
				return NUM * 100 + "%";
			} else {
				return NUM;
			}
		}
		function playHC_AT() {
			hc_index++;
			// $(".hc img").attr("src", "Content/PetPhoto/h_z" + hc_id + "_" + hc_index + ".png");
			if (hc_index >= hc_max) {
				hc_index = 0;
			}

			if (hc_index >= hc_max) {
				$(".hc").addClass("yingbi").removeClass("hc");
				hc_index = 1;
			} else {
				$(".hc").addClass("yingbi").removeClass("hc");
			}



			$(".hc" + hc_index).removeClass("yingbi").addClass("hc");
		}

		// 地图API集成函数
		function initBattleWithMapData() {
			// 检查是否有新的地图战斗信息
			if (window.parent.battleInfo) {
				console.log('使用新的地图API数据初始化战斗:', window.parent.battleInfo);

				var battleInfo = window.parent.battleInfo;

				// 设置地图ID
				if (battleInfo.mapId) {
					map = battleInfo.mapId;
					window.parent.mapIID = battleInfo.mapId;
				}

				// 设置怪物信息
				if (battleInfo.monsters && battleInfo.monsters.length > 0) {
					// 这里可以根据需要处理怪物信息
					console.log('地图怪物信息:', battleInfo.monsters);
				}

				// 设置奖励信息
				if (battleInfo.rewards && battleInfo.rewards.length > 0) {
					// 这里可以根据需要处理奖励信息
					console.log('地图奖励信息:', battleInfo.rewards);
				}

				// 清除战斗信息，避免重复使用
				window.parent.battleInfo = null;
			} else {
				console.log('使用传统方式初始化战斗');
			}
		}

		// 战斗结束后更新地图进度
		async function updateMapProgress(score, completionTimeSeconds) {
			if (typeof window.parent.gameAPI !== 'undefined' && window.parent.gameAPI.updateMapProgress && map) {
				try {
					const userId = window.parent.gameAPI.getCurrentUserId();
					const result = await window.parent.gameAPI.updateMapProgress(map, userId, score, completionTimeSeconds);

					if (result.success) {
						if (result.isNewRecord) {
							window.parent.recvMsg("sm|恭喜！创造了新记录！最佳成绩：" + result.newBestScore);
						}
					} else {
						console.error('更新地图进度失败:', result.message);
					}
				} catch (error) {
					console.error('更新地图进度异常:', error);
				}
			}
		}

		// 重写原有的战斗结束函数，添加进度更新
		var originalJiesuan = jiesuan;
		jiesuan = function(json) {
			// 调用原有的结算逻辑
			originalJiesuan(json);

			// 添加地图进度更新
			if (json && json.经验 && map) {
				var score = parseInt(json.经验) || 0;
				var completionTime = 60; // 默认完成时间，可以根据实际情况计算
				updateMapProgress(score, completionTime);
			}
		};
	</script>
</body>

</html>