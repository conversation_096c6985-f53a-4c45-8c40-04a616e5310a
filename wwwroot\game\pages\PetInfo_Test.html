<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>宠物信息页面 - 接口对接测试</title>
    <link href="Content/CSS/PetInfo.css" rel="stylesheet">
    <script src="Content/Javascript/jquery-1.8.3.min.js"></script>
    <script src="./Content/Javascript/petList.js?123"></script>
    <script src="../js/cache-manager.js"></script>
    <script src="../js/loading-manager.js?v=7"></script>
    <script src="../js/auth-manager.js"></script>
    <script src="../js/pet-info-api-adapter.js?v=8"></script>
    
    <style>
        .test-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 5px;
            z-index: 10000;
            font-size: 12px;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-log {
            max-height: 200px;
            overflow-y: auto;
            background: rgba(255,255,255,0.1);
            padding: 5px;
            margin-top: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 10px;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .info { color: #2196F3; }
    </style>
</head>

<body>
    <!-- 测试面板 -->
    <div class="test-panel">
        <h4>🧪 接口对接测试面板</h4>
        <div>
            <button class="test-button" onclick="testGetPetInfo()">测试获取宠物信息</button>
            <button class="test-button" onclick="testSwitchPet()">测试切换宠物</button>
            <button class="test-button" onclick="testSkillUpgrade()">测试技能升级</button>
        </div>
        <div>
            <button class="test-button" onclick="testGetEquipment()">测试获取装备</button>
            <button class="test-button" onclick="testForgetSkill()">测试遗忘技能</button>
            <button class="test-button" onclick="clearTestLog()">清空日志</button>
        </div>
        <div class="test-log" id="testLog">
            <div class="info">📋 测试日志将显示在这里...</div>
        </div>
    </div>

    <!-- 原始页面内容 -->
    <div id="Layer1" style="cursor:pointer" onClick="window.parent.$('gw').src='./function/City_Mod.php'">
        <label></label>
    </div>
    
    <div class="task">
        <div class="task_left"></div>
        <div class="task_right">
            <ul class="task_nav">
                <li id="tab1" onClick="setTab(1,3)" class="on"><a class="a01" href="javascript:void(0)"></a></li>
                <li id="tab2" onClick="setTab(2,3)"><a class="a02" href="javascript:void(0)"></a></li>
                <li id="tab3" onClick="setTab(3,3)"><a class="a03" href="javascript:void(0)"></a></li>
            </ul>
            
            <!-- 第一页：宠物信息 -->
            <div class="dt_task" id="page1">
                <div class="box01">
                    <div class="box02">
                        <div class="bb01"><img src="Content/Img/PetInfo/petinfo_03.jpg" width="95" height="25"></div>
                    </div>
                    <div class="box03">
                        <div class="p1_left">
                            <div class="ppp1">
                                <div class="petO"></div>
                            </div>
                            <div class="btn_zd" onclick="showZindex()">宠物置顶</div>
                        </div>
                        <div class="p1_right">
                            <div class="p1_right_top">
                                <div class="玩家名字">测试玩家</div>
                                <div class="性别">未知</div>
                            </div>
                            <div class="p1_right_center">
                                金币：<span class="金币">0</span><br/>
                                水晶：<span class="水晶">0</span><br/>
                                元宝：<span class="元宝">0</span><br/>
                                主宠：<span class="主宠名字">无</span><br/>
                                宠物：<span class="宠物数量">0</span>只
                            </div>
                        </div>
                    </div>
                    <div class="box04">
                        <div id="bottom">
                            <!-- 宠物列表将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 第二页：宠物属性 -->
            <div class="dt_task con" id="page2">
                <div class="box01">
                    <div class="box02">
                        <div class="bb01"><img src="Content/Img/PetInfo/petinfo_05.jpg" width="95" height="25"></div>
                    </div>
                    <div class="box03">
                        <div class="p2_left">
                            等级：<span class="等级">0</span><br/>
                            五行：<span class="五行">无</span><br/>
                            境界：<span class="境界">无</span><br/>
                            生命：<span class="生命">0</span><br/>
                            魔法：<span class="魔法">0</span><br/>
                            攻击：<span class="攻击">0</span><br/>
                            防御：<span class="防御">0</span><br/>
                            命中：<span class="命中">0</span><br/>
                            闪避：<span class="闪避">0</span><br/>
                            速度：<span class="速度">0</span><br/>
                            成长：<span class="成长">0</span><br/>
                            战斗力：<span class="战斗力">0</span>
                        </div>
                        <div class="p2_right" id="p2_right">
                            金抗：0<br>
                            木抗：0<br>
                            水抗：0<br>
                            火抗：0<br>
                            土抗：0<br>
                            加深：<span class="加深">0%</span><br>
                            抵消：<span class="抵消">0%</span><br>
                            吸血：<span class="吸血">0%</span><br>
                            吸魔：<span class="吸魔">0%</span><br>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 第三页：技能和装备 -->
            <div class="dt_task con" id="page3">
                <div class="box01">
                    <div class="box02">
                        <div class="bb01"><img src="Content/Img/PetInfo/petinfo_07.jpg" width="95" height="25"></div>
                    </div>
                    <div class="box03">
                        <div class="p3_left">
                            <div class="技能列表">
                                <!-- 技能列表将在这里显示 -->
                            </div>
                        </div>
                        <div class="p3_right">
                            <div class="装备区域">
                                <div class="身体">身体</div>
                                <div class="武器">武器</div>
                                <div class="脚部">脚部</div>
                                <div class="翅膀">翅膀</div>
                                <div class="宝石">宝石</div>
                                <div class="道具">道具</div>
                                <div class="戒指">戒指</div>
                                <div class="手镯">手镯</div>
                                <div class="项链">项链</div>
                                <div class="头部">头部</div>
                                <div class="灵饰">灵饰</div>
                                <div class="法宝">法宝</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 技能提示框 -->
    <div class="skillTips" style="display:none; position:absolute; background:rgba(0,0,0,0.8); color:white; padding:10px; border-radius:5px; z-index:9999;">
        <div class="s_name" style="font-weight:bold;"></div>
        <div class="s_type" style="color:#ccc;"></div>
        <div class="s_value" style="margin-top:5px;"></div>
    </div>

    <script>
        // 测试函数
        let testUserId = 1; // 测试用户ID
        let testPetId = null; // 当前测试宠物ID
        
        function logTest(message, type = 'info') {
            const log = document.getElementById('testLog');
            const time = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `[${time}] ${message}`;
            log.appendChild(div);
            log.scrollTop = log.scrollHeight;
        }
        
        function clearTestLog() {
            document.getElementById('testLog').innerHTML = '<div class="info">📋 测试日志已清空...</div>';
        }
        
        async function testGetPetInfo() {
            logTest('🔄 开始测试获取宠物信息...', 'info');
            try {
                const result = await window.external.getPetInfoPageData(testUserId);
                if (result.success) {
                    logTest('✅ 获取宠物信息成功', 'success');
                    logTest(`📊 数据: 玩家=${result.data.player?.nickname}, 宠物数量=${result.data.pets?.length}`, 'info');
                    if (result.data.pets && result.data.pets.length > 0) {
                        testPetId = result.data.pets[0].id;
                        logTest(`🐾 设置测试宠物ID: ${testPetId}`, 'info');
                    }
                } else {
                    logTest(`❌ 获取宠物信息失败: ${result.message}`, 'error');
                }
            } catch (error) {
                logTest(`💥 测试异常: ${error.message}`, 'error');
            }
        }
        
        async function testSwitchPet() {
            if (!testPetId) {
                logTest('⚠️ 请先测试获取宠物信息以设置测试宠物ID', 'error');
                return;
            }
            logTest(`🔄 开始测试切换宠物 (ID: ${testPetId})...`, 'info');
            try {
                const result = await window.external.switchPet(testPetId);
                if (result) {
                    logTest('✅ 切换宠物成功', 'success');
                } else {
                    logTest('❌ 切换宠物失败', 'error');
                }
            } catch (error) {
                logTest(`💥 测试异常: ${error.message}`, 'error');
            }
        }
        
        async function testSkillUpgrade() {
            if (!testPetId) {
                logTest('⚠️ 请先测试获取宠物信息以设置测试宠物ID', 'error');
                return;
            }
            logTest(`🔄 开始测试技能升级 (宠物ID: ${testPetId})...`, 'info');
            try {
                // 先获取宠物技能
                const skills = await window.external.getPetSkills(testPetId);
                if (skills && skills.length > 0) {
                    const skillId = skills[0].skillId;
                    logTest(`🎯 找到技能ID: ${skillId}`, 'info');
                    const result = await window.external.upgradeSkill(testPetId, skillId);
                    if (result.success) {
                        logTest('✅ 技能升级成功', 'success');
                    } else {
                        logTest(`❌ 技能升级失败: ${result.message}`, 'error');
                    }
                } else {
                    logTest('⚠️ 该宠物没有技能可升级', 'error');
                }
            } catch (error) {
                logTest(`💥 测试异常: ${error.message}`, 'error');
            }
        }
        
        async function testGetEquipment() {
            if (!testPetId) {
                logTest('⚠️ 请先测试获取宠物信息以设置测试宠物ID', 'error');
                return;
            }
            logTest(`🔄 开始测试获取装备 (宠物ID: ${testPetId})...`, 'info');
            try {
                const equipment = await window.external.getPetEquipments(testPetId, testUserId);
                logTest(`✅ 获取装备成功，装备数量: ${equipment.length}`, 'success');
                if (equipment.length > 0) {
                    logTest(`🛡️ 装备详情: ${equipment.map(e => e.name || '未知装备').join(', ')}`, 'info');
                }
            } catch (error) {
                logTest(`💥 测试异常: ${error.message}`, 'error');
            }
        }
        
        async function testForgetSkill() {
            if (!testPetId) {
                logTest('⚠️ 请先测试获取宠物信息以设置测试宠物ID', 'error');
                return;
            }
            logTest(`🔄 开始测试遗忘技能 (宠物ID: ${testPetId})...`, 'info');
            try {
                // 先获取宠物技能
                const skills = await window.external.getPetSkills(testPetId);
                if (skills && skills.length > 0) {
                    const skillId = skills[0].skillId;
                    logTest(`🎯 准备遗忘技能ID: ${skillId}`, 'info');
                    logTest('⚠️ 这是测试，实际不会执行遗忘操作', 'info');
                    // const result = await window.external.forgetSkill(testPetId, skillId);
                    // 模拟成功
                    logTest('✅ 遗忘技能测试完成（未实际执行）', 'success');
                } else {
                    logTest('⚠️ 该宠物没有技能可遗忘', 'error');
                }
            } catch (error) {
                logTest(`💥 测试异常: ${error.message}`, 'error');
            }
        }
        
        // 页面初始化
        $(function() {
            logTest('🚀 页面初始化完成，开始自动测试...', 'info');
            
            // 创建父窗口函数的降级处理
            if (!window.parent || window.parent === window) {
                window.parent = window.parent || {};
                window.parent.showBox = function(message) {
                    logTest(`📢 消息: ${message}`, 'info');
                };
                window.parent.loadOK = function() {
                    logTest('✅ 页面加载完成回调', 'success');
                };
            }
            
            // 设置标签页切换
            function setTab(cursel, n) {
                for (var i = 1; i <= n; i++) {
                    var menu = document.getElementById("tab" + i);
                    var con = document.getElementById("page" + i);
                    if (menu) menu.className = i == cursel ? "on" : "";
                    if (con) con.style.display = i == cursel ? "block" : "none";
                }
            }
            window.setTab = setTab;
            
            // 延迟自动测试
            setTimeout(async function() {
                await testGetPetInfo();
            }, 1000);
        });
    </script>
</body>
</html>
