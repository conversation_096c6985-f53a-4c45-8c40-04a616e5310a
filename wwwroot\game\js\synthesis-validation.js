/**
 * 宠物合成验证脚本
 * 用于验证宠物是否可以参与合成
 */

class SynthesisValidation {
    constructor() {
        this.validPetsCache = null;
        this.cacheExpiry = null;
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
    }

    /**
     * 验证宠物是否可合成
     * @param {number} petNo 宠物编号
     * @returns {Promise<boolean>} 是否可合成
     */
    async validatePetForSynthesis(petNo) {
        try {
            const response = await fetch(`/api/SynthesisValidPets/${petNo}/validate`);
            const result = await response.json();
            
            if (result.success) {
                return result.data;
            } else {
                console.error('验证宠物失败:', result.message);
                return false;
            }
        } catch (error) {
            console.error('验证宠物异常:', error);
            return false;
        }
    }

    /**
     * 批量验证宠物是否可合成
     * @param {number[]} petNos 宠物编号列表
     * @returns {Promise<Object>} 验证结果字典
     */
    async validateMultiplePets(petNos) {
        try {
            const response = await fetch('/api/SynthesisValidPets/validate-batch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(petNos)
            });
            
            const result = await response.json();
            
            if (result.success) {
                return result.data;
            } else {
                console.error('批量验证宠物失败:', result.message);
                return {};
            }
        } catch (error) {
            console.error('批量验证宠物异常:', error);
            return {};
        }
    }

    /**
     * 获取所有可合成宠物ID（带缓存）
     * @returns {Promise<number[]>} 可合成宠物ID列表
     */
    async getAllValidPets() {
        // 检查缓存
        if (this.validPetsCache && this.cacheExpiry && Date.now() < this.cacheExpiry) {
            return this.validPetsCache;
        }

        try {
            const response = await fetch('/api/SynthesisValidPets');
            const result = await response.json();
            
            if (result.success) {
                // 更新缓存
                this.validPetsCache = result.data;
                this.cacheExpiry = Date.now() + this.cacheTimeout;
                return result.data;
            } else {
                console.error('获取可合成宠物列表失败:', result.message);
                return [];
            }
        } catch (error) {
            console.error('获取可合成宠物列表异常:', error);
            return [];
        }
    }

    /**
     * 检查宠物是否在可合成列表中（使用缓存）
     * @param {number} petNo 宠物编号
     * @returns {Promise<boolean>} 是否可合成
     */
    async isValidSynthesisPet(petNo) {
        const validPets = await this.getAllValidPets();
        return validPets.includes(petNo);
    }

    /**
     * 刷新缓存
     * @returns {Promise<boolean>} 是否成功
     */
    async refreshCache() {
        try {
            // 清除本地缓存
            this.validPetsCache = null;
            this.cacheExpiry = null;

            // 调用服务端刷新缓存
            const response = await fetch('/api/SynthesisValidPets/refresh', {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                console.log('缓存刷新成功');
                return true;
            } else {
                console.error('刷新缓存失败:', result.message);
                return false;
            }
        } catch (error) {
            console.error('刷新缓存异常:', error);
            return false;
        }
    }

    /**
     * 获取可合成宠物数量
     * @returns {Promise<number>} 数量
     */
    async getValidPetsCount() {
        try {
            const response = await fetch('/api/SynthesisValidPets/count');
            const result = await response.json();
            
            if (result.success) {
                return result.data;
            } else {
                console.error('获取可合成宠物数量失败:', result.message);
                return 0;
            }
        } catch (error) {
            console.error('获取可合成宠物数量异常:', error);
            return 0;
        }
    }

    /**
     * 初始化默认数据
     * @returns {Promise<boolean>} 是否成功
     */
    async initializeDefaultData() {
        try {
            const response = await fetch('/api/SynthesisValidPets/initialize', {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                console.log('默认数据初始化成功');
                await this.refreshCache(); // 刷新缓存
                return true;
            } else {
                console.error('初始化默认数据失败:', result.message);
                return false;
            }
        } catch (error) {
            console.error('初始化默认数据异常:', error);
            return false;
        }
    }
}

// 全局实例
const synthesisValidation = new SynthesisValidation();

/**
 * 宠物选择验证辅助函数
 */
window.SynthesisValidationHelper = {
    /**
     * 在宠物选择时调用验证
     * @param {number} petNo 宠物编号
     * @param {Function} onValid 验证通过回调
     * @param {Function} onInvalid 验证失败回调
     */
    async onPetSelected(petNo, onValid, onInvalid) {
        const isValid = await synthesisValidation.validatePetForSynthesis(petNo);
        
        if (isValid) {
            if (onValid) onValid(petNo);
        } else {
            if (onInvalid) onInvalid(petNo);
            this.showWarning(`宠物${petNo}不能参与合成！`);
        }
    },

    /**
     * 显示警告信息
     * @param {string} message 警告信息
     */
    showWarning(message) {
        // 这里可以根据实际的UI框架来实现
        if (typeof showMessage === 'function') {
            showMessage(message, 'warning');
        } else if (typeof alert === 'function') {
            alert(message);
        } else {
            console.warn(message);
        }
    },

    /**
     * 显示成功信息
     * @param {string} message 成功信息
     */
    showSuccess(message) {
        if (typeof showMessage === 'function') {
            showMessage(message, 'success');
        } else {
            console.log(message);
        }
    },

    /**
     * 验证合成前的宠物选择
     * @param {number} mainPetNo 主宠编号
     * @param {number} vicePetNo 副宠编号
     * @returns {Promise<boolean>} 是否都可以合成
     */
    async validateSynthesisPets(mainPetNo, vicePetNo) {
        const results = await synthesisValidation.validateMultiplePets([mainPetNo, vicePetNo]);
        
        const mainValid = results[mainPetNo] || false;
        const viceValid = results[vicePetNo] || false;

        if (!mainValid) {
            this.showWarning(`主宠物[${mainPetNo}]不能参与合成！`);
            return false;
        }

        if (!viceValid) {
            this.showWarning(`副宠物[${vicePetNo}]不能参与合成！`);
            return false;
        }

        return true;
    },

    /**
     * 初始化验证系统
     */
    async initialize() {
        try {
            const count = await synthesisValidation.getValidPetsCount();
            console.log(`合成验证系统初始化完成，共${count}个可合成宠物`);
            
            // 如果没有数据，尝试初始化默认数据
            if (count === 0) {
                console.log('检测到没有可合成宠物数据，正在初始化...');
                await synthesisValidation.initializeDefaultData();
            }
        } catch (error) {
            console.error('初始化验证系统失败:', error);
        }
    }
};

// 页面加载完成后自动初始化
if (typeof document !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        window.SynthesisValidationHelper.initialize();
    });
}

// 导出给其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SynthesisValidation, synthesisValidation };
}
