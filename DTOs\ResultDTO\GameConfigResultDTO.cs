namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 游戏配置结果DTO
    /// </summary>
    public class GameConfigResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 宠物配置列表
        /// </summary>
        public List<PetConfigDTO> PetConfigs { get; set; } = new List<PetConfigDTO>();

        /// <summary>
        /// 技能配置列表
        /// </summary>
        public List<SkillConfigDTO> SkillConfigs { get; set; } = new List<SkillConfigDTO>();

        /// <summary>
        /// 道具配置列表
        /// </summary>
        public List<ItemConfigDTO> ItemConfigs { get; set; } = new List<ItemConfigDTO>();

        /// <summary>
        /// 怪物配置列表
        /// </summary>
        public List<MonsterConfigDTO> MonsterConfigs { get; set; } = new List<MonsterConfigDTO>();

        /// <summary>
        /// 境界配置列表
        /// </summary>
        public List<RealmConfigDTO> RealmConfigs { get; set; } = new List<RealmConfigDTO>();

        /// <summary>
        /// 装备配置列表
        /// </summary>
        public List<EquipmentConfigDTO> EquipmentConfigs { get; set; } = new List<EquipmentConfigDTO>();
    }

    /// <summary>
    /// 宠物配置DTO
    /// </summary>
    public class PetConfigDTO
    {
        /// <summary>
        /// 宠物编号
        /// </summary>
        public int PetNo { get; set; }

        /// <summary>
        /// 宠物名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 宠物属性（五行）
        /// </summary>
        public string Attribute { get; set; } = string.Empty;

        /// <summary>
        /// 技能列表
        /// </summary>
        public string Skills { get; set; } = string.Empty;
    }

    /// <summary>
    /// 技能配置DTO
    /// </summary>
    public class SkillConfigDTO
    {
        /// <summary>
        /// 技能ID
        /// </summary>
        public string SkillId { get; set; } = string.Empty;

        /// <summary>
        /// 技能名称
        /// </summary>
        public string SkillName { get; set; } = string.Empty;

        /// <summary>
        /// 技能百分比
        /// </summary>
        public decimal SkillPercent { get; set; }

        /// <summary>
        /// 效果类型
        /// </summary>
        public string EffectType { get; set; } = string.Empty;

        /// <summary>
        /// 魔法消耗
        /// </summary>
        public int ManaCost { get; set; }

        /// <summary>
        /// 五行限制
        /// </summary>
        public string ElementLimit { get; set; } = string.Empty;
    }

    /// <summary>
    /// 道具配置DTO
    /// </summary>
    public class ItemConfigDTO
    {
        /// <summary>
        /// 道具编号
        /// </summary>
        public int ItemNo { get; set; }

        /// <summary>
        /// 道具名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 道具类型
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 道具描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 道具品质
        /// </summary>
        public string Quality { get; set; } = string.Empty;

        /// <summary>
        /// 道具图标
        /// </summary>
        public string Icon { get; set; } = string.Empty;

        /// <summary>
        /// 道具价格
        /// </summary>
        public int Price { get; set; }
    }

    /// <summary>
    /// 怪物配置DTO
    /// </summary>
    public class MonsterConfigDTO
    {
        /// <summary>
        /// 怪物编号
        /// </summary>
        public int MonsterNo { get; set; }

        /// <summary>
        /// 怪物名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 怪物属性（五行）
        /// </summary>
        public string Attribute { get; set; } = string.Empty;

        /// <summary>
        /// 技能编号
        /// </summary>
        public string Skill { get; set; } = string.Empty;
    }

    /// <summary>
    /// 境界配置DTO
    /// </summary>
    public class RealmConfigDTO
    {
        /// <summary>
        /// 境界ID
        /// </summary>
        public int RealmId { get; set; }

        /// <summary>
        /// 境界名称
        /// </summary>
        public string RealmName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 装备配置DTO
    /// </summary>
    public class EquipmentConfigDTO
    {
        /// <summary>
        /// 装备ID
        /// </summary>
        public string EquipId { get; set; } = string.Empty;

        /// <summary>
        /// 装备名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 装备类型ID
        /// </summary>
        public string EquipTypeId { get; set; } = string.Empty;

        /// <summary>
        /// 装备类型名称
        /// </summary>
        public string TypeName { get; set; } = string.Empty;

        /// <summary>
        /// 五行属性
        /// </summary>
        public string Element { get; set; } = string.Empty;

        /// <summary>
        /// 装备图标
        /// </summary>
        public string Icon { get; set; } = string.Empty;
    }
} 