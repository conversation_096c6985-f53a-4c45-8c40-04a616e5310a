using WebApplication_HM.Models;
using WebApplication_HM.DTOs;

// 类型别名
using user_info = WebApplication_HM.Models.user;

namespace WebApplication_HM.Services.PropScript
{
    /// <summary>
    /// 道具脚本执行结果
    /// </summary>
    public class PropScriptResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 是否需要多脚本选择
        /// </summary>
        public bool RequiresMultiSelect { get; set; }

        /// <summary>
        /// 多脚本选择选项
        /// </summary>
        public List<string> SelectOptions { get; set; } = new();

        /// <summary>
        /// 多脚本选择映射
        /// </summary>
        public Dictionary<string, string> ScriptMapping { get; set; } = new();

        /// <summary>
        /// 额外数据
        /// </summary>
        public Dictionary<string, object> ExtraData { get; set; } = new();

        /// <summary>
        /// 创建成功结果
        /// </summary>
        public static PropScriptResult CreateSuccess(string message)
        {
            return new PropScriptResult
            {
                Success = true,
                Message = message
            };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        public static PropScriptResult CreateFailure(string message)
        {
            return new PropScriptResult
            {
                Success = false,
                Message = message
            };
        }

        /// <summary>
        /// 创建多脚本选择结果
        /// </summary>
        public static PropScriptResult MultiSelect(List<string> options, Dictionary<string, string> scriptMapping)
        {
            return new PropScriptResult
            {
                Success = true,
                RequiresMultiSelect = true,
                Message = "请选择要执行的脚本",
                SelectOptions = options,
                ScriptMapping = scriptMapping
            };
        }

        /// <summary>
        /// 创建带额外数据的成功结果
        /// </summary>
        public static PropScriptResult SuccessWithData(string message, Dictionary<string, object> extraData)
        {
            return new PropScriptResult
            {
                Success = true,
                Message = message,
                ExtraData = extraData
            };
        }
    }

    /// <summary>
    /// 多脚本选择请求
    /// </summary>
    public class MultiScriptSelectRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 道具ID
        /// </summary>
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// 选择的选项
        /// </summary>
        public string SelectedOption { get; set; } = string.Empty;

        /// <summary>
        /// 脚本映射
        /// </summary>
        public Dictionary<string, string> ScriptMapping { get; set; } = new();
    }

    /// <summary>
    /// 道具脚本执行请求
    /// </summary>
    public class PropScriptExecuteRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 道具ID
        /// </summary>
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// 使用数量
        /// </summary>
        public int UseCount { get; set; } = 1;

        /// <summary>
        /// 指定脚本（用于多脚本选择）
        /// </summary>
        public string SpecificScript { get; set; } = string.Empty;

        /// <summary>
        /// 额外参数
        /// </summary>
        public Dictionary<string, object> ExtraParams { get; set; } = new();
    }

    /// <summary>
    /// 脚本类型枚举
    /// </summary>
    public enum ScriptType
    {
        /// <summary>
        /// 扩展道具格子
        /// </summary>
        ExpandPropCapacity,

        /// <summary>
        /// 扩展牧场格子
        /// </summary>
        ExpandPastureCapacity,

        /// <summary>
        /// 学习技能
        /// </summary>
        LearnSkill,

        /// <summary>
        /// 宠物经验
        /// </summary>
        PetExperience,

        /// <summary>
        /// 巫族宠物经验
        /// </summary>
        WuClanPetExperience,

        /// <summary>
        /// 龙珠经验
        /// </summary>
        DragonBallExp,

        /// <summary>
        /// 龙珠突破
        /// </summary>
        DragonBallBreakthrough,

        /// <summary>
        /// 道具合成
        /// </summary>
        ComposeItem,

        /// <summary>
        /// 扣除并获得道具
        /// </summary>
        DeductAndGainItem,

        /// <summary>
        /// 召唤宠物
        /// </summary>
        SummonPet,

        /// <summary>
        /// 镶嵌宝石
        /// </summary>
        GemstoneInlay,

        /// <summary>
        /// 获得称号
        /// </summary>
        GainTitle,

        /// <summary>
        /// 魂宠召唤
        /// </summary>
        SoulPetSummon,

        /// <summary>
        /// 多脚本选择
        /// </summary>
        MultiScriptSelect,

        /// <summary>
        /// 简单经验
        /// </summary>
        SimpleExperience,

        /// <summary>
        /// 简单金币
        /// </summary>
        SimpleMoney,

        /// <summary>
        /// 简单元宝
        /// </summary>
        SimpleYuanbao,

        /// <summary>
        /// 未知类型
        /// </summary>
        Unknown
    }

    /// <summary>
    /// 脚本执行上下文
    /// </summary>
    public class ScriptExecutionContext
    {
        /// <summary>
        /// 用户信息
        /// </summary>
        public user_info User { get; set; }

        /// <summary>
        /// 道具信息
        /// </summary>
        public PropInfo Prop { get; set; }

        /// <summary>
        /// 主宠物信息
        /// </summary>
        public user_pet MainPet { get; set; }

        /// <summary>
        /// 执行时间
        /// </summary>
        public DateTime ExecuteTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 额外参数
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 脚本验证结果
    /// </summary>
    public class ScriptValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 脚本类型
        /// </summary>
        public ScriptType ScriptType { get; set; }

        /// <summary>
        /// 解析后的参数
        /// </summary>
        public string[] Parameters { get; set; } = Array.Empty<string>();

        /// <summary>
        /// 创建有效结果
        /// </summary>
        public static ScriptValidationResult Valid(ScriptType scriptType, string[] parameters)
        {
            return new ScriptValidationResult
            {
                IsValid = true,
                ScriptType = scriptType,
                Parameters = parameters
            };
        }

        /// <summary>
        /// 创建无效结果
        /// </summary>
        public static ScriptValidationResult Invalid(string errorMessage)
        {
            return new ScriptValidationResult
            {
                IsValid = false,
                ErrorMessage = errorMessage,
                ScriptType = ScriptType.Unknown
            };
        }
    }
}
