using WebApplication_HM.DTOs.ResultDTO;
using WebApplication_HM.DTOs.RequestDTO;

namespace WebApplication_HM.Interface
{
    public interface IPlayerService
    {
        /// <summary>
        /// 根据战斗请求，自动分配怪物并结算战斗，返回战斗结果
        /// </summary>
        /// <param name="request">战斗请求参数</param>
        /// <returns>战斗结果</returns>
        Task<BattleResultDTO> BattleCalculate(BattleRequestDTO request);

        /// <summary>
        /// 用户登录，校验账号密码，返回登录结果
        /// </summary>
        /// <param name="request">登录请求参数</param>
        /// <returns>登录结果</returns>
        LoginResultDTO Login(LoginRequestDTO request);

        /// <summary>
        /// 玩家注册
        /// </summary>
        /// <param name="request">注册请求参数</param>
        /// <returns>注册结果</returns>
        RegisterResultDTO Register(RegisterRequestDTO request);

        /// <summary>
        /// 获取玩家基础信息
        /// </summary>
        /// <param name="request">玩家信息查询请求</param>
        /// <returns>玩家信息结果</returns>
        PlayerInfoResultDTO GetPlayerInfo(PlayerInfoRequestDTO request);

        /// <summary>
        /// 领取战斗奖励
        /// </summary>
        /// <param name="request">领取奖励请求</param>
        /// <returns>奖励结果</returns>
        Task<ClaimRewardResultDTO> ClaimBattleReward(ClaimRewardRequestDTO request);

        /// <summary>
        /// 获取主战宠物信息 - 专为Battle.html设计
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>主战宠物信息</returns>
        Task<BattlePetInfoResultDTO> GetMainPet(int userId);

        /// <summary>
        /// 获取用户宠物列表
        /// </summary>
        /// <param name="request">宠物列表查询请求</param>
        /// <returns>宠物列表结果</returns>
        WebApplication_HM.DTOs.ResultDTO.PetListResultDTO GetUserPets(WebApplication_HM.DTOs.RequestDTO.PetListRequestDTO request);

        /// <summary>
        /// 获取宠物详细信息
        /// </summary>
        /// <param name="request">宠物详情查询请求</param>
        /// <returns>宠物详情结果</returns>
        Task<PetDetailResultDTO> GetPetDetail(PetDetailRequestDTO request);

        /// <summary>
        /// 设置主战宠物
        /// </summary>
        /// <param name="request">设置主战宠物请求</param>
        /// <returns>操作结果</returns>
        LoginResultDTO SetMainPet(WebApplication_HM.DTOs.RequestDTO.SetMainPetRequestDTO request);

        /// <summary>
        /// 计算宠物最终属性（用于战斗和显示）
        /// </summary>
        /// <param name="request">属性计算请求</param>
        /// <returns>属性计算结果</returns>
        AttributeResultDTO GetPetAttributes(AttributeRequestDTO request);

        /// <summary>
        /// 获取地图列表
        /// </summary>
        /// <param name="request">地图列表查询请求</param>
        /// <returns>地图列表结果</returns>
        MapListResultDTO GetMapList(MapListRequestDTO request);

        /// <summary>
        /// 获取地图详细信息
        /// </summary>
        /// <param name="request">地图详情查询请求</param>
        /// <returns>地图详情结果</returns>
        MapDetailResultDTO GetMapDetail(MapDetailRequestDTO request);

        /// <summary>
        /// 获取游戏配置数据
        /// </summary>
        /// <param name="request">配置查询请求</param>
        /// <returns>游戏配置结果</returns>
        GameConfigResultDTO GetGameConfig(ConfigRequestDTO request);

        /// <summary>
        /// 获取宠物配置列表
        /// </summary>
        /// <returns>宠物配置结果</returns>
        GameConfigResultDTO GetPetConfigs();

        /// <summary>
        /// 获取技能配置列表
        /// </summary>
        /// <returns>技能配置结果</returns>
        GameConfigResultDTO GetSkillConfigs();

        /// <summary>
        /// 获取道具配置列表
        /// </summary>
        /// <returns>道具配置结果</returns>
        GameConfigResultDTO GetItemConfigs();

        /// <summary>
        /// 获取怪物配置列表
        /// </summary>
        /// <returns>怪物配置结果</returns>
        GameConfigResultDTO GetMonsterConfigs();

        /// <summary>
        /// 获取境界配置列表
        /// </summary>
        /// <returns>境界配置结果</returns>
        GameConfigResultDTO GetRealmConfigs();

        /// <summary>
        /// 获取装备配置列表
        /// </summary>
        /// <returns>装备配置结果</returns>
        GameConfigResultDTO GetEquipmentConfigs();

        /// <summary>
        /// 获取用户装备背包
        /// </summary>
        /// <param name="request">装备列表查询请求</param>
        /// <returns>装备列表结果</returns>
        EquipmentListResultDTO GetUserEquipments(EquipmentListRequestDTO request);



        /// <summary>
        /// 装备道具
        /// </summary>
        /// <param name="request">装备请求</param>
        /// <returns>操作结果</returns>
        LoginResultDTO EquipItem(EquipRequestDTO request);

        /// <summary>
        /// 卸下装备
        /// </summary>
        /// <param name="request">卸装请求</param>
        /// <returns>操作结果</returns>
        LoginResultDTO UnequipItem(UnequipRequestDTO request);

        /// <summary>
        /// 获取背包物品列表
        /// </summary>
        /// <param name="request">背包查询请求</param>
        /// <returns>背包结果</returns>
        BagResultDTO GetBag(BagRequestDTO request);

        /// <summary>
        /// 使用道具
        /// </summary>
        /// <param name="request">使用道具请求</param>
        /// <returns>使用结果</returns>
        UseItemResultDTO UseItem(UseItemRequestDTO request);

        /// <summary>
        /// 出售道具
        /// </summary>
        /// <param name="request">出售道具请求</param>
        /// <returns>出售结果</returns>
        SellItemResultDTO SellItem(SellItemRequestDTO request);

        /// <summary>
        /// 整理背包
        /// </summary>
        /// <param name="request">整理背包请求</param>
        /// <returns>整理结果</returns>
        SortBagResultDTO SortBag(SortBagRequestDTO request);

        /// <summary>
        /// 获取可合成宠物列表（五系宠物）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>可合成宠物列表</returns>
        PetListResultDTO GetSynthesisAvailablePets(int userId);

        /// <summary>
        /// 获取可涅槃宠物列表（神系宠物）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>可涅槃宠物列表</returns>
        PetListResultDTO GetNirvanaAvailablePets(int userId);

        /// <summary>
        /// 获取可进化宠物列表（只包含携带的宠物）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>可进化宠物列表</returns>
        PetListResultDTO GetEvolutionAvailablePets(int userId);
    }
}