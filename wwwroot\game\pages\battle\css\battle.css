/* Vue版本战斗页面样式 */

/* 全局样式 */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: '华文新魏', serif;
    overflow: hidden;
}

/* 战斗应用容器 */
.battle-app {
    position: relative;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 战斗场景 */
.battle-arena {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.arena-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.background-gradient {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, 
        rgba(102, 126, 234, 0.8) 0%, 
        rgba(118, 75, 162, 0.8) 100%);
}

/* 宠物和怪物 */
.player-pet, .monster {
    position: absolute;
    transition: all 0.3s ease;
    z-index: 10;
    cursor: pointer;
}

.player-pet {
    left: 100px;
    bottom: 100px;
}

.monster {
    right: 100px;
    bottom: 100px;
}

/* 宠物和怪物图像 */
.pet-image, .monster-image {
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.3);
    margin-bottom: 10px;
}

.pet-placeholder, .monster-placeholder {
    font-size: 60px;
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.5));
}

/* 攻击动画 */
.player-pet.attacking {
    transform: translateX(50px) scale(1.1);
    filter: brightness(1.3);
}

.monster.attacking {
    transform: translateX(-50px) scale(1.1);
    filter: brightness(1.3);
}

/* 受击动画 */
.player-pet.hit, .monster.hit {
    animation: shake 0.5s ease-in-out;
    filter: hue-rotate(180deg);
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-10px); }
    75% { transform: translateX(10px); }
}

/* 死亡动画 */
.monster.dying {
    opacity: 0.5;
    transform: scale(0.8);
    filter: grayscale(100%);
}

/* HP条样式 */
.hp-bar {
    width: 150px;
    text-align: center;
}

.hp-label {
    color: white;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.hp-bar-container {
    width: 100%;
    height: 12px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.hp-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff4444 0%, #ffaa00 50%, #44ff44 100%);
    transition: width 1s ease;
    border-radius: 6px;
}

.hp-text {
    color: white;
    font-size: 12px;
    margin-top: 3px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

/* 伤害显示 */
.damage-display {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 20px;
    border-radius: 10px;
    font-family: '华文新魏', serif;
    font-size: 18px;
    z-index: 1000;
    min-width: 250px;
    text-align: center;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.skill-name {
    color: #FFD700;
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 20px;
    text-shadow: 0 0 5px #FFD700;
}

.damage-value {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.damage-value.normal {
    color: #FF4444;
}

.damage-value.critical {
    color: #FF6600;
    text-shadow: 0 0 15px #FF6600;
    animation: pulse 0.5s ease-in-out infinite alternate;
}

.damage-value.miss {
    color: #888888;
    font-style: italic;
}

@keyframes pulse {
    from { transform: scale(1); }
    to { transform: scale(1.1); }
}

.effect-text {
    font-size: 16px;
    margin: 3px 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.effect-text.amplified { color: #FF66FF; }
.effect-text.reduced { color: #66FFFF; }
.effect-text.life-steal { color: #66FF66; }
.effect-text.mana-steal { color: #FF66FF; }

/* 过渡动画 */
.damage-fade-enter-active, .damage-fade-leave-active {
    transition: all 0.5s ease;
}

.damage-fade-enter-from {
    opacity: 0;
    transform: translateX(-50%) translateY(-30px) scale(0.8);
}

.damage-fade-leave-to {
    opacity: 0;
    transform: translateX(-50%) translateY(30px) scale(0.8);
}

/* 战斗提示 */
.battle-hints {
    position: absolute;
    top: 50px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-size: 18px;
    text-align: center;
    z-index: 100;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

/* 战斗结果 */
.battle-result {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    z-index: 2000;
    border: 3px solid #FFD700;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
}

.battle-result h2 {
    margin: 0 0 15px 0;
    font-size: 32px;
    color: #FFD700;
    text-shadow: 0 0 10px #FFD700;
}

/* 调试信息 */
.debug-info {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px;
    border-radius: 5px;
    font-size: 12px;
    z-index: 1500;
    max-width: 200px;
}

.debug-info p {
    margin: 2px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .player-pet {
        left: 20px;
        bottom: 50px;
    }
    
    .monster {
        right: 20px;
        bottom: 50px;
    }
    
    .pet-image, .monster-image {
        width: 80px;
        height: 80px;
    }
    
    .pet-placeholder, .monster-placeholder {
        font-size: 40px;
    }
    
    .damage-display {
        font-size: 16px;
        padding: 15px;
        min-width: 200px;
    }
    
    .skill-name {
        font-size: 18px;
    }
    
    .damage-value {
        font-size: 24px;
    }
}
