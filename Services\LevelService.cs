using WebApplication_HM.Interface;
using WebApplication_HM.Models;
using WebApplication_HM.DTOs;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 等级服务实现
    /// </summary>
    public class LevelService : ILevelService
    {
        private readonly ILevelRepository _levelRepository;
        private readonly ILevelCacheService _cacheService;
        private readonly ILogger<LevelService> _logger;

        public LevelService(
            ILevelRepository levelRepository,
            ILevelCacheService cacheService,
            ILogger<LevelService> logger)
        {
            _levelRepository = levelRepository;
            _cacheService = cacheService;
            _logger = logger;
        }

        /// <summary>
        /// 根据经验计算等级
        /// </summary>
        public async Task<int> CalculateLevelAsync(long exp, string systemName = "pet")
        {
            try
            {
                // 参数验证
                if (exp < 0) return 1;
                
                // 使用缓存优化
                return await _cacheService.GetOrCalculateLevelAsync(exp, systemName, async () =>
                {
                    return await _levelRepository.CalculateLevelAsync(exp, systemName);
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算等级失败，经验值: {Exp}, 系统: {SystemName}", exp, systemName);
                return 1; // 默认返回1级
            }
        }

        /// <summary>
        /// 获取指定等级所需累积经验
        /// </summary>
        public async Task<long> GetRequiredExpAsync(int level, string systemName = "pet")
        {
            try
            {
                if (level <= 1) return 0;
                
                var config = await _levelRepository.GetLevelConfigAsync(level, systemName);
                return config?.required_exp ?? 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取等级所需经验失败，等级: {Level}, 系统: {SystemName}", level, systemName);
                return 0;
            }
        }

        /// <summary>
        /// 获取升级到下一级所需经验
        /// </summary>
        public async Task<long> GetUpgradeExpAsync(int currentLevel, string systemName = "pet")
        {
            try
            {
                var nextLevelConfig = await _levelRepository.GetLevelConfigAsync(currentLevel, systemName);
                if (nextLevelConfig == null) return 0; // 已达最高级
                
                return nextLevelConfig.required_exp;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取升级所需经验失败，当前等级: {Level}, 系统: {SystemName}", currentLevel, systemName);
                return 0;
            }
        }

        /// <summary>
        /// 获取等级范围内的经验配置
        /// </summary>
        public async Task<List<LevelConfig>> GetLevelRangeAsync(int startLevel, int endLevel, string systemName = "pet")
        {
            try
            {
                return await _levelRepository.GetLevelRangeAsync(startLevel, endLevel, systemName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取等级配置失败，范围: {StartLevel}-{EndLevel}, 系统: {SystemName}",
                    startLevel, endLevel, systemName);
                return new List<LevelConfig>();
            }
        }

        /// <summary>
        /// 获取等级配置（兼容旧接口）
        /// </summary>
        public async Task<List<LevelConfig>> GetLevelConfigsAsync(int startLevel = 1, int endLevel = 200, string systemName = "pet")
        {
            return await GetLevelRangeAsync(startLevel, endLevel, systemName);
        }

        /// <summary>
        /// 验证经验值是否有效
        /// </summary>
        public async Task<bool> ValidateExpAsync(long exp, string systemName = "pet")
        {
            try
            {
                if (exp < 0) return false;
                
                var systemConfig = await _levelRepository.GetSystemConfigAsync(systemName);
                if (systemConfig == null) return true; // 没有配置则认为有效
                
                return exp <= systemConfig.max_exp;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证经验值失败，经验: {Exp}, 系统: {SystemName}", exp, systemName);
                return false;
            }
        }

        /// <summary>
        /// 获取经验系统配置
        /// </summary>
        public async Task<ExpSystemConfig?> GetSystemConfigAsync(string systemName)
        {
            try
            {
                return await _levelRepository.GetSystemConfigAsync(systemName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统配置失败，系统: {SystemName}", systemName);
                return null;
            }
        }

        /// <summary>
        /// 计算等级详细信息
        /// </summary>
        public async Task<LevelCalculationResultDTO> CalculateLevelDetailAsync(long exp, string systemName = "pet")
        {
            try
            {
                var currentLevel = await CalculateLevelAsync(exp, systemName);
                var requiredExp = await GetRequiredExpAsync(currentLevel, systemName);
                var upgradeExp = await GetUpgradeExpAsync(currentLevel, systemName);
                
                // 计算到下一级还需要的经验
                var nextLevelRequiredExp = await GetRequiredExpAsync(currentLevel + 1, systemName);
                var expToNextLevel = nextLevelRequiredExp > 0 ? nextLevelRequiredExp - exp : 0;
                
                // 计算进度百分比
                decimal progress = 0;
                if (upgradeExp > 0)
                {
                    var currentLevelStartExp = requiredExp - upgradeExp;
                    var expInCurrentLevel = exp - currentLevelStartExp;
                    progress = Math.Round((decimal)expInCurrentLevel / upgradeExp * 100, 2);
                    progress = Math.Max(0, Math.Min(100, progress)); // 限制在0-100之间
                }
                
                // 检查是否已达最高等级
                var systemConfig = await GetSystemConfigAsync(systemName);
                var isMaxLevel = systemConfig != null && currentLevel >= systemConfig.max_level;
                
                return new LevelCalculationResultDTO
                {
                    Success = true,
                    CurrentLevel = currentLevel,
                    CurrentExp = exp,
                    RequiredExp = requiredExp,
                    UpgradeExp = upgradeExp,
                    ExpToNextLevel = expToNextLevel,
                    Progress = progress,
                    IsMaxLevel = isMaxLevel
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算等级详细信息失败，经验: {Exp}, 系统: {SystemName}", exp, systemName);
                return new LevelCalculationResultDTO
                {
                    Success = false,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 批量计算等级
        /// </summary>
        public async Task<List<int>> BatchCalculateLevelAsync(List<long> expList, string systemName = "pet")
        {
            try
            {
                var tasks = expList.Select(exp => CalculateLevelAsync(exp, systemName));
                var results = await Task.WhenAll(tasks);
                return results.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量计算等级失败，数量: {Count}, 系统: {SystemName}", expList.Count, systemName);
                return expList.Select(_ => 1).ToList(); // 返回默认等级
            }
        }

        /// <summary>
        /// 获取等级进度信息
        /// </summary>
        public async Task<LevelProgressDTO> GetLevelProgressAsync(long exp, string systemName = "pet")
        {
            try
            {
                var currentLevel = await CalculateLevelAsync(exp, systemName);
                var currentLevelRequiredExp = await GetRequiredExpAsync(currentLevel, systemName);
                var nextLevelRequiredExp = await GetRequiredExpAsync(currentLevel + 1, systemName);
                
                // 计算当前等级的起始经验
                var upgradeExp = await GetUpgradeExpAsync(currentLevel, systemName);
                var levelStartExp = currentLevelRequiredExp - upgradeExp;
                
                // 计算当前等级内的经验
                var expInCurrentLevel = exp - levelStartExp;
                var totalExpInLevel = upgradeExp;
                
                // 计算进度百分比
                decimal progressPercentage = 0;
                if (totalExpInLevel > 0)
                {
                    progressPercentage = Math.Round((decimal)expInCurrentLevel / totalExpInLevel * 100, 2);
                    progressPercentage = Math.Max(0, Math.Min(100, progressPercentage));
                }
                
                // 检查是否已达最高等级
                var systemConfig = await GetSystemConfigAsync(systemName);
                var isMaxLevel = systemConfig != null && currentLevel >= systemConfig.max_level;
                
                return new LevelProgressDTO
                {
                    CurrentLevel = currentLevel,
                    CurrentExp = exp,
                    LevelStartExp = levelStartExp,
                    NextLevelStartExp = nextLevelRequiredExp,
                    ExpInCurrentLevel = expInCurrentLevel,
                    TotalExpInLevel = totalExpInLevel,
                    ProgressPercentage = progressPercentage,
                    IsMaxLevel = isMaxLevel
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取等级进度失败，经验: {Exp}, 系统: {SystemName}", exp, systemName);
                return new LevelProgressDTO
                {
                    CurrentLevel = 1,
                    CurrentExp = exp,
                    IsMaxLevel = false
                };
            }
        }

        /// <summary>
        /// 检查是否可以升级
        /// </summary>
        public async Task<(bool CanLevelUp, int NewLevel, int LevelDiff)> CheckLevelUpAsync(long currentExp, long addExp, string systemName = "pet")
        {
            try
            {
                var oldLevel = await CalculateLevelAsync(currentExp, systemName);
                var newLevel = await CalculateLevelAsync(currentExp + addExp, systemName);
                
                var canLevelUp = newLevel > oldLevel;
                var levelDiff = newLevel - oldLevel;
                
                return (canLevelUp, newLevel, levelDiff);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查升级失败，当前经验: {CurrentExp}, 增加经验: {AddExp}, 系统: {SystemName}", 
                    currentExp, addExp, systemName);
                return (false, 1, 0);
            }
        }
    }
}
