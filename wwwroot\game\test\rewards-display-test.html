<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎁 任务奖励显示测试</title>
    <style>
        body {
            font-family: '宋体', SimSun, serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .format-comparison {
            display: flex;
            gap: 20px;
            margin: 15px 0;
        }
        .old-format, .new-format {
            flex: 1;
            padding: 15px;
            border-radius: 5px;
        }
        .old-format {
            background: #ffebee;
            border-left: 4px solid #f44336;
        }
        .new-format {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .任务奖励 {
            display: inline-block;
            border: 0px solid;
            padding-left: 15px;
            line-height: 16px;
            font-size: 12px;
            color: #724908;
        }
        .reward-display {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .reward-display h4 {
            margin-top: 0;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎁 任务奖励显示测试</h1>
        
        <div class="test-section">
            <h3>📋 奖励格式变更说明</h3>
            <div class="format-comparison">
                <div class="old-format">
                    <h4>🔴 旧格式（数组）</h4>
                    <div class="code-block">"rewards": [
  {
    "rewardType": "EXPERIENCE",
    "rewardName": "经验",
    "rewardAmount": 100,
    "rewardDescription": "经验 100"
  },
  {
    "rewardType": "GOLD",
    "rewardName": "金币", 
    "rewardAmount": 100,
    "rewardDescription": "金币 100"
  }
]</div>
                    <p><strong>处理方式</strong>: 需要遍历数组，拼接每个奖励的描述</p>
                </div>
                <div class="new-format">
                    <h4>🟢 新格式（字符串）</h4>
                    <div class="code-block">"rewards": "<span>经验 100、金币 100、飞升丹 10、 [宝石]3级蓝宝石 50 </span>"</div>
                    <p><strong>处理方式</strong>: 直接显示字符串，无需额外处理</p>
                    <p><strong>优势</strong>: 
                        <br>• 支持HTML格式化
                        <br>• 减少前端处理逻辑
                        <br>• 更灵活的显示效果
                    </p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 前端处理逻辑</h3>
            <div class="code-block">// 新的处理逻辑（兼容新旧格式）
let rewardsHtml = '';
if (taskInfo.rewards) {
    if (typeof taskInfo.rewards === 'string') {
        // 新格式：rewards是字符串，直接显示
        rewardsHtml = taskInfo.rewards;
    } else if (Array.isArray(taskInfo.rewards) && taskInfo.rewards.length > 0) {
        // 旧格式：rewards是数组，兼容处理
        taskInfo.rewards.forEach(reward => {
            rewardsHtml += `${reward.rewardDescription || reward.rewardName || ''}<br/>`;
        });
    }
}
$(".任务奖励").html(rewardsHtml);</div>
        </div>

        <div class="test-section">
            <h3>🧪 实际测试</h3>
            <button class="test-button" onclick="testNewFormat()">测试新格式奖励显示</button>
            <button class="test-button" onclick="testOldFormat()">测试旧格式兼容性</button>
            <button class="test-button" onclick="testRealAPI()">测试真实API数据</button>
            
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h3>🎯 显示效果预览</h3>
            
            <div class="reward-display">
                <h4>新格式奖励显示效果：</h4>
                <div class="任务奖励" id="newFormatDisplay">
                    <span>经验 100、金币 100、飞升丹 10、 [宝石]3级蓝宝石 50 </span>
                </div>
            </div>

            <div class="reward-display">
                <h4>旧格式奖励显示效果：</h4>
                <div class="任务奖励" id="oldFormatDisplay">
                    经验 100<br/>金币 100<br/>飞升丹 10<br/>[宝石]3级蓝宝石 50<br/>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>✅ 验证检查项</h3>
            <button class="test-button" onclick="validateRewardDisplay()">验证奖励显示</button>
            
            <div id="validationResults"></div>
        </div>
    </div>

    <script>
        function testNewFormat() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="info result">正在测试新格式奖励显示...</div>';
            
            // 模拟新格式数据
            const newFormatData = {
                rewards: "<span>经验 100、金币 100、飞升丹 10、 [宝石]3级蓝宝石 50 </span>"
            };
            
            // 应用新的处理逻辑
            let rewardsHtml = '';
            if (newFormatData.rewards) {
                if (typeof newFormatData.rewards === 'string') {
                    rewardsHtml = newFormatData.rewards;
                } else if (Array.isArray(newFormatData.rewards) && newFormatData.rewards.length > 0) {
                    newFormatData.rewards.forEach(reward => {
                        rewardsHtml += `${reward.rewardDescription || reward.rewardName || ''}<br/>`;
                    });
                }
            }
            
            // 显示结果
            document.getElementById('newFormatDisplay').innerHTML = rewardsHtml;
            
            resultsDiv.innerHTML = `
                <div class="success result">
                    <strong>✅ 新格式测试成功</strong><br>
                    数据类型: ${typeof newFormatData.rewards}<br>
                    处理方式: 直接显示字符串<br>
                    HTML支持: ✅ 支持<br>
                    显示效果: 正常
                </div>
            `;
        }

        function testOldFormat() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="info result">正在测试旧格式兼容性...</div>';
            
            // 模拟旧格式数据
            const oldFormatData = {
                rewards: [
                    { rewardDescription: "经验 100" },
                    { rewardDescription: "金币 100" },
                    { rewardDescription: "飞升丹 10" },
                    { rewardDescription: "[宝石]3级蓝宝石 50" }
                ]
            };
            
            // 应用新的处理逻辑
            let rewardsHtml = '';
            if (oldFormatData.rewards) {
                if (typeof oldFormatData.rewards === 'string') {
                    rewardsHtml = oldFormatData.rewards;
                } else if (Array.isArray(oldFormatData.rewards) && oldFormatData.rewards.length > 0) {
                    oldFormatData.rewards.forEach(reward => {
                        rewardsHtml += `${reward.rewardDescription || reward.rewardName || ''}<br/>`;
                    });
                }
            }
            
            // 显示结果
            document.getElementById('oldFormatDisplay').innerHTML = rewardsHtml;
            
            resultsDiv.innerHTML = `
                <div class="success result">
                    <strong>✅ 旧格式兼容性测试成功</strong><br>
                    数据类型: ${Array.isArray(oldFormatData.rewards) ? 'Array' : typeof oldFormatData.rewards}<br>
                    处理方式: 遍历数组拼接<br>
                    兼容性: ✅ 完全兼容<br>
                    显示效果: 正常
                </div>
            `;
        }

        async function testRealAPI() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="info result">正在测试真实API数据...</div>';
            
            try {
                const response = await fetch('/api/task/detail/1/TASK_002');
                const data = await response.json();
                
                if (data.success && data.data.userTask.taskInfo.rewards) {
                    const rewards = data.data.userTask.taskInfo.rewards;
                    const rewardType = typeof rewards;
                    
                    // 应用新的处理逻辑
                    let rewardsHtml = '';
                    if (rewards) {
                        if (typeof rewards === 'string') {
                            rewardsHtml = rewards;
                        } else if (Array.isArray(rewards) && rewards.length > 0) {
                            rewards.forEach(reward => {
                                rewardsHtml += `${reward.rewardDescription || reward.rewardName || ''}<br/>`;
                            });
                        }
                    }
                    
                    // 更新显示
                    document.getElementById('newFormatDisplay').innerHTML = rewardsHtml;
                    
                    resultsDiv.innerHTML = `
                        <div class="success result">
                            <strong>✅ 真实API测试成功</strong><br>
                            API响应: 正常<br>
                            奖励数据类型: ${rewardType}<br>
                            奖励内容: ${rewardType === 'string' ? '字符串格式' : '数组格式'}<br>
                            显示效果: 正常<br>
                            原始数据: <code>${JSON.stringify(rewards).substring(0, 100)}...</code>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div class="error result">❌ API返回数据格式异常</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error result">❌ API测试失败: ${error.message}</div>`;
            }
        }

        function validateRewardDisplay() {
            const resultsDiv = document.getElementById('validationResults');
            resultsDiv.innerHTML = '<div class="info result">正在验证奖励显示...</div>';
            
            const checks = [
                {
                    name: '新格式字符串处理',
                    test: () => {
                        const testData = { rewards: "<span>测试奖励</span>" };
                        return typeof testData.rewards === 'string';
                    }
                },
                {
                    name: '旧格式数组兼容',
                    test: () => {
                        const testData = { rewards: [{ rewardDescription: "测试" }] };
                        return Array.isArray(testData.rewards);
                    }
                },
                {
                    name: 'HTML标签支持',
                    test: () => {
                        const htmlContent = "<span>经验 100、金币 100</span>";
                        return htmlContent.includes('<span>') && htmlContent.includes('</span>');
                    }
                },
                {
                    name: 'CSS样式应用',
                    test: () => {
                        const element = document.querySelector('.任务奖励');
                        return element && window.getComputedStyle(element).fontSize === '12px';
                    }
                }
            ];
            
            let passedChecks = 0;
            let resultHtml = '<div class="success result"><strong>验证结果:</strong><br>';
            
            checks.forEach(check => {
                const passed = check.test();
                if (passed) passedChecks++;
                resultHtml += `${passed ? '✅' : '❌'} ${check.name}<br>`;
            });
            
            resultHtml += `<br><strong>通过率: ${passedChecks}/${checks.length} (${Math.round(passedChecks/checks.length*100)}%)</strong></div>`;
            
            resultsDiv.innerHTML = resultHtml;
        }

        // 初始化显示
        window.onload = function() {
            testNewFormat();
        };
    </script>
</body>
</html>
