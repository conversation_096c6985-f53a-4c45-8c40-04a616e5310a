using WebApplication_HM.DTOs.RequestDTO;
using WebApplication_HM.DTOs.ResultDTO;

namespace WebApplication_HM.Interface
{
    /// <summary>
    /// 地图服务接口
    /// </summary>
    public interface IMapService
    {
        /// <summary>
        /// 获取用户可用地图列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>地图列表结果</returns>
        Task<MapListResultDTO> GetAvailableMapsAsync(int userId);

        /// <summary>
        /// 获取地图详细信息
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <param name="userId">用户ID</param>
        /// <returns>地图详细信息</returns>
        Task<MapDetailResultDTO> GetMapDetailAsync(int mapId, int userId);

        /// <summary>
        /// 检查用户是否可以进入地图
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="mapId">地图ID</param>
        /// <returns>是否可以进入</returns>
        Task<bool> CanEnterMapAsync(int userId, int mapId);

        /// <summary>
        /// 获取地图怪物列表
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>怪物列表</returns>
        Task<List<MapMonsterDTO>> GetMapMonstersAsync(int mapId);

        /// <summary>
        /// 获取地图掉落信息
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>掉落信息列表</returns>
        Task<List<MapDropDTO>> GetMapDropsAsync(int mapId);

        /// <summary>
        /// 进入地图
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="mapId">地图ID</param>
        /// <param name="petId">宠物ID</param>
        /// <returns>进入结果</returns>
        Task<EnterMapResultDTO> EnterMapAsync(int userId, int mapId, int petId);

        /// <summary>
        /// 解锁地图
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="mapId">地图ID</param>
        /// <param name="unlockMethod">解锁方式</param>
        /// <returns>解锁结果</returns>
        Task<UnlockMapResultDTO> UnlockMapAsync(int userId, int mapId, string unlockMethod);

        /// <summary>
        /// 更新地图进度
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="mapId">地图ID</param>
        /// <param name="score">得分</param>
        /// <param name="completionTime">完成时间</param>
        /// <returns>更新结果</returns>
        Task<UpdateMapProgressResultDTO> UpdateMapProgressAsync(int userId, int mapId, int score, TimeSpan completionTime);

        /// <summary>
        /// 获取用户地图进度
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户地图进度</returns>
        Task<UserMapProgressDTO> GetUserMapProgressAsync(int userId);

        /// <summary>
        /// 检查地图解锁条件
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="mapId">地图ID</param>
        /// <returns>解锁条件检查结果</returns>
        Task<MapUnlockCheckResultDTO> CheckMapUnlockConditionsAsync(int userId, int mapId);
    }
}
