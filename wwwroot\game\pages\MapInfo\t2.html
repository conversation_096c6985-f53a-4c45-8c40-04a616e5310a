<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>地图详情</title>
    <script src="../Content/Javascript/jquery-1.8.3.min.js"></script>
    <script src="/game/js/game-api-adapter.js"></script>
    
    <style type="text/css"> 
        body { width: auto; height: auto; margin: 0px auto; font-size: 12px; background:#eeeeee;}
        *{ margin: 0px; padding: 0px; }
        pre { margin: 0px; padding: 0px; float: left; }
        img { margin: 0px; padding: 0px; border: 0px; }
        ul { margin: 0px; padding: 0px; }
        li { display: inline; list-style-type: none; }
        tr td{
            float:left;
            margin-right:10px;
        }
        .zdzd_box { width:788px; height:319px; float:left;}
        .box_left { width:301px; height:319px; float:left; background:url(img/zdzd_bj1.jpg);}
        .box_zhong { width:283px; height:319px; float:left; background:url(img/zdzd_bj2.jpg);}
        .box_right { width:204px; height:319px; float:left; background:url(img/zdzd_bj3.jpg);}
        .zdzd_jiesao { width:230px; height:90px; float:left; margin:70px 0px 0px 40px; float:left; line-height:22px; overflow:auto;}
        .zdzd_cong { width:247px; height:84px; float:left; margin:50px 0px 0px 10px; line-height:22px;}
        .zhong_list { width:260px; height:210px; float:left; margin:0px 0px 0px 5px;line-height:22px; margin-top:59px; color:#335e00; overflow:hidden;}
        .zhong_list a { text-decoration:none; color:#335e00;}
        .zhong_list a:hover { text-decoration:none; color:#335e00;}
        .zhong_list li { width:240px; height:26px; float:left;line-height:27px; display:inline;}
        .title { width:120px; height:22px; float:left;line-height:22px; overflow:hidden;}
        .title2 { width:35px; height:22px; float:left;line-height:22px;}
        .title3 { width:69px; height:17px; float:left;line-height:22px; text-align:center; margin-top:2px;}
        .anniu { width:265px; height:29px; float:left; margin:15px 0px 0px 5px;}
        .anniu1 { width:80px; height:29px; float:left; margin:0px 0px 0px 7px;}
        .zdzd_zxwj { width:170px; height:260px; float:left; margin:44px 0px 0px 10px; color:#335e00; overflow:auto; line-height:26px;}
        .zdzd_zxwj a{ text-decoration:none; color:#335e00;}
        .zdzd_zxwj a:hover{ text-decoration:none; color:#335e00;}
        .zxwj_list  { width:30px; height:26px; line-height:26px; float:left;}
        .zxwj_list img { margin:5px 0px 0px 4px;}
        .zxwj_list2  { width:120px; height:26px; line-height:26px; float:left;}
        
        .yy {
            cursor:pointer;
            filter:alpha(opacity=50);
            opacity:0.5;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            text-align: center;
            padding: 20px;
            color: #f44336;
        }
        
        .pet-selected {
            filter:alpha(opacity=100);
            opacity:1.0;
            border: 2px solid #4CAF50;
        }
    </style>
</head>

<body>
    <div class="zdzd_box">
        <!-- 左侧：地图信息和宠物选择 -->
        <div class="box_left">
            <div class="zdzd_jiesao" id="mapDescription">
                <div class="loading">正在加载地图信息...</div>
            </div>
            <div class="zdzd_cong">
                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr id="petSelection">
                        <td width="25">&nbsp;</td>
                        <td class="loading">正在加载宠物...</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- 中间：组队信息和操作按钮 -->
        <div class="box_zhong">
            <div class="zhong_list" id="teamList">
                <table border="0" style="border-bottom:1px solid #CCCCCC; color:#005500">
                    <tr>
                        <td align="center" height="21" width="95">队长</td>
                        <td align="center" width="50">队员人数</td>
                        <td align="center" width="80">申请加入</td>
                    </tr>
                </table>
                <div id="teamContent">
                    <div class="loading">正在加载组队信息...</div>
                </div>
            </div>
            
            <div class="anniu">
                <div class="anniu1">
                    <img src="img/zd.gif" width="78" height="29" 
                         style="cursor:pointer;" onclick="startBattle()" />
                </div>
                <div class="anniu1">
                    <img src="img/cjdw.gif" width="78" height="29" 
                         style="cursor:pointer;" onclick="createTeam()" />
                </div>
            </div>
        </div>
        
        <!-- 右侧：在线玩家 -->
        <div class="box_right">
            <div class="zdzd_zxwj" id="onlinePlayers">
                <div class="loading">正在加载在线玩家...</div>
            </div>
        </div>
    </div>
    
    <script type="text/javascript">
        let currentMapId = null;
        let currentMapData = null;
        let selectedPetId = null;
        let userPets = [];
        
        // 页面加载时初始化
        $(document).ready(function() {
            // 从URL获取地图ID
            currentMapId = getMapIdFromUrl();
            if (currentMapId) {
                loadMapData();
                loadUserPets();
                loadTeamInfo();
                loadOnlinePlayers();
            } else {
                showError('无效的地图ID');
            }
        });
        
        // 从URL获取地图ID
        function getMapIdFromUrl() {
            const path = window.location.pathname;
            const match = path.match(/t(\d+)\.html$/);
            return match ? parseInt(match[1]) : null;
        }
        
        // 加载地图数据
        async function loadMapData() {
            try {
                const userId = parent.gameAPI.getCurrentUserId();
                const result = await parent.gameAPI.getMapDetail(currentMapId, userId);
                
                if (result.success) {
                    currentMapData = result;
                    renderMapInfo();
                } else {
                    showError('加载失败: ' + result.message);
                }
            } catch (error) {
                console.error('加载地图数据失败:', error);
                showError('网络错误，请刷新重试');
            }
        }
        
        // 渲染地图信息
        function renderMapInfo() {
            const { mapInfo, monsters, drops } = currentMapData;
            
            let content = `<div><strong>${mapInfo.mapName}</strong></div>`;
            
            // 怪物信息
            if (monsters && monsters.length > 0) {
                content += '<div><strong>出现怪物：</strong></div>';
                monsters.forEach(monster => {
                    content += `<div>${monster.monsterName} (${monster.levelRange}) [${monster.element}]</div>`;
                });
            } else {
                content += '<div><strong>出现怪物：</strong>暂无怪物信息</div>';
            }
            
            // 奖励信息
            if (drops && drops.length > 0) {
                content += '<div><strong>可能掉落：</strong></div>';
                drops.forEach(drop => {
                    content += `<div>${drop.itemName} (${(drop.dropRate * 100).toFixed(1)}%)</div>`;
                });
            }
            
            $('#mapDescription').html(content);
        }
        
        // 加载用户宠物
        async function loadUserPets() {
            try {
                const userId = parent.gameAPI.getCurrentUserId();
                const result = await parent.gameAPI.getUserPets(userId);
                
                if (result.success) {
                    userPets = result.pets || [];
                    renderPetSelection();
                } else {
                    $('#petSelection').html('<td colspan="4">加载宠物失败</td>');
                }
            } catch (error) {
                console.error('加载宠物失败:', error);
                $('#petSelection').html('<td colspan="4">加载宠物失败</td>');
            }
        }
        
        // 渲染宠物选择
        function renderPetSelection() {
            const container = $('#petSelection');
            container.html('<td width="25">&nbsp;</td>');
            
            if (userPets.length === 0) {
                container.append('<td colspan="3">暂无宠物</td>');
                return;
            }
            
            userPets.slice(0, 3).forEach((pet, index) => {
                const isMain = pet.status === '0';
                const petHtml = `
                    <td class="${isMain ? 'pet-selected' : 'yy'}" onclick="selectPet(${pet.petSeq}, this)">
                        <img src="../Content/PetPhoto/k${pet.image}.gif" 
                             onerror="this.src='../Content/PetPhoto/k${pet.image}.png'"
                             style="width: 50px; height: 50px;" />
                    </td>
                `;
                container.append(petHtml);
                
                if (isMain) {
                    selectedPetId = pet.petSeq;
                }
            });
            
            // 填充空位
            for (let i = userPets.length; i < 3; i++) {
                container.append('<td></td>');
            }
            container.append('<td width="10">&nbsp;</td>');
        }
        
        // 选择宠物
        function selectPet(petId, element) {
            selectedPetId = petId;
            $('#petSelection td').removeClass('pet-selected').addClass('yy');
            $(element).removeClass('yy').addClass('pet-selected');
        }
        
        // 加载组队信息
        function loadTeamInfo() {
            // TODO: 实现组队信息加载
            $('#teamContent').html('<div style="text-align: center; padding: 20px;">暂无组队信息</div>');
        }
        
        // 加载在线玩家
        function loadOnlinePlayers() {
            // TODO: 实现在线玩家加载
            $('#onlinePlayers').html('<div style="text-align: center; padding: 20px;">暂无在线玩家</div>');
        }
        
        // 开始战斗
        async function startBattle() {
            if (!selectedPetId) {
                parent.showBox('请先选择一个宠物');
                return;
            }
            
            if (!currentMapData || !currentMapData.mapInfo.isUnlocked) {
                parent.showBox('该地图尚未解锁');
                return;
            }
            
            try {
                const userId = parent.gameAPI.getCurrentUserId();
                const result = await parent.gameAPI.enterMap(currentMapId, userId, selectedPetId);
                
                if (result.success) {
                    // 将战斗信息传递给父窗口
                    parent.battleInfo = result.battleInfo;
                    parent.StartBattle(currentMapId);
                } else {
                    parent.showBox(result.message || '进入战斗失败');
                }
            } catch (error) {
                console.error('开始战斗失败:', error);
                parent.showBox('网络错误，请稍后重试');
            }
        }
        
        // 创建队伍
        function createTeam() {
            if (confirm('确定要建立你的队伍？')) {
                // TODO: 实现创建队伍逻辑
                parent.showBox('创建队伍功能开发中...');
            }
        }
        
        // 显示错误信息
        function showError(message) {
            $('#mapDescription').html(`<div class="error">${message}</div>`);
        }
        
        // 兼容旧的函数调用
        function updateBattle_page(mapId, mode) {
            startBattle();
        }
    </script>
</body>
</html>
