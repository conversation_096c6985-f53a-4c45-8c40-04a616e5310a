-- 牧场页面测试数据
-- 为用户ID=1创建测试宠物数据

-- 1. 确保测试用户存在
INSERT IGNORE INTO user (id, username, password, nickname, gold, diamond, crystal, pasture_capacity, create_time) VALUES
(1, 'testuser', 'password123', '测试用户', 100000, 5000, 3000, 80, NOW());

-- 2. 创建测试宠物配置（如果不存在）
INSERT IGNORE INTO pet_config (pet_no, name, attribute, image, description, create_time) VALUES
(264, '小火龙', '火', '264', '火系宠物', NOW()),
(265, '小水龟', '水', '265', '水系宠物', NOW()),
(266, '小草精', '草', '266', '草系宠物', NOW()),
(267, '小雷鸟', '雷', '267', '雷系宠物', NOW()),
(268, '小土熊', '土', '268', '土系宠物', NOW()),
(269, '风精灵', '风', '269', '风系宠物', NOW()),
(270, '光天使', '光', '270', '光系宠物', NOW()),
(271, '暗影兽', '暗', '271', '暗系宠物', NOW());

-- 3. 清理用户1的现有宠物数据（避免重复）
DELETE FROM user_pet WHERE user_id = 1;

-- 4. 创建测试用户宠物数据
-- 牧场中的宠物（状态为"牧场"）
INSERT INTO user_pet (user_id, pet_no, custom_name, level, exp, growth, element, hp, mp, status, is_main, create_time) VALUES
(1, 264, '小火龙', 15, 12000, 25.5, '火', 150, 80, '牧场', 0, NOW()),
(1, 265, '小水龟', 18, 15000, 28.2, '水', 180, 100, '牧场', 0, NOW()),
(1, 266, '小草精', 12, 8000, 22.8, '草', 120, 120, '牧场', 0, NOW()),
(1, 267, '小雷鸟', 20, 18000, 30.1, '雷', 160, 140, '牧场', 0, NOW()),
(1, 268, '小土熊', 16, 13000, 26.7, '土', 200, 60, '牧场', 0, NOW());

-- 携带中的宠物（状态为"携带"）
INSERT INTO user_pet (user_id, pet_no, custom_name, level, exp, growth, element, hp, mp, status, is_main, create_time) VALUES
(1, 269, '风精灵', 25, 25000, 35.5, '风', 180, 160, '携带', 1, NOW()),  -- 主战宠物
(1, 270, '光天使', 22, 20000, 32.3, '光', 170, 180, '携带', 0, NOW()),
(1, 271, '暗影兽', 19, 16000, 29.8, '暗', 190, 110, '携带', 0, NOW());

-- 5. 验证数据
SELECT '=== 用户信息 ===' as info;
SELECT id, username, nickname, gold, diamond, crystal, pasture_capacity FROM user WHERE id = 1;

SELECT '=== 牧场宠物 ===' as info;
SELECT id, pet_no, custom_name, level, growth, element, status, is_main FROM user_pet WHERE user_id = 1 AND status = '牧场';

SELECT '=== 携带宠物 ===' as info;
SELECT id, pet_no, custom_name, level, growth, element, status, is_main FROM user_pet WHERE user_id = 1 AND status = '携带';

SELECT '=== 宠物统计 ===' as info;
SELECT 
    status,
    COUNT(*) as count,
    SUM(CASE WHEN is_main = 1 THEN 1 ELSE 0 END) as main_count
FROM user_pet 
WHERE user_id = 1 
GROUP BY status;

-- 6. 更新用户的牧场容量信息（如果需要）
UPDATE user SET pasture_capacity = 80 WHERE id = 1 AND (pasture_capacity IS NULL OR pasture_capacity = 0);

COMMIT;
