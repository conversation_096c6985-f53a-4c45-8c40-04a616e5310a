<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>聊天功能调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>聊天功能调试页面</h1>
        
        <div class="section">
            <h3>1. 脚本加载检查</h3>
            <div id="scriptStatus"></div>
            <button onclick="checkScripts()">检查脚本加载</button>
        </div>
        
        <div class="section">
            <h3>2. WebSocket连接测试</h3>
            <div id="wsStatus"></div>
            <button onclick="testWebSocket()">测试WebSocket连接</button>
            <button onclick="disconnectWebSocket()">断开连接</button>
        </div>
        
        <div class="section">
            <h3>3. 聊天功能测试</h3>
            <div id="chatStatus"></div>
            <input type="number" id="testPlayerId" placeholder="玩家ID" value="1001">
            <input type="text" id="testPlayerName" placeholder="玩家名称" value="测试用户">
            <button onclick="testLogin()">测试登录</button>
            <button onclick="testSendMessage()">发送测试消息</button>
        </div>
        
        <div class="section">
            <h3>4. 实时日志</h3>
            <div id="logOutput" class="log"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="section">
            <h3>5. 系统信息</h3>
            <div id="systemInfo"></div>
            <button onclick="getSystemInfo()">获取系统信息</button>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logOutput.textContent += logEntry;
            logOutput.scrollTop = logOutput.scrollHeight;
            
            // 同时输出到控制台
            console.log(logEntry);
        }

        // 检查脚本加载
        function checkScripts() {
            const scriptStatus = document.getElementById('scriptStatus');
            let results = [];
            
            // 检查jQuery
            if (typeof $ !== 'undefined') {
                results.push('<div class="status success">✓ jQuery 已加载</div>');
            } else {
                results.push('<div class="status error">✗ jQuery 未加载</div>');
            }
            
            // 检查ChatWebSocketClient
            if (typeof ChatWebSocketClient !== 'undefined') {
                results.push('<div class="status success">✓ ChatWebSocketClient 已加载</div>');
            } else {
                results.push('<div class="status error">✗ ChatWebSocketClient 未加载</div>');
            }
            
            // 检查全局变量
            if (typeof window.chatClient !== 'undefined') {
                results.push('<div class="status success">✓ window.chatClient 已定义</div>');
            } else {
                results.push('<div class="status warning">⚠ window.chatClient 未定义</div>');
            }
            
            scriptStatus.innerHTML = results.join('');
            log('脚本加载检查完成');
        }

        // 测试WebSocket连接
        function testWebSocket() {
            const wsStatus = document.getElementById('wsStatus');
            
            try {
                // 创建WebSocket客户端
                if (typeof ChatWebSocketClient === 'undefined') {
                    wsStatus.innerHTML = '<div class="status error">ChatWebSocketClient 未加载</div>';
                    log('WebSocket测试失败：ChatWebSocketClient未加载', 'error');
                    return;
                }
                
                window.testChatClient = new ChatWebSocketClient();
                
                // 重写事件处理器以显示状态
                const originalOnOpen = window.testChatClient.onOpen;
                window.testChatClient.onOpen = function(event) {
                    originalOnOpen.call(this, event);
                    wsStatus.innerHTML = '<div class="status success">WebSocket 连接成功</div>';
                    log('WebSocket连接成功', 'success');
                };
                
                const originalOnClose = window.testChatClient.onClose;
                window.testChatClient.onClose = function(event) {
                    originalOnClose.call(this, event);
                    wsStatus.innerHTML = '<div class="status error">WebSocket 连接已关闭</div>';
                    log('WebSocket连接已关闭', 'warning');
                };
                
                const originalOnError = window.testChatClient.onError;
                window.testChatClient.onError = function(error) {
                    originalOnError.call(this, error);
                    wsStatus.innerHTML = '<div class="status error">WebSocket 连接错误</div>';
                    log('WebSocket连接错误: ' + error, 'error');
                };
                
                // 连接
                window.testChatClient.connect();
                wsStatus.innerHTML = '<div class="status warning">正在连接WebSocket...</div>';
                log('开始连接WebSocket');
                
            } catch (error) {
                wsStatus.innerHTML = '<div class="status error">WebSocket 测试异常: ' + error.message + '</div>';
                log('WebSocket测试异常: ' + error.message, 'error');
            }
        }

        // 断开WebSocket连接
        function disconnectWebSocket() {
            if (window.testChatClient) {
                window.testChatClient.disconnect();
                log('手动断开WebSocket连接');
            }
        }

        // 测试登录
        function testLogin() {
            const chatStatus = document.getElementById('chatStatus');
            const playerId = parseInt(document.getElementById('testPlayerId').value);
            const playerName = document.getElementById('testPlayerName').value;
            
            if (!window.testChatClient || !window.testChatClient.isConnected) {
                chatStatus.innerHTML = '<div class="status error">请先建立WebSocket连接</div>';
                log('登录测试失败：WebSocket未连接', 'error');
                return;
            }
            
            try {
                window.testChatClient.login(playerId, playerName, '');
                chatStatus.innerHTML = '<div class="status success">登录请求已发送</div>';
                log(`发送登录请求：玩家ID=${playerId}, 名称=${playerName}`);
            } catch (error) {
                chatStatus.innerHTML = '<div class="status error">登录测试失败: ' + error.message + '</div>';
                log('登录测试失败: ' + error.message, 'error');
            }
        }

        // 发送测试消息
        function testSendMessage() {
            const chatStatus = document.getElementById('chatStatus');
            
            if (!window.testChatClient || !window.testChatClient.isConnected) {
                chatStatus.innerHTML = '<div class="status error">请先建立WebSocket连接</div>';
                log('发送消息失败：WebSocket未连接', 'error');
                return;
            }
            
            try {
                const testMessage = `测试消息 - ${new Date().toLocaleTimeString()}&&黑`;
                const success = window.testChatClient.sendChatMessage(testMessage, 'chat');
                
                if (success) {
                    chatStatus.innerHTML = '<div class="status success">测试消息已发送</div>';
                    log('测试消息发送成功: ' + testMessage);
                } else {
                    chatStatus.innerHTML = '<div class="status error">测试消息发送失败</div>';
                    log('测试消息发送失败', 'error');
                }
            } catch (error) {
                chatStatus.innerHTML = '<div class="status error">发送消息异常: ' + error.message + '</div>';
                log('发送消息异常: ' + error.message, 'error');
            }
        }

        // 获取系统信息
        function getSystemInfo() {
            const systemInfo = document.getElementById('systemInfo');
            
            const info = {
                'User Agent': navigator.userAgent,
                'URL': window.location.href,
                'WebSocket支持': 'WebSocket' in window ? '是' : '否',
                '当前时间': new Date().toLocaleString(),
                'jQuery版本': typeof $ !== 'undefined' ? $.fn.jquery : '未加载',
                'ChatClient状态': window.testChatClient ? 
                    (window.testChatClient.isConnected ? '已连接' : '未连接') : '未创建'
            };
            
            let infoHtml = '<table border="1" style="width:100%; border-collapse:collapse;">';
            for (const [key, value] of Object.entries(info)) {
                infoHtml += `<tr><td style="padding:5px;"><strong>${key}</strong></td><td style="padding:5px;">${value}</td></tr>`;
            }
            infoHtml += '</table>';
            
            systemInfo.innerHTML = infoHtml;
            log('系统信息已更新');
        }

        // 清空日志
        function clearLog() {
            document.getElementById('logOutput').textContent = '';
            log('日志已清空');
        }

        // 页面加载完成后自动检查
        window.addEventListener('load', function() {
            log('调试页面加载完成');
            setTimeout(() => {
                checkScripts();
                getSystemInfo();
            }, 500);
        });

        // 捕获全局错误
        window.addEventListener('error', function(event) {
            log(`全局错误: ${event.error.message} 在 ${event.filename}:${event.lineno}`, 'error');
        });

        // 动态加载脚本（如果需要）
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }

        // 如果jQuery未加载，尝试加载
        if (typeof $ === 'undefined') {
            log('jQuery未加载，尝试动态加载...', 'warning');
            loadScript('pages/Content/Javascript/jquery-1.8.3.min.js')
                .then(() => {
                    log('jQuery动态加载成功', 'success');
                    checkScripts();
                })
                .catch(() => {
                    log('jQuery动态加载失败', 'error');
                });
        }

        // 如果ChatWebSocketClient未加载，尝试加载
        if (typeof ChatWebSocketClient === 'undefined') {
            log('ChatWebSocketClient未加载，尝试动态加载...', 'warning');
            loadScript('js/chat-websocket.js')
                .then(() => {
                    log('ChatWebSocketClient动态加载成功', 'success');
                    return loadScript('js/chat-handler.js');
                })
                .then(() => {
                    log('chat-handler.js动态加载成功', 'success');
                    checkScripts();
                })
                .catch(() => {
                    log('聊天脚本动态加载失败', 'error');
                });
        }
    </script>
</body>
</html>
