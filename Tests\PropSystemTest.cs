using System.Text;
using System.Text.Json;
using WebApplication_HM.Models;
using WebApplication_HM.Services;
using Xunit;

namespace WebApplication_HM.Tests
{
    public class PropSystemTest
    {
        [Fact]
        public void TestPropInfoModel()
        {
            // 测试PropInfo模型的基本功能
            var prop = new PropInfo
            {
                ItemId = "test_001",
                ItemCount = 10,
                ItemPos = 1
            };

            Assert.Equal("test_001", prop.ItemId);
            Assert.Equal(10, prop.ItemCount);
            Assert.Equal(1, prop.ItemPos);

            // 测试中文属性访问器
            Assert.Equal("test_001", prop.道具类型ID);
            Assert.Equal("10", prop.道具数量);
            Assert.Equal("1", prop.道具位置);
        }

        [Fact]
        public void TestPropInfoJsonSerialization()
        {
            // 测试PropInfo的JSON序列化
            var prop = new PropInfo
            {
                ItemId = "test_002",
                ItemCount = 5,
                ItemPos = 2
            };

            var json = JsonSerializer.Serialize(prop);
            Assert.NotNull(json);
            Assert.Contains("test_002", json);

            var deserializedProp = JsonSerializer.Deserialize<PropInfo>(json);
            Assert.NotNull(deserializedProp);
            Assert.Equal("test_002", deserializedProp.ItemId);
            Assert.Equal(5, deserializedProp.ItemCount);
            Assert.Equal(2, deserializedProp.ItemPos);
        }
    }
}
