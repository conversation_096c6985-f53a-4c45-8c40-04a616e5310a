using WebApplication_HM.DTOs;
using WebApplication_HM.Models;

namespace WebApplication_HM.Interface
{
    /// <summary>
    /// 宠物进化服务接口
    /// </summary>
    public interface IPetEvolutionService
    {
        /// <summary>
        /// 执行宠物进化
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="request">进化请求</param>
        /// <returns>进化结果</returns>
        Task<EvolutionResultDto> EvolvePetAsync(int userId, EvolutionRequestDto request);

        /// <summary>
        /// 获取宠物进化信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="userPetId">用户宠物ID</param>
        /// <returns>进化信息</returns>
        Task<PetEvolutionInfoDto> GetPetEvolutionInfoAsync(int userId, int userPetId);

        /// <summary>
        /// 获取进化配置
        /// </summary>
        /// <param name="petNo">宠物编号</param>
        /// <param name="evolutionType">进化类型</param>
        /// <returns>进化配置</returns>
        Task<pet_evolution_config> GetEvolutionConfigAsync(int petNo, string evolutionType);

        /// <summary>
        /// 获取宠物进化历史
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="userPetId">用户宠物ID</param>
        /// <returns>进化历史列表</returns>
        Task<List<EvolutionHistoryDto>> GetEvolutionHistoryAsync(int userId, int userPetId);

        /// <summary>
        /// 验证进化条件
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="userPet">用户宠物</param>
        /// <param name="config">进化配置</param>
        /// <returns>验证结果</returns>
        Task<(bool IsValid, string ErrorMessage)> ValidateEvolutionConditionsAsync(int userId, user_pet userPet, pet_evolution_config config);

        /// <summary>
        /// 计算宠物等级
        /// </summary>
        /// <param name="exp">经验值</param>
        /// <returns>等级</returns>
        int CalculateLevel(long exp);

        /// <summary>
        /// 生成默认进化配置
        /// </summary>
        /// <param name="petNo">宠物编号</param>
        /// <param name="evolutionType">进化类型</param>
        /// <returns>默认配置</returns>
        Task<pet_evolution_config> GenerateDefaultConfigAsync(int petNo, string evolutionType);
    }
}
