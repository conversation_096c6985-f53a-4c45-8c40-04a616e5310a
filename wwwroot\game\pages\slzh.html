<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时空神殿</title>
    <link rel="stylesheet" href="css/slzh.css">
    <script src="js/jquery-1.8.3.min.js"></script>

    <script>
        var configData = [];
        //当前显示第几个
        var thisIndex = 0;
        $(function () {
            jbBD();
            //     alert(window.parent.sljson[0].Name)
            configData = window.parent.sljson;

            //这个读取应该在总配置加载完毕后！！！
            // alert(configData.length)

            $(".btn").mousemove(function () {
                data = $(this).attr('data');
                $(".lv").hide();
                $("." + data).show();
                $(".infoTJ").show();
            })
            $(".btn").mouseout(function () {
                $(".infoTJ").hide();
            })

            var newData = [
                {
                    "Name": "测试",
                    "注灵经验": 0,
                    "赐福经验": 0,
                    "羁绊经验": 0
                }, {
                    "Name": "测试",
                    "注灵经验": 100,
                    "赐福经验": 11111,
                    "羁绊经验": 1111
                }
            ];

            window.external.updateShiLing_Page();
            //  loadShiLing(newData);
        })
        var JSONLIST = [];
        function loadShiLing(JSON) {
            //   alert(JSON)
            JSON = $.parseJSON(JSON);
            JSONLIST = JSON;
            show(JSONLIST, thisIndex)
        }
        function fanye(i) {
            thisIndex += i;
            show(JSONLIST, thisIndex)

        }
        var thisJson = null;
        var userJson = null;
        function show(JSON, I) {
            //当前翻页页码小于0就不继续执行
            if (I < 0) {
                thisIndex = 0;
                window.parent.alert("已经是第一页啦")
                return;
            }
            //当前翻页页码大于成员数量则不继续执行
            if (I >= JSON.length) {
                thisIndex = JSON.length - 1;
                window.parent.alert("已经是最后一页啦")
                return;
            }

            //把当前JSON放到全局变量中，方便其他地方调用
            userJson = JSON[I];
            thisJson = findMemberByName(userJson["Name"]);
            //播放
            //      alert(thisJson["Name"]);
            $(".Name").html(userJson["Name"])
            loadImages(userJson["Name"], thisJson["帧数"], thisJson["播放延迟"]);
            //        alert("注灵等级："+getZhuLingLevel(userJson["注灵经验"])+"，羁绊等级："+getJiBanLevel(userJson["羁绊经验"]))
            var calcResult = calcAll();
            for (var r in calcResult) {
                $("." + r).html(accMul(calcResult[r], 100) + "%");

            }
            // for (var key in thisJson["羁绊配置"]) {
            //     if (thisJson["羁绊配置"].hasOwnProperty(key)) {
            //         //console.log("Key: " + key , thisJson["羁绊配置"][key]);
            //     }
            // }
            //赐福礼包
            var giftLv = getCiFuGiftLevel(userJson["赐福经验"]);
            // for (var key in thisJson["赐福礼包配置"]) {
            //     if (thisJson["赐福礼包配置"].hasOwnProperty(key)) {
            //         if (getCiFuLevel(userJson["赐福经验"]) >= key) giftLv++
            //     }
            // }
            // alert("赐福礼包LV: " + giftLv );
            if (giftLv > 0) {
                $("#cfGift").show()
                if (!userJson["赐福时间"] || userJson["赐福时间"] < getCurrentDateFormatted()) {
                    $('#cfGift').addClass('cfGiftLV' + giftLv);
                } else {
                    $('#cfGift').addClass('cfGiftLV' + giftLv + "_hide");
                }
            } else {
                $("#cfGift").hide()
            }

        }
        function findMemberByName(name) {

            // alert("2=="+configData.length)
            for (var i = 0; i < configData.length; i++) {
                if (configData[i].Name === name) {
                    return configData[i];
                }
            }
            return null;
        }
        //对羁绊的素材进行事件绑定，每次刷新列表都要调用，之所以不用on事件，是因为IE不一定兼容
        function jbBD() {

            $(".jbicon img").mousemove(function () {
                data = $(this).attr('data');
                var src = $(this).attr('src');
                if (src.indexOf('-') === -1) {
                    $(this).attr('src', 'img/slcw/jbImg/' + data + '-鼠标悬浮.png');
                }
            })
            $(".jbicon img").mouseout(function () {
                var src = $(this).attr('src');
                if (src.indexOf('-鼠标悬浮') != -1) {
                    data = $(this).attr('data');
                    $(this).attr('src', 'img/slcw/jbImg/' + data + '.png');

                }
            })

        }
        //提前预加载序列帧
        //参数1：名称
        //参数2：帧数
        //参数3：播放间隔
        function loadImages(name, num, s) {

            $(".image").html("");
            for (var i = 1; i <= num; i++) {
                $(".image").append("<img class='yingbi' src='img/slcw/petimg/" + name + "_" + i + ".png'>");
            }
            clearInterval(playTime);
            playTime = setInterval(play, s);
        }
        var playTime = 0;
        //当前播放到了哪里
        var thisPlayerNum = 0;
        function play() {
            var doms = $(".image img");
            if (thisPlayerNum >= doms.length) {
                thisPlayerNum = 0;
            }
            $(doms).hide();//全部隐蔽
            $(doms[thisPlayerNum]).show();//显示指定帧
            thisPlayerNum++;
        }

        function getCurrentDateFormatted() {
            var date = new Date();
            var year = date.getFullYear();
            var month = ("0" + (date.getMonth() + 1)).slice(-2); // 月份是从0开始的，需要加1并补零
            var day = ("0" + date.getDate()).slice(-2); // 补零
            return year + month + day;
        }

        /*
        获取赐福礼包等级
        */
        function getCiFuGiftLevel(exp) {
            var giftLv = 0
            for (var key in thisJson["赐福礼包配置"]) {
                if (thisJson["赐福礼包配置"].hasOwnProperty(key)) {
                    if (getCiFuLevel(exp) >= key) giftLv++
                }
            }
            return giftLv
        }

        /**
     * 获取注灵等级的方法，基于 ShiLingInfo 对象
     * @param {ShiLingInfo} exp - ShiLingInfo 对象
     * @returns {number} - 返回注灵等级（整数）
     */
        function getZhuLingLevel(exp) {
            var zl = thisJson;
            if (zl === null) return -1; // 没有配置

            // 每一级是100点
            if (exp > zl["注灵最高等级"] * 100) return zl["注灵最高等级"];
            else if (exp >= 100) return Math.floor(exp / 100);
            else return 0;
        }
        /**
         * 计算达到下一级所需的经验值
         * @param {number} currentExp - 当前经验值
         * @returns {number} - 达到下一级所需的经验值
         */
        function getNextZhuLingExp(currentExp) {
            var zl = thisJson;
            if (zl === null || currentExp < 0) return -1; // 没有配置或非法输入

            var baseExp = 100; // 每级需要的基础经验值
            var maxLevel = zl["注灵最高等级"]; // 最高等级

            for (var i = 1; i <= maxLevel; i++) {
                var levelExp = i * baseExp;
                if (currentExp < levelExp) {
                    return levelExp - currentExp;
                }
            }

            return -1; // 已经是最高等级
        }
        /**
         * 获取羁绊等级的方法，基于 ShiLingInfo 对象
         * @param {ShiLingInfo} exp - ShiLingInfo 对象
         * @returns {number} - 返回羁绊等级（整数）
         */

        function getJiBanLevel(exp) {
            var jb = thisJson;
            if (jb === null) return -1; // 没有配置

            // 第一级从500开始，每级递增200
            if (exp > 500 + (jb["羁绊最高等级"] - 1) * 200) return jb["羁绊最高等级"];
            else if (exp >= 500) {
                // 根据经验计算等级的公式
                // (经验-500)/200+1 得到等级
                return Math.floor((exp - 500) / 200) + 1;
            } else {
                return 0;
            }
        }
        /**
         * 计算达到下一级所需的经验值
         * @param {number} currentExp - 当前经验值
         * @returns {number} - 达到下一级所需的经验值
         */
        function getNextJiBanExp(currentExp) {
            var jb = thisJson;
            if (jb === null || currentExp < 0) return -1; // 没有配置或非法输入

            var baseExp = 200; // 每级需要的基础经验值
            var maxLevel = jb["羁绊最高等级"]; // 最高等级

            for (var i = 1; i <= maxLevel; i++) {
                var levelExp = 500 + (i - 1) * baseExp;
                if (currentExp < levelExp) {
                    return levelExp - currentExp;
                }
            }

            return -1; // 已经是最高等级
        }

        /**
         * 获取赐福等级
         * @param {object} l - ShiLingInfo 对象
         * @returns {number} - 计算出的赐福等级
         */
        function getCiFuLevel(exp) {
            var cf = thisJson;
            if (cf === null) return -1; // 没有配置
            // 根据等级配置每一级是100
            if (exp >= 300 + (cf["赐福最高等级"] - 1) * 100) return cf["赐福最高等级"];
            else if (exp >= 100) return Math.floor((exp - 300) / 100) + 1;
            else return 0;
        }
        /**
         * 获取下一级所需经验
         * @param {number} currentLevel - 当前等级
         * @returns {number} - 下一级所需经验值
         */
        function getNextLevelExp_cf(currentExp) {
            if (currentExp < 0) return -1; // 非法输入或无效配置信息

            var baseExp = 100; // 每级需要的基础经验值
            var maxLevel = thisJson["赐福最高等级"];

            var currentLevelExp = 300 + (maxLevel - 1) * baseExp; // 当前等级所需总经验

            for (var i = 1; i <= maxLevel; i++) {
                var levelExp = 300 + (i - 1) * baseExp;
                if (currentExp < levelExp) {
                    return levelExp - currentExp;
                }
            }

            return currentLevelExp - currentExp; // 返回剩余经验直至到达当前等级所需的总经验
        }
        function calcAll() {
            //计算玩家所有灵的属性
            var user = userJson;
            var zl = thisJson;

            var calcResult = {};
            calcResult["攻击"] = 0;
            calcResult["命中"] = 0;
            calcResult["防御"] = 0;
            calcResult["速度"] = 0;
            calcResult["闪避"] = 0;
            calcResult["生命"] = 0;
            calcResult["魔法"] = 0;
            calcResult["加深"] = 0;
            calcResult["抵消"] = 0;
            calcResult["吸血"] = 0;
            calcResult["吸魔"] = 0;
            if (zl == null || zl.length === 0) return calcResult; //没有配置信息
            //这里拿到对应灵的配置信息
            var l = user
            var l_cfg = findMemberByName(l.Name);
            //计算注灵属性
            var lv = getZhuLingLevel(l["注灵经验"]);

            var zlNextExp = getNextZhuLingExp(l["注灵经验"]);
            $(".注灵所需经验").html(zlNextExp);
            $(".注灵等级").html("Lv." + lv);
            //console.log("注灵等级为："+lv)
            if (lv > 0) {
                if (l_cfg == null) return;
                //计算注灵等级属性
                var LastLv = 0;
                for (var j in l_cfg["等级区间配置"]) {
                    var k = l_cfg["等级区间配置"][j];
                    if (lv > LastLv) {
                        if (lv > j) {
                            for (var key in k) {
                                calcResult[key] += k[key] * Math.abs(LastLv - j); //属性加成*(上次等级上限-本次区间等级上限)
                            }
                        } else {
                            for (var key in k) {
                                calcResult[key] += k[key] * (lv - LastLv); //属性加成*等级差
                            }
                        }
                    }
                    LastLv = j;
                }
            }
            var cfLv = getCiFuLevel(l["赐福经验"]);
            var cfNextExp = getNextLevelExp_cf(l["赐福经验"]);

            $(".赐福所需经验").html(cfNextExp);
            $(".赐福等级").html("Lv." + cfLv);

            var jbLv = getJiBanLevel(l["羁绊经验"]);
            var jbNextExp = getNextJiBanExp(l["羁绊经验"]);
            $(".羁绊所需经验").html(jbNextExp);
            //console.log("羁绊等级为："+jbLv);

            $(".羁绊等级").html("Lv." + jbLv);
            $('.jblist').html("");
            if (jbLv > 0) {
                //计算羁绊属性
                for (var j in l_cfg["羁绊配置"]) {
                    var k = l_cfg["羁绊配置"][j];
                    if (jbLv >= j) {
                        var dataValue = k["图标"];
                        var srcValue = "img/slcw/jbImg/" + k["图标"] + ".png";

                        var htmlToInsert = '<div class="jbicon"><img data="' + dataValue + '" src="' + srcValue + '"/></div>';

                        $('.jblist').append(htmlToInsert);

                        for (var key in k["属性"]) {
                            calcResult[key] += k["属性"][key]; //计算属性
                        }
                    }
                }
            }


            jbBD();
            //console.log(calcResult);
            return calcResult;
        }
        //乘法 
        function accMul(arg1, arg2) {
            if (arg1 == null) arg1 = 0;
            if (arg2 == null) arg1 = 0;
            var m = 0,
                s1 = arg1.toString(),
                s2 = arg2.toString();
            try {
                m += s1.split(".")[1].length
            } catch (e) { }
            try {
                m += s2.split(".")[1].length
            } catch (e) { }
            return (Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m)).toFixed(2)
        }
        //注灵
        function zl() {
            var num = $("#zlNum").val();
            var msg = window.external.ShiLing_AddExp(userJson["Name"], 1, num);
            window.parent.Alert(msg);

        }
        //赐福
        function cf() {
            var num = $("#cfNum").val();
            var msg = window.external.ShiLing_AddExp(userJson["Name"], 2, num);
            window.parent.Alert(msg);

        }
        //羁绊
        function jb() {
            var num = $("#jbNum").val();
            var msg = window.external.ShiLing_AddExp(userJson["Name"], 3, num);
            window.parent.Alert(msg);

        }
        //赐福礼包
        function cfLB() {
            var msg = window.external.receiveDailyGift_CiFu(userJson["Name"]);
            window.parent.Alert(msg);
            if (msg.indexOf("成功") != -1) {
                var giftLv = getCiFuGiftLevel(userJson["赐福经验"]);
                if (giftLv > 0) {
                    $('#cfGift').removeClass('cfGiftLV' + giftLv)
                    $('#cfGift').addClass('cfGiftLV' + giftLv + "_hide");
                }

            }
        }


    </script>
</head>

<body>
    <div class="main_box">
        <div class="returnPage" onclick="window.parent.openNpc(9)"></div>
        <div class="btns">
            <div class="btn zlbtn" data="注灵" onclick="$('.box').hide();$('.zlBox').show()"></div>
            <div class="btn cfbtn" data="赐福" onclick="$('.box').hide();$('.cfBox').show()"></div>
            <div class="btn jbbtn" data="羁绊" onclick="$('.box').hide();$('.jbBox').show()"></div>
        </div>

        <div class="cfGift" id="cfGift" data="赐福礼包" onclick="cfLB()"></div>

        <div class="image">


        </div>
        <div class="jblist">
            <div class="jbicon">
                <img data="蝶化庄生" src="img/slcw/jbImg/蝶化庄生.png" />
            </div>
            <div class="jbicon">
                <img data="蝶化庄生" src="img/slcw/jbImg/蝶化庄生-未激活.png" />
            </div>
            <div class="jbicon">
                <img data="蝶化庄生" src="img/slcw/jbImg/蝶化庄生-未激活.png" />
            </div>
            <div class="jbicon">
                <img data="蝶化庄生" src="img/slcw/jbImg/蝶化庄生-未激活.png" />
            </div>
        </div>
        <div class="fanye">
            <div class="_left" onclick="fanye(-1)"></div>

            <div class="_right" onclick="fanye(1)"></div>
        </div>
        <div class="box jbBox">
            <div class="boxLeft">
                <img src="img/slcw/icon_jb.png" />
            </div>
            <div class="boxRight">
                <div class="boxSR">
                    <span class="text-with-shadow-ie6">*使用数量</span><br>
                    <input type="text" value="1" id="jbNum" class="input"  maxlength="4" />
                    <div class="useBtn" onclick="jb()"></div>
                </div>
                <div class="BoxInfo">
                    <div class="BoxInfoLine">
                        <div class="BoxUseTitle text-with-shadow-ie6 ">
                            当前羁绊等级：
                        </div>
                        <div class="BoxUseText text-with-shadow-ie6 羁绊等级">
                            Lv.100
                        </div>

                        <div class="c"></div>
                    </div>
                    <div class="BoxInfoLine">
                        <div class="BoxUseTitle text-with-shadow-ie6 ">
                            下级所需经验：
                        </div>
                        <div class="BoxUseText text-with-shadow-ie6 羁绊所需经验">
                            10000
                        </div>

                        <div class="c"></div>
                    </div>
                </div>
            </div>
            <div class="close" onclick="$('.jbBox').hide()"> </div>
        </div>
        <div class="box cfBox">
            <div class="boxLeft">
                <img src="img/slcw/icon_cf.png" />
            </div>
            <div class="boxRight">
                <div class="boxSR">
                    <span class="text-with-shadow-ie6">*使用数量</span><br>
                    <input type="text" id="cfNum" value="1" class="input"  maxlength="4" />
                    <div class="useBtn" onclick="cf()"></div>
                </div>
                <div class="BoxInfo">
                    <div class="BoxInfoLine">
                        <div class="BoxUseTitle text-with-shadow-ie6 ">
                            当前赐福等级：
                        </div>
                        <div class="BoxUseText text-with-shadow-ie6 赐福等级">
                            Lv.100
                        </div>

                        <div class="c"></div>
                    </div>
                    <div class="BoxInfoLine">
                        <div class="BoxUseTitle text-with-shadow-ie6 ">
                            下级所需经验：
                        </div>
                        <div class="BoxUseText text-with-shadow-ie6 赐福所需经验  ">
                            10000
                        </div>

                        <div class="c"></div>
                    </div>
                </div>
            </div>
            <div class="close" onclick="$('.cfBox').hide()"> </div>
        </div>
        <div class="box zlBox">
            <div class="boxLeft">
                <img src="img/slcw/icon_zl.png" />
            </div>
            <div class="boxRight">
                <div class="boxSR">
                    <span class="text-with-shadow-ie6">*使用数量</span><br>
                    <input type="text" id="zlNum" value="1" class="input" maxlength="4" />
                    <div class="useBtn" onclick="zl()"></div>
                </div>
                <div class="BoxInfo">
                    <div class="BoxInfoLine">
                        <div class="BoxUseTitle text-with-shadow-ie6 ">
                            当前注灵等级：
                        </div>
                        <div class="BoxUseText text-with-shadow-ie6 注灵等级">
                            Lv.100
                        </div>

                        <div class="c"></div>
                    </div>
                    <div class="BoxInfoLine">
                        <div class="BoxUseTitle text-with-shadow-ie6 ">
                            下级所需经验：
                        </div>
                        <div class="BoxUseText text-with-shadow-ie6 注灵所需经验">
                            10000
                        </div>

                        <div class="c"></div>
                    </div>
                </div>
            </div>
            <div class="close" onclick="$('.zlBox').hide()"> </div>
        </div>
        <div class="box infoTJ">
            <div class="title Name">崽种·北冥·吃我一拳</div>
            <div class="sxList">
                <div class="box1">
                    <div class="boxLine 注灵 lv">
                        <div class="boxTitle">注灵等级：</div>
                        <div class="boxText 注灵等级">Lv.0</div>
                        <div class="c"></div>
                    </div>
                    <div class="boxLine 赐福 lv" style="display: none;">
                        <div class="boxTitle">赐福等级：</div>
                        <div class="boxText 赐福等级">Lv.0</div>
                        <div class="c"></div>
                    </div>
                    <div class="boxLine 羁绊 lv" style="display: none;">
                        <div class="boxTitle">羁绊等级：</div>
                        <div class="boxText 羁绊等级">Lv.0</div>
                        <div class="c"></div>
                    </div>
                    <div class="boxLine">
                        <div class="boxTitle">生命加成：</div>
                        <div class="boxText 生命">0%</div>
                        <div class="c"></div>
                    </div>
                    <div class="boxLine">
                        <div class="boxTitle">防御加成：</div>
                        <div class="boxText 防御">0%</div>
                        <div class="c"></div>
                    </div>
                    <div class="boxLine">
                        <div class="boxTitle">攻击加成：</div>
                        <div class="boxText 攻击">0%</div>
                        <div class="c"></div>
                    </div>
                    <div class="boxLine">
                        <div class="boxTitle">命中加成：</div>
                        <div class="boxText 命中">0%</div>
                        <div class="c"></div>
                    </div>
                </div>
                <div class="box2">
                    <div class="boxLine">
                        <div class="boxTitle">速度加成：</div>
                        <div class="boxText 速度">0%</div>
                        <div class="c"></div>
                    </div>
                    <div class="boxLine">
                        <div class="boxTitle">闪避加成：</div>
                        <div class="boxText 闪避">0%</div>
                        <div class="c"></div>
                    </div>
                    <div class="boxLine">
                        <div class="boxTitle">吸血加成：</div>
                        <div class="boxText 吸血">0%</div>
                        <div class="c"></div>
                    </div>
                    <div class="boxLine">
                        <div class="boxTitle">抵消加成：</div>
                        <div class="boxText 抵消">0%</div>
                        <div class="c"></div>
                    </div>
                    <div class="boxLine">
                        <div class="boxTitle">伤害加深：</div>
                        <div class="boxText 加深">0%</div>
                        <div class="c"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>


</body>

</html>