using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WebApplication_HM.DTOs;
using WebApplication_HM.DTOs.Common;
using WebApplication_HM.Interface;
using WebApplication_HM.Utils;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 转生系统控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class NirvanaController : ControllerBase
    {
        private readonly INirvanaService _nirvanaService;
        private readonly ILogger<NirvanaController> _logger;

        public NirvanaController(INirvanaService nirvanaService, ILogger<NirvanaController> logger)
        {
            _nirvanaService = nirvanaService;
            _logger = logger;
        }

        /// <summary>
        /// 执行转生
        /// </summary>
        /// <param name="request">转生请求</param>
        /// <returns>转生结果</returns>
        [HttpPost("execute")]
        public async Task<ApiResult<NirvanaResultDto>> ExecuteNirvana([FromBody] NirvanaRequestDto request)
        {
            try
            {
                _logger.LogInformation("收到转生请求 - 用户ID:{UserId}", request.UserId);

                if (!ModelState.IsValid)
                {
                    return ApiResult<NirvanaResultDto>.CreateError("请求参数无效");
                }

                var result = await _nirvanaService.ExecuteNirvanaAsync(request);
                
                _logger.LogInformation("转生请求处理完成 - 用户ID:{UserId}, 结果:{IsSuccess}", 
                    request.UserId, result.IsSuccess);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理转生请求失败 - 用户ID:{UserId}", request?.UserId);
                return ApiResult<NirvanaResultDto>.CreateError("转生请求处理失败");
            }
        }

        /// <summary>
        /// 执行变脸
        /// </summary>
        /// <param name="request">变脸请求</param>
        /// <returns>变脸结果</returns>
        [HttpPost("face-change")]
        public async Task<ApiResult<FaceChangeResultDto>> ExecuteFaceChange([FromBody] FaceChangeRequestDto request)
        {
            try
            {
                _logger.LogInformation("收到变脸请求 - 用户ID:{UserId}", request.UserId);

                if (!ModelState.IsValid)
                {
                    return ApiResult<FaceChangeResultDto>.CreateError("请求参数无效");
                }

                var result = await _nirvanaService.ExecuteFaceChangeAsync(request);

                _logger.LogInformation("变脸请求处理完成 - 用户ID:{UserId}, 结果:{IsSuccess}",
                    request.UserId, result.IsSuccess);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理变脸请求失败 - 用户ID:{UserId}", request?.UserId);
                return ApiResult<FaceChangeResultDto>.CreateError("变脸请求处理失败");
            }
        }

        /// <summary>
        /// 预览转生结果
        /// </summary>
        /// <param name="request">预览请求</param>
        /// <returns>预览结果</returns>
        [HttpPost("preview")]
        public async Task<ApiResult<NirvanaPreviewDto>> PreviewNirvana([FromBody] NirvanaPreviewRequestDto request)
        {
            try
            {
                _logger.LogInformation("收到转生预览请求 - 用户ID:{UserId}", request.UserId);

                if (!ModelState.IsValid)
                {
                    return ApiResult<NirvanaPreviewDto>.CreateError("请求参数无效");
                }

                var result = await _nirvanaService.PreviewNirvanaAsync(request);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理转生预览请求失败 - 用户ID:{UserId}", request?.UserId);
                return ApiResult<NirvanaPreviewDto>.CreateError("转生预览请求处理失败");
            }
        }

        /// <summary>
        /// 获取用户的涅槃兽列表
        /// </summary>
        /// <returns>涅槃兽列表</returns>
        [HttpGet("nirvana-beasts")]
        public async Task<ApiResult<List<object>>> GetUserNirvanaBeasts()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId <= 0)
                {
                    return ApiResult<List<object>>.CreateError("用户未登录或用户ID无效");
                }

                _logger.LogInformation("获取用户涅槃兽列表 - 用户ID:{UserId}", userId);

                // 获取用户所有宠物，筛选出涅槃兽（pet_no = 103）
                var nirvanaBeasts = await _nirvanaService.GetUserNirvanaBeastsAsync(userId);

                _logger.LogInformation("获取到{Count}只涅槃兽 - 用户ID:{UserId}", nirvanaBeasts.Count, userId);

                return ApiResult<List<object>>.CreateSuccess(nirvanaBeasts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户涅槃兽列表失败");
                return ApiResult<List<object>>.CreateError("获取涅槃兽列表失败");
            }
        }

        /// <summary>
        /// 获取指定用户的涅槃兽列表（保留原接口用于兼容性）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>涅槃兽列表</returns>
        [HttpGet("nirvana-beasts/{userId}")]
        public async Task<ApiResult<List<object>>> GetUserNirvanaBeastsByUserId(int userId)
        {
            try
            {
                _logger.LogInformation("获取用户涅槃兽列表 - 用户ID:{UserId}", userId);

                if (userId <= 0)
                {
                    return ApiResult<List<object>>.CreateError("用户ID无效");
                }

                // 获取用户所有宠物，筛选出涅槃兽（pet_no = 103）
                var nirvanaBeasts = await _nirvanaService.GetUserNirvanaBeastsAsync(userId);

                _logger.LogInformation("获取到{Count}只涅槃兽 - 用户ID:{UserId}", nirvanaBeasts.Count, userId);

                return ApiResult<List<object>>.CreateSuccess(nirvanaBeasts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户涅槃兽列表失败 - 用户ID:{UserId}", userId);
                return ApiResult<List<object>>.CreateError("获取涅槃兽列表失败");
            }
        }

        /// <summary>
        /// 获取转生配置列表
        /// </summary>
        /// <returns>配置列表</returns>
        [HttpGet("configs")]
        public async Task<ApiResult<List<PetNirvanaConfigDto>>> GetConfigs()
        {
            try
            {
                _logger.LogInformation("获取转生配置列表");

                var result = await _nirvanaService.GetNirvanaConfigsAsync();
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取转生配置列表失败");
                return ApiResult<List<PetNirvanaConfigDto>>.CreateError("获取转生配置失败");
            }
        }

        /// <summary>
        /// 获取用户转生记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="page">页码</param>
        /// <param name="size">页大小</param>
        /// <returns>转生记录</returns>
        [HttpGet("records/{userId}")]
        public async Task<ApiResult<PagedResult<NirvanaRecordDto>>> GetUserRecords(
            int userId, [FromQuery] int page = 1, [FromQuery] int size = 20)
        {
            try
            {
                _logger.LogInformation("获取用户转生记录 - 用户ID:{UserId}, 页码:{Page}", userId, page);

                if (page < 1) page = 1;
                if (size < 1 || size > 100) size = 20;

                var result = await _nirvanaService.GetUserRecordsAsync(userId, page, size);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户转生记录失败 - 用户ID:{UserId}", userId);
                return ApiResult<PagedResult<NirvanaRecordDto>>.CreateError("获取转生记录失败");
            }
        }

        /// <summary>
        /// 获取转生统计信息
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>统计信息</returns>
        [HttpGet("statistics")]
        public async Task<ApiResult<NirvanaStatisticsDto>> GetStatistics(
            [FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
        {
            try
            {
                _logger.LogInformation("获取转生统计信息 - 开始日期:{StartDate}, 结束日期:{EndDate}", 
                    startDate, endDate);

                // 默认查询最近30天
                if (!startDate.HasValue)
                    startDate = DateTime.Now.AddDays(-30);
                
                if (!endDate.HasValue)
                    endDate = DateTime.Now;

                var result = await _nirvanaService.GetStatisticsAsync(startDate, endDate);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取转生统计信息失败");
                return ApiResult<NirvanaStatisticsDto>.CreateError("获取统计信息失败");
            }
        }

        /// <summary>
        /// 获取用户转生冷却信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>冷却信息</returns>
        [HttpGet("cooldown/{userId}")]
        public async Task<ApiResult<object>> GetUserCooldown(int userId)
        {
            try
            {
                // 这里可以调用反作弊服务获取冷却信息
                // 暂时返回简单的响应
                var result = new
                {
                    UserId = userId,
                    CooldownRemaining = 0, // 剩余冷却时间（秒）
                    CanNirvana = true,
                    LastNirvanaTime = (DateTime?)null
                };

                return ApiResult<object>.CreateSuccess(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户转生冷却信息失败 - 用户ID:{UserId}", userId);
                return ApiResult<object>.CreateError("获取冷却信息失败");
            }
        }

        /// <summary>
        /// 获取转生帮助信息
        /// </summary>
        /// <returns>帮助信息</returns>
        [HttpGet("help")]
        [AllowAnonymous]
        public ApiResult<object> GetHelp()
        {
            try
            {
                var helpInfo = new
                {
                    Title = "转生系统帮助",
                    Description = "转生系统允许玩家将高级宠物重生为更强大的形态",
                    Rules = new[]
                    {
                        "需要主宠、副宠和涅槃兽三只宠物",
                        "所有宠物等级必须达到要求",
                        "转生有一定成功率，失败会损失副宠和涅槃兽",
                        "成功后获得新宠物，继承部分属性和成长",
                        "VIP用户享有成长加成",
                        "转生有冷却时间限制"
                    },
                    Tips = new[]
                    {
                        "使用辅助道具可以提高成功率",
                        "高成长的宠物转生收益更大",
                        "合理选择转生时机很重要",
                        "变脸功能可以改变外观但保持属性"
                    }
                };

                return ApiResult<object>.CreateSuccess(helpInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取转生帮助信息失败");
                return ApiResult<object>.CreateError("获取帮助信息失败");
            }
        }

        /// <summary>
        /// 验证转生条件
        /// </summary>
        /// <param name="request">转生请求</param>
        /// <returns>验证结果</returns>
        [HttpPost("validate")]
        public async Task<ApiResult<object>> ValidateNirvana([FromBody] NirvanaRequestDto request)
        {
            try
            {
                _logger.LogInformation("验证转生条件 - 用户ID:{UserId}", request.UserId);

                if (!ModelState.IsValid)
                {
                    return ApiResult<object>.CreateError("请求参数无效");
                }

                // 这里可以调用计算服务验证条件
                var result = new
                {
                    CanNirvana = true,
                    ValidationErrors = new List<string>(),
                    EstimatedCost = 500000L,
                    EstimatedSuccessRate = 0.3m
                };

                return ApiResult<object>.CreateSuccess(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证转生条件失败 - 用户ID:{UserId}", request?.UserId);
                return ApiResult<object>.CreateError("验证转生条件失败");
            }
        }

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        /// <returns>用户ID</returns>
        private int GetCurrentUserId()
        {
            try
            {
                // 1. 优先从中间件设置的上下文获取用户ID
                if (HttpContext.Items.TryGetValue("UserId", out var contextUserId) &&
                    contextUserId is int userId && userId > 0)
                {
                    return userId;
                }

                // 2. 从Session中获取用户ID
                var sessionUserId = GetUserIdFromSession();
                if (sessionUserId > 0)
                {
                    return sessionUserId;
                }

                // 3. 从请求头中获取用户ID（用于API调用）
                var headerUserId = GetUserIdFromHeaders();
                if (headerUserId > 0)
                {
                    return headerUserId;
                }

                // 4. 从查询参数中获取用户ID（用于测试）
                var queryUserId = GetUserIdFromQuery();
                if (queryUserId > 0)
                {
                    return queryUserId;
                }

                // 5. 开发环境下的默认测试用户
                if (HttpContext.RequestServices.GetService<IWebHostEnvironment>()?.IsDevelopment() == true)
                {
                    _logger.LogWarning("开发环境：使用默认测试用户ID 1");
                    return 1;
                }

                // 6. 生产环境下返回0表示未认证
                _logger.LogWarning("未找到有效的用户身份信息");
                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户ID时发生错误");
                return 0;
            }
        }

        /// <summary>
        /// 从Session中获取用户ID
        /// </summary>
        /// <returns>用户ID</returns>
        private int GetUserIdFromSession()
        {
            try
            {
                if (HttpContext.Session.TryGetValue("UserId", out var userIdBytes))
                {
                    return BitConverter.ToInt32(userIdBytes, 0);
                }

                // 尝试从Session中获取用户登录信息
                var userLoginJson = HttpContext.Session.GetString("UserLogin");
                if (!string.IsNullOrEmpty(userLoginJson))
                {
                    var userLogin = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(userLoginJson);
                    if (userLogin.TryGetValue("userId", out var userIdObj) &&
                        int.TryParse(userIdObj.ToString(), out var userId))
                    {
                        return userId;
                    }
                }

                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从Session获取用户ID失败");
                return 0;
            }
        }

        /// <summary>
        /// 从请求头中获取用户ID
        /// </summary>
        /// <returns>用户ID</returns>
        private int GetUserIdFromHeaders()
        {
            try
            {
                // 从自定义请求头获取用户ID
                var userIdHeader = HttpContext.Request.Headers["X-User-Id"].FirstOrDefault();
                if (!string.IsNullOrEmpty(userIdHeader) && int.TryParse(userIdHeader, out var userId))
                {
                    return userId;
                }

                // 从游戏客户端传递的用户ID头获取
                var gameUserIdHeader = HttpContext.Request.Headers["X-Game-User-Id"].FirstOrDefault();
                if (!string.IsNullOrEmpty(gameUserIdHeader) && int.TryParse(gameUserIdHeader, out var gameUserId))
                {
                    return gameUserId;
                }

                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从请求头获取用户ID失败");
                return 0;
            }
        }

        /// <summary>
        /// 从查询参数中获取用户ID（主要用于测试）
        /// </summary>
        /// <returns>用户ID</returns>
        private int GetUserIdFromQuery()
        {
            try
            {
                var userIdQuery = HttpContext.Request.Query["userId"].FirstOrDefault();
                if (!string.IsNullOrEmpty(userIdQuery) && int.TryParse(userIdQuery, out var userId))
                {
                    return userId;
                }

                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从查询参数获取用户ID失败");
                return 0;
            }
        }
    }
}
