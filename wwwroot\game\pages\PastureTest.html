<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>牧场功能测试</title>
    <script src="Content/Javascript/jquery-1.8.3.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 牧场系统功能测试</h1>
    
    <div class="test-section">
        <h2>1. API 连接测试</h2>
        <button onclick="testApiConnection()">测试 API 连接</button>
        <div id="apiTestResult"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 获取宠物列表测试</h2>
        <button onclick="testGetPetList()">获取宠物列表</button>
        <div id="petListResult"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 获取牧场容量测试</h2>
        <button onclick="testGetCapacity()">获取牧场容量</button>
        <div id="capacityResult"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 宠物操作测试</h2>
        <input type="number" id="testPetId" placeholder="输入宠物ID" value="14">
        <button onclick="testCarryPet()">携带宠物</button>
        <button onclick="testStorePet()">存放宠物</button>
        <div id="operationResult"></div>
    </div>
    
    <div class="test-section">
        <h2>5. 前端JavaScript模块测试</h2>
        <button onclick="testJavaScriptModule()">测试 PetManagement 类</button>
        <div id="jsModuleResult"></div>
    </div>

    <script>
        // 测试用户ID
        const TEST_USER_ID = 1;
        
        // 显示测试结果
        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            container.appendChild(resultDiv);
        }
        
        // 显示JSON数据
        function showJsonResult(containerId, data, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `
                <strong>${new Date().toLocaleTimeString()}</strong>: 
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            container.appendChild(resultDiv);
        }
        
        // 1. 测试API连接
        async function testApiConnection() {
            try {
                showResult('apiTestResult', '正在测试API连接...', 'info');
                
                const response = await fetch('/api/PetManagement/capacity/1');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult('apiTestResult', '✅ API连接测试成功', 'success');
                    showJsonResult('apiTestResult', data, 'success');
                } else {
                    showResult('apiTestResult', '❌ API连接测试失败: ' + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                showResult('apiTestResult', '❌ API连接测试异常: ' + error.message, 'error');
            }
        }
        
        // 2. 测试获取宠物列表
        async function testGetPetList() {
            try {
                showResult('petListResult', '正在获取宠物列表...', 'info');
                
                const response = await fetch(`/api/PetManagement/pasture/${TEST_USER_ID}?status=牧场&page=1&pageSize=10`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult('petListResult', `✅ 获取宠物列表成功，共 ${data.pets.length} 只宠物`, 'success');
                    showJsonResult('petListResult', {
                        message: data.message,
                        petCount: data.pets.length,
                        currentCount: data.currentCount,
                        maxCapacity: data.maxCapacity,
                        samplePet: data.pets[0] || null
                    }, 'success');
                } else {
                    showResult('petListResult', '❌ 获取宠物列表失败: ' + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                showResult('petListResult', '❌ 获取宠物列表异常: ' + error.message, 'error');
            }
        }
        
        // 3. 测试获取牧场容量
        async function testGetCapacity() {
            try {
                showResult('capacityResult', '正在获取牧场容量...', 'info');
                
                const response = await fetch(`/api/PetManagement/capacity/${TEST_USER_ID}`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult('capacityResult', '✅ 获取牧场容量成功', 'success');
                    showJsonResult('capacityResult', data.data, 'success');
                } else {
                    showResult('capacityResult', '❌ 获取牧场容量失败: ' + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                showResult('capacityResult', '❌ 获取牧场容量异常: ' + error.message, 'error');
            }
        }
        
        // 4. 测试携带宠物
        async function testCarryPet() {
            const petId = document.getElementById('testPetId').value;
            if (!petId) {
                showResult('operationResult', '❌ 请输入宠物ID', 'error');
                return;
            }
            
            try {
                showResult('operationResult', `正在携带宠物 ${petId}...`, 'info');
                
                const response = await fetch('/api/PetManagement/carry', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        userId: TEST_USER_ID,
                        petId: parseInt(petId)
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult('operationResult', `✅ 携带宠物 ${petId} 成功`, 'success');
                    showJsonResult('operationResult', data, 'success');
                } else {
                    showResult('operationResult', `❌ 携带宠物 ${petId} 失败: ` + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                showResult('operationResult', `❌ 携带宠物 ${petId} 异常: ` + error.message, 'error');
            }
        }
        
        // 5. 测试存放宠物
        async function testStorePet() {
            const petId = document.getElementById('testPetId').value;
            if (!petId) {
                showResult('operationResult', '❌ 请输入宠物ID', 'error');
                return;
            }
            
            try {
                showResult('operationResult', `正在存放宠物 ${petId}...`, 'info');
                
                const response = await fetch('/api/PetManagement/store', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        userId: TEST_USER_ID,
                        petId: parseInt(petId)
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult('operationResult', `✅ 存放宠物 ${petId} 成功`, 'success');
                    showJsonResult('operationResult', data, 'success');
                } else {
                    showResult('operationResult', `❌ 存放宠物 ${petId} 失败: ` + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                showResult('operationResult', `❌ 存放宠物 ${petId} 异常: ` + error.message, 'error');
            }
        }
        
        // 6. 测试JavaScript模块
        function testJavaScriptModule() {
            try {
                showResult('jsModuleResult', '正在测试JavaScript模块...', 'info');
                
                // 检查PetManagement类是否存在
                if (typeof PetManagement !== 'undefined') {
                    showResult('jsModuleResult', '✅ PetManagement 类已加载', 'success');
                    
                    // 尝试创建实例
                    const petMgmt = new PetManagement();
                    showResult('jsModuleResult', '✅ PetManagement 实例创建成功', 'success');
                    
                    // 检查关键方法
                    const methods = ['init', 'loadPets', 'carryPet', 'storePet', 'setMainPet'];
                    const availableMethods = methods.filter(method => typeof petMgmt[method] === 'function');
                    
                    showResult('jsModuleResult', `✅ 可用方法: ${availableMethods.join(', ')}`, 'success');
                    
                } else {
                    showResult('jsModuleResult', '❌ PetManagement 类未加载，请检查 pet-management.js 文件', 'error');
                }
            } catch (error) {
                showResult('jsModuleResult', '❌ JavaScript模块测试异常: ' + error.message, 'error');
            }
        }
        
        // 页面加载完成后自动运行基础测试
        $(document).ready(function() {
            showResult('apiTestResult', '页面加载完成，可以开始测试', 'info');
        });
    </script>
    
    <!-- 尝试加载PetManagement模块 -->
    <script src="/game/js/pet-management.js"></script>
</body>
</html>
