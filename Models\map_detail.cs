﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///地图明细表
    ///</summary>
    [SugarTable("map_detail")]
    public partial class map_detail
    {
           public map_detail(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:地图ID（关联map_config.map_id）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int map_id {get;set;}

           /// <summary>
           /// Desc:限制成长
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? limit_growth {get;set;}

           /// <summary>
           /// Desc:限制等级
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? limit_level {get;set;}

           /// <summary>
           /// Desc:限制钥匙（1为限制，0为不限制）
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? limit_key {get;set;}

           /// <summary>
           /// Desc:最小金币
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? min_gold {get;set;}

           /// <summary>
           /// Desc:最小元宝
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? min_yuanbao {get;set;}

           /// <summary>
           /// Desc:最大金币
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? max_gold {get;set;}

           /// <summary>
           /// Desc:最大元宝
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? max_yuanbao {get;set;}

           /// <summary>
           /// Desc:最大掉落
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? max_drop {get;set;}

           /// <summary>
           /// Desc:最小掉落
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? min_drop {get;set;}

           /// <summary>
           /// Desc:地图图标
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? icon {get;set;}

           /// <summary>
           /// Desc:地图类型
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? type {get;set;}

    }
}
