-- 宠物进化功能测试数据
-- 创建测试用户和宠物数据

-- 1. 创建测试用户（如果不存在）
INSERT IGNORE INTO user (id, username, password, email, gold, exp, level, create_time) 
VALUES (1, 'testuser', 'password123', '<EMAIL>', 100000, 0, 1, NOW());

-- 2. 创建测试宠物配置（如果不存在）
INSERT IGNORE INTO pet_config (pet_no, name, attribute, image, description, create_time) VALUES
(1, '火龙', '火', '001', '火系宠物', NOW()),
(2, '水龟', '水', '002', '水系宠物', NOW()),
(3, '风鹰', '风', '003', '风系宠物', NOW()),
(4, '土熊', '土', '004', '土系宠物', NOW()),
(5, '雷豹', '雷', '005', '雷系宠物', NOW()),
(10, '神龙', '神', '010', '神系宠物', NOW()),
(11, '圣兽', '神圣', '011', '神圣系宠物', NOW());

-- 3. 创建测试用户宠物
INSERT IGNORE INTO user_pet (id, user_id, pet_no, custom_name, level, exp, growth, evolve_count, element, image, state, create_time) VALUES
(1, 1, 1, '小火龙', 45, 150000, 25.5, 0, '火', 1, 0, NOW()),
(2, 1, 2, '小水龟', 50, 200000, 30.2, 1, '水', 2, 0, NOW()),
(3, 1, 10, '神龙宝宝', 65, 500000, 45.8, 2, '神', 10, 0, NOW());

-- 4. 创建测试道具配置
INSERT IGNORE INTO item_config (item_no, name, type, description, image, create_time) VALUES
(2016110512, 'A丹', '进化道具', 'A路线进化道具', '512', NOW()),
(2016110513, 'B丹', '进化道具', 'B路线进化道具', '513', NOW()),
(2016110545, '玉露结晶', '神系进化道具', '神系A路线进化道具', '545', NOW()),
(2016110546, '天仙玉露', '神系进化道具', '神系B路线进化道具', '546', NOW());

-- 5. 给测试用户添加进化道具
INSERT IGNORE INTO user_item (user_id, item_id, item_count, create_time) VALUES
(1, '2016110512', 10, NOW()),
(1, '2016110513', 10, NOW()),
(1, '2016110545', 5, NOW()),
(1, '2016110546', 5, NOW());

-- 6. 验证进化配置数据是否存在
SELECT COUNT(*) as evolution_config_count FROM pet_evolution_config;

-- 7. 如果进化配置为空，插入一些测试配置
INSERT IGNORE INTO pet_evolution_config (pet_no, evolution_type, target_pet_no, required_level, required_item_id, required_item_count, cost_gold, success_rate, growth_min, growth_max, is_active, description, create_time) VALUES
-- 火龙进化配置
(1, 'A', 2, 40, '2016110512', 1, 1000, 100.00, 0.100, 0.500, 1, 'A路线进化：火龙 → 水龟', NOW()),
(1, 'B', 3, 40, '2016110513', 1, 1000, 100.00, 0.500, 1.000, 1, 'B路线进化：火龙 → 风鹰', NOW()),

-- 水龟进化配置
(2, 'A', 4, 45, '2016110512', 1, 1000, 100.00, 0.100, 0.500, 1, 'A路线进化：水龟 → 土熊', NOW()),
(2, 'B', 5, 45, '2016110513', 1, 1000, 100.00, 0.500, 1.000, 1, 'B路线进化：水龟 → 雷豹', NOW()),

-- 神龙进化配置
(10, 'A', 11, 60, '2016110545', 1, 1000, 100.00, 0.100, 0.300, 1, 'A路线进化：神龙 → 圣兽', NOW()),
(10, 'B', 10, 60, '2016110546', 1, 1000, 100.00, 0.300, 0.600, 1, 'B路线进化：神龙强化', NOW());

-- 8. 查看测试数据
SELECT '=== 用户信息 ===' as info;
SELECT id, username, gold, level FROM user WHERE id = 1;

SELECT '=== 用户宠物 ===' as info;
SELECT id, pet_no, custom_name, level, growth, evolve_count, element FROM user_pet WHERE user_id = 1;

SELECT '=== 用户道具 ===' as info;
SELECT ui.item_id, ic.name, ui.item_count 
FROM user_item ui 
LEFT JOIN item_config ic ON ui.item_id = ic.item_no 
WHERE ui.user_id = 1;

SELECT '=== 进化配置 ===' as info;
SELECT pet_no, evolution_type, target_pet_no, required_level, required_item_id, growth_min, growth_max 
FROM pet_evolution_config 
WHERE pet_no IN (1, 2, 10) 
ORDER BY pet_no, evolution_type;

-- 9. 创建等级配置表数据（如果需要）
INSERT IGNORE INTO level_config (level, required_exp, type, create_time) VALUES
(1, 0, 'pet', NOW()),
(10, 1000, 'pet', NOW()),
(20, 5000, 'pet', NOW()),
(30, 20000, 'pet', NOW()),
(40, 50000, 'pet', NOW()),
(50, 100000, 'pet', NOW()),
(60, 200000, 'pet', NOW()),
(70, 400000, 'pet', NOW()),
(80, 800000, 'pet', NOW()),
(90, 1600000, 'pet', NOW()),
(100, 3200000, 'pet', NOW());

COMMIT;
