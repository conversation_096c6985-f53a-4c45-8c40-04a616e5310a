using WebApplication_HM.Interface;
using WebApplication_HM.DTOs;
using WebApplication_HM.Models;
using System.Text.Json;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 宝石服务实现
    /// </summary>
    public class GemstoneService : IGemstoneService
    {
        private readonly IEquipmentRepository _equipmentRepository;
        private readonly ILogger<GemstoneService> _logger;

        public GemstoneService(IEquipmentRepository equipmentRepository, ILogger<GemstoneService> logger)
        {
            _equipmentRepository = equipmentRepository;
            _logger = logger;
        }

        public async Task<List<GemstoneConfigDto>> GetAllGemstonesAsync()
        {
            try
            {
                var configs = await _equipmentRepository.GetAllGemstoneConfigsAsync();
                return configs.Select(MapToDto).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有宝石配置失败");
                throw;
            }
        }

        public async Task<List<GemstoneConfigDto>> GetGemstonesByLevelAsync(int level)
        {
            try
            {
                var configs = await _equipmentRepository.GetAllGemstoneConfigsAsync();
                return configs.Where(x => x.level == level)
                             .Select(MapToDto)
                             .ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取指定等级宝石配置失败，等级: {Level}", level);
                throw;
            }
        }

        public async Task<GemstoneConfigDto?> GetGemstoneByTypeNameAsync(string typeName)
        {
            try
            {
                var config = await _equipmentRepository.GetGemstoneConfigAsync(typeName);
                return config != null ? MapToDto(config) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宝石配置失败，类型: {TypeName}", typeName);
                throw;
            }
        }

        public async Task<List<GemstoneDto>> GetEquipmentGemstonesAsync(int userEquipmentId)
        {
            try
            {
                var gemstones = await _equipmentRepository.GetEquipmentGemstonesAsync(userEquipmentId);
                var result = new List<GemstoneDto>();

                foreach (var gemstone in gemstones)
                {
                    var config = await _equipmentRepository.GetGemstoneConfigAsync(gemstone.gemstone_type_name);
                    if (config != null)
                    {
                        result.Add(new GemstoneDto
                        {
                            TypeName = config.type_name,
                            UpType = config.up_type,
                            UpNum = config.up_num,
                            Level = config.level,
                            Color = config.color,
                            TypeClass = config.type_class,
                            Position = gemstone.position,
                            CreateTime = gemstone.create_time
                        });
                    }
                }

                return result.OrderBy(x => x.Position).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取装备宝石失败，装备ID: {EquipmentId}", userEquipmentId);
                throw;
            }
        }

        public async Task<bool> CanEmbedGemstoneAsync(int userEquipmentId, string gemstoneTypeName, int position)
        {
            try
            {
                // 1. 检查宝石配置是否存在
                var gemstoneConfig = await _equipmentRepository.GetGemstoneConfigAsync(gemstoneTypeName);
                if (gemstoneConfig == null)
                {
                    return false;
                }

                // 2. 检查位置是否有效
                if (position < 1 || position > 3)
                {
                    return false;
                }

                // 3. 检查宝石是否可以镶嵌在指定位置
                var positions = ParseJsonArray(gemstoneConfig.positions);
                if (positions != null && !positions.Contains(position.ToString()))
                {
                    return false;
                }

                // 4. 检查位置是否已有宝石
                var existingGemstones = await _equipmentRepository.GetEquipmentGemstonesAsync(userEquipmentId);
                if (existingGemstones.Any(x => x.position == position))
                {
                    return false;
                }

                // 5. 检查装备槽位数量
                var equipment = await _equipmentRepository.GetEquipmentByIdAsync(userEquipmentId);
                if (equipment == null || equipment.slot < position)
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查宝石镶嵌条件失败");
                return false;
            }
        }

        private GemstoneConfigDto MapToDto(gemstone_config config)
        {
            return new GemstoneConfigDto
            {
                Id = config.id,
                TypeName = config.type_name,
                UpType = config.up_type,
                UpNum = config.up_num,
                Level = config.level,
                Color = config.color,
                PropId = config.prop_id,
                TypeClass = config.type_class,
                EquipTypes = ParseJsonArray(config.equip_types),
                Positions = ParseJsonArray(config.positions)?.Select(int.Parse).ToList(),
                OrderNum = config.order_num ?? 0
            };
        }

        private List<string>? ParseJsonArray(string? jsonString)
        {
            if (string.IsNullOrEmpty(jsonString))
                return null;

            try
            {
                return JsonSerializer.Deserialize<List<string>>(jsonString);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "解析JSON数组失败: {JsonString}", jsonString);
                return null;
            }
        }
    }
}
