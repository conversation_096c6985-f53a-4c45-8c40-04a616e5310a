﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///任务奖励记录表
    ///</summary>
    [SugarTable("task_reward_log")]
    public partial class task_reward_log
    {
           public task_reward_log(){


           }
           /// <summary>
           /// Desc:记录ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int log_id {get;set;}

           /// <summary>
           /// Desc:用户ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:任务ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string task_id {get;set;}

           /// <summary>
           /// Desc:奖励类型(ITEM,CURRENCY,EQUIPMENT等)
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string reward_type {get;set;}

           /// <summary>
           /// Desc:奖励物品ID
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? reward_id {get;set;}

           /// <summary>
           /// Desc:奖励数量
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int reward_amount {get;set;}

           /// <summary>
           /// Desc:奖励描述
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? reward_description {get;set;}

           /// <summary>
           /// Desc:发放时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? granted_at {get;set;}

    }
}
