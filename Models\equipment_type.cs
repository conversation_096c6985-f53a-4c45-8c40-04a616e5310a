﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("equipment_type")]
    public partial class equipment_type
    {
           public equipment_type(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:装备类型ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string equip_type_id {get;set;}

           /// <summary>
           /// Desc:装备类型名称
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string type_name {get;set;}

           /// <summary>
           /// Desc:类型描述
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? description {get;set;}

           /// <summary>
           /// Desc:排序号
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? sort_order {get;set;}

           /// <summary>
           /// Desc:是否启用
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? is_active {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? update_time {get;set;}

    }
}
