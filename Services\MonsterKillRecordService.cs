using WebApplication_HM.Models;
using SqlSugar;
using Microsoft.Extensions.Logging;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 怪物击杀记录服务（简化版）
    /// </summary>
    public class MonsterKillRecordService
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<MonsterKillRecordService> _logger;

        public MonsterKillRecordService(ISqlSugarClient db, ILogger<MonsterKillRecordService> logger)
        {
            _db = db;
            _logger = logger;
        }

        /// <summary>
        /// 获取用户指定怪物的击杀数量
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="monsterId">怪物ID</param>
        /// <param name="startDate">开始日期（可选）</param>
        /// <param name="endDate">结束日期（可选）</param>
        /// <returns>击杀数量</returns>
        public async Task<int> GetUserMonsterKillCountAsync(int userId, int monsterId, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                var query = _db.Queryable<monster_kill_record>()
                    .Where(x => x.user_id == userId && x.monster_id == monsterId);

                if (startDate.HasValue)
                {
                    query = query.Where(x => x.kill_time >= startDate.Value);
                }

                if (endDate.HasValue)
                {
                    query = query.Where(x => x.kill_time <= endDate.Value);
                }

                return await query.CountAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户击杀数量失败: UserId={UserId}, MonsterId={MonsterId}", userId, monsterId);
                return 0;
            }
        }

        /// <summary>
        /// 获取用户今日击杀数量
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="monsterId">怪物ID</param>
        /// <returns>今日击杀数量</returns>
        public async Task<int> GetUserTodayKillCountAsync(int userId, int monsterId)
        {
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);
            return await GetUserMonsterKillCountAsync(userId, monsterId, today, tomorrow);
        }

        /// <summary>
        /// 插入击杀记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="monsterId">怪物ID</param>
        /// <param name="battleId">战斗ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> InsertKillRecordAsync(int userId, int monsterId, string battleId)
        {
            try
            {
                var killRecord = new monster_kill_record
                {
                    user_id = userId,
                    monster_id = monsterId,
                    battle_id = battleId,
                    kill_time = DateTime.Now,
                    created_at = DateTime.Now
                };

                var result = await _db.Insertable(killRecord).ExecuteCommandAsync();
                _logger.LogInformation("击杀记录已保存: UserId={UserId}, MonsterId={MonsterId}, BattleId={BattleId}",
                    userId, monsterId, battleId);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存击杀记录失败: UserId={UserId}, MonsterId={MonsterId}", userId, monsterId);
                return false;
            }
        }

        /// <summary>
        /// 获取用户击杀统计
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="date">指定日期（可选，默认今天）</param>
        /// <returns>击杀统计</returns>
        public async Task<v_user_daily_kill_stats?> GetUserDailyKillStatsAsync(int userId, DateTime? date = null)
        {
            try
            {
                var targetDate = date ?? DateTime.Today;
                return await _db.Queryable<v_user_daily_kill_stats>()
                    .Where(x => x.user_id == userId && x.kill_date.Date == targetDate.Date)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户每日击杀统计失败: UserId={UserId}", userId);
                return null;
            }
        }

        /// <summary>
        /// 获取用户指定怪物的击杀统计
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="monsterId">怪物ID</param>
        /// <param name="date">指定日期（可选，默认今天）</param>
        /// <returns>怪物击杀统计</returns>
        public async Task<v_monster_kill_stats?> GetMonsterKillStatsAsync(int userId, int monsterId, DateTime? date = null)
        {
            try
            {
                var targetDate = date ?? DateTime.Today;
                return await _db.Queryable<v_monster_kill_stats>()
                    .Where(x => x.user_id == userId && x.monster_id == monsterId && x.kill_date.Date == targetDate.Date)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取怪物击杀统计失败: UserId={UserId}, MonsterId={MonsterId}", userId, monsterId);
                return null;
            }
        }

        /// <summary>
        /// 检查用户是否完成击杀任务
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="monsterId">怪物ID</param>
        /// <param name="targetCount">目标击杀数量</param>
        /// <param name="startDate">开始日期（可选）</param>
        /// <returns>是否完成任务</returns>
        public async Task<bool> CheckKillTaskCompletionAsync(int userId, int monsterId, int targetCount, DateTime? startDate = null)
        {
            try
            {
                var killCount = await GetUserMonsterKillCountAsync(userId, monsterId, startDate);
                return killCount >= targetCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查击杀任务完成状态失败: UserId={UserId}, MonsterId={MonsterId}", userId, monsterId);
                return false;
            }
        }
    }
}
