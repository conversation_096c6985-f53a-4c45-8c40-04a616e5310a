using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 合成有效宠物验证服务接口
    /// </summary>
    public interface ISynthesisValidPetsService
    {
        /// <summary>
        /// 验证宠物是否可以参与合成
        /// </summary>
        /// <param name="petNo">宠物编号</param>
        /// <returns>是否可以合成</returns>
        Task<bool> IsValidSynthesisPetAsync(int petNo);

        /// <summary>
        /// 获取所有可合成宠物编号
        /// </summary>
        /// <returns>可合成宠物编号列表</returns>
        Task<List<int>> GetAllValidPetNosAsync();

        /// <summary>
        /// 刷新缓存
        /// </summary>
        /// <returns></returns>
        Task RefreshValidPetsAsync();

        /// <summary>
        /// 验证宠物是否可以参与合成（直接查询数据库）
        /// </summary>
        /// <param name="petNo">宠物编号</param>
        /// <returns>是否可以合成</returns>
        Task<bool> ValidatePetForSynthesisAsync(int petNo);

        /// <summary>
        /// 批量验证宠物是否可以参与合成
        /// </summary>
        /// <param name="petNos">宠物编号列表</param>
        /// <returns>验证结果字典</returns>
        Task<Dictionary<int, bool>> ValidateMultiplePetsAsync(List<int> petNos);

        /// <summary>
        /// 获取可合成宠物数量
        /// </summary>
        /// <returns>数量</returns>
        Task<int> GetValidPetsCountAsync();

        /// <summary>
        /// 初始化默认数据（如果表为空）
        /// </summary>
        /// <returns></returns>
        Task InitializeDefaultDataAsync();
    }
}
