-- 装备模块基础数据迁移脚本
-- 执行前请确保已创建相关表结构

-- 清空现有数据（可选，谨慎使用）
-- DELETE FROM equipment_gemstone;
-- DELETE FROM gemstone_config;
-- DELETE FROM suit_attribute;
-- DELETE FROM suit_config;

-- 插入宝石配置数据
INSERT INTO gemstone_config (type_name, up_type, up_num, level, color, prop_id, type_class, equip_types, positions, order_num) VALUES
-- 一孔宝石
('红宝石', '攻击', 50.00, 1, '红色', '2017060101', '一孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[1]', 1),
('蓝宝石', '魔法', 50.00, 1, '蓝色', '2017060102', '一孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[1]', 2),
('绿宝石', '生命', 100.00, 1, '绿色', '2017060103', '一孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[1]', 3),
('黄宝石', '防御', 30.00, 1, '黄色', '2017060104', '一孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[1]', 4),
('紫宝石', '速度', 30.00, 1, '紫色', '2017060105', '一孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[1]', 5),
('白宝石', '命中', 30.00, 1, '白色', '2017060106', '一孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[1]', 6),
('黑宝石', '闪避', 30.00, 1, '黑色', '2017060107', '一孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[1]', 7),

-- 二孔宝石
('混元石', '攻击', 100.00, 2, '紫色', '2017060201', '二孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[2]', 11),
('赤炎石', '魔法', 100.00, 2, '红色', '2017060202', '二孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[2]', 12),
('烛龙石', '生命', 200.00, 2, '金色', '2017060203', '二孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[2]', 13),
('玄武石', '防御', 60.00, 2, '黑色', '2017060204', '二孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[2]', 14),
('青龙石', '速度', 60.00, 2, '青色', '2017060205', '二孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[2]', 15),
('白虎石', '命中', 60.00, 2, '白色', '2017060206', '二孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[2]', 16),
('朱雀石', '闪避', 60.00, 2, '朱红', '2017060207', '二孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[2]', 17),

-- 三孔宝石
('极阳石', '攻击', 200.00, 3, '金色', '2017060301', '三孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[3]', 21),
('圣光石', '魔法', 200.00, 3, '白色', '2017060302', '三孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[3]', 22),
('虚空石', '生命', 400.00, 3, '黑色', '2017060303', '三孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[3]', 23),
('混沌石', '防御', 120.00, 3, '灰色', '2017060304', '三孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[3]', 24),
('时空石', '速度', 120.00, 3, '蓝紫', '2017060305', '三孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[3]', 25),
('破军石', '命中', 120.00, 3, '血红', '2017060306', '三孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[3]', 26),
('贪狼石', '闪避', 120.00, 3, '墨绿', '2017060307', '三孔宝石', '["武器","护甲","头盔","鞋子","饰品"]', '[3]', 27);

-- 插入套装配置数据
INSERT INTO suit_config (suit_id, suit_name, equipment_list, description, total_pieces) VALUES
('2017070101', '天魔套装', '["2017070101001","2017070101002","2017070101003","2017070101004","2017070101005"]', '强大的天魔套装，提供全面的属性加成', 5),
('2017063001', '自然套装', '["2017063001001","2017063001002","2017063001003","2017063001004","2017063001005"]', '自然之力的套装，平衡各项属性', 5),
('20170612', '黑白套装', '["20170612001","20170612002","20170612003","20170612004","20170612005"]', '黑白无常套装，攻防兼备', 5),
('2016123001', '盛世套装', '["2016123001001","2016123001002","2016123001003","2016123001004","2016123001005"]', '盛世王朝套装，王者风范', 5),
('2017051801', '龙鳞套装', '["2017051801001","2017051801002","2017051801003","2017051801004","2017051801005"]', '龙族传承套装，威力无穷', 5),
('2017042901', '凤羽套装', '["2017042901001","2017042901002","2017042901003","2017042901004","2017042901005"]', '凤凰之羽套装，优雅高贵', 5);

-- 插入套装属性数据
INSERT INTO suit_attribute (suit_id, piece_count, attribute_type, attribute_value, is_percentage) VALUES
-- 天魔套装属性
('2017070101', 2, '攻击', '100', 0),
('2017070101', 2, '命中', '50', 0),
('2017070101', 3, '攻击', '200', 0),
('2017070101', 3, '防御', '100', 0),
('2017070101', 4, '攻击', '300', 0),
('2017070101', 4, '生命', '500', 0),
('2017070101', 5, '攻击', '500', 0),
('2017070101', 5, '加深', '0.05', 1),

-- 自然套装属性
('2017063001', 2, '生命', '200', 0),
('2017063001', 2, '魔法', '200', 0),
('2017063001', 3, '生命', '400', 0),
('2017063001', 3, '魔法', '400', 0),
('2017063001', 4, '生命', '600', 0),
('2017063001', 4, '魔法', '600', 0),
('2017063001', 5, '生命', '1000', 0),
('2017063001', 5, '抵消', '0.03', 1),

-- 黑白套装属性
('20170612', 2, '攻击', '80', 0),
('20170612', 2, '防御', '80', 0),
('20170612', 3, '攻击', '160', 0),
('20170612', 3, '防御', '160', 0),
('20170612', 4, '攻击', '240', 0),
('20170612', 4, '防御', '240', 0),
('20170612', 5, '攻击', '400', 0),
('20170612', 5, '防御', '400', 0),

-- 盛世套装属性
('2016123001', 2, '速度', '60', 0),
('2016123001', 2, '闪避', '60', 0),
('2016123001', 3, '速度', '120', 0),
('2016123001', 3, '闪避', '120', 0),
('2016123001', 4, '速度', '180', 0),
('2016123001', 4, '闪避', '180', 0),
('2016123001', 5, '速度', '300', 0),
('2016123001', 5, '闪避', '300', 0),

-- 龙鳞套装属性
('2017051801', 2, '生命', '150', 0),
('2017051801', 2, '攻击', '75', 0),
('2017051801', 3, '生命', '300', 0),
('2017051801', 3, '攻击', '150', 0),
('2017051801', 4, '生命', '450', 0),
('2017051801', 4, '攻击', '225', 0),
('2017051801', 5, '生命', '750', 0),
('2017051801', 5, '攻击', '375', 0),

-- 凤羽套装属性
('2017042901', 2, '魔法', '150', 0),
('2017042901', 2, '命中', '75', 0),
('2017042901', 3, '魔法', '300', 0),
('2017042901', 3, '命中', '150', 0),
('2017042901', 4, '魔法', '450', 0),
('2017042901', 4, '命中', '225', 0),
('2017042901', 5, '魔法', '750', 0),
('2017042901', 5, '命中', '375', 0);

-- 验证数据插入结果
SELECT '宝石配置数据' as table_name, COUNT(*) as record_count FROM gemstone_config
UNION ALL
SELECT '套装配置数据' as table_name, COUNT(*) as record_count FROM suit_config
UNION ALL
SELECT '套装属性数据' as table_name, COUNT(*) as record_count FROM suit_attribute;

-- 显示宝石配置按等级分组统计
SELECT 
    level as 宝石等级,
    type_class as 宝石分类,
    COUNT(*) as 数量
FROM gemstone_config 
GROUP BY level, type_class
ORDER BY level;

-- 显示套装配置统计
SELECT 
    suit_name as 套装名称,
    total_pieces as 总件数,
    COUNT(sa.id) as 属性数量
FROM suit_config sc
LEFT JOIN suit_attribute sa ON sc.suit_id = sa.suit_id
GROUP BY sc.suit_id, sc.suit_name, sc.total_pieces
ORDER BY sc.suit_name;
