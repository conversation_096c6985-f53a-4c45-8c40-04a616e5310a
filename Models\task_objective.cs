﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///任务目标表
    ///</summary>
    [SugarTable("task_objective")]
    public partial class task_objective
    {
           public task_objective(){


           }
           /// <summary>
           /// Desc:目标ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int objective_id {get;set;}

           /// <summary>
           /// Desc:任务ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string task_id {get;set;}

           /// <summary>
           /// Desc:目标类型(KILL_MONSTER,COLLECT_ITEM,REACH_LEVEL等)
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string objective_type {get;set;}

           /// <summary>
           /// Desc:目标ID(怪物ID/道具ID等)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? target_id {get;set;}

           /// <summary>
           /// Desc:目标数量
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int target_amount {get;set;}

           /// <summary>
           /// Desc:目标顺序
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? objective_order {get;set;}

           /// <summary>
           /// Desc:目标描述
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? objective_description {get;set;}

            /// <summary>
            /// Desc:进度模版
            /// Default:
            /// Nullable:True
            /// </summary>           
            public string? complete_template { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:CURRENT_TIMESTAMP
        /// Nullable:True
        /// </summary>           
        public DateTime? created_at {get;set;}

    }
}
