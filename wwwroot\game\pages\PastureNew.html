<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>牧场管理</title>
    <link href="Content/CSS/Pasture.css" rel="stylesheet" />
    <script src="Content/Javascript/jquery-1.8.3.min.js"></script>
    <script src="/game/js/pet-management.js"></script>
    <style>
        /* 现代化样式 */
        .pasture-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .capacity-info {
            background: #fff;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .capacity-info .current-count {
            color: #007bff;
            font-weight: bold;
        }
        
        .capacity-info .max-capacity {
            color: #6c757d;
        }
        
        .pet-list-container {
            background: #fff;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .pet-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .pet-table th {
            background: #f8f9fa;
            padding: 12px;
            text-align: left;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
        }
        
        .pet-table td {
            padding: 12px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        
        .pet-table tr:hover {
            background: #f8f9fa;
        }
        
        .pet-table tr.selected {
            background: #e3f2fd !important;
        }
        
        .pet-image {
            width: 32px;
            height: 32px;
            margin-right: 8px;
            vertical-align: middle;
        }
        
        .main-pet-badge {
            background: #dc3545;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            margin-left: 8px;
        }
        
        .pet-detail-panel {
            background: #fff;
            border-radius: 6px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: none;
        }
        
        .pet-detail-panel.show {
            display: block;
        }
        
        .operation-buttons {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .operation-buttons button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .operation-buttons button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover:not(:disabled) {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover:not(:disabled) {
            background: #1e7e34;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover:not(:disabled) {
            background: #e0a800;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover:not(:disabled) {
            background: #c82333;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 12px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .pet-attributes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .attribute-group {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 4px;
        }
        
        .attribute-group h4 {
            margin: 0 0 8px 0;
            color: #495057;
            font-size: 14px;
        }
        
        .attribute-item {
            display: flex;
            justify-content: space-between;
            margin: 4px 0;
            font-size: 13px;
        }
        
        .filter-controls {
            background: #fff;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-controls select,
        .filter-controls input {
            padding: 6px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            gap: 5px;
        }
        
        .pagination button {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            background: #fff;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .pagination button:hover:not(:disabled) {
            background: #e9ecef;
        }
        
        .pagination button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .pagination button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .pasture-container {
                padding: 10px;
            }
            
            .capacity-info {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
            
            .filter-controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .operation-buttons {
                justify-content: center;
            }
            
            .operation-buttons button {
                flex: 1;
                min-width: 120px;
            }
            
            .pet-table {
                font-size: 14px;
            }
            
            .pet-table th,
            .pet-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="pasture-container">
        <!-- 页面标题 -->
        <h1 style="text-align: center; color: #495057; margin-bottom: 30px;">🏡 宠物牧场</h1>
        
        <!-- 牧场容量显示 -->
        <div class="capacity-info">
            <div>
                <span>当前数量：<span class="current-count" id="currentCount">0</span></span>
                <span style="margin-left: 20px;">最大数量：<span class="max-capacity" id="maxCapacity">80</span></span>
            </div>
            <div>
                <span>可用空间：<span id="availableSlots">80</span></span>
            </div>
        </div>
        
        <!-- 筛选和排序控件 -->
        <div class="filter-controls">
            <label>状态筛选：
                <select id="statusFilter">
                    <option value="牧场">牧场</option>
                    <option value="携带">携带</option>
                    <option value="全部">全部</option>
                </select>
            </label>
            
            <label>排序方式：
                <select id="sortBy">
                    <option value="pet_no">宠物编号</option>
                    <option value="level">等级</option>
                    <option value="growth">成长</option>
                    <option value="create_time">获得时间</option>
                </select>
            </label>
            
            <label>排序方向：
                <select id="sortDirection">
                    <option value="asc">升序</option>
                    <option value="desc">降序</option>
                </select>
            </label>
            
            <button class="btn-primary" onclick="petManagement.loadPets()">刷新列表</button>
        </div>
        
        <!-- 宠物列表 -->
        <div class="pet-list-container">
            <table id="petTable" class="pet-table">
                <thead>
                    <tr>
                        <th>宠物</th>
                        <th>五行</th>
                        <th>等级</th>
                        <th>成长</th>
                        <th>状态</th>
                        <th>获得时间</th>
                    </tr>
                </thead>
                <tbody id="petTableBody">
                    <tr>
                        <td colspan="6" class="loading">正在加载宠物列表...</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 分页控件 -->
        <div class="pagination" id="pagination">
            <!-- 动态生成分页按钮 -->
        </div>
        
        <!-- 宠物详情面板 -->
        <div class="pet-detail-panel" id="petDetailPanel">
            <h3>宠物详情</h3>
            <div id="petDetailContent">
                <!-- 动态生成宠物详情 -->
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="operation-buttons">
            <button id="carryBtn" class="btn-primary" onclick="petManagement.carryPet()" disabled>
                📦 携带
            </button>
            <button id="storeBtn" class="btn-success" onclick="petManagement.storePet()" disabled>
                🏠 存放
            </button>
            <button id="setMainBtn" class="btn-warning" onclick="petManagement.setMainPet()" disabled>
                ⭐ 设为主宠
            </button>
            <button id="renameBtn" class="btn-primary" onclick="petManagement.renamePet()" disabled>
                ✏️ 重命名
            </button>
            <button id="discardBtn" class="btn-danger" onclick="petManagement.discardPet()" disabled>
                🗑️ 丢弃
            </button>
        </div>
        
        <!-- 消息提示区域 -->
        <div id="messageArea"></div>
    </div>

    <script>
        // 页面加载完成后初始化
        $(document).ready(function() {
            // 从游戏缓存或其他方式获取用户ID
            const userId = gameCache?.get('userId') || window.currentUserId || 1;
            
            // 初始化宠物管理系统
            if (typeof PetManagement !== 'undefined') {
                window.petManagement = new PetManagement();
                window.petManagement.init(userId);
            } else {
                console.error('PetManagement 类未加载');
                showMessage('系统初始化失败，请刷新页面重试', 'error');
            }
        });
        
        // 消息显示函数
        function showMessage(message, type = 'info') {
            const messageArea = document.getElementById('messageArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
            messageDiv.textContent = message;
            
            messageArea.appendChild(messageDiv);
            
            // 3秒后自动移除消息
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 3000);
        }
    </script>
</body>
</html>
