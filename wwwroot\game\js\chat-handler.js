/**
 * 聊天功能处理器
 * 替换原有的window.external聊天功能
 * 集成用户认证管理
 */

// 全局变量
let xname = ""; // 私聊目标用户名
let currentUser = null; // 当前用户信息

/**
 * 初始化用户认证信息
 */
function initializeUserAuth() {
    // 1. 优先从认证管理器获取用户信息
    if (window.authManager && window.authManager.isLoggedIn()) {
        const user = window.authManager.getCurrentUser();
        if (user && user.userId) {
            currentUser = {
                userId: user.userId,
                userName: user.nickname || user.username || `用户${user.userId}`,
                isAuthenticated: true,
                authSource: 'authManager'
            };
            console.log('✅ 从认证管理器获取用户信息:', currentUser);
            return currentUser;
        }
    }

    // 2. 备用方案：从全局函数获取
    if (typeof window.getCurrentUserId === 'function') {
        const userId = window.getCurrentUserId();
        if (userId && userId > 0) {
            currentUser = {
                userId: userId,
                userName: `用户${userId}`,
                isAuthenticated: true,
                authSource: 'globalFunction'
            };
            console.log('✅ 从全局函数获取用户信息:', currentUser);
            return currentUser;
        }
    }

    // 3. 检查是否为开发环境
    if (isDevelopmentMode()) {
        console.warn('⚠️ 开发环境：用户未登录，聊天功能受限');
        currentUser = {
            userId: null,
            userName: null,
            isAuthenticated: false,
            authSource: 'development'
        };
        return currentUser;
    }

    // 4. 生产环境：用户未登录，跳转到登录页面
    console.error('❌ 用户未登录，需要跳转到登录页面');
    redirectToLogin();
    return null;
}

/**
 * 获取当前用户信息
 */
function getCurrentUserInfo() {
    if (!currentUser) {
        currentUser = initializeUserAuth();
    }
    return currentUser;
}

/**
 * 检查用户是否已认证
 */
function isUserAuthenticated() {
    const user = getCurrentUserInfo();
    return user && user.isAuthenticated;
}

/**
 * 跳转到登录页面
 */
function redirectToLogin() {
    const loginUrl = getLoginUrl();
    console.log('跳转到登录页面:', loginUrl);

    // 显示提示信息
    showChatError('用户未登录，即将跳转到登录页面...');

    // 延迟跳转，让用户看到提示信息
    setTimeout(() => {
        window.location.href = loginUrl;
    }, 2000);
}

/**
 * 获取登录页面URL
 */
function getLoginUrl() {
    // 1. 检查是否有配置的登录URL
    if (window.LOGIN_URL) {
        return window.LOGIN_URL;
    }

    // 2. 根据当前路径推断登录页面
    const currentPath = window.location.pathname;
    if (currentPath.includes('/game/pages/')) {
        return '/game/pages/Login.html';
    }

    // 3. 默认登录页面
    return '/Login.html';
}

/**
 * 检查是否为开发环境
 */
function isDevelopmentMode() {
    return window.location.hostname === 'localhost' ||
           window.location.hostname === '127.0.0.1' ||
           window.location.hostname.includes('dev');
}

/**
 * 重构的发送消息函数
 * 替换原有的sendMsg()函数
 */
function sendMsg() {
    // 检查用户认证状态
    if (!isUserAuthenticated()) {
        console.error('用户未认证，无法发送消息');
        showChatError('用户未登录，无法发送消息');
        return;
    }

    if (!window.chatClient || !window.chatClient.isConnected) {
        console.error('聊天服务未连接');
        showChatError('聊天服务未连接，请稍后重试');
        return;
    }

    const content = $("#cmsg").val().trim();
    if (!content) {
        return;
    }
    
    // 处理私聊目标用户名
    if (xname && content.indexOf(xname) === -1) {
        xname = "";
    }
    
    // 获取当前聊天类型和颜色
    const chatType = getCurrentChatType();
    const color = $(".dqys").html() || "黑";
    const messageContent = `${content}&&${color}`;
    
    // 根据聊天类型发送消息
    let success = false;
    switch(chatType) {
        case 'chat':
            success = window.chatClient.sendChatMessage(messageContent, 'chat');
            break;
        case 'private':
            const targetUser = getPrivateChatTarget();
            if (!targetUser) {
                showChatError('请先选择私聊对象');
                return;
            }
            success = window.chatClient.sendChatMessage(messageContent, 'private', targetUser);
            break;
        case 'team':
            // 暂时使用chat类型，后续可扩展为team频道
            success = window.chatClient.sendChatMessage(`[队聊]${messageContent}`, 'chat');
            break;
        case 'guild':
            // 暂时使用chat类型，后续可扩展为guild频道
            success = window.chatClient.sendChatMessage(`[家族]${messageContent}`, 'chat');
            break;
        default:
            success = window.chatClient.sendChatMessage(messageContent, 'chat');
    }
    
    if (success) {
        // 清空输入框或保留私聊目标
        if (xname) {
            $("#cmsg").val(xname);
        } else {
            $("#cmsg").val("");
        }
    }
}

/**
 * 获取当前聊天类型
 */
function getCurrentChatType() {
    const selectedText = $("#select_lt span").text().trim();
    switch(selectedText) {
        case '私聊': return 'private';
        case '队聊': return 'team';
        case '家族聊': return 'guild';
        case '公聊':
        default: 
            return 'chat';
    }
}

/**
 * 获取私聊目标用户
 */
function getPrivateChatTarget() {
    // 如果有设置私聊目标用户名
    if (xname) {
        return xname;
    }
    
    // 可以从输入框中解析@用户名
    const content = $("#cmsg").val().trim();
    const atMatch = content.match(/@(\w+)/);
    if (atMatch) {
        return atMatch[1];
    }
    
    return null;
}

/**
 * 设置私聊目标用户
 * 原有的sl()函数
 */
function sl(username) {
    if (!username) return;
    
    // 切换到私聊模式
    $("#select_lt span").text("私聊");
    
    // 设置私聊目标
    xname = username;
    
    // 在输入框中显示目标用户名
    $("#cmsg").val(username + " ");
    $("#cmsg").focus();
    
    console.log('设置私聊目标:', username);
}

/**
 * 键盘事件处理
 * 保持原有的key()函数
 */
function key() {
    if (event.keyCode == 13) { // 回车键
        sendMsg();
    }
}

/**
 * 显示聊天错误信息
 */
function showChatError(message) {
    const errorHtml = `<div class='send_ms0'>
        <font color="#dc3545">[错误]：${message}</font>
    </div>`;
    
    const chatDiv = document.getElementById('chatDiv');
    if (chatDiv) {
        chatDiv.innerHTML += errorHtml;
        chatDiv.scrollTop = chatDiv.scrollHeight;
    }
}

/**
 * 初始化聊天系统
 */
function initializeChatSystem() {
    console.log('初始化聊天系统...');
    
    // 创建WebSocket客户端
    if (!window.chatClient) {
        window.chatClient = new ChatWebSocketClient();
    }
    
    // 连接WebSocket
    window.chatClient.connect();
    
    // 绑定聊天频道切换事件
    bindChatChannelEvents();
    
    // 绑定颜色选择事件
    bindColorSelectionEvents();
    
    console.log('聊天系统初始化完成');
}

/**
 * 绑定聊天频道切换事件
 */
function bindChatChannelEvents() {
    // 绑定频道选择事件
    $("#select_lt ul li a").off('click').on('click', function(e) {
        e.preventDefault();
        const channelName = $(this).attr('title');
        const channelValue = $(this).attr('name');
        
        // 更新显示的频道名称
        $("#select_lt span").text(channelName);
        
        // 隐藏下拉菜单
        $("#select_lt ul").addClass('hidden');
        
        // 如果切换到非私聊频道，清空私聊目标
        if (channelName !== '私聊') {
            xname = "";
        }
        
        console.log('切换聊天频道:', channelName, channelValue);
    });
    
    // 绑定频道下拉菜单显示/隐藏
    $("#select_lt").off('click').on('click', function() {
        const ul = $(this).find('ul');
        if (ul.hasClass('hidden')) {
            ul.removeClass('hidden');
        } else {
            ul.addClass('hidden');
        }
    });
}

/**
 * 绑定颜色选择事件
 */
function bindColorSelectionEvents() {
    // 绑定颜色选择事件
    $("#select_ys ul li a").off('click').on('click', function(e) {
        e.preventDefault();
        const colorName = $(this).attr('title');
        
        // 更新显示的颜色
        $(".dqys").text(colorName.replace('色', ''));
        
        // 隐藏下拉菜单
        $("#select_ys ul").addClass('hidden');
        
        console.log('选择聊天颜色:', colorName);
    });
}

/**
 * 设置聊天颜色
 * 原有的setYS()函数
 */
function setYS(color) {
    $(".dqys").text(color);
    $("#select_ys ul").addClass('hidden');
}

/**
 * 显示特定类型的消息
 * 原有的showSpecialMsg()函数
 */
function showSpecialMsg(type) {
    console.log('显示特定消息类型:', type);
    // 这里可以实现消息过滤功能
    // 暂时保留原有逻辑
}

/**
 * 在用户登录成功后初始化聊天
 */
function initializeChatAfterLogin(playerId, playerName, password = '') {
    console.log('用户登录后初始化聊天:', playerId, playerName);
    
    if (window.chatClient) {
        if (window.chatClient.isConnected) {
            // 如果已连接，直接登录
            window.chatClient.login(playerId, playerName, password);
        } else {
            // 如果未连接，设置用户信息，连接成功后自动登录
            window.chatClient.playerId = playerId;
            window.chatClient.playerName = playerName;
        }
    } else {
        // 如果聊天客户端未初始化，先初始化
        initializeChatSystem();
        setTimeout(() => {
            if (window.chatClient) {
                window.chatClient.playerId = playerId;
                window.chatClient.playerName = playerName;
            }
        }, 1000);
    }
}

/**
 * 获取聊天系统状态
 */
function getChatSystemStatus() {
    if (window.chatClient) {
        return window.chatClient.getConnectionStatus();
    }
    return {
        isConnected: false,
        playerId: null,
        playerName: null,
        reconnectAttempts: 0
    };
}

/**
 * 手动重连聊天服务
 */
function reconnectChatService() {
    if (window.chatClient) {
        window.chatClient.disconnect();
        setTimeout(() => {
            window.chatClient.connect();
        }, 1000);
    } else {
        initializeChatSystem();
    }
}

// 页面卸载时断开连接
window.addEventListener('beforeunload', function() {
    if (window.chatClient) {
        window.chatClient.disconnect();
    }
});

// 页面可见性变化时的处理
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // 页面隐藏时暂停心跳
        if (window.chatClient) {
            window.chatClient.stopHeartbeat();
        }
    } else {
        // 页面显示时恢复心跳
        if (window.chatClient && window.chatClient.isConnected) {
            window.chatClient.startHeartbeat();
        }
    }
});
