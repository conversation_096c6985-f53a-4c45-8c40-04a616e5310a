using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace WebApplication_HM.Models
{
    /// <summary>
    /// 等级配置表模型
    /// </summary>
    [SugarTable("level_config")]
    public class LevelConfig
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int id { get; set; }
        
        /// <summary>
        /// 等级
        /// </summary>
        [SugarColumn(ColumnName = "level")]
        public int level { get; set; }
        
        /// <summary>
        /// 该等级所需累积经验
        /// </summary>
        [SugarColumn(ColumnName = "required_exp")]
        public long required_exp { get; set; }
        
        /// <summary>
        /// 升级到下一级所需经验
        /// </summary>
        [SugarColumn(ColumnName = "upgrade_exp")]
        public long upgrade_exp { get; set; }
        
        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(ColumnName = "is_active")]
        public int is_active { get; set; } = 1;
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnName = "created_at")]
        public DateTime? created_at { get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(ColumnName = "updated_at")]
        public DateTime? updated_at { get; set; }
    }

    /// <summary>
    /// 经验系统配置模型
    /// </summary>
    [SugarTable("exp_system_config")]
    public class ExpSystemConfig
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int id { get; set; }
        
        /// <summary>
        /// 系统名称
        /// </summary>
        [SugarColumn(ColumnName = "system_name")]
        public string system_name { get; set; } = string.Empty;
        
        /// <summary>
        /// 经验上限
        /// </summary>
        [SugarColumn(ColumnName = "max_exp")]
        public long max_exp { get; set; }
        
        /// <summary>
        /// 等级上限
        /// </summary>
        [SugarColumn(ColumnName = "max_level")]
        public int max_level { get; set; }
        
        /// <summary>
        /// 经验计算公式
        /// </summary>
        [SugarColumn(ColumnName = "exp_formula")]
        public string? exp_formula { get; set; }
        
        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(ColumnName = "is_active")]
        public int is_active { get; set; } = 1;
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnName = "created_at")]
        public DateTime? created_at { get; set; }
    }

    /// <summary>
    /// 等级变更日志模型
    /// </summary>
    [SugarTable("level_change_log")]
    public class LevelChangeLog
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int id { get; set; }
        
        /// <summary>
        /// 用户ID
        /// </summary>
        [SugarColumn(ColumnName = "user_id")]
        public int user_id { get; set; }
        
        /// <summary>
        /// 宠物ID
        /// </summary>
        [SugarColumn(ColumnName = "pet_id")]
        public int pet_id { get; set; }
        
        /// <summary>
        /// 原等级
        /// </summary>
        [SugarColumn(ColumnName = "old_level")]
        public int old_level { get; set; }
        
        /// <summary>
        /// 新等级
        /// </summary>
        [SugarColumn(ColumnName = "new_level")]
        public int new_level { get; set; }
        
        /// <summary>
        /// 原经验
        /// </summary>
        [SugarColumn(ColumnName = "old_exp")]
        public long old_exp { get; set; }
        
        /// <summary>
        /// 新经验
        /// </summary>
        [SugarColumn(ColumnName = "new_exp")]
        public long new_exp { get; set; }
        
        /// <summary>
        /// 变更原因
        /// </summary>
        [SugarColumn(ColumnName = "change_reason")]
        public string change_reason { get; set; } = string.Empty;
        
        /// <summary>
        /// 变更时间
        /// </summary>
        [SugarColumn(ColumnName = "change_time")]
        public DateTime change_time { get; set; }
    }

    /// <summary>
    /// 等级经验信息（用于数据导入）
    /// </summary>
    public class LevelExpInfo
    {
        /// <summary>
        /// 等级
        /// </summary>
        public int Level { get; set; }
        
        /// <summary>
        /// 该等级所需累积经验
        /// </summary>
        public long RequiredExp { get; set; }
        
        /// <summary>
        /// 升级到下一级所需经验
        /// </summary>
        public long UpgradeExp { get; set; }
    }

    /// <summary>
    /// 导入结果
    /// </summary>
    public class ImportResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 导入数量
        /// </summary>
        public int ImportedCount { get; set; }
        
        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;
    }
}
