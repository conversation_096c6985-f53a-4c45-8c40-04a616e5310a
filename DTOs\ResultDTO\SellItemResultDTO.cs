namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 出售道具结果DTO
    /// </summary>
    public class SellItemResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 道具ID
        /// </summary>
        public string ItemId { get; set; }

        /// <summary>
        /// 出售数量
        /// </summary>
        public int SoldCount { get; set; }

        /// <summary>
        /// 剩余数量
        /// </summary>
        public long RemainingCount { get; set; }

        /// <summary>
        /// 获得金币
        /// </summary>
        public int GainedGold { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public int UnitPrice { get; set; }

        /// <summary>
        /// 总价
        /// </summary>
        public int TotalPrice { get; set; }
    }
} 