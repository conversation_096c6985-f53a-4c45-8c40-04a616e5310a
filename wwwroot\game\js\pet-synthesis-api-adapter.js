/**
 * 宠物合成API适配器
 * 提供与后端合成系统的接口对接功能
 */
class PetSynthesisApiAdapter {
    constructor() {
        this.baseUrl = '/api/PetSynthesis';
        this.evolutionUrl = '/api/PetEvolution';
    }

    /**
     * 获取认证请求头
     * @returns {Object} 包含认证信息的请求头
     */
    getAuthHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };

        // 1. 尝试从认证管理器获取用户ID
        if (window.authManager && window.authManager.isLoggedIn()) {
            const userId = window.authManager.getCurrentUserId();
            if (userId) {
                headers['X-User-Id'] = userId.toString();
            }
        }

        // 2. 尝试从全局函数获取用户ID
        if (typeof window.getCurrentUserId === 'function') {
            const userId = window.getCurrentUserId();
            if (userId) {
                headers['X-Game-User-Id'] = userId.toString();
            }
        }

        // 3. 从URL参数获取用户ID
        const urlParams = new URLSearchParams(window.location.search);
        const userIdFromUrl = urlParams.get('userId');
        if (userIdFromUrl) {
            headers['X-User-Id'] = userIdFromUrl;
        }

        return headers;
    }

    /**
     * 获取合成配置信息
     * @param {number} mainPetId - 主宠ID
     * @param {number} vicePetId - 副宠ID
     * @returns {Promise<Object>} 合成配置
     */
    async getSynthesisConfig(mainPetId, vicePetId) {
        try {
            const headers = this.getAuthHeaders();
            const response = await fetch(`${this.baseUrl}/${mainPetId}/${vicePetId}/config`, {
                method: 'GET',
                headers: headers
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取合成配置失败:', error);
            return { success: false, message: '获取合成配置失败：' + error.message };
        }
    }

    /**
     * 验证合成条件
     * @param {number} mainPetId - 主宠ID
     * @param {number} vicePetId - 副宠ID
     * @returns {Promise<Object>} 验证结果
     */
    async validateSynthesis(mainPetId, vicePetId) {
        try {
            const headers = this.getAuthHeaders();
            const response = await fetch(`${this.baseUrl}/${mainPetId}/${vicePetId}/validate`, {
                method: 'GET',
                headers: headers
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('验证合成条件失败:', error);
            return { success: false, message: '验证合成条件失败：' + error.message };
        }
    }

    /**
     * 执行宠物合成
     * @param {number} mainPetId - 主宠ID
     * @param {number} vicePetId - 副宠ID
     * @param {Array<string>} usedItems - 使用的道具列表
     * @returns {Promise<Object>} 合成结果
     */
    async synthesizePet(mainPetId, vicePetId, usedItems = []) {
        try {
            const headers = this.getAuthHeaders();
            const response = await fetch(`${this.baseUrl}/synthesize`, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({
                    MainPetId: mainPetId,
                    VicePetId: vicePetId,
                    UsedItems: usedItems
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('宠物合成失败:', error);
            return { success: false, message: '宠物合成失败：' + error.message };
        }
    }

    /**
     * 获取合成历史记录
     * @param {number} petId - 宠物ID（可选）
     * @returns {Promise<Object>} 合成历史
     */
    async getSynthesisHistory(petId = null) {
        try {
            const url = petId ? `${this.baseUrl}/history?petId=${petId}` : `${this.baseUrl}/history`;
            const response = await fetch(url, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('获取合成历史失败:', error);
            return { success: false, message: '获取合成历史失败：' + error.message };
        }
    }

    /**
     * 获取合成统计信息
     * @returns {Promise<Object>} 合成统计
     */
    async getSynthesisStatistics() {
        try {
            const response = await fetch(`${this.baseUrl}/statistics`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('获取合成统计失败:', error);
            return { success: false, message: '获取合成统计失败：' + error.message };
        }
    }

    /**
     * 获取宠物进化信息
     * @param {number} userPetId - 用户宠物ID
     * @returns {Promise<Object>} 进化信息
     */
    async getPetEvolutionInfo(userPetId) {
        try {
            const response = await fetch(`${this.evolutionUrl}/${userPetId}/info`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('获取进化信息失败:', error);
            return { success: false, message: '获取进化信息失败：' + error.message };
        }
    }

    /**
     * 执行宠物进化
     * @param {number} userPetId - 用户宠物ID
     * @param {string} evolutionType - 进化类型（A或B）
     * @returns {Promise<Object>} 进化结果
     */
    async evolvePet(userPetId, evolutionType) {
        try {
            const response = await fetch(`${this.evolutionUrl}/evolve`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    UserPetId: userPetId,
                    EvolutionType: evolutionType
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('宠物进化失败:', error);
            return { success: false, message: '宠物进化失败：' + error.message };
        }
    }

    /**
     * 执行神宠涅槃
     * @param {number} mainPetId - 主宠ID
     * @param {number} subPetId - 副宠ID
     * @param {number} nirvanaPetId - 涅槃兽ID
     * @param {string} usedItemId - 使用的道具ID
     * @returns {Promise<Object>} 涅槃结果
     */
    async executeNirvana(mainPetId, subPetId, nirvanaPetId, usedItemId = null) {
        try {
            const response = await fetch('/api/Nirvana/execute', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    UserId: 1, // 这里应该从全局变量获取
                    MainPetId: mainPetId,
                    SubPetId: subPetId,
                    NirvanaPetId: nirvanaPetId,
                    UsedItemId: usedItemId
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('神宠涅槃失败:', error);
            return { success: false, message: '神宠涅槃失败：' + error.message };
        }
    }

    /**
     * 预览涅槃结果
     * @param {number} mainPetId - 主宠ID
     * @param {number} subPetId - 副宠ID
     * @param {number} nirvanaPetId - 涅槃兽ID
     * @returns {Promise<Object>} 预览结果
     */
    async previewNirvana(mainPetId, subPetId, nirvanaPetId) {
        try {
            const response = await fetch('/api/Nirvana/preview', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    UserId: 1, // 这里应该从全局变量获取
                    MainPetId: mainPetId,
                    SubPetId: subPetId,
                    NirvanaPetId: nirvanaPetId
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('预览涅槃失败:', error);
            return { success: false, message: '预览涅槃失败：' + error.message };
        }
    }

    /**
     * 获取宠物进化信息
     * @param {number} userPetId - 用户宠物ID
     * @returns {Promise<Object>} 进化信息
     */
    async getPetEvolutionInfo(userPetId) {
        try {
            const response = await fetch(`${this.evolutionUrl}/${userPetId}/info`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取进化信息失败:', error);
            return { success: false, message: '获取进化信息失败：' + error.message };
        }
    }

    /**
     * 获取进化历史记录
     * @param {number} userPetId - 用户宠物ID
     * @returns {Promise<Object>} 进化历史
     */
    async getEvolutionHistory(userPetId) {
        try {
            const response = await fetch(`${this.evolutionUrl}/${userPetId}/history`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('获取进化历史失败:', error);
            return { success: false, message: '获取进化历史失败：' + error.message };
        }
    }

    /**
     * 显示加载状态
     * @param {string} message - 加载消息
     */
    showLoading(message = '处理中...') {
        if (window.loadingManager) {
            window.loadingManager.show(message);
        }
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        if (window.loadingManager) {
            window.loadingManager.hide();
        }
    }

    /**
     * 显示成功消息
     * @param {string} message - 成功消息
     */
    showSuccess(message) {
        if (window.loadingManager) {
            window.loadingManager.showSuccess(message);
        } else if (window.parent && window.parent.showBox) {
            window.parent.showBox(message);
        } else {
            alert(message);
        }
    }

    /**
     * 显示错误消息
     * @param {string} message - 错误消息
     */
    showError(message) {
        if (window.loadingManager) {
            window.loadingManager.showError(message);
        } else if (window.parent && window.parent.showBox) {
            window.parent.showBox(message);
        } else {
            alert(message);
        }
    }
}

// 创建全局实例
window.petSynthesisApi = new PetSynthesisApiAdapter();

console.log('宠物合成API适配器已加载');
