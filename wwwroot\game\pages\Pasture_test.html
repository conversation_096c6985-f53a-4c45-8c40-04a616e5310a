﻿
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>牧场</title>
    <link href="Content/CSS/Pasture.css" rel="stylesheet" />
    <link href="Content/CSS/main.css" rel="stylesheet" />
    <script src="Content/Javascript/jquery-1.8.3.min.js"></script>
    <script type="text/javascript">
		var jLoad="[{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"4\",\"形象\":\"614\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮saber≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|153|0,|154|0\",\"技能显示\":\",誓约之剑|0|153|5000|false|270%,风王结界|0|154|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"153\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"153\",\"技能名字\":\"誓约之剑\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"154\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"154\",\"技能名字\":\"风王结界\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"24\",\"形象\":\"668\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮王昭君≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|288|0,|289|0\",\"技能显示\":\",落雁之歌|0|288|5000|false|270%,雁飞暮雪|0|289|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"288\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"288\",\"技能名字\":\"落雁之歌\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"289\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"289\",\"技能名字\":\"雁飞暮雪\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"25\",\"形象\":\"669\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮五虎·马超≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|285|0,|286|0\",\"技能显示\":\",火凤燎原|0|285|5000|false|270%,踏破沙场|0|286|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"285\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"285\",\"技能名字\":\"火凤燎原\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"286\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"286\",\"技能名字\":\"踏破沙场\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"26\",\"形象\":\"684\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮爱丽丝≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|334|0,|339|0\",\"技能显示\":\",黄金苹果|0|334|5000|false|270%,含苞待放|0|339|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"334\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"334\",\"技能名字\":\"黄金苹果\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"339\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"339\",\"技能名字\":\"含苞待放\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"27\",\"形象\":\"690\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮御天·蜃龙≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|355|0,|356|0\",\"技能显示\":\",拂袖蜃影|0|355|5000|false|270%,罔相仪形|0|356|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"355\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"355\",\"技能名字\":\"拂袖蜃影\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"356\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"356\",\"技能名字\":\"罔相仪形\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"28\",\"形象\":\"674\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮莫格·莱尼≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|319|0,|320|0\",\"技能显示\":\",十字图腾|0|319|5000|false|270%,红心卡牌|0|320|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"319\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"319\",\"技能名字\":\"十字图腾\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"320\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"320\",\"技能名字\":\"红心卡牌\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"30\",\"形象\":\"699\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮异星·女神≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|382|0,|383|0\",\"技能显示\":\",永恒昼烬|0|382|5000|false|270%,星陨挽歌|0|383|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"382\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"382\",\"技能名字\":\"永恒昼烬\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"383\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"383\",\"技能名字\":\"星陨挽歌\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"31\",\"形象\":\"704\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮无畏·战车≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|393|0,|394|0\",\"技能显示\":\",无限闪耀|0|393|5000|false|270%,钢铁意志|0|394|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"393\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"393\",\"技能名字\":\"无限闪耀\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"394\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"394\",\"技能名字\":\"钢铁意志\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"32\",\"形象\":\"682\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮花信·仙子≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|326|0,|327|0\",\"技能显示\":\",杏花之舞|0|326|5000|false|270%,杏花烟雨|0|327|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"326\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"326\",\"技能名字\":\"杏花之舞\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"327\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"327\",\"技能名字\":\"杏花烟雨\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"1\",\"形象\":\"660\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮仙·寒江雪≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|254|0,|255|0\",\"技能显示\":\",江雪无双|0|254|5000|false|270%,惊弦华章|0|255|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"254\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"254\",\"技能名字\":\"江雪无双\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"255\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"255\",\"技能名字\":\"惊弦华章\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"33\",\"形象\":\"650\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮白泽·女神≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|229|0,|230|0\",\"技能显示\":\",神之皇鳞|0|229|5000|false|270%,祥瑞之气|0|230|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"229\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"229\",\"技能名字\":\"神之皇鳞\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"230\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"230\",\"技能名字\":\"祥瑞之气\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"34\",\"形象\":\"643\",\"五行\":\"聖\",\"当前经验\":\"0\",\"等级\":\"1\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮妖刀姬≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|214|0,|215|0\",\"技能显示\":\",逐日之剑|0|214|5000|false|270%,妖刀连斩|0|215|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"214\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"214\",\"技能名字\":\"逐日之剑\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"215\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"215\",\"技能名字\":\"妖刀连斩\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"35\",\"形象\":\"640\",\"五行\":\"聖\",\"当前经验\":\"0\",\"等级\":\"1\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮圣诞·女神≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|205|0,|206|0\",\"技能显示\":\",圣诞恋歌|0|205|5000|false|270%,圣诞祝福|0|206|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"205\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"205\",\"技能名字\":\"圣诞恋歌\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"206\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"206\",\"技能名字\":\"圣诞祝福\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"2\",\"形象\":\"656\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮花未央≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|244|0,|245|0\",\"技能显示\":\",花尽虫稀|0|244|5000|false|270%,夏之契约|0|245|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"244\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"244\",\"技能名字\":\"花尽虫稀\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"245\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"245\",\"技能名字\":\"夏之契约\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"36\",\"形象\":\"633\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮亚丝娜≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|188|0,|189|0\",\"技能显示\":\",星屑飞溅|0|188|5000|false|270%,闪烁之光|0|189|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"188\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"188\",\"技能名字\":\"星屑飞溅\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"189\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"189\",\"技能名字\":\"闪烁之光\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"37\",\"形象\":\"689\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮游方·玄鹤≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|352|0,|353|0\",\"技能显示\":\",神鹤凤翎|0|352|5000|false|280%,三世繁花|0|353|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"352\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"352\",\"技能名字\":\"神鹤凤翎\",\"技能百分比\":\"1.8\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"353\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"353\",\"技能名字\":\"三世繁花\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"38\",\"形象\":\"606\",\"五行\":\"聖\",\"当前经验\":\"85000000001\",\"等级\":\"130\",\"生命\":\"4052521\",\"魔法\":\"736821\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"859625\",\"防御\":\"491213\",\"命中\":\"859625\",\"闪避\":\"491213\",\"速度\":\"1842055\",\"状态\":\"0\",\"宠物名字\":\"≮夜叉·仙君≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"1\",\"技能列表\":\",|11|0\",\"技能显示\":\",加强攻击|0|11|500|false|105%\",\"信息\":[{\"技能序号\":\"11\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"11\",\"技能名字\":\"加强攻击\",\"技能百分比\":\"0.05\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"500\",\"BUFF\":\"false\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"62\",\"形象\":\"172\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"36124130\",\"魔法\":\"6568023\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7662694\",\"防御\":\"4378682\",\"命中\":\"7662694\",\"闪避\":\"4378682\",\"速度\":\"16420059\",\"状态\":\"0\",\"宠物名字\":\"★蜡笔MM★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"64\",\"形象\":\"171\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"85145\",\"魔法\":\"15480\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"18061\",\"防御\":\"10320\",\"命中\":\"18061\",\"闪避\":\"10320\",\"速度\":\"38702\",\"状态\":\"0\",\"宠物名字\":\"★GM鸭子★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"51\",\"形象\":\"174\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"36125209\",\"魔法\":\"6568219\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7662923\",\"防御\":\"4378813\",\"命中\":\"7662923\",\"闪避\":\"4378813\",\"速度\":\"16420549\",\"状态\":\"0\",\"宠物名字\":\"★囧娃娃★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"67\",\"形象\":\"173\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"36129050\",\"魔法\":\"6568918\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7663737\",\"防御\":\"4379278\",\"命中\":\"7663737\",\"闪避\":\"4379278\",\"速度\":\"16422295\",\"状态\":\"0\",\"宠物名字\":\"★忍者小乌龟★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"40\",\"形象\":\"639\",\"五行\":\"聖\",\"当前经验\":\"0\",\"等级\":\"1\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮薇薇安≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|202|0,|203|0\",\"技能显示\":\",灵魂安抚|0|202|5000|false|280%,如沐春风|0|203|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"202\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"202\",\"技能名字\":\"灵魂安抚\",\"技能百分比\":\"1.8\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"203\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"203\",\"技能名字\":\"如沐春风\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"41\",\"形象\":\"646\",\"五行\":\"聖\",\"当前经验\":\"0\",\"等级\":\"1\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮亓玉·女神≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|219|0,|220|0\",\"技能显示\":\",灵兔之舞|0|219|5000|false|280%,美妙梦境|0|220|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"219\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"219\",\"技能名字\":\"灵兔之舞\",\"技能百分比\":\"1.8\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"220\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"220\",\"技能名字\":\"美妙梦境\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"42\",\"形象\":\"687\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮Re·Rem≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|343|0,|344|0\",\"技能显示\":\",蕾姆の守护|0|343|5000|false|270%,如梦如幻|0|344|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"343\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"343\",\"技能名字\":\"蕾姆の守护\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"344\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"344\",\"技能名字\":\"如梦如幻\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"77\",\"形象\":\"678\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮汤圆妹妹≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|316|0,|317|0\",\"技能显示\":\",汤圆团子|0|316|5000|false|270%,福气缠绕|0|317|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"316\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"316\",\"技能名字\":\"汤圆团子\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"317\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"317\",\"技能名字\":\"福气缠绕\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"78\",\"形象\":\"655\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮灵刀·千夏≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|241|0,|242|0\",\"技能显示\":\",樱花乱舞|0|241|5000|false|280%,万灵聚元|0|242|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"241\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"241\",\"技能名字\":\"樱花乱舞\",\"技能百分比\":\"1.8\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"242\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"242\",\"技能名字\":\"万灵聚元\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"81\",\"形象\":\"659\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮剑圣·李白≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|251|0,|252|0\",\"技能显示\":\",惊鸿逐日|0|251|5000|false|280%,太白之道|0|252|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"251\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"251\",\"技能名字\":\"惊鸿逐日\",\"技能百分比\":\"1.8\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"252\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"252\",\"技能名字\":\"太白之道\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"61\",\"形象\":\"175\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"4312\",\"魔法\":\"1238\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"668\",\"防御\":\"454\",\"命中\":\"710\",\"闪避\":\"1052\",\"速度\":\"1948\",\"状态\":\"0\",\"宠物名字\":\"★四叶草宝宝★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"66\",\"形象\":\"593\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮玉藻皇≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|108|0,|109|0\",\"技能显示\":\",星火燎原|0|108|5000|false|270%,红莲结界|0|109|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"108\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"108\",\"技能名字\":\"星火燎原\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"109\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"109\",\"技能名字\":\"红莲结界\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.40\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"68\",\"形象\":\"587\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮紫霞·仙子≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|110|0,|111|0\",\"技能显示\":\",紫气东来|0|110|5000|false|270%,冰火重生|0|111|0|true|+40%生命\",\"信息\":[{\"技能序号\":\"110\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"110\",\"技能名字\":\"紫气东来\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"111\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"111\",\"技能名字\":\"冰火重生\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"生命\",\"附带效果增量\":\"0.40\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"71\",\"形象\":\"619\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮九天·玄女≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|166|0,|167|0\",\"技能显示\":\",九天玄火|0|166|5000|false|270%,天律信仰|0|167|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"166\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"166\",\"技能名字\":\"九天玄火\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"167\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"167\",\"技能名字\":\"天律信仰\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"80\",\"形象\":\"612\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮羲和·女神≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|144|0,|145|0\",\"技能显示\":\",花晨月夕|0|144|5000|false|270%,月缔时光|0|145|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"144\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"144\",\"技能名字\":\"花晨月夕\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"145\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"145\",\"技能名字\":\"月缔时光\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.40\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"82\",\"形象\":\"603\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮誓言·女神≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|134|0,|135|0\",\"技能显示\":\",一瞬千逝|0|134|5000|false|270%,日月如梭|0|135|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"134\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"134\",\"技能名字\":\"一瞬千逝\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"135\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"135\",\"技能名字\":\"日月如梭\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.40\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"83\",\"形象\":\"617\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮高桥·璃茉≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|160|0,|161|0\",\"技能显示\":\",梦中幽曲|0|160|5000|false|270%,魅惑之心|0|161|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"160\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"160\",\"技能名字\":\"梦中幽曲\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"161\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"161\",\"技能名字\":\"魅惑之心\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"7\",\"形象\":\"601\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮鹿仙·璃落≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|132|0,|133|0\",\"技能显示\":\",蕉鹿之梦|0|132|5000|false|250%,轻风听雨|0|133|0|true|+40%攻击\",\"信息\":[{\"技能序号\":\"132\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"132\",\"技能名字\":\"蕉鹿之梦\",\"技能百分比\":\"1.5\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"133\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"133\",\"技能名字\":\"轻风听雨\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"攻击\",\"附带效果增量\":\"0.40\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"8\",\"形象\":\"632\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮龙族·敖丙≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|191|0,|192|0\",\"技能显示\":\",冰动山河|0|191|5000|false|270%,龙吟长空|0|192|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"191\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"191\",\"技能名字\":\"冰动山河\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"192\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"192\",\"技能名字\":\"龙吟长空\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"39\",\"形象\":\"630\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮魔丸·哪吒≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|181|0,|182|0\",\"技能显示\":\",火焰三尖枪|0|181|5000|false|270%,乾坤天降|0|182|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"181\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"181\",\"技能名字\":\"火焰三尖枪\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"182\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"182\",\"技能名字\":\"乾坤天降\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"43\",\"形象\":\"623\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"★破宇暴龙★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|171|0,|172|0\",\"技能显示\":\",狂雷领域|0|171|5000|false|250%,狂龙噬灵|0|172|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"171\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"171\",\"技能名字\":\"狂雷领域\",\"技能百分比\":\"1.5\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"172\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"172\",\"技能名字\":\"狂龙噬灵\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"45\",\"形象\":\"641\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"★九尾小白★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|208|0,|209|0\",\"技能显示\":\",火之辉耀|0|208|5000|false|250%,冥火之拥|0|209|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"208\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"208\",\"技能名字\":\"火之辉耀\",\"技能百分比\":\"1.5\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"209\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"209\",\"技能名字\":\"冥火之拥\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"46\",\"形象\":\"621\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"★鼠小可★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|169|0,|170|0\",\"技能显示\":\",卡牌切割|0|169|5000|false|250%,力念庇护|0|170|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"169\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"169\",\"技能名字\":\"卡牌切割\",\"技能百分比\":\"1.5\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"170\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"170\",\"技能名字\":\"力念庇护\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"47\",\"形象\":\"611\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮亓玥·公主≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|148|0,|149|0\",\"技能显示\":\",灵牌冲击|0|148|5000|false|250%,萌之物语|0|149|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"148\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"148\",\"技能名字\":\"灵牌冲击\",\"技能百分比\":\"1.5\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"149\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"149\",\"技能名字\":\"萌之物语\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"50\",\"形象\":\"567\",\"五行\":\"聖\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"164747\",\"魔法\":\"23064\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"36244\",\"防御\":\"6589\",\"命中\":\"19769\",\"闪避\":\"9884\",\"速度\":\"49424\",\"状态\":\"0\",\"宠物名字\":\"≮冰瑶☆女君≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\",|87|0,|88|0\",\"技能显示\":\",混沌冰灵|0|87|5000|false|240%,玄冰散灵|0|88|0|true|+30%命中\",\"信息\":[{\"技能序号\":\"87\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"87\",\"技能名字\":\"混沌冰灵\",\"技能百分比\":\"1.4\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"88\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"88\",\"技能名字\":\"玄冰散灵\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.30\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"52\",\"形象\":\"579\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮铃木·奈奈≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|91|0,|92|0\",\"技能显示\":\",桃之夭夭|0|91|5000|false|230%,灼灼其华|0|92|0|true|+30%命中\",\"信息\":[{\"技能序号\":\"91\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"91\",\"技能名字\":\"桃之夭夭\",\"技能百分比\":\"1.3\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"92\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"92\",\"技能名字\":\"灼灼其华\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.30\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"53\",\"形象\":\"590\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"★琴音宝宝★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|106|0,|107|0\",\"技能显示\":\",弦歌不绝|0|106|5000|false|250%,靡靡之音|0|107|0|true|+40%速度\",\"信息\":[{\"技能序号\":\"106\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"106\",\"技能名字\":\"弦歌不绝\",\"技能百分比\":\"1.5\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"107\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"107\",\"技能名字\":\"靡靡之音\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"速度\",\"附带效果增量\":\"0.40\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"54\",\"形象\":\"598\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮媚若·仙子≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|130|0,|131|0\",\"技能显示\":\",★飞龙九天★|0|130|5000|false|240%,春回大地|0|131|0|true|+40%生命\",\"信息\":[{\"技能序号\":\"130\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"130\",\"技能名字\":\"★飞龙九天★\",\"技能百分比\":\"1.4\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"131\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"131\",\"技能名字\":\"春回大地\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"生命\",\"附带效果增量\":\"0.40\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"56\",\"形象\":\"564\",\"五行\":\"神\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"3000\",\"魔法\":\"1000\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2000\",\"防御\":\"1400\",\"命中\":\"3000\",\"闪避\":\"1400\",\"速度\":\"500\",\"状态\":\"0\",\"宠物名字\":\"祈愿★精灵\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|84|0,|85|0,|86|0\",\"技能显示\":\",梦幻闪光|0|84|5000|false|240%,祈愿术|0|85|0|true|+35%命中,瞬影术|0|86|0|true|+20%速度\",\"信息\":[{\"技能序号\":\"84\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"84\",\"技能名字\":\"梦幻闪光\",\"技能百分比\":\"1.4\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"85\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"85\",\"技能名字\":\"祈愿术\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.35\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}},{\"技能序号\":\"86\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"86\",\"技能名字\":\"瞬影术\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"速度\",\"附带效果增量\":\"0.20\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"57\",\"形象\":\"562\",\"五行\":\"神\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"3000\",\"魔法\":\"1000\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2000\",\"防御\":\"1400\",\"命中\":\"3000\",\"闪避\":\"1400\",\"速度\":\"500\",\"状态\":\"0\",\"宠物名字\":\"★骨龙★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|73|0,|74|0\",\"技能显示\":\",死亡凋零|0|73|5000|false|240%,亢龙决|0|74|0|true|+20%加深\",\"信息\":[{\"技能序号\":\"73\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"73\",\"技能名字\":\"死亡凋零\",\"技能百分比\":\"1.4\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"74\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"74\",\"技能名字\":\"亢龙决\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"加深\",\"附带效果增量\":\"0.20\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"89\",\"形象\":\"158\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"80048\",\"魔法\":\"14554\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"16980\",\"防御\":\"9702\",\"命中\":\"16980\",\"闪避\":\"9702\",\"速度\":\"36385\",\"状态\":\"0\",\"宠物名字\":\"≮寒江雪≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"90\",\"形象\":\"355\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"36125897\",\"魔法\":\"6568344\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7663069\",\"防御\":\"4378896\",\"命中\":\"7663069\",\"闪避\":\"4378896\",\"速度\":\"16420862\",\"状态\":\"0\",\"宠物名字\":\"蛋挞妹\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"5\",\"形象\":\"385\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"36131460\",\"魔法\":\"6569356\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7664249\",\"防御\":\"4379570\",\"命中\":\"7664249\",\"闪避\":\"4379570\",\"速度\":\"16423390\",\"状态\":\"0\",\"宠物名字\":\"召唤之龙\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"72\",\"形象\":\"696\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮天使·银河≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|373|0,|374|0\",\"技能显示\":\",星空回溯|0|373|5000|false|300%,银河之羽|0|374|0|true|+60%命中\",\"信息\":[{\"技能序号\":\"373\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"373\",\"技能名字\":\"星空回溯\",\"技能百分比\":\"2.0\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"374\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"374\",\"技能名字\":\"银河之羽\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.6\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"73\",\"形象\":\"670\",\"五行\":\"聖\",\"当前经验\":\"0\",\"等级\":\"1\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"§红莲★朱雀§\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|293|0,|294|0\",\"技能显示\":\",业火红莲|0|293|5000|false|270%,红莲之印|0|294|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"293\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"293\",\"技能名字\":\"业火红莲\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"294\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"294\",\"技能名字\":\"红莲之印\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"60\",\"形象\":\"671\",\"五行\":\"聖\",\"当前经验\":\"0\",\"等级\":\"1\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"§圣灵★翼兽§\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|296|0,|297|0\",\"技能显示\":\",赤炎之拳|0|296|5000|false|270%,焰翼之风|0|297|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"296\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"296\",\"技能名字\":\"赤炎之拳\",\"技能百分比\":\"1.7\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"297\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"297\",\"技能名字\":\"焰翼之风\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"70\",\"形象\":\"675\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"★麒麟宝宝★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|322|0,|323|0\",\"技能显示\":\",祥瑞圣火|0|322|5000|false|260%,祥瑞环绕|0|323|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"322\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"322\",\"技能名字\":\"祥瑞圣火\",\"技能百分比\":\"1.6\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"323\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"323\",\"技能名字\":\"祥瑞环绕\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"48\",\"形象\":\"645\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"★魔女松绮★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|217|0,|218|0\",\"技能显示\":\",绮梦剑阵|0|217|5000|false|260%,绮梦花语|0|218|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"217\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"217\",\"技能名字\":\"绮梦剑阵\",\"技能百分比\":\"1.6\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"218\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"218\",\"技能名字\":\"绮梦花语\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"75\",\"形象\":\"625\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"★甜心女孩★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|173|0,|174|0\",\"技能显示\":\",甜心暴击|0|173|5000|false|260%,甜心咒语|0|174|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"173\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"173\",\"技能名字\":\"甜心暴击\",\"技能百分比\":\"1.6\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"174\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"174\",\"技能名字\":\"甜心咒语\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"76\",\"形象\":\"652\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"★幽翼灵喵★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|232|0,|233|0\",\"技能显示\":\",幽翼乱舞|0|232|5000|false|260%,洞察意志|0|233|0|true|+40%命中\",\"信息\":[{\"技能序号\":\"232\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"232\",\"技能名字\":\"幽翼乱舞\",\"技能百分比\":\"1.6\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"233\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"233\",\"技能名字\":\"洞察意志\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.4\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"79\",\"形象\":\"185\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"36127099\",\"魔法\":\"6568563\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7663324\",\"防御\":\"4379042\",\"命中\":\"7663324\",\"闪避\":\"4379042\",\"速度\":\"16421408\",\"状态\":\"0\",\"宠物名字\":\"亲吻鱼（沫）\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"84\",\"形象\":\"693\",\"五行\":\"聖\",\"当前经验\":\"87000000001\",\"等级\":\"130\",\"生命\":\"6765829\",\"魔法\":\"947216\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"1488482\",\"防御\":\"270633\",\"命中\":\"811899\",\"闪避\":\"405949\",\"速度\":\"2029748\",\"状态\":\"0\",\"宠物名字\":\"★小熊座★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"1\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"58\",\"形象\":\"701\",\"五行\":\"聖\",\"当前经验\":\"85000000001\",\"等级\":\"130\",\"生命\":\"52356858\",\"魔法\":\"7329959\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"11518508\",\"防御\":\"2094274\",\"命中\":\"6282822\",\"闪避\":\"3141411\",\"速度\":\"15707057\",\"状态\":\"0\",\"宠物名字\":\"★幽萤仙子★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"49\",\"形象\":\"184\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"36128623\",\"魔法\":\"6568840\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7663647\",\"防御\":\"4379227\",\"命中\":\"7663647\",\"闪避\":\"4379227\",\"速度\":\"16422101\",\"状态\":\"0\",\"宠物名字\":\"亲吻鱼（濡）\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"86\",\"形象\":\"396\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"36127879\",\"魔法\":\"6568705\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7663489\",\"防御\":\"4379136\",\"命中\":\"7663489\",\"闪避\":\"4379136\",\"速度\":\"16421763\",\"状态\":\"0\",\"宠物名字\":\"≮金羽凤凰≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"63\",\"形象\":\"663\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮圣光·希珞娅≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|273|0,|274|0\",\"技能显示\":\",天使光辉|0|273|5000|false|300%,空之境界|0|274|0|true|+60%命中\",\"信息\":[{\"技能序号\":\"273\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"273\",\"技能名字\":\"天使光辉\",\"技能百分比\":\"2.0\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":null}},{\"技能序号\":\"274\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"274\",\"技能名字\":\"空之境界\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.6\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"85\",\"形象\":\"665\",\"五行\":\"聖\",\"当前经验\":\"85000000000\",\"等级\":\"130\",\"生命\":\"4500\",\"魔法\":\"1500\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2500\",\"防御\":\"2000\",\"命中\":\"4000\",\"闪避\":\"2000\",\"速度\":\"5500\",\"状态\":\"0\",\"宠物名字\":\"≮双影·露迩≯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|265|0,|269|0\",\"技能显示\":\",圣·信仰之翼|0|265|0|true|+100%生命,光暗交错|0|269|0|true|+60%命中\",\"信息\":[{\"技能序号\":\"265\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"265\",\"技能名字\":\"圣·信仰之翼\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"生命\",\"附带效果增量\":\"1.0\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}},{\"技能序号\":\"269\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"269\",\"技能名字\":\"光暗交错\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"命中\",\"附带效果增量\":\"0.6\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"29\",\"形象\":\"262\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35887453\",\"魔法\":\"6524991\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612489\",\"防御\":\"4349994\",\"命中\":\"7612489\",\"闪避\":\"4349994\",\"速度\":\"16312478\",\"状态\":\"0\",\"宠物名字\":\"旋花妖王\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"44\",\"形象\":\"306\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35888561\",\"魔法\":\"6525192\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612725\",\"防御\":\"4350128\",\"命中\":\"7612725\",\"闪避\":\"4350128\",\"速度\":\"16312982\",\"状态\":\"0\",\"宠物名字\":\"★马鲁斯★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"3\",\"形象\":\"308\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35886700\",\"魔法\":\"6524854\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612330\",\"防御\":\"4349902\",\"命中\":\"7612330\",\"闪避\":\"4349902\",\"速度\":\"16312136\",\"状态\":\"0\",\"宠物名字\":\"爆爆卷\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"55\",\"形象\":\"311\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35887411\",\"魔法\":\"6524983\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612481\",\"防御\":\"4349989\",\"命中\":\"7612481\",\"闪避\":\"4349989\",\"速度\":\"16312459\",\"状态\":\"0\",\"宠物名字\":\"斧-狂战神\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"59\",\"形象\":\"322\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35887338\",\"魔法\":\"6524970\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612465\",\"防御\":\"4349980\",\"命中\":\"7612465\",\"闪避\":\"4349980\",\"速度\":\"16312426\",\"状态\":\"0\",\"宠物名字\":\"童童木\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"74\",\"形象\":\"394\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35883331\",\"魔法\":\"6524241\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7611615\",\"防御\":\"4349494\",\"命中\":\"7611615\",\"闪避\":\"4349494\",\"速度\":\"16310605\",\"状态\":\"0\",\"宠物名字\":\"白马骑士\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"88\",\"形象\":\"120\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35886934\",\"魔法\":\"6524897\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612379\",\"防御\":\"4349931\",\"命中\":\"7612379\",\"闪避\":\"4349931\",\"速度\":\"16312242\",\"状态\":\"0\",\"宠物名字\":\"圣兽赤牝鹿\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"92\",\"形象\":\"206\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35887374\",\"魔法\":\"6524977\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612473\",\"防御\":\"4349984\",\"命中\":\"7612473\",\"闪避\":\"4349984\",\"速度\":\"16312443\",\"状态\":\"0\",\"宠物名字\":\"幽蜚-R-01\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"93\",\"形象\":\"194\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35884645\",\"魔法\":\"6524480\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7611894\",\"防御\":\"4349653\",\"命中\":\"7611894\",\"闪避\":\"4349653\",\"速度\":\"16311202\",\"状态\":\"0\",\"宠物名字\":\"★阿莲娜★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"91\",\"形象\":\"219\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35885625\",\"魔法\":\"6524659\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612102\",\"防御\":\"4349772\",\"命中\":\"7612102\",\"闪避\":\"4349772\",\"速度\":\"16311647\",\"状态\":\"0\",\"宠物名字\":\"爆虚\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"95\",\"形象\":\"294\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35886377\",\"魔法\":\"6524795\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612261\",\"防御\":\"4349863\",\"命中\":\"7612261\",\"闪避\":\"4349863\",\"速度\":\"16311989\",\"状态\":\"0\",\"宠物名字\":\"蜥蜴拳皇\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"96\",\"形象\":\"295\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35885027\",\"魔法\":\"6524550\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7611975\",\"防御\":\"4349700\",\"命中\":\"7611975\",\"闪避\":\"4349700\",\"速度\":\"16311376\",\"状态\":\"0\",\"宠物名字\":\"彩灯仙子\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"69\",\"形象\":\"288\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35884838\",\"魔法\":\"6524515\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7611935\",\"防御\":\"4349677\",\"命中\":\"7611935\",\"闪避\":\"4349677\",\"速度\":\"16311290\",\"状态\":\"0\",\"宠物名字\":\"幻影·鹦鹉\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"87\",\"形象\":\"207\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35885065\",\"魔法\":\"6524557\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7611983\",\"防御\":\"4349704\",\"命中\":\"7611983\",\"闪避\":\"4349704\",\"速度\":\"16311393\",\"状态\":\"0\",\"宠物名字\":\"狻猊\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"97\",\"形象\":\"110\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35883294\",\"魔法\":\"6524235\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7611607\",\"防御\":\"4349490\",\"命中\":\"7611607\",\"闪避\":\"4349490\",\"速度\":\"16310588\",\"状态\":\"0\",\"宠物名字\":\"雪羽凤凰\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"99\",\"形象\":\"117\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35886167\",\"魔法\":\"6524757\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612217\",\"防御\":\"4349838\",\"命中\":\"7612217\",\"闪避\":\"4349838\",\"速度\":\"16311894\",\"状态\":\"0\",\"宠物名字\":\"★龙蛇玄武★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"94\",\"形象\":\"106\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35800558\",\"魔法\":\"6509192\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7594057\",\"防御\":\"4339461\",\"命中\":\"7594057\",\"闪避\":\"4339461\",\"速度\":\"16272981\",\"状态\":\"0\",\"宠物名字\":\"★青龙★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\",|11|0\",\"技能显示\":\",加强攻击|0|11|500|false|105%\",\"信息\":[{\"技能序号\":\"11\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"11\",\"技能名字\":\"加强攻击\",\"技能百分比\":\"0.05\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"500\",\"BUFF\":\"false\",\"限制五行\":null}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"98\",\"形象\":\"143\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35887148\",\"魔法\":\"6524936\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612425\",\"防御\":\"4349957\",\"命中\":\"7612425\",\"闪避\":\"4349957\",\"速度\":\"16312340\",\"状态\":\"0\",\"宠物名字\":\"神龙媚若\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"101\",\"形象\":\"195\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35883871\",\"魔法\":\"6524340\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7611730\",\"防御\":\"4349559\",\"命中\":\"7611730\",\"闪避\":\"4349559\",\"速度\":\"16310850\",\"状态\":\"0\",\"宠物名字\":\"★佑碧★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"103\",\"形象\":\"218\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35887740\",\"魔法\":\"6525043\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612551\",\"防御\":\"4350029\",\"命中\":\"7612551\",\"闪避\":\"4350029\",\"速度\":\"16312609\",\"状态\":\"0\",\"宠物名字\":\"天驹\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"105\",\"形象\":\"314\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35885625\",\"魔法\":\"6524659\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612102\",\"防御\":\"4349772\",\"命中\":\"7612102\",\"闪避\":\"4349772\",\"速度\":\"16311647\",\"状态\":\"0\",\"宠物名字\":\"圣兽光雨鹿\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"102\",\"形象\":\"109\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35888089\",\"魔法\":\"6525107\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612625\",\"防御\":\"4350071\",\"命中\":\"7612625\",\"闪避\":\"4350071\",\"速度\":\"16312768\",\"状态\":\"0\",\"宠物名字\":\"火羽凤凰\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"107\",\"形象\":\"209\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35886006\",\"魔法\":\"6524728\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612183\",\"防御\":\"4349818\",\"命中\":\"7612183\",\"闪避\":\"4349818\",\"速度\":\"16311820\",\"状态\":\"0\",\"宠物名字\":\"齐伦\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"108\",\"形象\":\"196\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35882895\",\"魔法\":\"6524162\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7611523\",\"防御\":\"4349441\",\"命中\":\"7611523\",\"闪避\":\"4349441\",\"速度\":\"16310406\",\"状态\":\"0\",\"宠物名字\":\"★西西★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"110\",\"形象\":\"169\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35886184\",\"魔法\":\"6524760\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612220\",\"防御\":\"4349840\",\"命中\":\"7612220\",\"闪避\":\"4349840\",\"速度\":\"16311901\",\"状态\":\"0\",\"宠物名字\":\"圣火麒麟\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"100\",\"形象\":\"180\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35886760\",\"魔法\":\"6524865\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612343\",\"防御\":\"4349910\",\"命中\":\"7612343\",\"闪避\":\"4349910\",\"速度\":\"16312163\",\"状态\":\"0\",\"宠物名字\":\"年兽\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"104\",\"形象\":\"217\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35884292\",\"魔法\":\"6524416\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7611819\",\"防御\":\"4349611\",\"命中\":\"7611819\",\"闪避\":\"4349611\",\"速度\":\"16311041\",\"状态\":\"0\",\"宠物名字\":\"熊猫ORZ-烈\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"111\",\"形象\":\"251\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35885589\",\"魔法\":\"6524652\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612094\",\"防御\":\"4349768\",\"命中\":\"7612094\",\"闪避\":\"4349768\",\"速度\":\"16311631\",\"状态\":\"0\",\"宠物名字\":\"歌莉娅\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"106\",\"形象\":\"221\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35889055\",\"魔法\":\"6525282\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612830\",\"防御\":\"4350188\",\"命中\":\"7612830\",\"闪避\":\"4350188\",\"速度\":\"16313207\",\"状态\":\"0\",\"宠物名字\":\"帝铯\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"112\",\"形象\":\"252\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"35886373\",\"魔法\":\"6524795\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"7612261\",\"防御\":\"4349863\",\"命中\":\"7612261\",\"闪避\":\"4349863\",\"速度\":\"16311988\",\"状态\":\"0\",\"宠物名字\":\"业火·无明\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"109\",\"形象\":\"216\",\"五行\":\"神\",\"当前经验\":\"1\",\"等级\":\"1\",\"生命\":\"9717757\",\"魔法\":\"1766864\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"2061342\",\"防御\":\"1177909\",\"命中\":\"2061342\",\"闪避\":\"1177909\",\"速度\":\"4417162\",\"状态\":\"0\",\"宠物名字\":\"兔儿啾啾\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"10\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"114\",\"形象\":\"716\",\"五行\":\"巫\",\"当前经验\":\"0\",\"等级\":\"1\",\"生命\":\"2700\",\"魔法\":\"1000\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"1140\",\"防御\":\"3000\",\"命中\":\"3300\",\"闪避\":\"2000\",\"速度\":\"2900\",\"状态\":\"0\",\"宠物名字\":\"宝宝龙\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"115\",\"形象\":\"716\",\"五行\":\"巫\",\"当前经验\":\"0\",\"等级\":\"1\",\"生命\":\"2700\",\"魔法\":\"1000\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"1140\",\"防御\":\"3000\",\"命中\":\"3300\",\"闪避\":\"2000\",\"速度\":\"2900\",\"状态\":\"0\",\"宠物名字\":\"宝宝龙\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"12\",\"形象\":\"716\",\"五行\":\"巫\",\"当前经验\":\"0\",\"等级\":\"1\",\"生命\":\"2700\",\"魔法\":\"1000\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"1140\",\"防御\":\"3000\",\"命中\":\"3300\",\"闪避\":\"2000\",\"速度\":\"2900\",\"状态\":\"0\",\"宠物名字\":\"宝宝龙\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"15\",\"形象\":\"716\",\"五行\":\"巫\",\"当前经验\":\"0\",\"等级\":\"1\",\"生命\":\"2700\",\"魔法\":\"1000\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"1140\",\"防御\":\"3000\",\"命中\":\"3300\",\"闪避\":\"2000\",\"速度\":\"2900\",\"状态\":\"0\",\"宠物名字\":\"宝宝龙\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"16\",\"形象\":\"716\",\"五行\":\"巫\",\"当前经验\":\"0\",\"等级\":\"1\",\"生命\":\"2700\",\"魔法\":\"1000\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"1140\",\"防御\":\"3000\",\"命中\":\"3300\",\"闪避\":\"2000\",\"速度\":\"2900\",\"状态\":\"0\",\"宠物名字\":\"宝宝龙\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"19\",\"形象\":\"716\",\"五行\":\"巫\",\"当前经验\":\"0\",\"等级\":\"1\",\"生命\":\"2700\",\"魔法\":\"1000\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"1140\",\"防御\":\"3000\",\"命中\":\"3300\",\"闪避\":\"2000\",\"速度\":\"2900\",\"状态\":\"0\",\"宠物名字\":\"宝宝龙\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\"0\",\"技能显示\":\"\",\"信息\":[],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"6\",\"形象\":\"681\",\"五行\":\"巫\",\"当前经验\":\"0\",\"等级\":\"1\",\"生命\":\"2700\",\"魔法\":\"1000\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"1140\",\"防御\":\"3000\",\"命中\":\"3300\",\"闪避\":\"2000\",\"速度\":\"2900\",\"状态\":\"0\",\"宠物名字\":\"★猪苗苗★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|324|0,|325|0\",\"技能显示\":\",旋风滑翔|0|324|5000|false|160%,苗苗意志|0|325|0|true|+20%攻击\",\"信息\":[{\"技能序号\":\"324\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"324\",\"技能名字\":\"旋风滑翔\",\"技能百分比\":\"0.6\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":\"巫\"}},{\"技能序号\":\"325\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"325\",\"技能名字\":\"苗苗意志\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"攻击\",\"附带效果增量\":\"0.2\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":\"巫\"}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"9\",\"形象\":\"681\",\"五行\":\"巫\",\"当前经验\":\"0\",\"等级\":\"1\",\"生命\":\"2700\",\"魔法\":\"1000\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"1140\",\"防御\":\"3000\",\"命中\":\"3300\",\"闪避\":\"2000\",\"速度\":\"2900\",\"状态\":\"0\",\"宠物名字\":\"★猪苗苗★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|324|0,|325|0\",\"技能显示\":\",旋风滑翔|0|324|5000|false|160%,苗苗意志|0|325|0|true|+20%攻击\",\"信息\":[{\"技能序号\":\"324\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"324\",\"技能名字\":\"旋风滑翔\",\"技能百分比\":\"0.6\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":\"巫\"}},{\"技能序号\":\"325\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"325\",\"技能名字\":\"苗苗意志\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"攻击\",\"附带效果增量\":\"0.2\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":\"巫\"}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"},{\"指定五行\":null,\"抵消\":null,\"加深\":null,\"吸魔\":null,\"吸血\":null,\"地狱之门\":false,\"TTT\":false,\"世界BOSS\":false,\"自定义宠物名字\":null,\"指定形象\":\"290\",\"宠物序号\":\"10\",\"形象\":\"681\",\"五行\":\"巫\",\"当前经验\":\"0\",\"等级\":\"1\",\"生命\":\"2700\",\"魔法\":\"1000\",\"最大生命\":null,\"最大魔法\":null,\"攻击\":\"1140\",\"防御\":\"3000\",\"命中\":\"3300\",\"闪避\":\"2000\",\"速度\":\"2900\",\"状态\":\"0\",\"宠物名字\":\"★猪苗苗★\",\"成长上限\":\"20000000\",\"成长\":\"0\",\"已进化次数\":\"0\",\"技能列表\":\",|324|0,|325|0\",\"技能显示\":\",旋风滑翔|0|324|5000|false|160%,苗苗意志|0|325|0|true|+20%攻击\",\"信息\":[{\"技能序号\":\"324\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"324\",\"技能名字\":\"旋风滑翔\",\"技能百分比\":\"0.6\",\"技能附带效果\":\"null\",\"附带效果增量\":\"null\",\"耗蓝量\":\"5000\",\"BUFF\":\"false\",\"限制五行\":\"巫\"}},{\"技能序号\":\"325\",\"技能等级\":\"0\",\"信息\":{\"技能ID\":\"325\",\"技能名字\":\"苗苗意志\",\"技能百分比\":\"1.0\",\"技能附带效果\":\"攻击\",\"附带效果增量\":\"0.2\",\"耗蓝量\":\"0\",\"BUFF\":\"true\",\"限制五行\":\"巫\"}}],\"境界\":\"元神初具\",\"TalismanState\":null,\"成长突破等级\":\"0\",\"成长突破累计成功率\":\"0\",\"种族突破等级\":\"0\",\"种族突破累计成功率\":\"0\"}]";
        var ashjk;
		var JSONS;
		$(function(){
	
			if(window.external.check()=="true"){
				updatePetList(jLoad);
				JSONS= $.parseJSON(jLoad);
			}
		});
		var wuxing = "";
				function updateMainPet(json){
			
			$(".等级").html(json.等级);
			if(typeof(json.等级)=="undefined")$(".等级").html("0");
		
			$(".五行").html(json.五行);
			if(typeof(json.五行)=="undefined")$(".五行").html("0");
			wuxing=json.五行;
			$(".生命").html(json.生命);
			if(typeof(json.生命)=="undefined")$(".生命").html("0");
			
			
			$(".魔法").html(json.魔法);
					if(typeof(json.魔法)=="undefined")$(".魔法").html("0");


					$(".攻击").html(json.攻击);
					if(typeof(json.攻击)=="undefined")$(".攻击").html("0");


					$(".防御").html(json.防御);
					if(typeof(json.防御)=="undefined")$(".防御").html("0");

					$(".命中").html(json.命中);
					if(typeof(json.命中)=="undefined")$(".命中").html("0");


					$(".闪避").html(json.闪避);
					if(typeof(json.闪避)=="undefined")$(".闪避").html("0");

					$(".速度").html(json.速度);
					if(typeof(json.速度)=="undefined")$(".速度").html("0");

					$(".成长").html(json.成长);
					if(typeof(json.成长)=="undefined")$(".成长").html("0");

					$(".当前经验").html(json.当前经验);
					if(typeof(json.当前经验)=="undefined")$(".当前经验").html("0");

					//	$(".升级经验").html(window.external.getExp(JSON.等级));
			
			$(".宠物名字").html(json.宠物名字);
			if(typeof(json.宠物名字)=="undefined")$(".宠物名字").html("0");
			setPetICO(json.形象);
				}
		function setPetICO(id){
			if(wuxing!="聖" && wuxing!="佛"){
				$(".形象").attr("src","Content/PetPhoto/q"+id+".gif");
			}else{
				$(".形象").attr("src","Content/PetPhoto/q"+id+".png");	
			}
		}
		function updatePet(json){
		
			$(".箭头 td").removeClass("ch01");
			var j = $.parseJSON(json);
		    $(".背包宠物").html("");
		
			
			var su = 3;
			var i;
			var html;
			for(i = 0;i<j.length;i++){
				var fc = "bb02";
				if(j[i].状态==0){
					fc="bb01";
				}
				su=su-1;
				html = "<td class=\"petMain_ "+fc+"\"><div class=\"pet\" id=\"p1\"><img src=\"Content/PetPhoto/k"+j[i].形象+".gif\" onerror=\"this.src='Content/PetPhoto/k"+j[i].形象+".png'\" style=\"cursor:pointer;\"></div><p class=\"petid\" style=\"display:none\">"+j[i].宠物序号+"</p><p class=\"i\" style=\"display:none\">"+i+"</p></td>";
				$(".背包宠物").html( $(".背包宠物").html()+html);
			
				

				if(j[i].状态==0){
				
					$("#j"+(i+1)).addClass("ch01");	
					
				}

			}
			for(i = 0;i<su;i++){
				html = "<td><div class=\"pet\" id=\"p1\"></div></td>";
				$(".背包宠物").html( $(".背包宠物").html()+html);
			}
			$(".petMain_").click(function(){
				zhixiang(this,$(this).find(".petid").html(),$(this).find(".i").html());
				Display($(this).find(".petid").html());
			});
		}
        function setTab(cursel, n) {
			var name="tab";
            for (var i = 1; i <= n; i++) {
                var menu = document.getElementById(name + i);
                var con = document.getElementById("con_tab_" + i);
                menu.className = i == cursel ? "on" : "";
                con.style.display = i == cursel ? "block" : "none";
            }
        }
		function updatePetList(json){
            ashjk=window.external.fhfvnsd();
			var j = $.parseJSON(json);
		    $("#petList").html("");
			$(".当前数量").html(j.length);
            $(".最大数量").html(ashjk);
			$("#tgbb").html("<option value=\"\">可托管宠物</option>");
			for(var i = 0;i<j.length;i++){
				var html=
				"<tr onClick=\"selec(this,"+j[i].宠物序号+");copyWord('"+j[i].宠物名字+"');Display("+j[i].宠物序号+");\">"+
					"<td width=\"130px\" onMouseOver=\"mcbbshow("+j[i].宠物序号+","+i+");\" style=\"cursor:pointer;text-align:left;\" onMouseOut=\"ctips(this)\"><img src=\"Content/Img/Pasture/mc05.gif\">"+j[i].宠物名字+"</td>"+
					"<td width=\"70px\" style=\"text-align:left;\">"+j[i].五行+"</td>"+
					"<td style=\"text-align:left;\">LV "+j[i].等级+"</td>"+
				"</tr>";
				
				
			
				 $(html).appendTo($("#petList"));
				 html = "<option value=\""+j[i].宠物序号+"\">"+j[i].宠物名字+"</option>";
				 $(html).appendTo($("#tgbb"));

			}
	
				
		}
		
		
    </script>
    <script language="javascript" src="../javascript/prototype.js"></script>
</head>


<body>
    <div id="Layer1" style="cursor:pointer" onClick="window.parent.$('gw').src='./function/City_Mod.php'">
        <label></label>
    </div>
    <div class="task">
        <div class="task_left"></div>

        <div class="task_right">

            <ul class="task_nav">
                <li id="tab1" onClick="setTab(1,2)" class="on"><a class="a01" href="javascript:void(0)"></a></li>
                <!--<li id="tab2" onClick="setTab(2,2)"><a class="a02" href="javascript:void(0)"></a></li>-->
            </ul>
          
            <div class="dt_task" id="con_tab_1">

                <div class="box01">
                    <div class="box02">
                        <div class="bb01"><img src="Content/Img/Pasture/muchang_09.jpg" width="95" height="25"></div>
                    </div>
                    <div class="box03">
                        <div id="showbba">
                            <table border="0" cellpadding="0" cellspacing="0" class="tit01">
                                <tbody>
                                    <tr>
                                        <td height="24" align="center" style="width:40px;">名称</td>
                                        <td align="center" style="width:80px;">五行</td>
                                        <td align="center" style="width:80px;">等级</td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="dt_list clearfix">
                                <table class="tit01" id="shoplist">
                                    <tbody id="petList">
                                       
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- added by Zheng.Ping -->
                        <div style="display:none; width:100%; text-align:left; font-size:12px; line-height:1.5; height:127px; overflow:auto; scrollbar-face-color:#E1D395; scrollbar-highlight-color:#ffffff; scrollbar-3dlight-color:#E1D395; scrollbar-shadow-color:#ffffff; scrollbar-darkshadow-color:#F3EDC9; scrollbar-track-color:#F3EDC9; scrollbar-arrow-color:#ffffff; cursor:default; color:#BF7D1A; margin-left:3px; margin-top:60px; z-index:3; position:absolute; left: 156px; top: 31px;" id="abc">
                            原密码：<input type="password" name="old_pwd" id="old_pwd"><br/><br/>新新建密码：<input type="password" name="pwd" id="pwd"><br/><br/>
                            重复密码：<input type="password" name="repwd" id="repwd"><br/><br/>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="submit" name="Submit" onClick="resetPwd();" value="提交" hidefocus="">&nbsp;&nbsp;&nbsp;&nbsp;
                            <input type="button" name="Submit2" value="取消" onClick="pwd2()" hidefocus="">
                        </div>

                        <div style="display:none; width:300px; text-align:left; font-size:12px; line-height:1.5; height:127px; overflow:auto; scrollbar-face-color:#E1D395; scrollbar-highlight-color:#ffffff; scrollbar-3dlight-color:#E1D395; scrollbar-shadow-color:#ffffff; scrollbar-darkshadow-color:#F3EDC9; scrollbar-track-color:#F3EDC9; scrollbar-arrow-color:#ffffff; cursor:default; color:#BF7D1A; margin-left:3px; margin-top:60px; z-index:3; position:absolute; left: 156px; top: 31px;" id="pwd_input">
                            密码：<input type="password" name="pwd2" id="pwd2"><br/><br/>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="submit" name="Submit" onClick="delbb()" value="提交" hidefocus="">&nbsp;&nbsp;&nbsp;&nbsp;
                            <input type="button" name="Submit2" value="取消" onClick="pwd2()" hidefocus="">
                        </div>
                        <!-- added by Zheng.Ping -->
                    </div>
                    <div class="box04">牧场宠物数量：<span class="当前数量"></span>/<span class="最大数量"></span><input type="button" class="conbtn" value="主战" onClick="Setbb();"><input type="button" class="conbtn" value="寄养" onClick="Savebb();"><input type="button" class="conbtn" value="携带" onClick="Getbb();"></div>
                </div>
                <div class="box01">
                    <div class="box02">
                        <div class="bb01"><img src="Content/Img/Pasture/muchang_11.jpg" width="82" height="25"></div>
                    </div>
                    <div id="mcbbshow" style="height: 232px; width: 290px; padding: 0px; z-index: 100; position: absolute; left: 460px; top: 59px; display: none;">
                        <div style="z-index:10000; width:40px; height:20px; position:absolute; left:269px; font-size:12px; text-align:center; padding-top:5px; padding-right:5px"><span onClick="mcbbdisplay();" style="cursor:pointer"><font color="#FF0000">关闭</font></span></div>
                        <div style=" clear:both;width:300px;height:230px; background-image:url(Content/Img/Pasture/petbg.gif) ; background-repeat:no-repeat;position:absolute; z-index:9999; ">
                          
                                <div  style="width:177px;height:230px;float:left;"><img class="形象" src="" width="177" height="230"></div>
                                <div style="width:123px;height:230px;float:left;position:relative">
                                    <div style="position:absolute; text-align:center;top:16px;left:9px;width:99px;height:24px; font-size:12px; color:#FFFFFF;font-family:微软雅黑,黑体,arial,vendana;color:#ffffff;"><span class="宠物名字"></span></div>
                                    <div style="font-size:12px;line-height:20px;position:absolute;top:40px;padding:2px;left:5px;height:180px;width:110px;overflow:hidden;">
                                        五行：<span class="五行"></span><br/>
                                        生命：<span class="生命"></span><br/>
                                        魔法：<span class="魔法"></span><br/>
                                        攻击：<span class="攻击"></span><br/>
                                        防御：<span class="防御"></span><br/>
                                        命中：<span class="命中"></span><br/>
                                        闪避：<span class="闪避"></span><br/>
                                        成长：<span class="成长"></span><br/>
                                        等级：<span class="等级"></span>
                                    </div>
                              
                            </div>
                        </div>
                    </div>
                    <div class="box06">
                        <table class="petab">
                            <tbody>
                                <tr class="箭头">
                                    <td id="j1" class="">&nbsp;</td>
                                    <td id="j2">&nbsp;</td>
                                    <td id="j3">&nbsp;</td>

                                </tr>
                                <tr class="背包宠物">
                                  
                                  
                                </tr>
                            </tbody>
                        </table>
                        <p>
                            <b>说明：</b><br/>
                            1）设置主战宠物后，该宠物可获得装备道具、以及 获得任务经验、道具使用效果等。<br/>
                            2）主战宠物无法寄养，请设置其他宠物后再寄养 。<br/>
                        </p>

                    </div>


                    <div class="plus" style="text-align:left"><input type="button" class="conbtn" value="丢弃" onClick="discardBb();"><a onClick="pwd()" href="#"><img src="Content/Img/Pasture/add02.gif" border="0"></a></div>
                </div>
            </div>

            <div class="dt_task con" id="con_tab_2">
                <div class="answer">
                    <p>
                        <b>托管说明：托管开启时间为：22：00--10：00，托管后会扣除玩家托管时间在托管期间勿取回宠物，否则需要再消耗托管时间进行托管(只有放置在牧场且等级到达10级以上的宠物才可以托管)。</b><br/>
                        <strong>
                            托管内容说明：<br/>
                            休息：获得正常经验，无法获得道具，每托管1小时消耗1小时托管时间。<br/>
                            武力修炼：获得2倍托管经验，无法获得道具，每托管1小时消耗2小时托管时间。<br/>
                            冒险修炼：获得2.5倍托管经验，每5分钟随机获得道具1个，每托管1小时消耗3小时托管时间。<br/>
                            加速：点击后可立即完成托管，需要消耗水晶，每小时消耗100水晶币<br/>
                        </strong>
                    </p>
                    <table class="tgtab" style="font-size:12px">
                        <tbody>
                            <tr>
                                <td>托管宠物</td>
                                <td>
                                    <select name="tgbb" id="tgbb">
                                        <option value="">可托管宠物</option>

                                     
                                    </select>
                                </td>
                                <td>托管时间</td>
                                <td>
                                    <select name="tgtimes" id="tgtimes">
                                        <option value="1">1小时</option>
                                        <option value="2">2小时</option>
                                        <option value="4">4小时</option>
                                        <option value="8">8小时</option>
                                        <option value="10">10小时</option>
                                    </select>
                                </td>
                                <td>托管内容</td>
                                <td>
                                    <select name="tgmes" id="tgmes">
                                        <option value="1">休息</option>
                                        <option value="2">武力修炼</option>
                                        <option value="3">冒险修炼</option>
                                    </select>
                                </td>
                                <td>
                                    <span id="tgflag">
                                        <span style="">
                                            <input name="button" type="button" class="conbtn" onClick="tg();" value="托管">
                                            <input name="button2" type="button" class="conbtn" onClick="auto();" value="自动">
                                           
                                        </span>
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table style="color:#BF7D1A; font-size:12px; margin-left:20px;">
                        <tbody>
                            <tr>
                                <td>
                                    已托管的宠物&nbsp;&nbsp;<select name="bb2" id="bb2" onChange="gettginfo(this.options[this.selectedIndex].value)">
                                        <option value="">在托管所的宠物</option>

                                    </select>&nbsp;&nbsp;<span style="left:10px;" id="tgflags"></span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="plus" style="width:580px;margin-left:auto;margin-right:auto">
                    水晶币：9015 剩余托管时间：1674小时  剩余托管最大数目：1
                    <input type="button" class="conbtn" value="取回" onClick="offpets();"><input type="button" class="conbtn" value="详情" onClick="detail();"><input type="button" class="conbtn" value="加速" onClick="times();">
                </div>
            </div>
        </div>
    </div>


    <script language="javascript">
        var style = "";
        if (style == 2) {
            setTab('tab', 2, 4);
        }
        var dtips = document.createElement('DIV');
        var bid = 0;
        var selid = 0;
        var types = -1;
		var xid=0;
        dtips.id = 'dtips';
        dtips.style.cssText = 'position:absolute;display:none;padding:5px;line-height:1.6;fong-size:12px;border:1px solid #fff;color:gray;z-index:1;background-color:#DFDABC;';
        document.body.appendChild(dtips);
        function vtips(obj) {
            dtips.style.left = event.x + 20 + 'px';
            dtips.style.top = (event.y > 225 ? event.y - 70 : event.y + 10) + 'px';
            dtips.innerHTML = obj.title;
            obj.title = '';
            dtips.style.display = '';
            obj.style.border = 'solid 1px #DFD496';
        }
        function ctips(obj) {
            obj.title = dtips.innerHTML;
            dtips.innerHTML = '';
            dtips.style.display = 'none';
            obj.style.border = '0';
        }
      
        function copyWord(words) {
            window.parent.$('baike_input').value = words;
        }
		var odom;
        function selec(obj,id) {
     
			
			$("#petList tr").css("backgroundColor","#FFF");

	        selid = id;
			odom=obj;
            obj.style.backgroundColor = '#DFD496';
        }
        function Display(bbid) {
            bid = bbid;

        }
    
        function Savebb()	
        {
            if (selid == 0) { alert('您需要先选择一个宝宝噢!'); return; }
            if(window.external.jiyang(selid)){
				selid=0;
			
			}else{
				alert("存入仓库失败,主宠是不能存入宠物仓库的噢!");	
			}
        }
    
        function Getbb()
        {
            if (selid == 0) {alert('您需要先选择一个宝宝噢!'); return; }
           	if(window.external.xiedai(selid)){
				selid=0;
				$(odom).remove();
			}else{
				alert("携带失败,背包已满或者宠物已经在您的背包中.");	
			}
        }
        function Setbb()	
        {
            if (xid == 0) { alert('您需要先选择一个宝宝噢!'); return; }
            window.external.getPetInfo(xid);
			window.parent.jid=null;
			window.parent.jname="普通攻击";
			alert("设置主宠成功!");
			selid=0;
        }
        /*function delbb()
        {
            var pwd = $('pwd2').value;
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    var ret = parseInt(t.responseText);
                    if (ret == 1) {
                        alert('密码错误！');
                    } else if (t.responseText != '') { alert(t.responseText); window.location.reload(); }
                },
                asynchronous: true
            };

	        var ajax = new Ajax.Request('../function/mcGate.php?op=d&pwd=' + pwd + '&id=' + bid, opt);
        }
        String.prototype.trim = function () {
            return this.replace(/^[\s\t ]+|[\s\t ]$/g, '');
        };*/

        function discardBb() {
            if (bid == 0) { alert('您需要先选择一个宝宝噢!'); return; }
            else if (!confirm('为了口袋精灵世界的健康发展，丢弃需要花费您10000金币处理费!\n注意，您一旦丢弃宝宝，就再也找不回来了，并且宝宝穿戴的装备\n也会一起消失。当然您在丢弃前也可以先取下来，您确定要丢弃该宝宝吗?')) return;
			
        	var r = window.external.deletePet(selid,1);   
			
			if(r!=true){
				alert("丢弃失败!请确定您丢弃的宠物不是主战宠物或者他存在!");	
			}else{
				
				$(odom).remove();
				alert("丢弃成功!");
			}
        }

        /*function pwd() {
            $('abc').style.display = "";
            $('pwd_input').style.display = "none";
            $('shoplist').style.display = "none";
            $('showbba').style.display = "none";
        }

        function pwd2() {
            $('abc').style.display = "none";
            $('pwd_input').style.display = "none";
            $('shoplist').style.display = "";
            $('showbba').style.display = "";
        }

        function pwd3() {
            $('abc').style.display = "none";
            $('pwd_input').style.display = "";
            $('shoplist').style.display = "none";
            $('showbba').style.display = "none";
        }

        function jiami() {
            var pwd = $('pwd').value;
            var repwd = $('repwd').value;
            if (pwd == "") {
                alert("请先填写密码！");
                return false;
            }
            if (repwd == "") {
                alert("请先填写重复密码！");
                return false;
            }
            if (repwd != pwd) {
                alert("两次密码不一致！");
                return false;
            }
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    var n = parseInt(t.responseText);
                    if (n == 0) {
                        alert("请先填写密码！");
                        return false;
                    }
                    else if (n == 1) {
                        alert("请先填写重复密码！");
                        return false;
                    }
                    else if (n == 2) {
                        alert("两次密码不一致！");
                        return false;
                    }
                    else if (n == 3) {
                        alert("您的牧场已经设置密码,如要修改，请先输入旧密码！");
                        return false;
                    }
                    else if (n == 4) {
                        alert("您输入的密码长度不正确！");
                        return false;
                    }
                    else if (n == 10) {
                        if (!confirm("请牢记您的密码！！如若遗忘，后果自负！您确定要设置密码吗？")) {
                            return false;
                        }
                        dos(pwd);
                    }
                    else {
                        alert("设置密码失败！");
                    }
                },
                on404: function (t) {
                },
                onFailure: function (t) {
                },
                asynchronous: true
            };
	        var ajax = new Ajax.Request('../function/ext_Muchang.php?pwd=' + pwd + '&repwd=' + repwd + '&action=reg', opt);
        }

        function dos(pwd) {
            if (pwd == "") {
                alert("信息有误！");
                return false;
            }
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    var n = parseInt(t.responseText);
                    if (n == 0) {
                        alert("住处有误");
                        return false;
                    }
                    else if (n == 10) {
                        window.location.href = '../function/Muchang_Mod.php';
                        alert("密码设置成功！");
                    }
                },
                on404: function (t) {
                },
                onFailure: function (t) {
                },
                asynchronous: true
            };

	        var ajax = new Ajax.Request('../function/ext_Muchang.php?pwd=' + pwd + '&action=do', opt);
        }

        function login() {
            var pwd = $('login').value;
            if (pwd == "") {
                alert("请先填写密码！");
                return false;
            }
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    var n = parseInt(t.responseText);
                    if (n == 0) {
                        alert("请先填写密码！");
                        return false;
                    }
                    else if (n == 1) {
                        alert("密码错误！");
                    }
                    else if (n == 10) {
                        window.location.href = '../function/Muchang_Mod.php';
                    }
                },
                on404: function (t) {
                },
                onFailure: function (t) {
                },
                asynchronous: true
            };
	        var ajax = new Ajax.Request('../function/ext_Muchang.php?pwd=' + pwd + '&action=login', opt);
        }

        function update() {
            var pwd = $('login').value;
            if (pwd == "") {
                alert("请先填写旧密码！");
                return false;
            }
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    var n = parseInt(t.responseText);
                    if (n == 0) {
                        alert("请先填写旧密码！");
                        return false;
                    }
                    else if (n == 1) {
                        alert("密码错误！");
                    }
                    else if (n == 10) {
                        $('abc').style.display = "";
                        $('shoplist').style.display = "none";
                        $('showbba').style.display = "none";
                    }
                },
                on404: function (t) {
                },
                onFailure: function (t) {
                },
                asynchronous: true
            };
	        var ajax = new Ajax.Request('../function/ext_Muchang.php?pwd=' + pwd + '&action=login', opt);
        }

        function resetPwd() {
            var oldPwd = $('old_pwd').value;
            var pwd = $('pwd').value;
            var repwd = $('repwd').value;
            if (oldPwd == "") {
                alert("请先填写原密码！");
                $('old_pwd').focus();
                return false;
            }
            if (pwd == "") {
                alert("请填写新密码！");
                $('pwd').focus();
                return false;
            }
            if (repwd == "") {
                alert("请重新输入新密码！");
                $('repwd').focus();
                return false;
            }
            if (repwd != pwd) {
                alert("两次输入的新密码不一致！");
                $('repwd').focus();
                return false;
            }
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    var n = parseInt(t.responseText);
                    if (n == 0) {
                        alert("请先填写密码！");
                        return false;
                    }
                    else if (n == 1) {
                        alert("原密码错误！");
                    }
                    else if (n == 10) {
                        alert("密码设置成功！");
                        window.location.href = '../function/Muchang_Mod.php';
                    }
                },
                on404: function (t) {
                },
                onFailure: function (t) {
                },
                asynchronous: true
            };
	        var ajax = new Ajax.Request('../function/ext_Muchang.php?pwd=' + oldPwd + '&action=reset&repwd=' + repwd, opt);
        }


        function times() {
            var pets = $('bb2').value;
            if (pets <= 0) {
                return;
            }
            if (!confirm('使用水晶币加速将大大提高宠物托管效率，\r\n每节省1小时消耗100水晶币！是否要立即完成托管？')) {
                return;
            }
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    var str = t.responseText;
                    if (str == 1) {
                        alert('数据错误！');
                        return;
                    } else if (str == 2) {
                        alert('该宠物正在等待，不能加速！');
                        return false;
                    } else if (str == 3) {
                        alert('该宠物该宠物托管已经完成，不用加速了！');
                        return;
                    } else {
                        if (!confirm(str)) {
                            return;
                        }
                        var opt = {
                            method: 'get',
                            onSuccess: function (t) {
                                var str = t.responseText;
                                if (str == 1) {
                                    alert('您的水晶不足！');
                                }
                                else {
                                    alert('加速成功！');
                                    if (!confirm(str)) {
                                        return;
                                    }
                                    off(pets);
                                }
                            },
                            on404: function (t) {
                            },
                            onFailure: function (t) {
                            },
                            asynchronous: true
                        };
	                    var ajax = new Ajax.Request('../function/tuoGuanGate.php?id=' + pets + '&action=timesdo', opt);
                    }
                },
                on404: function (t) {
                },
                onFailure: function (t) {
                },
                asynchronous: true
            };
	        var ajax = new Ajax.Request('../function/tuoGuanGate.php?id=' + pets + '&action=times', opt);
        }
        function gettginfo(bid) {
            if (bid == '') {
                return;
            }
            $('tgflags').innerHTML = '数据加载中，请稍候……';
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    var n = t.responseText;
                    if (n == 1) {
                        return;
                    }
                    else {
                        $('tgflags').innerHTML = n;
                    }
                },
                on404: function (t) {
                },
                onFailure: function (t) {
                },
                asynchronous: true
            };
	        var ajax = new Ajax.Request('../function/tuoGuanGate.php?id=' + bid + '&action=getinfo', opt);
        }
        function tg() {
            var pets = $('tgbb').value;
            var time = $('tgtimes').value;
            var mes = $('tgmes').value;
            if (pets == '' || time == '' || mes == '') {
                alert('请先选择宠物及托管信息！');
                return;
            }
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    var n = t.responseText;
                    if (n == 0) {
                        alert("只有22：00--10：00 才可以托管！");
                    }
                    else if (n == 1) {
                        alert("请选择要托管的宠物！");
                    }
                    else if (n == 2) {
                        alert("托管失败，您的托管时间不足！您可以购买“托管卷”来增加时间。");
                    }
                    else if (n == 3) {
                        alert("您的该宠物正在托管中！");
                    }
                    else if (n == 4) {
                        alert("当前宠物托管已完成，请先取回再托管!");
                    }
                    else if (n == 5) {
                        alert("托管个数已达上限，不能再托管了!");
                    }
                    else if (n == 6) {
                        alert("您的托管个数已经达到上限，请通过购买使用托管所扩充卷扩充您的托管所！");
                    }
                    else if (n == 7) {
                        alert("超出托管结束时间,请重新选择时间！");
                    }
                    else if (n == 8) {
                        alert("该宠物正在等待中！");
                    }
                    else if (n == 10) {
                        alert("托管成功!");
                        window.parent.$('gw').src = './function/Muchang_Mod.php?style=2';
                    } else {
                        alert(n);
                    }
                },
                on404: function (t) {
                },
                onFailure: function (t) {
                },
                asynchronous: true
            };
	        var ajax = new Ajax.Request('../function/tuoGuanGate.php?pets=' + pets + '&time=' + time + '&mes=' + mes + '&action=tuoguan', opt);
        }

        function getflag(obj) {
            var petsid = obj.value;
            var id = obj.id;
            var num = id.split("s");
            var id1 = id.replace(id, "flag" + num[1]);
            var org = $(id1);
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    var n = parseInt(t.responseText);
                    if (n == 0) {
                        org.innerHTML = "未托管";
                    }
                    else if (n == 1) {
                        org.innerHTML = "托管中";
                    }
                    else if (n == 2) {
                        org.innerHTML = "托管完成";
                    }
                    else if (n == 3) {
                        org.innerHTML = "等待中";
                    }
                },
                on404: function (t) {
                },
                onFailure: function (t) {
                },
                asynchronous: true
            };
	        var ajax = new Ajax.Request('../function/tuoGuanGate.php?id=' + petsid + '&action=change', opt);
        }

        function offpets() {

            var pets = $('bb2').value;
            if (pets <= 0) {
                return;
            }
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    var n = parseInt(t.responseText);
                    if (n == 0) {
                        alert("请选择您要取回的宠物！");
                    }
                    else if (n == 1) {
                        alert("您还没有进行任何托管操作，不用取回宠物。");
                    }
                    else if (n == 2) {
                        alert("托管已完成，您可以取回您的宠物了！");
                        off(pets);
                    }
                    else if (n == 3) {
                        if (!confirm("提前取回宠物，您之前消耗托管时间将失效，确认取回吗？")) {
                            return false;
                        }
                        off(pets);
                    }
                    else if (n == 4) {
                        if (!confirm("还没有开始托管，如果取回但是时间不会退还哦~！")) {
                            return false;
                        }
                        off(pets);
                    }
                },
                on404: function (t) {
                },
                onFailure: function (t) {
                },
                asynchronous: true
            };
	        var ajax = new Ajax.Request('../function/tuoGuanGate.php?id=' + pets + '&action=offpets', opt);
        }
        function off(id) {
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    var n = parseInt(t.responseText);
                    if (n == 10) {
                        alert("取回宠物成功！");
                        window.location.reload();
                    }
                    else if (n == 11) {
                        alert("取回宠物失败！");
                        window.location.reload();
                    }
                    else if (n == 0) {
                        alert("信息出错！");
                    }
                    else if (n == 12) {
                        alert("包裹空间不够，请先清理包裹！");
                    }
                    else if (n == 13) {
                        alert("牧场格子已经占满！");
                    }
                    else {
                        alert("信息出错！");
                    }
                },
                on404: function (t) {
                },
                onFailure: function (t) {
                },
                asynchronous: true
            };
	        var ajax = new Ajax.Request('../function/tuoGuanGate.php?id=' + id + '&action=offpet', opt);
        }

        function detail() {
            var pets = $('bb2').value;
            if (pets <= 0) {
                return;
            }
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    var str = t.responseText;
                    alert(str);
                },
                on404: function (t) {
                },
                onFailure: function (t) {
                },
                asynchronous: true
            };
	        var ajax = new Ajax.Request('../function/tuoGuanGate.php?id=' + pets + '&action=show', opt);
        }


        function auto() {
            var pets = $('tgbb').value;
            var time = $('tgtimes').value;
            var mes = $('tgmes').value;
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    var n = t.responseText;
                    if (n == 1) {
                        alert("请选择要托管的宠物！");
                    }
                    else if (n == 2) {
                        alert("托管失败，您的托管时间不足！您可以购买“托管卷”来增加时间。");
                    }
                    else if (n == 3) {
                        alert("您的该宠物正在托管中！");
                    }
                    else if (n == 4) {
                        alert("当前宠物托管已完成，请先取回再托管!");
                    }
                    else if (n == 5) {
                        alert("托管个数已达上限，不能再托管了!");
                    }
                    else if (n == 6) {
                        alert("您的托管个数已经达到上限，请通过购买使用托管所扩充卷扩充您的托管所！");
                    }
                    else if (n == 7) {
                        alert("超出托管结束时间,请重新选择时间！");
                    }
                    else if (n == 10) {
                        alert("自动托管成功!");
                        window.location.reload();
                    }
                    else if (n == 8) {
                        alert("该宠物正在等待中！");
                    }
                    else {
                        alert("自动托管失败!");
                        window.location.reload();
                    }
                },
                on404: function (t) {
                },
                onFailure: function (t) {
                },
                asynchronous: true
            };

	        var ajax = new Ajax.Request('../function/tuoGuanGate.php?pets=' + pets + '&time=' + time + '&mes=' + mes + '&action=auto', opt);
        }*/

        var bbT;

   
        function mcbbshow(id,i) {
			
		//	alert(getPetInfo(id));
           updateMainPet(JSONS[i]);
		   
			$("#mcbbshow").show();
			$("#mcbbshow").width(291);
        }
        function getPetInfo(id)
        {
			var j = JSONS;
            for (var i = 0; i < j.length; i++)
            {
                if (j[i].宠物序号 == id)
                {
					return j[i];	
				}	
            }
	        return -1;
        }

        function mcbbdisplay() {
            $("#mcbbshow").css("display","NONE");
        }
		var xz_=-1;
        function zhixiang(obj, id,i) {
			bid=id;
         	$(".箭头 td").removeClass("ch01");
			$(".箭头 td:eq("+i+")").addClass("ch01");
			xz_=i;
			if (selid != 0) odom.style.backgroundColor = '#fff';
            selid = id;
			xid=id;
          
        }

    </script><div id="dtips" style="position: absolute; display: none; padding: 5px; line-height: 1.6; border: 1px solid rgb(255, 255, 255); color: gray; z-index: 1; background-color: rgb(223, 218, 188);"></div>
</body>
</html>