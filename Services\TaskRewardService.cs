using SqlSugar;
using Newtonsoft.Json;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;
using WebApplication_HM.DTOs;
using WebApplication_HM.Models.Define;
using TaskStatus = WebApplication_HM.Models.Define.TaskStatus;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 任务奖励服务实现
    /// </summary>
    public class TaskRewardService : ITaskRewardService
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<TaskRewardService> _logger;
        private readonly ILevelService _levelService;

        public TaskRewardService(ISqlSugarClient db, ILogger<TaskRewardService> logger, ILevelService levelService)
        {
            _db = db;
            _logger = logger;
            _levelService = levelService;
        }

        #region 奖励处理

        /// <summary>
        /// 解析奖励配置
        /// </summary>
        public async Task<List<TaskRewardDto>> ParseRewardConfigAsync(string rewardConfig)
        {
            try
            {
                if (string.IsNullOrEmpty(rewardConfig))
                {
                    return new List<TaskRewardDto>();
                }

                var rewards = new List<TaskRewardDto>();

                // 支持三种格式：新的复合JSON格式、数组JSON格式和原有的字符串格式
                if (rewardConfig.StartsWith("{"))
                {
                    // 新的复合JSON格式: {"exp":1000,"gold":2000,"items":[...]}
                    var rewardObject = JsonConvert.DeserializeObject<TaskRewardObjectDto>(rewardConfig);
                    if (rewardObject != null)
                    {
                        // 处理经验奖励
                        if (rewardObject.Exp.HasValue && rewardObject.Exp.Value > 0)
                        {
                            var expReward = await ConvertToRewardDto("EXPERIENCE", "exp", rewardObject.Exp.Value);
                            if (expReward != null) rewards.Add(expReward);
                        }

                        // 处理金币奖励
                        if (rewardObject.Gold.HasValue && rewardObject.Gold.Value > 0)
                        {
                            var goldReward = await ConvertToRewardDto("CURRENCY", "gold", rewardObject.Gold.Value);
                            if (goldReward != null) rewards.Add(goldReward);
                        }

                        // 处理元宝奖励
                        if (rewardObject.Yuanbao.HasValue && rewardObject.Yuanbao.Value > 0)
                        {
                            var yuanbaoReward = await ConvertToRewardDto("CURRENCY", "yuanbao", rewardObject.Yuanbao.Value);
                            if (yuanbaoReward != null) rewards.Add(yuanbaoReward);
                        }

                        // 处理水晶奖励
                        if (rewardObject.Crystal.HasValue && rewardObject.Crystal.Value > 0)
                        {
                            var crystalReward = await ConvertToRewardDto("CURRENCY", "crystal", rewardObject.Crystal.Value);
                            if (crystalReward != null) rewards.Add(crystalReward);
                        }

                        // 处理道具奖励
                        if (rewardObject.Items != null && rewardObject.Items.Any())
                        {
                            foreach (var item in rewardObject.Items)
                            {
                                var itemReward = await ConvertToRewardDto("ITEM", item.Id, item.Amount);
                                if (itemReward != null) rewards.Add(itemReward);
                            }
                        }
                    }
                }
               

                return rewards;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析奖励配置失败: {RewardConfig}", rewardConfig);
                return new List<TaskRewardDto>();
            }
        }

        /// <summary>
        /// 发放任务奖励
        /// </summary>
        public async Task<bool> GrantTaskRewardsAsync(int userId, string taskId, List<TaskRewardDto> rewards)
        {
            try
            {
                if (!rewards.Any())
                {
                    return true;
                }

                // 开始事务
                _db.Ado.BeginTran();

                try
                {
                    foreach (var reward in rewards)
                    {
                        var granted = await GrantSingleRewardAsync(userId, reward);
                        if (!granted)
                        {
                            _db.Ado.RollbackTran();
                            return false;
                        }
                    }

                    // 记录奖励日志
                    await LogTaskRewardsAsync(userId, taskId, rewards);

                    _db.Ado.CommitTran();
                    return true;
                }
                catch
                {
                    _db.Ado.RollbackTran();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发放任务奖励失败，用户ID: {UserId}, 任务ID: {TaskId}", userId, taskId);
                return false;
            }
        }

        /// <summary>
        /// 记录奖励发放日志
        /// </summary>
        public async Task<bool> LogTaskRewardsAsync(int userId, string taskId, List<TaskRewardDto> rewards)
        {
            try
            {
                var logs = rewards.Select(reward => new task_reward_log
                {
                    user_id = userId,
                    task_id = taskId,
                    reward_type = reward.RewardType,
                    reward_id = reward.RewardId,
                    reward_amount = reward.RewardAmount,
                    reward_description = reward.RewardDescription,
                    granted_at = DateTime.Now
                }).ToList();

                if (logs.Any())
                {
                    var result = await _db.Insertable(logs).ExecuteCommandAsync();
                    return result > 0;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录奖励日志失败，用户ID: {UserId}, 任务ID: {TaskId}", userId, taskId);
                return false;
            }
        }

        /// <summary>
        /// 获取用户奖励历史
        /// </summary>
        public async Task<List<task_reward_log>> GetUserRewardHistoryAsync(int userId, int pageIndex = 1, int pageSize = 20)
        {
            try
            {
                return await _db.Queryable<task_reward_log>()
                    .Where(trl => trl.user_id == userId)
                    .OrderBy(trl => trl.granted_at, OrderByType.Desc)
                    .ToPageListAsync(pageIndex, pageSize);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户奖励历史失败，用户ID: {UserId}", userId);
                return new List<task_reward_log>();
            }
        }

        #endregion

        #region 奖励验证

        /// <summary>
        /// 验证奖励配置
        /// </summary>
        public async Task<(bool IsValid, string Message)> ValidateRewardConfigAsync(string rewardConfig)
        {
            try
            {
                if (string.IsNullOrEmpty(rewardConfig))
                {
                    return (true, "奖励配置为空");
                }

                var rewards = await ParseRewardConfigAsync(rewardConfig);
                if (!rewards.Any())
                {
                    return (false, "奖励配置解析失败");
                }

                // 验证每个奖励项
                foreach (var reward in rewards)
                {
                    if (string.IsNullOrEmpty(reward.RewardType))
                    {
                        return (false, "奖励类型不能为空");
                    }

                    if (reward.RewardAmount <= 0)
                    {
                        return (false, "奖励数量必须大于0");
                    }

                    // 根据奖励类型验证ID
                    var validId = await ValidateRewardIdAsync(reward.RewardType, reward.RewardId);
                    if (!validId.IsValid)
                    {
                        return (false, validId.Message);
                    }
                }

                return (true, "奖励配置有效");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证奖励配置失败: {RewardConfig}", rewardConfig);
                return (false, "验证失败");
            }
        }

        /// <summary>
        /// 检查用户是否可以获得奖励
        /// </summary>
        public async Task<(bool CanReceive, string Message)> CheckRewardEligibilityAsync(int userId, List<TaskRewardDto> rewards)
        {
            try
            {
                // 检查用户是否存在
                var user = await _db.Queryable<user>().Where(u => u.id == userId).FirstAsync();
                if (user == null)
                {
                    return (false, "用户不存在");
                }

                // 检查背包空间(如果有道具奖励)
                var itemRewards = rewards.Where(r => r.RewardType == RewardTypes.ITEM).ToList();
                if (itemRewards.Any())
                {
                    var availableSlots = await GetAvailableBackpackSlotsAsync(userId);
                    if (availableSlots < itemRewards.Count())
                    {
                        return (false, "背包空间不足");
                    }
                }

                return (true, "可以获得奖励");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查奖励资格失败，用户ID: {UserId}", userId);
                return (false, "检查失败");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 转换为奖励DTO
        /// </summary>
        private async Task<TaskRewardDto> ConvertToRewardDto(string type, string id, int amount)
        {
            try
            {
                var reward = new TaskRewardDto
                {
                    RewardType = MapRewardType(type),
                    RewardId = id,
                    RewardAmount = amount
                };

                // 根据类型获取名称和描述
                switch (reward.RewardType)
                {
                    case RewardTypes.ITEM:
                        var item = await _db.Queryable<item_config>().Where(ic => ic.item_no.ToString() == id).FirstAsync();
                        reward.RewardName = item?.name ?? "未知道具";
                        reward.RewardDescription = $"{reward.RewardName} x{amount}";
                        break;

                    case RewardTypes.CURRENCY:
                        reward.RewardName = GetCurrencyName(id);
                        reward.RewardDescription = $"{reward.RewardName} x{amount}";
                        break;

                    case RewardTypes.EQUIPMENT:
                        var equipment = await _db.Queryable<equipment_type>().Where(et => et.equip_type_id.ToString() == id).FirstAsync();
                        reward.RewardName = equipment?.type_name ?? "未知装备";
                        reward.RewardDescription = $"{reward.RewardName} x{amount}";
                        break;

                    case RewardTypes.EXPERIENCE:
                        reward.RewardName = "经验";
                        reward.RewardDescription = $"经验 x{amount}";
                        break;

                    default:
                        reward.RewardName = "未知奖励";
                        reward.RewardDescription = $"未知奖励 x{amount}";
                        break;
                }

                return reward;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "转换奖励DTO失败，类型: {Type}, ID: {Id}", type, id);
                return null;
            }
        }

        /// <summary>
        /// 发放单个奖励
        /// </summary>
        private async Task<bool> GrantSingleRewardAsync(int userId, TaskRewardDto reward)
        {
            try
            {
                switch (reward.RewardType)
                {
                    case RewardTypes.ITEM:
                        return await GrantItemRewardAsync(userId, reward.RewardId, reward.RewardAmount);

                    case RewardTypes.CURRENCY:
                        return await GrantCurrencyRewardAsync(userId, reward.RewardId, reward.RewardAmount);

                    case RewardTypes.EQUIPMENT:
                        return await GrantEquipmentRewardAsync(userId, reward.RewardId, reward.RewardAmount);

                    case RewardTypes.EXPERIENCE:
                        return await GrantExperienceRewardAsync(userId, reward.RewardAmount);

                    default:
                        _logger.LogWarning("未知奖励类型: {RewardType}", reward.RewardType);
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发放单个奖励失败，用户ID: {UserId}, 奖励类型: {RewardType}", userId, reward.RewardType);
                return false;
            }
        }

        /// <summary>
        /// 发放道具奖励
        /// </summary>
        private async Task<bool> GrantItemRewardAsync(int userId, string itemId, int amount)
        {
            try
            {
                // 检查道具配置是否存在
                var itemExists = await _db.Queryable<item_config>()
                    .Where(ic => ic.item_no.ToString() == itemId)
                    .AnyAsync();

                if (!itemExists)
                {
                    _logger.LogWarning("道具不存在: {ItemId}", itemId);
                    return false;
                }

                // 检查用户是否已经拥有该道具（在背包中）
                var existingUserItem = await _db.Queryable<user_item>()
                    .Where(ui => ui.user_id == userId &&
                               ui.item_id == itemId &&
                               ui.item_pos == 1) // 1表示背包
                    .FirstAsync();

                int result;
                if (existingUserItem != null)
                {
                    // 用户已有该道具，更新数量
                    existingUserItem.item_count += amount;
                    result = await _db.Updateable(existingUserItem).ExecuteCommandAsync();

                    _logger.LogInformation("更新用户道具数量，用户ID: {UserId}, 道具ID: {ItemId}, 增加数量: {Amount}, 新总数: {NewCount}",
                        userId, itemId, amount, existingUserItem.item_count);
                }
                else
                {
                    // 用户没有该道具，新增记录
                    var userItem = new user_item
                    {
                        user_id = userId,
                        item_id = itemId,
                        item_count = amount,
                        item_pos = 1, // 背包
                        item_seq = await GetNextItemSeqAsync(userId), // 获取下一个序号
                        create_time = DateTime.Now,
                        settlement_num = 0 // 初始化结算数量
                    };

                    result = await _db.Insertable(userItem).ExecuteCommandAsync();

                    _logger.LogInformation("新增用户道具，用户ID: {UserId}, 道具ID: {ItemId}, 数量: {Amount}",
                        userId, itemId, amount);
                }

                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发放道具奖励失败，用户ID: {UserId}, 道具ID: {ItemId}, 数量: {Amount}", userId, itemId, amount);
                return false;
            }
        }

        /// <summary>
        /// 发放货币奖励
        /// </summary>
        private async Task<bool> GrantCurrencyRewardAsync(int userId, string currencyType, int amount)
        {
            try
            {
                var user = await _db.Queryable<user>().Where(u => u.id == userId).FirstAsync();
                if (user == null) return false;

                switch (currencyType.ToLower())
                {
                    case "金币":
                    case "gold":
                        user.gold = (user.gold ?? 0) + amount;
                        break;
                    case "元宝":
                    case "diamond":
                        user.yuanbao = (user.yuanbao ?? 0) + amount;
                        break;
                    case "水晶":
                    case "crystal":
                        user.crystal = (user.crystal ?? 0) + amount;
                        break;
                    default:
                        _logger.LogWarning("未知货币类型: {CurrencyType}", currencyType);
                        return false;
                }

                var result = await _db.Updateable(user).ExecuteCommandAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发放货币奖励失败，用户ID: {UserId}, 货币类型: {CurrencyType}", userId, currencyType);
                return false;
            }
        }

        /// <summary>
        /// 发放装备奖励
        /// </summary>
        private async Task<bool> GrantEquipmentRewardAsync(int userId, string equipmentTypeId, int amount)
        {
            try
            {
                // 这里需要根据实际的装备系统实现
                // 暂时返回true，实际实现时需要调用装备服务
                _logger.LogInformation("发放装备奖励，用户ID: {UserId}, 装备类型: {EquipmentTypeId}, 数量: {Amount}", 
                    userId, equipmentTypeId, amount);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发放装备奖励失败，用户ID: {UserId}, 装备类型: {EquipmentTypeId}", userId, equipmentTypeId);
                return false;
            }
        }

        /// <summary>
        /// 发放经验奖励
        /// </summary>
        private async Task<bool> GrantExperienceRewardAsync(int userId, int amount)
        {
            try
            {
                // 获取用户的主战宠物
                var mainPet = await _db.Queryable<user_pet>()
                    .Where(up => up.user_id == userId && up.is_main == true)
                    .FirstAsync();

                if (mainPet == null)
                {
                    _logger.LogWarning("用户没有主战宠物，无法发放经验奖励，用户ID: {UserId}", userId);
                    return false;
                }

                // 记录升级前的等级
                var oldLevel = await _levelService.CalculateLevelAsync(mainPet.exp ?? 0, "pet");
                var newExp = (mainPet.exp ?? 0) + amount;
                var newLevel = await _levelService.CalculateLevelAsync(newExp, "pet");

                // 更新宠物经验
                await _db.Updateable<user_pet>()
                    .SetColumns(up => new user_pet { exp = newExp })
                    .Where(up => up.id == mainPet.id)
                    .ExecuteCommandAsync();

                // 记录等级变更日志（如果升级了）
                if (newLevel > oldLevel)
                {
                    var levelChangeLog = new LevelChangeLog
                    {
                        user_id = userId,
                        pet_id = mainPet.id,
                        old_level = oldLevel,
                        new_level = newLevel,
                        old_exp = mainPet.exp ?? 0,
                        new_exp = newExp,
                        change_reason = "任务奖励",
                        change_time = DateTime.Now
                    };

                    await _db.Insertable(levelChangeLog).ExecuteCommandAsync();

                    _logger.LogInformation("宠物升级！用户ID: {UserId}, 宠物ID: {PetId}, {OldLevel}级→{NewLevel}级",
                        userId, mainPet.id, oldLevel, newLevel);
                }

                _logger.LogInformation("发放经验奖励成功，用户ID: {UserId}, 经验: {Amount}, 等级: {OldLevel}→{NewLevel}",
                    userId, amount, oldLevel, newLevel);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发放经验奖励失败，用户ID: {UserId}, 经验: {Amount}", userId, amount);
                return false;
            }
        }

        /// <summary>
        /// 映射奖励类型
        /// </summary>
        private string MapRewardType(string originalType)
        {
            return originalType switch
            {
                "道具" or "ITEM" => RewardTypes.ITEM,
                "装备" or "EQUIPMENT" => RewardTypes.EQUIPMENT,
                "金币" or "CURRENCY" => RewardTypes.CURRENCY,
                "元宝" => RewardTypes.CURRENCY,
                "水晶" => RewardTypes.CURRENCY,
                "经验" or "EXPERIENCE" => RewardTypes.EXPERIENCE,
                _ => originalType
            };
        }

        /// <summary>
        /// 获取货币名称
        /// </summary>
        private string GetCurrencyName(string currencyId)
        {
            return currencyId switch
            {
                "金币" or "gold" => "金币",
                "元宝" or "diamond" or "yuanbao" => "元宝",
                "水晶" or "crystal" => "水晶",
                _ => currencyId
            };
        }

        /// <summary>
        /// 验证奖励ID
        /// </summary>
        private async Task<(bool IsValid, string Message)> ValidateRewardIdAsync(string rewardType, string rewardId)
        {
            try
            {
                switch (rewardType)
                {
                    case RewardTypes.ITEM:
                        var itemExists = await _db.Queryable<item_config>()
                            .Where(ic => ic.item_no.ToString() == rewardId)
                            .AnyAsync();
                        return itemExists ? (true, "道具ID有效") : (false, $"道具ID不存在: {rewardId}");

                    case RewardTypes.EQUIPMENT:
                        var equipmentExists = await _db.Queryable<equipment_type>()
                            .Where(et => et.equip_type_id.ToString() == rewardId)
                            .AnyAsync();
                        return equipmentExists ? (true, "装备ID有效") : (false, $"装备ID不存在: {rewardId}");

                    case RewardTypes.CURRENCY:
                        var validCurrencies = new[] { "金币", "元宝", "水晶", "gold", "diamond", "crystal" };
                        var isValidCurrency = validCurrencies.Contains(rewardId.ToLower());
                        return isValidCurrency ? (true, "货币类型有效") : (false, $"无效的货币类型: {rewardId}");

                    default:
                        return (true, "奖励ID有效");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证奖励ID失败，类型: {RewardType}, ID: {RewardId}", rewardType, rewardId);
                return (false, "验证失败");
            }
        }

        /// <summary>
        /// 获取可用背包槽位数
        /// </summary>
        private async Task<int> GetAvailableBackpackSlotsAsync(int userId)
        {
            try
            {
                // 这里需要根据实际的背包系统实现
                // 暂时返回一个固定值
                var usedSlots = await _db.Queryable<user_item>()
                    .Where(ui => ui.user_id == userId && ui.item_pos == 1)
                    .CountAsync();

                const int maxSlots = 100; // 假设最大100个槽位
                return Math.Max(0, maxSlots - usedSlots);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可用背包槽位失败，用户ID: {UserId}", userId);
                return 0;
            }
        }

        /// <summary>
        /// 获取用户下一个道具序号
        /// </summary>
        private async Task<int> GetNextItemSeqAsync(int userId)
        {
            try
            {
                // 获取用户当前最大的道具序号
                var maxSeq = await _db.Queryable<user_item>()
                    .Where(ui => ui.user_id == userId)
                    .MaxAsync(ui => ui.item_seq);

                return maxSeq + 1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户下一个道具序号失败，用户ID: {UserId}", userId);
                return 1; // 如果出错，返回默认序号1
            }
        }

        #endregion
    }
}
