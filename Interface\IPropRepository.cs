using WebApplication_HM.Models;

namespace WebApplication_HM.Interface
{
    /// <summary>
    /// 道具数据访问接口
    /// </summary>
    public interface IPropRepository
    {
        #region 用户道具管理
        
        /// <summary>
        /// 获取用户所有道具
        /// </summary>
        Task<List<PropInfo>> GetUserItemsAsync(int userId);
        
        /// <summary>
        /// 按道具ID获取用户道具
        /// </summary>
        Task<PropInfo> GetUserItemByIdAsync(int userId, string itemId);
        
        /// <summary>
        /// 按序号获取道具
        /// </summary>
        Task<PropInfo> GetItemBySeqAsync(int itemSeq);
        
        /// <summary>
        /// 获取用户指定位置的道具
        /// </summary>
        Task<List<PropInfo>> GetUserItemsByPositionAsync(int userId, int position);
        
        /// <summary>
        /// 添加道具到用户背包
        /// </summary>
        Task<bool> AddUserItemAsync(int userId, string itemId, long count, int? position = null);
        
        /// <summary>
        /// 修改或删除道具
        /// </summary>
        Task<bool> UpdateOrDeleteItemAsync(int itemSeq, long newCount);
        
        /// <summary>
        /// 删除道具
        /// </summary>
        Task<bool> DeleteItemAsync(int itemSeq);
        
        /// <summary>
        /// 更新道具数量
        /// </summary>
        Task<bool> UpdateItemCountAsync(int itemSeq, long newCount);
        
        /// <summary>
        /// 更新道具位置
        /// </summary>
        Task<bool> UpdateItemPositionAsync(int itemSeq, int newPosition);
        
        #endregion
        
        #region 道具配置管理
        
        /// <summary>
        /// 获取道具配置信息
        /// </summary>
        Task<PropConfig> GetItemConfigAsync(string itemId);
        
        /// <summary>
        /// 获取道具配置信息（按编号）
        /// </summary>
        Task<PropConfig> GetItemConfigByNoAsync(int itemNo);
        
        /// <summary>
        /// 获取所有道具配置
        /// </summary>
        Task<List<PropConfig>> GetAllItemConfigsAsync();
        
        #endregion
        
        #region 道具脚本管理
        
        /// <summary>
        /// 获取道具脚本信息
        /// </summary>
        Task<PropScriptInfo> GetItemScriptAsync(string itemId);
        
        /// <summary>
        /// 获取道具脚本信息（按编号）
        /// </summary>
        Task<PropScriptInfo> GetItemScriptByNoAsync(int itemNo);
        
        #endregion
        
        #region 辅助方法
        
        /// <summary>
        /// 检查用户是否拥有指定道具
        /// </summary>
        Task<bool> HasItemAsync(int userId, string itemId);
        
        /// <summary>
        /// 获取用户道具总数
        /// </summary>
        Task<int> GetUserItemCountAsync(int userId);
        
        /// <summary>
        /// 生成新的道具序号
        /// </summary>
        Task<int> GenerateNewItemSeqAsync(int userId);
        
        #endregion
    }
}
