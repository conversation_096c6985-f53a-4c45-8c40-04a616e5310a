using WebApplication_HM.Models;
using WebApplication_HM.DTOs;

namespace WebApplication_HM.Interface
{
    /// <summary>
    /// 等级服务接口
    /// </summary>
    public interface ILevelService
    {
        /// <summary>
        /// 根据经验计算等级
        /// </summary>
        /// <param name="exp">当前经验值</param>
        /// <param name="systemName">系统名称，默认为宠物系统</param>
        /// <returns>等级</returns>
        Task<int> CalculateLevelAsync(long exp, string systemName = "pet");
        
        /// <summary>
        /// 获取指定等级所需累积经验
        /// </summary>
        /// <param name="level">等级</param>
        /// <param name="systemName">系统名称，默认为宠物系统</param>
        /// <returns>累积经验</returns>
        Task<long> GetRequiredExpAsync(int level, string systemName = "pet");
        
        /// <summary>
        /// 获取升级到下一级所需经验
        /// </summary>
        /// <param name="currentLevel">当前等级</param>
        /// <param name="systemName">系统名称，默认为宠物系统</param>
        /// <returns>升级经验</returns>
        Task<long> GetUpgradeExpAsync(int currentLevel, string systemName = "pet");
        
        /// <summary>
        /// 获取等级范围内的经验配置
        /// </summary>
        /// <param name="startLevel">起始等级</param>
        /// <param name="endLevel">结束等级</param>
        /// <param name="systemName">系统名称，默认为宠物系统</param>
        /// <returns>等级配置列表</returns>
        Task<List<LevelConfig>> GetLevelConfigsAsync(int startLevel = 1, int endLevel = 200, string systemName = "pet");
        
        /// <summary>
        /// 验证经验值是否有效
        /// </summary>
        /// <param name="exp">经验值</param>
        /// <param name="systemName">系统名称，默认为宠物系统</param>
        /// <returns>是否有效</returns>
        Task<bool> ValidateExpAsync(long exp, string systemName = "pet");
        
        /// <summary>
        /// 获取经验系统配置
        /// </summary>
        /// <param name="systemName">系统名称</param>
        /// <returns>系统配置</returns>
        Task<ExpSystemConfig?> GetSystemConfigAsync(string systemName);
        
        /// <summary>
        /// 计算等级详细信息
        /// </summary>
        /// <param name="exp">当前经验值</param>
        /// <param name="systemName">系统名称，默认为宠物系统</param>
        /// <returns>等级计算结果</returns>
        Task<LevelCalculationResultDTO> CalculateLevelDetailAsync(long exp, string systemName = "pet");
        
        /// <summary>
        /// 批量计算等级
        /// </summary>
        /// <param name="expList">经验值列表</param>
        /// <param name="systemName">系统名称，默认为宠物系统</param>
        /// <returns>等级列表</returns>
        Task<List<int>> BatchCalculateLevelAsync(List<long> expList, string systemName = "pet");
        
        /// <summary>
        /// 获取等级进度信息
        /// </summary>
        /// <param name="exp">当前经验值</param>
        /// <param name="systemName">系统名称，默认为宠物系统</param>
        /// <returns>等级进度信息</returns>
        Task<LevelProgressDTO> GetLevelProgressAsync(long exp, string systemName = "pet");
        
        /// <summary>
        /// 检查是否可以升级
        /// </summary>
        /// <param name="currentExp">当前经验</param>
        /// <param name="addExp">增加的经验</param>
        /// <param name="systemName">系统名称，默认为宠物系统</param>
        /// <returns>升级信息</returns>
        Task<(bool CanLevelUp, int NewLevel, int LevelDiff)> CheckLevelUpAsync(long currentExp, long addExp, string systemName = "pet");
    }

    /// <summary>
    /// 等级仓储接口
    /// </summary>
    public interface ILevelRepository
    {
        /// <summary>
        /// 获取所有等级配置
        /// </summary>
        /// <param name="systemName">系统名称，默认为宠物系统</param>
        /// <returns>等级配置列表</returns>
        Task<List<LevelConfig>> GetAllLevelConfigsAsync(string systemName = "pet");
        
        /// <summary>
        /// 获取指定等级的配置
        /// </summary>
        /// <param name="level">等级</param>
        /// <param name="systemName">系统名称，默认为宠物系统</param>
        /// <returns>等级配置</returns>
        Task<LevelConfig?> GetLevelConfigAsync(int level, string systemName = "pet");
        
        /// <summary>
        /// 根据经验计算等级
        /// </summary>
        /// <param name="exp">经验值</param>
        /// <param name="systemName">系统名称，默认为宠物系统</param>
        /// <returns>等级</returns>
        Task<int> CalculateLevelAsync(long exp, string systemName = "pet");
        
        /// <summary>
        /// 获取经验系统配置
        /// </summary>
        /// <param name="systemName">系统名称</param>
        /// <returns>系统配置</returns>
        Task<ExpSystemConfig?> GetSystemConfigAsync(string systemName);
        
        /// <summary>
        /// 获取等级范围内的配置
        /// </summary>
        /// <param name="startLevel">起始等级</param>
        /// <param name="endLevel">结束等级</param>
        /// <param name="systemName">系统名称，默认为宠物系统</param>
        /// <returns>等级配置列表</returns>
        Task<List<LevelConfig>> GetLevelRangeAsync(int startLevel, int endLevel, string systemName = "pet");
        
        /// <summary>
        /// 添加等级变更日志
        /// </summary>
        /// <param name="log">变更日志</param>
        /// <returns>是否成功</returns>
        Task<bool> AddLevelChangeLogAsync(LevelChangeLog log);
        
        /// <summary>
        /// 获取用户等级变更历史
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="petId">宠物ID，可选</param>
        /// <param name="limit">限制数量</param>
        /// <returns>变更日志列表</returns>
        Task<List<LevelChangeLog>> GetLevelChangeHistoryAsync(int userId, int? petId = null, int limit = 50);
    }

    /// <summary>
    /// 等级缓存服务接口
    /// </summary>
    public interface ILevelCacheService
    {
        /// <summary>
        /// 获取或设置等级配置缓存
        /// </summary>
        /// <param name="systemName">系统名称</param>
        /// <param name="factory">数据工厂方法</param>
        /// <returns>等级配置列表</returns>
        Task<List<LevelConfig>> GetOrSetLevelConfigsAsync(string systemName, Func<Task<List<LevelConfig>>> factory);
        
        /// <summary>
        /// 缓存等级计算结果
        /// </summary>
        /// <param name="exp">经验值</param>
        /// <param name="systemName">系统名称</param>
        /// <param name="calculator">计算器方法</param>
        /// <returns>等级</returns>
        Task<int> GetOrCalculateLevelAsync(long exp, string systemName, Func<Task<int>> calculator);
        
        /// <summary>
        /// 清除缓存
        /// </summary>
        /// <param name="systemName">系统名称，为空则清除所有</param>
        /// <returns>是否成功</returns>
        Task<bool> ClearCacheAsync(string? systemName = null);
        
        /// <summary>
        /// 预热缓存
        /// </summary>
        /// <param name="systemName">系统名称</param>
        /// <returns>是否成功</returns>
        Task<bool> WarmupCacheAsync(string systemName = "pet");
    }
}
