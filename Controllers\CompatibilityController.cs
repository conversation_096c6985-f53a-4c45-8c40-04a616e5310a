using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.Services;
using WebApplication_HM.Models;
using WebApplication_HM.Interface;
using WebApplication_HM.DTOs.RequestDTO;
using WebApplication_HM.DTOs.ResultDTO;
using WebApplication_HM.Services.PropScript;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 兼容性API控制器 - 支持前端旧式调用
    /// </summary>
    [ApiController]
    [Route("api")]
    public class CompatibilityController : ControllerBase
    {
        private readonly PropService _propService;
        private readonly IPlayerService _playerService;
        private readonly ILogger<CompatibilityController> _logger;

        public CompatibilityController(
            PropService propService, 
            IPlayerService playerService,
            ILogger<CompatibilityController> logger)
        {
            _propService = propService;
            _playerService = playerService;
            _logger = logger;
        }

        /// <summary>
        /// 获取道具列表 - 兼容前端getPropList调用
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <param name="Type">类型 (pack=背包)</param>
        /// <returns>道具列表JSON</returns>
        [HttpGet("getPropList")]
        public async Task<ActionResult> GetPropList(
            [FromQuery] string username, 
            [FromQuery] string password, 
            [FromQuery] string Type = "pack")
        {
            try
            {
                // 1. 验证用户身份
                var loginResult = _playerService.Login(new LoginRequestDTO 
                { 
                    Number = username, 
                    Password = password 
                });

                if (!loginResult.Success)
                {
                    return Unauthorized("用户名或密码错误");
                }

                // 2. 获取用户道具
                List<PropInfo> items;
                if (Type == "pack")
                {
                    // 获取背包道具 (位置=1)
                    items = await _propService.GetALPPAsync(loginResult.UserId, PropLoaction.背包);
                }
                else
                {
                    // 获取所有道具
                    items = await _propService.GetPAPAsync(loginResult.UserId);
                }

                // 3. 转换为前端期望的格式
                var result = items.Select(item => new
                {
                    id = item.ItemSeq,           // 道具序号
                    itemId = item.ItemId,        // 道具ID
                    name = item.ItemName,        // 道具名称
                    count = item.ItemCount,      // 道具数量
                    icon = item.ItemIcon,        // 道具图标
                    price = item.ItemPrice,      // 道具价格
                    position = item.ItemPos,     // 道具位置
                    description = item.Description // 道具描述
                }).ToList();

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取道具列表失败，用户: {Username}", username);
                return StatusCode(500, "获取道具列表失败");
            }
        }

        /// <summary>
        /// 使用道具 - 兼容前端useProp调用
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <param name="id">道具序号</param>
        /// <returns>使用结果</returns>
        [HttpGet("useProp")]
        public async Task<ActionResult> UseProp(
            [FromQuery] string username, 
            [FromQuery] string password, 
            [FromQuery] int id)
        {
            try
            {
                // 1. 验证用户身份
                var loginResult = _playerService.Login(new LoginRequestDTO 
                { 
                    Number = username, 
                    Password = password 
                });

                if (!loginResult.Success)
                {
                    return Unauthorized("用户名或密码错误");
                }

                // 2. 获取道具信息
                var item = await _propService.GetAP_XHAsync(id.ToString());
                if (item == null)
                {
                    return NotFound("道具不存在");
                }

                // 3. 执行道具使用逻辑
                var scriptRequest = new PropScriptExecuteRequest
                {
                    UserId = loginResult.UserId,
                    ItemId = item.ItemId,
                    UseCount = 1
                };

                var result = _propService.UseItemWithScript(scriptRequest);

                // 4. 返回结果
                if (result.Success)
                {
                    return Ok(new {
                        success = true,
                        message = result.Message,
                        data = result.ExtraData
                    });
                }
                else
                {
                    return BadRequest(new {
                        success = false,
                        message = result.Message
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "使用道具失败，用户: {Username}, 道具ID: {ItemId}", username, id);
                return StatusCode(500, "使用道具失败");
            }
        }
    }
}
