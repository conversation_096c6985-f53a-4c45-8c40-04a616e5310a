using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.DTOs.PetManagement;
using WebApplication_HM.Services;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 宠物管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class PetManagementController : ControllerBase
    {
        private readonly PetManagementService _petManagementService;
        private readonly ILogger<PetManagementController> _logger;

        public PetManagementController(
            PetManagementService petManagementService,
            ILogger<PetManagementController> logger)
        {
            _petManagementService = petManagementService;
            _logger = logger;
        }

        /// <summary>
        /// 获取牧场宠物列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="status">宠物状态筛选</param>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页数量</param>
        /// <param name="sortBy">排序字段</param>
        /// <param name="sortDirection">排序方向</param>
        /// <returns>宠物列表</returns>
        [HttpGet("pasture/{userId}")]
        public async Task<IActionResult> GetPasturePets(
            int userId,
            [FromQuery] string status = "牧场",
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 100,
            [FromQuery] string sortBy = "pet_no",
            [FromQuery] string sortDirection = "asc")
        {
            try
            {
                if (userId <= 0)
                {
                    return BadRequest(new { success = false, message = "用户ID无效" });
                }

                var request = new PasturePetListRequestDTO
                {
                    UserId = userId,
                    Status = status,
                    Page = page,
                    PageSize = Math.Min(pageSize, 100), // 限制最大页面大小
                    SortBy = sortBy,
                    SortDirection = sortDirection
                };

                var result = await _petManagementService.GetPasturePetsAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取牧场宠物列表失败 - 用户ID: {UserId}", userId);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 携带宠物到背包
        /// </summary>
        /// <param name="request">携带宠物请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("carry")]
        public async Task<IActionResult> CarryPet([FromBody] CarryPetRequestDTO request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { success = false, message = "请求参数无效", errors = ModelState });
                }

                var result = await _petManagementService.CarryPetAsync(request);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "携带宠物失败 - 用户ID: {UserId}, 宠物ID: {PetId}", 
                    request.UserId, request.PetId);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 存放宠物到牧场
        /// </summary>
        /// <param name="request">存放宠物请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("store")]
        public async Task<IActionResult> StorePet([FromBody] StorePetRequestDTO request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { success = false, message = "请求参数无效", errors = ModelState });
                }

                var result = await _petManagementService.StorePetAsync(request);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "存放宠物失败 - 用户ID: {UserId}, 宠物ID: {PetId}", 
                    request.UserId, request.PetId);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 设置主战宠物
        /// </summary>
        /// <param name="request">设置主宠请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("setMain")]
        public async Task<IActionResult> SetMainPet([FromBody] SetMainPetRequestDTO request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { success = false, message = "请求参数无效", errors = ModelState });
                }

                var result = await _petManagementService.SetMainPetAsync(request);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置主战宠物失败 - 用户ID: {UserId}, 宠物ID: {PetId}", 
                    request.UserId, request.PetId);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 丢弃宠物
        /// </summary>
        /// <param name="request">丢弃宠物请求</param>
        /// <returns>操作结果</returns>
        [HttpDelete("discard")]
        public async Task<IActionResult> DiscardPet([FromBody] DiscardPetRequestDTO request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { success = false, message = "请求参数无效", errors = ModelState });
                }

                var result = await _petManagementService.DiscardPetAsync(request);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "丢弃宠物失败 - 用户ID: {UserId}, 宠物ID: {PetId}", 
                    request.UserId, request.PetId);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 重命名宠物
        /// </summary>
        /// <param name="request">重命名请求</param>
        /// <returns>操作结果</returns>
        [HttpPut("rename")]
        public async Task<IActionResult> RenamePet([FromBody] RenamePetRequestDTO request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { success = false, message = "请求参数无效", errors = ModelState });
                }

                var result = await _petManagementService.RenamePetAsync(request);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重命名宠物失败 - 用户ID: {UserId}, 宠物ID: {PetId}", 
                    request.UserId, request.PetId);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 获取牧场容量信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>容量信息</returns>
        [HttpGet("capacity/{userId}")]
        public async Task<IActionResult> GetPastureCapacity(int userId)
        {
            try
            {
                if (userId <= 0)
                {
                    return BadRequest(new { success = false, message = "用户ID无效" });
                }

                var result = await _petManagementService.GetPastureCapacityAsync(userId);
                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取牧场容量信息失败 - 用户ID: {UserId}", userId);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 获取宠物详细信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="petId">宠物ID</param>
        /// <returns>宠物详细信息</returns>
        [HttpGet("detail/{userId}/{petId}")]
        public async Task<IActionResult> GetPetDetail(int userId, int petId)
        {
            try
            {
                if (userId <= 0 || petId <= 0)
                {
                    return BadRequest(new { success = false, message = "参数无效" });
                }

                // 这里可以调用现有的 PlayerService.GetPetDetail 方法
                // 或者在 PetManagementService 中实现新的详情获取方法
                
                return Ok(new { success = true, message = "功能开发中" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物详情失败 - 用户ID: {UserId}, 宠物ID: {PetId}", userId, petId);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 批量操作宠物
        /// </summary>
        /// <param name="request">批量操作请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("batch")]
        public async Task<IActionResult> BatchOperation([FromBody] BatchPetOperationRequestDTO request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { success = false, message = "请求参数无效", errors = ModelState });
                }

                if (request.PetIds.Count > 10)
                {
                    return BadRequest(new { success = false, message = "批量操作最多支持10只宠物" });
                }

                var results = new List<object>();
                var successCount = 0;
                var failCount = 0;

                foreach (var petId in request.PetIds)
                {
                    PetOperationResultDTO result;
                    
                    switch (request.Operation.ToLower())
                    {
                        case "carry":
                            result = await _petManagementService.CarryPetAsync(new CarryPetRequestDTO 
                            { 
                                UserId = request.UserId, 
                                PetId = petId 
                            });
                            break;
                        case "store":
                            result = await _petManagementService.StorePetAsync(new StorePetRequestDTO 
                            { 
                                UserId = request.UserId, 
                                PetId = petId 
                            });
                            break;
                        default:
                            result = new PetOperationResultDTO 
                            { 
                                Success = false, 
                                Message = "不支持的操作类型" 
                            };
                            break;
                    }

                    results.Add(new { petId, result.Success, result.Message });
                    
                    if (result.Success)
                        successCount++;
                    else
                        failCount++;
                }

                return Ok(new 
                { 
                    success = true, 
                    message = $"批量操作完成：成功{successCount}个，失败{failCount}个",
                    data = new 
                    {
                        successCount,
                        failCount,
                        results
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量操作宠物失败 - 用户ID: {UserId}", request.UserId);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 获取数据迁移验证报告
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>迁移验证报告</returns>
        [HttpGet("migration-report/{userId}")]
        public async Task<IActionResult> GetMigrationReport(int userId)
        {
            try
            {
                var result = await _petManagementService.GetMigrationReportAsync(userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取迁移报告失败 - 用户ID: {UserId}", userId);
                return StatusCode(500, new { success = false, message = "服务器内部错误" });
            }
        }
    }
}
