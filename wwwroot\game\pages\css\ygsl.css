.main_box {
    background-image: url(../img/yg/bg.png);
    width: 788px;
    height: 311px;
}
body,html{
    padding: 0px;
    margin: 0px;
}
.btn{
    background-image: url(../img/yg/btn.png);
    background-repeat: no-repeat;
    height: 45px;
    width: 111px;
    cursor: pointer;
    float:left;
    margin:10px
}
.btn:hover{
    background-image: url(../img/yg/btn_hover.png);
    background-repeat: no-repeat;
}
.btn .btnTitle{
    text-align: center;
    line-height: 45px;
    text-align: center;
    line-height: 45px;
    font-weight: bold;
}
.btns_left{
    margin-left:50px;
    margin-top:244px;
    float:left;
}
.btns_right{
    margin-left:125px;
    margin-top:244px;
    float:left;
}
.end_btn{
    margin-left:30px
}
.ICO{
    position: absolute;
    left: 330px;
    top: 68px;
    cursor: pointer;
}
.ICO img{
    width: 80px;
}
.tip {
    background-image: url(../images/tip.png);
    width: 166px;
    height: 46px;
    line-height: 20px;
    position: absolute;
    color: #fff;
    font-size: 12px;
    padding: 17px;
    background-repeat: no-repeat;
    text-align: center;
    left: 100px;
    display: none;
}
.returnPage{
    background-image: url(../img/yg/close.png);
    width: 40px;
    height: 40px;
    position: absolute;
    top:10px;
    left:10px;
    background-repeat: no-repeat;
    background-position-x: 5px;
    
    background-position-y: 5px;
    cursor: pointer;
}
.returnPage:hover{

    background-image: url(../img/yg/close_hover.png);
    
    background-position-x: 0px;
    
    background-position-y: 0px;
}
.wenhao{
    background-image: url(../img/yg/wenhao.png);
    width: 32px;
    height: 32px;
    position: absolute;
    top: 51px;
    left: 8px;
    background-repeat: no-repeat;
    background-position-x: 5px;
    background-position-y: 5px;
    cursor: pointer;
}
.wenhao:hover{

    background-image: url(../img/yg/wenhao_hover.png);
    
    background-position-x: 0px;
    
    background-position-y: 0px;
    width: 32px;
    height: 32px;
}
.hq_info {
    background-image: url(../images/hei_box.png);
    height: 301px;
    width: 188px;
    margin-top: 0px;
    margin-left: 411px;
    color: #c7c7c7;
    padding: 10px;
    text-align: center;
    cursor: pointer;
    line-height: 19px;
    font-size: 15px;
    position: absolute;
    display: none;
    background-repeat: no-repeat;
}
.hq_info_jc {
    line-height: auto;
    position: absolute;
    margin-left: 6px;
    margin-top: -30px;
    padding: 2px;
    padding-left: 5px;
    padding-right: 5px;
}

.hq_info_jc1 {
    position: absolute;
    margin-left: 114px;
    margin-top: -30px;
    padding: 2px;
    padding-left: 5px;
    padding-right: 5px;
    width: 60px;
    text-align: right;
}

.lzlh{
    width: 370px;
    height: 247px;
    position: absolute;
    z-index: 999;
    background-image: url(../img/yg/lzlh/框.png);
    background-repeat: no-repeat;
    left: 220px;
    top: 20px;

}
.lzlh .close{
    width: 21px;
    height: 22px;
    position: absolute;
    background-image: url(../img/yg/lzlh/关闭_默认.png);
    background-repeat: no-repeat;
    left: 326px;
    top: 14px;
    cursor: pointer;
}
.lzlh .selectPop{
    margin: 60px 22px 0;
    position: relative;
}
.lzlh .use{
    margin: 0px 0 0 40px;
    position: absolute;
    top: 145px;
    left: 0;
}
.lzlh .use .span{
    color: #ed4040;
    font-weight: bold;
    text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.5); /* 水平偏移 阴影模糊值 阴影颜色 */
    filter: progid:DXImageTransform.Microsoft.Shadow(color=#333333, direction=135, strength=3); /* IE6 阴影滤镜 */
}
.lzlh .lz_size{
    width: 104px;
    height: 21px;
    margin: 8px 0px;
    background-repeat: no-repeat;
}
.lzlh .close:hover{
    background-image: url(../img/yg/lzlh/关闭_激活.png);
}

.lzlh .use .input{
    background-image: url(../img//yg/lzlh/使用数量输入框.png);
    width: 209px;
    height: 25px;
    padding-bottom: 5px;
    text-indent: 5px;
    text-align: center;
    background-color: transparent;
    outline: none;
    border: none;
    background-repeat: no-repeat;
}
.lzlh .use .useBtn{
    background-image: url(../img//yg/lzlh/使用道具.png);
    width: 72px;
    height: 38px;
    position: absolute;
    left: 220px;
    top: 15px;
    cursor: pointer;
}
.wclz{
    cursor: pointer;
    position: absolute;
    top: 0px;
    left: 10px;
    background-image: url(../img//yg/lzlh/五彩龙珠--点击前.png);
}
.wclz_xz{
    background-image: url(../img//yg/lzlh/五彩龙珠--选中后.png);
}
.chlz{
    cursor: pointer;
    position: absolute;
    top: 0px;
    left: 114px;
    background-image: url(../img//yg/lzlh/彩虹龙珠--点击前.png);
}
.chlz_xz{
    background-image: url(../img//yg/lzlh/彩虹龙珠--选中后.png);
}
.hclz{
    cursor: pointer;
    position: absolute;
    top: 0px;
    left: 218px;
    background-image: url(../img//yg/lzlh/幻彩龙珠--点击前.png);
}
.hclz_xz{
    background-image: url(../img//yg/lzlh/幻彩龙珠--选中后.png);
}
.mxlz{
    cursor: pointer;
    position: absolute;
    top: 30px;
    left: 60px;
    background-image: url(../img//yg/lzlh/梦想龙珠--点击前.png);
}
.mxlz_xz{
    background-image: url(../img//yg/lzlh/梦想龙珠--选中后.png);
}
.zzlz{
    cursor: pointer;
    position: absolute;
    top: 30px;
    left: 164px;
    background-image: url(../img//yg/lzlh/至尊龙珠--点击前.png);
}
.zzlz_xz{
    background-image: url(../img//yg/lzlh/至尊龙珠--选中后.png);
}